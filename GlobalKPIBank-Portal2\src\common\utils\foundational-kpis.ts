interface FoundationalKpisItems {
    NAME: string
    EXTERNAL_ID: string
}

const currentYear =
    new Date().getMonth() == 0 || (new Date().getMonth() == 1 && new Date().getDate() < 11)
        ? new Date().getFullYear() - 1
        : new Date().getFullYear()

export const FoundationalKpis: Record<string, FoundationalKpisItems> = {
    FLAWLESS_DAYS: {
        NAME: 'Flawless Days',
        EXTERNAL_ID: 'GKPI-SOL-FLD',
    },
    ACTUAL_COST_FORECAST: {
        NAME: 'Actual Cost - Forecast',
        EXTERNAL_ID: 'GKPI-SOL-ACF',
    },
    ACTUAL_COST: {
        NAME: 'Actual Cost',
        EXTERNAL_ID: 'GKPI-SOL-APC',
    },
    FORECAST_COST: {
        NAME: 'Forecast Cost',
        EXTERNAL_ID: 'GKPI-SOL-FPC',
    },
    ACTUAL_PRODUCTION_PLAN_FORECAST: {
        NAME: 'Actual Production Plan - Forecast',
        EXTERNAL_ID: 'GKPI-SOL-APF',
    },
    ACTUAL_PRODUCTION: {
        NAME: 'Actual Production',
        EXTERNAL_ID: 'GKPI-SOL-APV',
    },
    FORECAST_PRODUCTION: {
        NAME: 'Forecast Production',
        EXTERNAL_ID: 'GKPI-SOL-FPV',
    },
    DOLLAR_PER_KG_ACTUAL_FORECAST: {
        NAME: '$/KG Actual - Forecast',
        EXTERNAL_ID: 'GKPI-SOL-AOP',
    },
    PLANT_CASH_MARGIN: {
        NAME: 'Plant Cash Margin',
        EXTERNAL_ID: 'GKPI-SOL-PCM',
    },
    PLANT_CASH_MARGIN_PER_KG: {
        NAME: 'Plant Cash Margin/KG',
        EXTERNAL_ID: 'GKPI-SOL-MKG',
    },
    KG_PER_HEADCOUNT: {
        NAME: 'KG/Headcount',
        EXTERNAL_ID: 'GKPI-SOL-KGH',
    },
    PRODUCTIVITY_YEAR_FORECAST: {
        NAME: `Productivity - ${currentYear}`,
        EXTERNAL_ID: 'GKPI-SOL-PRD',
    },
    PRODUCTIVITY_PIPELINE_NEXT_YEAR: {
        NAME: `Productivity Pipeline - ${currentYear + 1}`,
        EXTERNAL_ID: 'GKPI-SOL-PNY',
    },
    PRODUCTIVITY_PIPELINE_PLUS_TWO_YEARS: {
        NAME: `Productivity Pipeline - ${currentYear + 2}`,
        EXTERNAL_ID: 'GKPI-SOL-PPN',
    },
} as const

export const FoundationalKpisKpidom: Record<string, FoundationalKpisItems> = {
    FLAWLESS_DAYS: {
        NAME: 'Flawless Days',
        EXTERNAL_ID: 'KPIG-FLD',
    },
    ACTUAL_COST_FORECAST: {
        NAME: 'Actual Cost - Forecast',
        EXTERNAL_ID: 'KPIG-ACF',
    },
    ACTUAL_COST: {
        NAME: 'Actual Cost',
        EXTERNAL_ID: 'KPIG-ACS',
    },
    FORECAST_COST: {
        NAME: 'Forecast Cost',
        EXTERNAL_ID: 'KPIG-FCS',
    },
    ACTUAL_PRODUCTION_PLAN_FORECAST: {
        NAME: 'Actual Production Plan - Forecast',
        EXTERNAL_ID: 'KPIG-APF',
    },
    ACTUAL_PRODUCTION: {
        NAME: 'Actual Production',
        EXTERNAL_ID: 'KPIG-APV',
    },
    FORECAST_PRODUCTION: {
        NAME: 'Forecast Production',
        EXTERNAL_ID: 'KPIG-FPV',
    },
    DOLLAR_PER_KG_ACTUAL_FORECAST: {
        NAME: '$/KG Actual to Forecast',
        EXTERNAL_ID: 'KPIG-AOP',
    },
    PLANT_CASH_MARGIN: {
        NAME: 'Plant Cash Margin',
        EXTERNAL_ID: 'KPIG-PCM',
    },
    PLANT_CASH_MARGIN_PER_KG: {
        NAME: 'Plant Cash Margin/KG',
        EXTERNAL_ID: 'KPIG-MKG',
    },
    KG_PER_HEADCOUNT: {
        NAME: 'KG/Headcount',
        EXTERNAL_ID: 'KPIG-KGH',
    },
    PRODUCTIVITY_YEAR_FORECAST: {
        NAME: `Productivity - ${currentYear}`,
        EXTERNAL_ID: 'KPIG-PRD',
    },
    PRODUCTIVITY_PIPELINE_NEXT_YEAR: {
        NAME: `Productivity Pipeline - ${currentYear + 1}`,
        EXTERNAL_ID: 'KPIG-PNY',
    },
    PRODUCTIVITY_PIPELINE_PLUS_TWO_YEARS: {
        NAME: `Productivity Pipeline - ${currentYear + 2}`,
        EXTERNAL_ID: 'KPIG-PPN',
    },
} as const
