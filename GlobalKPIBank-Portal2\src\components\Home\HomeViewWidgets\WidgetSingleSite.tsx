import { numberFormat } from '@/common/utils/numberFormat'
import { Box, Card, CardActionArea, CardContent, Typography } from '@mui/material'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import TrendingDownIcon from '@mui/icons-material/TrendingDown'
import './styles.css'

interface WidgetSingleSiteProps {
    isRed: boolean
    kpiValue?: any
    kpi?: any
    diff: number
    onClick?: (filters: any) => void
}

function getTargetValue(diff: number, isRed: boolean) {
    const color = isRed ? 'red' : 'green'

    return (
        <>
            (
            {Math.sign(diff) === 1 ? (
                <TrendingUpIcon
                    sx={{
                        color: color,
                        verticalAlign: 'middle',
                    }}
                />
            ) : (
                <TrendingDownIcon
                    sx={{
                        color: color,
                        verticalAlign: 'middle',
                    }}
                />
            )}{' '}
            {numberFormat(Math.abs(diff)) ?? '--'})
        </>
    )
}

export const WidgetSingleSite: React.FC<WidgetSingleSiteProps> = ({ isRed, kpiValue, kpi, diff, onClick }) => {
    const handleOpenModal = (site: any) => {
        onClick({ openModal: true, kpi: kpi, site: site })
    }

    return (
        <Card className="card" sx={{ borderColor: isRed ? 'red' : 'green' }}>
            <CardActionArea onClick={() => handleOpenModal(kpi.data[0].refReportingSite)}>
                <CardContent>
                    <>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'row',
                                color: isRed ? 'red' : 'green',
                                alignItems: 'baseline',
                            }}
                        >
                            <Box>
                                <Typography sx={{ fontSize: '36px' }}>{numberFormat(kpiValue)}</Typography>
                            </Box>
                            <Box sx={{ marginLeft: '5px' }}>
                                {isRed ? getTargetValue(diff, isRed) : getTargetValue(diff, isRed)}
                            </Box>
                        </Box>
                        <Box>
                            <Typography>{kpi.kpi.name}</Typography>
                        </Box>
                    </>
                </CardContent>
            </CardActionArea>
        </Card>
    )
}
