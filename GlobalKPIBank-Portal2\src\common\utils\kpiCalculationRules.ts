const foundationalTargets: Record<string, string> = {
    'KPIG-FLD': 'Average', // Flawless days
    'KPIG-ACF': 'Average', // Actual Cost - Forecast
    'KPIG-APF': 'Average', // Actual Production Plan - Forecast
    'KPIG-PCM': 'Average', // Plant Cash Margin
    'KPIG-MKG': 'Average', // Plant Cash Margin/KG
    'KPIG-KGH': 'Average', // KG/Heacount
    'KPIG-PRD': 'Sum', // Productivity - Year Forecast
    'KPIG-PNY': 'Sum', // Productivity Pipeline - Next Year
    'KPIG-PPN': 'Sum', // // Productivity Pipeline - +2 Years
}

const stewardshipTargets: Record<string, string> = {
    'KPIG-TTL': 'Sum', // TRIR Combined
    'KPIG-TEP': 'Sum', // TRIR Employee
    'KPIG-TCT': 'Sum', // TRIR Contractor
    'KPIG-RCI': 'Sum', // People Safety - Tier 1/2 Recordables
    'KPIG-NFA': 'Sum', // People Safety - Tier 3 First Aids
    'KPIG-PST': 'Sum', // Process Safety - Tier 1/2
    'KPIG-EVT': 'Sum', // Environmental - Tier 1/2
    'KPIG-NFT': 'Sum', // Fire - Tier 1/2
    'KPIG-NNM': 'Sum', // Near Misses
    'KPIG-HPE': 'Sum', // High Potential Events
}

const qualityTargets: Record<string, string> = {
    'KPIG-HQN': 'Sum', // HS QN1s
    'KPIG-QN1': 'Sum', // QN1s
    'KPIG-MAF': 'Sum', // Major Audir Findings - External
    'KPIG-FGB': 'Sum', // Block Stock - Finished Goods
}

const reliabilityTargets: Record<string, string> = {
    'KPIG-OEE': 'Average', // Overall Equipment Effectiveness
}

export const allTargets: Record<string, string> = {
    ...foundationalTargets,
    ...stewardshipTargets,
    ...qualityTargets,
    ...reliabilityTargets,
}

export const targetsExternalId = Object.keys(allTargets)

export const targetsOperationType = Object.values(allTargets)

export const getOperatorType = (kpiExternalId: string): string => {
    const idIndex = targetsExternalId.indexOf(kpiExternalId)
    if (idIndex === -1) return 'Average'

    return targetsOperationType[idIndex]
}
