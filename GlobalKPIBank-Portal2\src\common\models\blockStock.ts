import { ExternalEntity } from '.'
import { BusinessSegment } from './business-segment'
import { Site } from './site'
import { UnitOfMeasurement } from './unitOfMeasurement'

export interface BlockStockKpi extends ExternalEntity {
    manufacturingReportingSite: Site
    materialNumber: number | null
    blockedQuantity: number | null
    blockStockType: string
    unitOfMeasurement: UnitOfMeasurement | null
    businessSegment: BusinessSegment | null
}
