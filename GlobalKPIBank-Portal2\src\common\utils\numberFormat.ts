export function numberFormat(input: number, args = 0) {
    if (Number.isNaN(input)) return null
    const suffixes = ['K', 'M', 'G', 'T', 'P', 'E'],
        abs = Math.abs(input)

    if (abs < 1000) return (input < 0 ? '-' : '') + abs.toFixed(Number.isInteger(abs) ? 0 : 2)

    const exp = Math.floor(Math.log10(abs) / 3),
        num = abs / 1000 ** exp

    return (input < 0 ? '-' : '') + num.toFixed(num % 1 ? 2 : args) + suffixes[exp - 1]
}
