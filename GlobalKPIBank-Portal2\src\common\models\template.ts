import { Dayjs } from 'dayjs'
import { ExternalEntity } from '.'
import { Asset } from './asset'
import { TemplateItem } from './template-item'

export interface Template extends ExternalEntity {
    title: string
    description: string
    labels: string[]
    visibility: string
    status: string
    rootLocation: Asset
    assignedTo: string[]
    templateItems: TemplateItem[]
    createdTime: Dayjs
    lastUpdatedTime: Dayjs
    isArchived: boolean | undefined
}
