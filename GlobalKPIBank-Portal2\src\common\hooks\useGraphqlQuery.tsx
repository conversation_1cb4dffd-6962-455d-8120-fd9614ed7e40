import {
    ApolloError,
    ApolloQueryResult,
    DocumentNode,
    FetchMoreQueryOptions,
    OperationVariables,
    QueryHookOptions,
    useQuery,
} from '@apollo/client'
import { useMemo } from 'react'

export interface PageInfo {
    hasPreviousPage: boolean
    hasNextPage: boolean
    startCursor: string
    endCursor: string
}

export type UseGraphqlQueryResult<T> = {
    data: T[]
    pageInfo: PageInfo
    loading: boolean
    error?: ApolloError
    refetch: () => Promise<ApolloQueryResult<any>>
    fetchMore: <TFetchData = any, TFetchVars extends OperationVariables = any>(
        fetchMoreOptions: FetchMoreQueryOptions<any, any> & {
            updateQuery?: (
                previousQueryResult: any,
                options: {
                    fetchMoreResult: TFetchData
                    variables: TFetchVars
                }
            ) => any
        }
    ) => Promise<ApolloQueryResult<TFetchData>>
}

export function useGraphqlQuery<T>(
    query: DocumentNode,
    listName: string,
    options?: QueryHookOptions<any, any>
): UseGraphqlQueryResult<T> {
    const { variables, ...allOptions } = options || {}

    const defaultOptions = useMemo(
        () => ({
            variables: variables,
            notifyOnNetworkStatusChange: true,
            ...allOptions,
        }),
        [options]
    )
    const { data, loading, error, refetch, fetchMore } = useQuery(query, defaultOptions)

    const listData = useMemo<T[]>(() => data?.[listName]?.items ?? [], [data, listName])
    const pageInfo = useMemo<PageInfo>(() => data?.[listName]?.pageInfo ?? {}, [data, listName])

    return useMemo(
        () => ({ data: listData, pageInfo: pageInfo, loading, error, refetch, fetchMore }),
        [error, listData, pageInfo, loading, refetch, fetchMore]
    )
}
