"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/common/hooks/detailed-data/useQualityNotification.tsx":
/*!*******************************************************************!*\
  !*** ./src/common/hooks/detailed-data/useQualityNotification.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQualityNotification: function() { return /* binding */ useQualityNotification; }\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/graphql-tag/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useGraphqlQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useGraphqlQuery */ \"(app-pages-browser)/./src/common/hooks/useGraphqlQuery.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _common_utils_kpis_data_table_export__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/common/utils/kpis-data-table-export */ \"(app-pages-browser)/./src/common/utils/kpis-data-table-export.ts\");\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst queryBuilder = (request)=>{\n    let siteQ = \"\";\n    let businessSegmentQ = \"\";\n    let startDateQ = \"\";\n    let endDateQ = \"\";\n    let fixedFilters = \"\";\n    let limitQ = \"\";\n    let cursorQ = \"\";\n    if (request && request.kpiFilters.refSite.length > 0) {\n        const sites = request.kpiFilters.refSite.map((site)=>'\"'.concat(site, '\"')).join(\",\");\n        siteQ = \"{aboutReportingSite: {externalId: {in: [\".concat(sites, \"]}}},\");\n    }\n    if (request && request.kpiFilters.businessSegment !== \"\") {\n        businessSegmentQ = '{businessSegment: {externalId: {eq: \"'.concat(request.kpiFilters.businessSegment, '\"}}},');\n    }\n    if (request && request.kpiFilters.date !== \"\") {\n        let sanitizedInitialDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(request.kpiFilters.date).startOf(\"month\").format(\"YYYY-MM-DD\");\n        let sanitizedEndDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(request.kpiFilters.date).endOf(\"month\").format(\"YYYY-MM-DD\");\n        const time = \"T00:00:00+00:00\";\n        if (request.exportFilters && request.exportFilters.isExport) {\n            [sanitizedInitialDate, sanitizedEndDate] = (0,_common_utils_kpis_data_table_export__WEBPACK_IMPORTED_MODULE_3__.setExportDateRange)(request.exportFilters.range, request.kpiFilters.date);\n        }\n        startDateQ = '{notificationDate: {gte: \"'.concat(sanitizedInitialDate).concat(time, '\"}},');\n        endDateQ = '{notificationDate: {lte: \"'.concat(sanitizedEndDate).concat(time, '\"}},');\n    }\n    if (request && request.kpiFilters.kpiName !== \"\") {\n        if (request.kpiFilters.kpiName === \"HS QN1s\") {\n            fixedFilters = '\\n                {notificationType: {eq: \"Q1\"}},\\n                {causeCodeGroup: {eq: \"QMMAN500\"}},\\n                {or: [{systemStatusMapping: {eq: \"Accepted\"}}, {systemStatusMapping: {eq: \"Open\"}}]},\\n            ';\n        }\n        if (request.kpiFilters.kpiName === \"QN1s\") {\n            fixedFilters = '\\n                {notificationType: {eq: \"Q1\"}},\\n                {systemStatusMapping: {eq: \"Accepted\"}},\\n                {causeCodeGroup: {eq: \"QMMAN500\"}},\\n                {not: {priority: {eq: \"E\"}}},\\n            ';\n        }\n        if (request.kpiFilters.kpiName === \"Major Audit Findings - External\") {\n            fixedFilters = '\\n                {notificationType: {eq: \"Q3\"}},\\n                {priority: {eq: \"H\"}},\\n                {or: [{systemStatusMapping: {eq: \"Accepted\"}}, {systemStatusMapping: {eq: \"Open\"}}]},\\n                {qualityManagementCodeGroup: {eq: \"QMI00013\"}},\\n                {qualityManagementCode: {eq: \"0002\"}},\\n            ';\n        }\n    }\n    if (request && request.limit && request.limit !== \"\") {\n        limitQ = \"first: \".concat(request.limit);\n    }\n    if (request.cursor && request.cursor !== \"\") {\n        cursorQ = 'after: \"'.concat(request.cursor, '\"');\n    }\n    return \"query getKpiDataView {\\n        listQualityNotification (\\n            \".concat(limitQ, \"\\n            \").concat(cursorQ, \"\\n            filter: {and: [\").concat(siteQ, \" \").concat(businessSegmentQ, \" \").concat(startDateQ, \" \").concat(endDateQ, \" \").concat(fixedFilters, \"]}\\n        ) {\\n            pageInfo {\\n                hasNextPage\\n                endCursor\\n            }\\n            items {\\n                aboutReportingSite {\\n                    externalId\\n                    name\\n                }\\n                notificationDescription\\n                notificationNumber\\n                notificationDate\\n                notificationType\\n                systemStatusMapping\\n                systemStatus\\n                priority\\n                causeCodeGroup\\n                qualityManagementCodeGroup\\n                qualityManagementCode\\n                severity\\n                subjectCode\\n                businessSegment {\\n                    externalId\\n                    description\\n                }\\n            }\\n        }\\n    }\");\n};\nconst checkSystemStatus = (kpiName, systemStatus)=>{\n    let result = false;\n    const systemStatusArray = systemStatus.split(\" \");\n    const rank = \"RANK\";\n    if (kpiName === \"HS QN1s\") {\n        const words = [\n            \"NOCO\",\n            \"NOPR\",\n            \"OSNO\"\n        ];\n        const containsAtLeastOne = words.some((word)=>systemStatusArray.includes(word));\n        const containsRank = systemStatusArray.includes(rank);\n        if (containsAtLeastOne && containsRank) result = true;\n    }\n    if (kpiName === \"QN1s\") {\n        const words = [\n            \"NOCO\"\n        ];\n        const containsAtLeastOne = words.some((word)=>systemStatusArray.includes(word));\n        const containsRank = systemStatusArray.includes(rank);\n        if (containsAtLeastOne && containsRank) result = true;\n    }\n    if (kpiName === \"Major Audit Findings - External\") result = true;\n    return result;\n};\nconst mapQualityNotification = (data, kpiName)=>{\n    const mappedResult = [];\n    if (data && data.length > 0) {\n        data.map((item)=>{\n            var _item_notificationDate;\n            const isValidSystemStatus = checkSystemStatus(kpiName, item.systemStatus);\n            var _item_aboutReportingSite_name, _item_notificationDescription, _item_notificationNumber, _item_notificationDate_toString_split_, _item_notificationType, _item_systemStatusMapping, _item_priority, _item_causeCodeGroup, _item_qualityManagementCodeGroup, _item_qualityManagementCode, _item_severity, _item_subjectCode, _item_businessSegment_description;\n            const result = {\n                siteName: (_item_aboutReportingSite_name = item.aboutReportingSite.name) !== null && _item_aboutReportingSite_name !== void 0 ? _item_aboutReportingSite_name : \"\",\n                notificationDescription: (_item_notificationDescription = item.notificationDescription) !== null && _item_notificationDescription !== void 0 ? _item_notificationDescription : \"\",\n                notificationNumber: (_item_notificationNumber = item.notificationNumber) !== null && _item_notificationNumber !== void 0 ? _item_notificationNumber : \"\",\n                notificationDate: (_item_notificationDate_toString_split_ = (_item_notificationDate = item.notificationDate) === null || _item_notificationDate === void 0 ? void 0 : _item_notificationDate.toString().split(\"T\")[0]) !== null && _item_notificationDate_toString_split_ !== void 0 ? _item_notificationDate_toString_split_ : \"\",\n                notificationType: (_item_notificationType = item.notificationType) !== null && _item_notificationType !== void 0 ? _item_notificationType : \"\",\n                systemStatusMapping: (_item_systemStatusMapping = item.systemStatusMapping) !== null && _item_systemStatusMapping !== void 0 ? _item_systemStatusMapping : \"\",\n                priority: (_item_priority = item.priority) !== null && _item_priority !== void 0 ? _item_priority : \"\",\n                causeCodeGroup: (_item_causeCodeGroup = item.causeCodeGroup) !== null && _item_causeCodeGroup !== void 0 ? _item_causeCodeGroup : \"\",\n                qualityManagementCodeGroup: (_item_qualityManagementCodeGroup = item.qualityManagementCodeGroup) !== null && _item_qualityManagementCodeGroup !== void 0 ? _item_qualityManagementCodeGroup : \"\",\n                qualityManagementCode: (_item_qualityManagementCode = item.qualityManagementCode) !== null && _item_qualityManagementCode !== void 0 ? _item_qualityManagementCode : \"\",\n                severity: (_item_severity = item.severity) !== null && _item_severity !== void 0 ? _item_severity : \"\",\n                subjectCode: (_item_subjectCode = item.subjectCode) !== null && _item_subjectCode !== void 0 ? _item_subjectCode : \"\",\n                businessSegment: (_item_businessSegment_description = item.businessSegment.description) !== null && _item_businessSegment_description !== void 0 ? _item_businessSegment_description : \"\"\n            };\n            if (isValidSystemStatus) mappedResult.push(result);\n        });\n    }\n    return mappedResult;\n};\nconst useQualityNotification = (request)=>{\n    _s();\n    const query = queryBuilder(request);\n    const { data: fdmData, pageInfo: fdmPageInfo, loading, refetch } = (0,_useGraphqlQuery__WEBPACK_IMPORTED_MODULE_1__.useGraphqlQuery)((0,_apollo_client__WEBPACK_IMPORTED_MODULE_4__.gql)(query), \"listQualityNotification\", {\n        fetchPolicy: \"network-only\",\n        context: {\n            clientName: \"qualityView\"\n        }\n    });\n    const [resultData, setResultData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        data: []\n    });\n    const [pageInfoData, setPageInfoData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        hasPreviousPage: false,\n        hasNextPage: false,\n        startCursor: \"\",\n        endCursor: \"\"\n    });\n    const [loadMore, setLoadMore] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const loadMoreExport = ()=>{\n        if (!pageInfoData.hasNextPage) return;\n        setLoadMore(!loadMore);\n    };\n    const triggerFilter = ()=>{\n        setPageInfoData({\n            hasPreviousPage: false,\n            hasNextPage: false,\n            startCursor: \"\",\n            endCursor: \"\"\n        });\n        setResultData({\n            data: []\n        });\n        setLoadMore(!refetch);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (request.kpiFilters.kpiName !== \"\") {\n            if (fdmData === undefined) {\n                setResultData((prevState)=>({\n                        ...prevState,\n                        loading: true\n                    }));\n            } else {\n                if (request.exportFilters.isExport) {\n                    const mappedResult = mapQualityNotification(fdmData, request.kpiFilters.kpiName);\n                    setResultData({\n                        data: mappedResult\n                    });\n                    setPageInfoData(fdmPageInfo);\n                }\n                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {\n                    const mappedResult = mapQualityNotification(fdmData, request.kpiFilters.kpiName);\n                    setResultData((prevData)=>{\n                        var _prevData_data;\n                        return {\n                            data: [\n                                ...(_prevData_data = prevData.data) !== null && _prevData_data !== void 0 ? _prevData_data : [],\n                                ...mappedResult\n                            ]\n                        };\n                    });\n                    setPageInfoData(fdmPageInfo);\n                }\n            }\n        }\n    }, [\n        fdmData,\n        request.kpiFilters.kpiName,\n        loadMore\n    ]);\n    return {\n        kpiDataModelView: resultData.data,\n        pageInfoDataModelView: pageInfoData,\n        loadingDataModelView: loading,\n        refetchDataModelView: triggerFilter,\n        loadMoreExport: loadMoreExport\n    };\n};\n_s(useQualityNotification, \"IQIJgWDJDBuq6crxPYS5OnqFtj0=\", false, function() {\n    return [\n        _useGraphqlQuery__WEBPACK_IMPORTED_MODULE_1__.useGraphqlQuery\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/common/hooks/detailed-data/useQualityNotification.tsx\n"));

/***/ })

});