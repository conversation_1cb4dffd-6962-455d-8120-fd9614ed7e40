import { ExternalEntity } from '.'
import { Asset } from './asset'
import { ChecklistItemFile } from './checklist-item-file'
import { ReportingLocation } from './reportingLocation'
import { User } from './user'

export type ObservationStatus = 'Completed' | 'Sent' | 'Draft' | 'Failed' | 'Created in SAP'

export const ObservationStatusEnum = {
    Completed: 'Completed',
    Sent: 'Sent',
    Draft: 'Draft',
    Failed: 'Failed',
    CreatedInSap: 'Created in SAP',
} as const

export type ObservationPriority = 'High' | 'Medium' | 'Low' | 'Urgent'

export const ObservationPriorityEnum = {
    High: 'High',
    Medium: 'Medium',
    Low: 'Low',
    Urgent: 'Urgent',
} as const

export interface Observation extends ExternalEntity {
    description: string
    troubleshooting: string
    files: ChecklistItemFile[]
    space: string
    sourceId?: string
    source?: string
    title?: string
    labels?: string[]
    visibility?: string
    createdBy: User
    updatedBy?: User
    isArchived?: boolean
    status: ObservationStatus
    asset?: Asset
    rootLocation: ReportingLocation
    priority?: ObservationPriority | number
    type?: string
    lastUpdatedTime: string
    createdTime: string
}
