import { gql } from '@apollo/client'
import { useMemo } from 'react'
import { useGraphqlQuery } from './useGraphqlQuery'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'
import { Notifications } from '../models/notifications'

export interface NotificationsQueryRequest {
    start?: number
    end?: number
    nextPage?: string
}

const buildNotificationsQuery = (request: NotificationsQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.start) {
        filter.push(`{ createdDate: { gte: ${request.start} }}`)
    }

    if (request.end) {
        filter.push(`{ createdDate: { lte: ${request.end} }}`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.SAP, getLocalUserSite()?.siteCode)}", "${GetSpace(EntityType.SAP)}" ]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetNotification {
            listNotification (
                filter: ${queryFilter}, 
                first: 1000, 
                after: ${request.nextPage ? `"${request.nextPage}"` : 'null'}) {
                items {
                    externalId
                    space
                    name
                    description
                    createdBy
                    createdDate
                    aboutFunctionalLocation {
                        externalId
                        description
                    }
                    priority
                    priorityDescription

                    priorityType
                    systemStatus
                    userStatus
                    workCenter
                    workCenterDescription
                    workOrderNumber
                    lastUpdatedTime
                    createdTime
                    notificationType
                    notificationTypeDescription
                    reportedBy
                }
                pageInfo {
                    hasPreviousPage
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    return query
}

export const useNotifications = (request: NotificationsQueryRequest) => {
    const query = useMemo(() => gql(buildNotificationsQuery(request)), [request])

    return useGraphqlQuery<Notifications>(query, 'listNotification')
}
