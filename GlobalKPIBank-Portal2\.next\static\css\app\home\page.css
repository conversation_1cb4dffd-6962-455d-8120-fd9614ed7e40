/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!./src/components/Home/HomeViewWidgets/styles.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
.card {
    margin: 12px 6px;
    border-left: 5px solid;
    border-top: 1px solid;
    border-bottom: 1px solid;
    border-right: 1px solid;
    width: 313px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start !important;
    align-content: center;
}

/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!./src/components/Charts/styles.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
.apexcharts-tooltip {
    color: black;
}

/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!./src/components/Home/HomeViewGeneral/styles.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
.generalCard {
    margin: 6px 6px;
    border-left: 5px solid;
    border-top: 1px solid;
    border-bottom: 1px solid;
    border-right: 1px solid;
    width: 100%;
    flex-basis: 48%;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start !important;
    align-content: center;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!./src/components/Home/HomeViewPage/styles.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
.align-test {
    display: flex;
    width: 100%;
    align-items: flex-start;
    justify-content: space-between;
}

