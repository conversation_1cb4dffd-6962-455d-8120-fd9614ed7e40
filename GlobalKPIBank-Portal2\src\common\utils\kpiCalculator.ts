import dayjs from 'dayjs'
import { GlobalVisionKpiTableModel } from '../models/globalVisionKpiTableModel'
import { getOperatorType } from './kpiCalculationRules'
import { GeneralFilter } from '../models/homeGeneralFilter'

export const getKpiValue = (kpi: GlobalVisionKpiTableModel, period: string, generalFilter: GeneralFilter): number => {
    const filterBy = generalFilter.externalId === 'MONTH' ? dayjs(period).format('MMMM').toLowerCase() : 'ytd'
    const operatorType = getOperatorType(kpi.kpi.externalId)
    let kpiValue = 0

    if (kpi.data.length === 1) {
        kpiValue = kpi.data[0][filterBy]
    } else {
        const allKpiValues = kpi.data.reduce((accumulator, currentItem) => {
            return accumulator + currentItem[filterBy]
        }, 0)
        kpiValue = allKpiValues
    }

    if (operatorType === 'Sum') {
        return kpiValue
    }
    return kpiValue / kpi.data.length
}
