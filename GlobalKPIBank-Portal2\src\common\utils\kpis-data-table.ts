interface KpisDataTable {
    NAME: string
    TAB_NAME: string[]
    VIEWS: string[]
    INFO: string
}

const currentYear =
    new Date().getMonth() == 0 || (new Date().getMonth() == 1 && new Date().getDate() < 11)
        ? new Date().getFullYear() - 1
        : new Date().getFullYear()

export const KpisDataViews: Record<string, KpisDataTable> = {
    FLAWLESS_DAYS: {
        NAME: 'Flawless Days',
        TAB_NAME: ['Flawless days', 'Definition'],
        VIEWS: ['KPIManualInput', 'KpiInfo'],
        INFO: 'FLD',
    },
    ACTUAL_COST_FORECAST: {
        NAME: 'Actual Cost - Forecast',
        TAB_NAME: ['Cost', 'Definition'],
        VIEWS: ['CostVariance', 'KpiInfo'],
        INFO: 'ACF',
    },
    ACTUAL_COST: {
        NAME: 'Actual Cost',
        TAB_NAME: ['Cost', 'Definition'],
        VIEWS: ['CostVariance', 'KpiInfo'],
        INFO: 'ACS',
    },
    FORECAST_COST: {
        NAME: 'Forecast Cost',
        TAB_NAME: ['Cost', 'Definition'],
        VIEWS: ['CostVariance', 'KpiInfo'],
        INFO: 'FCS',
    },
    ACTUAL_PRODUCTION_PLAN_FORECAST: {
        NAME: 'Actual Production Plan - Forecast',
        TAB_NAME: ['Volume', 'Definition'],
        VIEWS: ['VolumeVariance', 'KpiInfo'],
        INFO: 'APF',
    },
    DOLLAR_PER_KG_ACTUAL_FORECAST: {
        NAME: '$/KG Actual to Forecast',
        TAB_NAME: ['DollarperKG', 'Definition'],
        VIEWS: ['DollarPerKG', 'KpiInfo'],
        INFO: 'AOP',
    },
    KG_PER_HEADCOUNT: {
        NAME: 'KG/Headcount',
        TAB_NAME: ['Headcount', 'Volume', 'Definition'],
        VIEWS: ['KPIManualInput', 'VolumeVariance', 'KpiInfo'],
        INFO: 'KGH',
    },
    PLANT_CASH_MARGIN: {
        NAME: 'Plant Cash Margin',
        TAB_NAME: ['Cash Margin', 'Definition'],
        VIEWS: ['CashMargin', 'KpiInfo'],
        INFO: 'PCM',
    },
    PLANT_CASH_MARGIN_PER_KG: {
        NAME: 'Plant Cash Margin/KG',
        TAB_NAME: ['Cash Margin', 'Definition'],
        VIEWS: ['CashMargin', 'KpiInfo'],
        INFO: 'MKG',
    },
    PRODUCTIVITY_YEAR_FORECAST: {
        NAME: `Productivity - ${currentYear}`,
        TAB_NAME: ['Productivity', 'Definition'],
        VIEWS: ['Productivity', 'KpiInfo'],
        INFO: 'PRD',
    },
    PRODUCTIVITY_PIPELINE_NEXT_YEAR: {
        NAME: `Productivity Pipeline - ${currentYear + 1}`,
        TAB_NAME: ['Productivity', 'Definition'],
        VIEWS: ['Productivity', 'KpiInfo'],
        INFO: 'PRD',
    },
    PRODUCTIVITY_PIPELINE_PLUS_TWO_YEARS: {
        NAME: `Productivity Pipeline - ${currentYear + 2}`,
        TAB_NAME: ['Productivity', 'Definition'],
        VIEWS: ['Productivity', 'KpiInfo'],
        INFO: 'PRD',
    },
    HS_QN1S: {
        NAME: 'HS QN1s',
        TAB_NAME: ['Quality Notification', 'Definition'],
        VIEWS: ['QualityNotification', 'KpiInfo'],
        INFO: 'HQS',
    },
    QN1S: {
        NAME: 'QN1s',
        TAB_NAME: ['Quality Notification', 'Definition'],
        VIEWS: ['QualityNotification', 'KpiInfo'],
        INFO: 'QN1',
    },
    MAJOR_AUDIT_FINDINGS_EXTERNAL: {
        NAME: 'Major Audit Findings - External',
        TAB_NAME: ['Quality Notification', 'Definition'],
        VIEWS: ['QualityNotification', 'KpiInfo'],
        INFO: 'MAF',
    },
    BLOCK_STOCK_FINISHED_GOODS: {
        NAME: 'Block Stock - Finished Goods *',
        TAB_NAME: ['Block Stock', 'Definition'],
        VIEWS: ['BlockStock', 'KpiInfo'],
        INFO: 'FGB',
    },
    BLOCK_STOCK_GENERATED: {
        NAME: 'Block Stock - Generated*',
        TAB_NAME: ['Block Stock', 'Definition'],
        VIEWS: ['BlockStock', 'KpiInfo'],
        INFO: 'GBS',
    },
    BLOCK_STOCK_CONSUMED: {
        NAME: 'Block Stock - Consumed*',
        TAB_NAME: ['Block Stock', 'Definition'],
        VIEWS: ['BlockStock', 'KpiInfo'],
        INFO: 'CBS',
    },
    TRIR_COMBINED: {
        NAME: 'TRIR Combined*',
        TAB_NAME: ['Working Hour Report', 'Incidents', 'Definition'],
        VIEWS: ['WorkingHourReport', 'SwpIncidentImpact', 'KpiInfo'],
        INFO: 'TTL',
    },
    TRIR_EMPLOYEE: {
        NAME: 'TRIR Employee*',
        TAB_NAME: ['Working Hour Report', 'Incidents', 'Definition'],
        VIEWS: ['WorkingHourReport', 'SwpIncidentImpact', 'KpiInfo'],
        INFO: 'TEP',
    },
    TRIR_CONTRACTOR: {
        NAME: 'TRIR Contractor*',
        TAB_NAME: ['Working Hour Report', 'Incidents', 'Definition'],
        VIEWS: ['WorkingHourReport', 'SwpIncidentImpact', 'KpiInfo'],
        INFO: 'TCT',
    },
    PEOPLE_SAFETY_TIER_RECORDABLES: {
        NAME: 'Peolple Safety - Tier 1/2 Recordables',
        TAB_NAME: ['Incidents', 'Definition'],
        VIEWS: ['SwpIncidentImpact', 'KpiInfo'],
        INFO: 'RCI',
    },
    PEOPLE_SAFETY_TIER_FIRST_AIDS: {
        NAME: 'People Safety - Tier 3 First Aids',
        TAB_NAME: ['Incidents', 'Definition'],
        VIEWS: ['SwpIncidentImpact', 'KpiInfo'],
        INFO: 'NFA',
    },
    PROCESS_SAFETY_TIER: {
        NAME: 'Process Safety - Tier 1/2',
        TAB_NAME: ['Incidents', 'Definition'],
        VIEWS: ['SwpIncidentImpact', 'KpiInfo'],
        INFO: 'PST',
    },
    ENVIRONMENTAL_TIER: {
        NAME: 'Environmental - Tier 1/2',
        TAB_NAME: ['Incidents', 'Definition'],
        VIEWS: ['SwpIncidentImpact', 'KpiInfo'],
        INFO: 'EVT',
    },
    FIRE_TIER: {
        NAME: 'Fire - Tier 1/2',
        TAB_NAME: ['Incidents', 'Definition'],
        VIEWS: ['SwpIncidentImpact', 'KpiInfo'],
        INFO: 'NFT',
    },
    NEAR_MISSES: {
        NAME: 'Near Misses',
        TAB_NAME: ['Cority incidents', 'Enablon incidents', 'Definition'],
        VIEWS: ['IncidentImpact', 'Incident', 'KpiInfo'],
        INFO: 'NNM',
    },
    HIGH_POTENTIAL_EVENTS: {
        NAME: 'High Potential Events',
        TAB_NAME: ['Incidents', 'Definition'],
        VIEWS: ['SwpIncidentImpact', 'KpiInfo'],
        INFO: 'HPE',
    },
    OEE: {
        NAME: 'OEE',
        TAB_NAME: ['Kpi', 'Definition'],
        VIEWS: ['', 'KpiInfo'],
        INFO: 'OEE',
    },
} as const
