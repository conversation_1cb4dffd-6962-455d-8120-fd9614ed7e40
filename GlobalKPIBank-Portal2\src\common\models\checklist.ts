import { ChecklistItem } from './checklist-item'
import { ExternalEntity } from '.'
import { Dayjs } from 'dayjs'
import { UpdatedBy } from './updated-by'

export type ChecklistStatus = 'In progress' | 'Ready' | 'Done'

export const ChecklistStatusEnum = {
    InProgress: 'In progress',
    Ready: 'Ready',
    Done: 'Done',
} as const

export interface Checklist extends ExternalEntity {
    sourceId: string
    title: string
    description: string
    type: string
    status: ChecklistStatus
    startTime: Dayjs
    endTime: Dayjs
    checklistItems: ChecklistItem[]
    assignedTo: string[]
    isArchived: boolean
    lastUpdatedTime: Dayjs
    updatedBy: UpdatedBy
}
