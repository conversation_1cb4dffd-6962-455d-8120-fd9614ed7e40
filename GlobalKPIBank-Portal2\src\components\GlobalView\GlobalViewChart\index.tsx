import { VisionKpiAggregation } from '@/common/models/visionKpiAggregation'
import { BarChart, QuantityBarItemChart } from '../../Charts/BarChart'
import { useEffect, useState } from 'react'
import { translate } from '@celanese/celanese-sdk'

interface GlobalViewChartProps {
    data: VisionKpiAggregation
    range?: number
    selectedKpi?: string
}

export const GlobalViewChart: React.FC<GlobalViewChartProps> = ({ data, range, selectedKpi }) => {
    const [formattedData, setFormattedData] = useState<QuantityBarItemChart[]>([])

    useEffect(() => {
        const formattedDataAux: QuantityBarItemChart[] = []
        if (selectedKpi == 'KPIG-PCM' || selectedKpi == 'KPIG-MKG') {
            formattedDataAux.push({ label: 'Q1', value: data.january })
            formattedDataAux.push({ label: 'Q2', value: data.april })
            formattedDataAux.push({ label: 'Q3', value: data.july })
            formattedDataAux.push({ label: 'Q4', value: data.october })
        } else {
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.JANUARY'), value: data.january })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.FEBRUARY'), value: data.february })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.MARCH'), value: data.march })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.APRIL'), value: data.april })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.MAY'), value: data.may })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.JUNE'), value: data.june })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.JULY'), value: data.july })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.AUGUST'), value: data.august })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.SEPTEMBER'), value: data.september })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.OCTOBER'), value: data.october })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.NOVEMBER'), value: data.november })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.DECEMBER'), value: data.december })
        }

        setFormattedData(formattedDataAux)
    }, [data])

    return (
        <BarChart
            id={data.refReportingSite.externalId}
            data={formattedData}
            title={data.refReportingSite.name}
            range={range}
            selectedKpi={selectedKpi}
        />
    )
}

export default GlobalViewChart
