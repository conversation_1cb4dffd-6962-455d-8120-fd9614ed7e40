import { TableCell } from '@mui/material'
import styled from 'styled-components'

interface StripedCellProps {
    index: number
}

export const StripedCell = styled(TableCell)<StripedCellProps>(({ index }) => ({
    '&:nth-of-type(even)': {
        backgroundColor: index % 2 === 0 ? '#f9f9f9' : '#ffffff',
    },
    '&:nth-of-type(odd)': {
        backgroundColor: index % 2 === 0 ? '#f9f9f9' : '#ffffff',
    },
}))
