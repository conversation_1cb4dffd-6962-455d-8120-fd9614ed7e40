import { ExternalEntity } from '.'
import { BusinessSegment } from './business-segment'
import { Site } from './site'

export interface FlawlessDaysKpi extends ExternalEntity {
    refSite: Site
    startDate: Date | undefined
    endDate: Date | undefined
    kpiValue: number
    description: string
    createdAt: Date | undefined
    createdBy: Date | undefined
    refBusinessSegment: BusinessSegment | null
}
