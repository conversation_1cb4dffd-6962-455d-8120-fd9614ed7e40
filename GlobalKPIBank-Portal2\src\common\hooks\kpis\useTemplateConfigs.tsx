import { useEffect, useState } from 'react'
import { useQueryResultsFunction } from '../general-functions/useQueryResultsFunction'
import { EntityType, GetSpace } from '@/common/utils/space-util'
import { getLocalUserSite } from '@/common/utils'

export interface TemplateConfigQueryRequest {
    units?: string[]
    discipline?: string[]
    location?: string[]
    shift?: string[]
}

const buildChecklistItemStatusQuery = (request: TemplateConfigQueryRequest): any => {
    const mapFilters = (items: string[] | undefined, space: string) => {
        return items?.map((item) => ({
            externalId: item,
            space,
        }))
    }

    const unitsByFilter =
        request.units && request.units.length > 0 ? mapFilters(request.units, 'REF-COR-ALL-DAT') : undefined
    const locationByFilter =
        request.location && request.location.length > 0 ? mapFilters(request.location, 'REF-COR-ALL-DAT') : undefined
    const disciplineByFilter = request.discipline && request.discipline.length > 0 ? request.discipline : undefined
    const shiftByFilter =
        request.shift && request.shift.length > 0
            ? mapFilters(request.shift, `SHI-${getLocalUserSite()?.siteCode}-ALL-DAT`)
            : undefined

    const query = {
        template: {
            nodes: {
                filter: {
                    and: [
                        {
                            equals: {
                                property: ['node', 'space'],
                                value: GetSpace(EntityType.CKM, getLocalUserSite()?.siteCode),
                            },
                        },
                        ...(unitsByFilter
                            ? [
                                  {
                                      in: {
                                          property: [
                                              'CKM-COR-ALL-DML',
                                              'ChecklistTemplateConfig/74c8110d31a406',
                                              'reportingUnit',
                                          ],
                                          values: unitsByFilter,
                                      },
                                  },
                              ]
                            : []),
                        ...(locationByFilter
                            ? [
                                  {
                                      in: {
                                          property: [
                                              'CKM-COR-ALL-DML',
                                              'ChecklistTemplateConfig/74c8110d31a406',
                                              'reportingLocation',
                                          ],
                                          values: locationByFilter,
                                      },
                                  },
                              ]
                            : []),
                        ...(shiftByFilter
                            ? [
                                  {
                                      in: {
                                          property: [
                                              'CKM-COR-ALL-DML',
                                              'ChecklistTemplateConfig/74c8110d31a406',
                                              'shiftConfigurations',
                                          ],
                                          values: shiftByFilter,
                                      },
                                  },
                              ]
                            : []),
                        ...(disciplineByFilter
                            ? [
                                  {
                                      in: {
                                          property: [
                                              'CKM-COR-ALL-DML',
                                              'ChecklistTemplateConfig/74c8110d31a406',
                                              'disciplines',
                                          ],
                                          values: disciplineByFilter,
                                      },
                                  },
                              ]
                            : []),
                    ],
                },
                chainTo: 'destination',
                direction: 'outwards',
            },
            limit: 10000,
        },
    }

    return query
}

export const useTemplateConfig = (request: TemplateConfigQueryRequest) => {
    const [filter, setFilter] = useState<any>()
    const [resultData, setResultData] = useState<{ data: any; loading: boolean }>({
        data: 0,
        loading: true,
    })

    const select = {
        template: {
            limit: 10000,
        },
    }

    const cursors = {
        template: null,
    }

    const { getAllResults: getAllData } = useQueryResultsFunction<any>(select, cursors)

    useEffect(() => {
        setFilter(buildChecklistItemStatusQuery(request))
    }, [request])

    useEffect(() => {
        filter &&
            getAllData(filter)
                .then((res) => {
                    if (res.items.template.length > 0) {
                        setResultData({
                            data: res.items.template.map((t: any) => t.externalId.replace('CTC-', '')),
                            loading: false,
                        })
                    } else {
                        setResultData({ data: [], loading: false })
                    }
                })
                .catch((e) => console.log('error', e, 'filter', filter))
    }, [filter])

    return {
        loading: resultData.loading,
        templateConfigs: resultData.data,
    }
}
