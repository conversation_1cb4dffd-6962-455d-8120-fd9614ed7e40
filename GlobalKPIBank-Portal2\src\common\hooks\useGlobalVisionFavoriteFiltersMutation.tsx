import { DeleteNodeOptions } from '../clients'
import { ArrayEntity, ExternalEntity } from '../models'
import { EdgeInstance } from '../models/edge-instance'
import { Site } from '../models/site'
import { User } from '../models/user'
import { EntityType, GetSpace } from '../utils/space-util'
import { useFdmMutation } from './useFdmMutation'

export interface FavoriteFiltersMutation extends ExternalEntity {
    name: string
    refUser: User
    refReportingSites: Site[]
    order?: number
}

export interface MutationDataResult extends ExternalEntity {
    createdTime: number
    instanceType: string
    lastUpdatedTime: number
    version: number
    wasModified: boolean
}

export const useGlobalVisionFavoriteFilterMutation = () => {
    const [mutationFn] = useFdmMutation<FavoriteFiltersMutation>('GlobalVisionFavoritesFilters', true, true)

    const convertUser = (GlobalVisionFavoritesFilters: FavoriteFiltersMutation[]): EdgeInstance[] => {
        return GlobalVisionFavoritesFilters.map((GlobalVisionFavoritesFilters): any => {
            const refUser = GlobalVisionFavoritesFilters.refUser

            const edgeExternalId =
                'GKPIVIEWFILTERWITHUSER-' + GlobalVisionFavoritesFilters.externalId + '-' + refUser.externalId
            const modelSpace = GetSpace(EntityType.INO)
            const instanceSpace = GetSpace(EntityType.Instance)
            const userSpace = refUser.space

            const edge: EdgeInstance = {
                instanceType: 'edge',
                space: instanceSpace,
                externalId: edgeExternalId,
                startNode: {
                    externalId: GlobalVisionFavoritesFilters.externalId,
                    space: instanceSpace,
                },
                endNode: {
                    externalId: refUser.externalId,
                    space: userSpace,
                },
                type: {
                    externalId: 'GlobalVisionFavoritesFilters.refUser',
                    space: modelSpace,
                },
            }

            return edge
        }).flat()
    }
    const convertReportingSite = (GlobalVisionFavoritesFilters: FavoriteFiltersMutation[]): EdgeInstance[] => {
        return GlobalVisionFavoritesFilters.map((GlobalVisionFavoritesFilters): any => {
            return GlobalVisionFavoritesFilters.refReportingSites.map((refReportingSites) => {
                const edgeExternalId =
                    'GKPIVIEWFILTERREPORTINGSITES-' +
                    GlobalVisionFavoritesFilters.externalId +
                    '-' +
                    refReportingSites.externalId
                const modelSpace = GetSpace(EntityType.INO)
                const instanceSpace = GetSpace(EntityType.Instance)
                const refReportingSitesSpace = refReportingSites.space
                    ? refReportingSites.space
                    : GetSpace(EntityType.REF)
                const edge: EdgeInstance = {
                    instanceType: 'edge',
                    space: instanceSpace,
                    externalId: edgeExternalId,
                    startNode: {
                        externalId: GlobalVisionFavoritesFilters.externalId,
                        space: instanceSpace,
                    },
                    endNode: {
                        externalId: refReportingSites.externalId,
                        space: refReportingSitesSpace,
                    },
                    type: {
                        externalId: 'GlobalVisionFavoritesFilters.refReportingSites',
                        space: modelSpace,
                    },
                }

                return edge
            })
        }).flat()
    }

    const getExistingRefReportingSites = (
        GlobalVisionFavoritesFilters: FavoriteFiltersMutation[]
    ): DeleteNodeOptions => {
        const externalIds = GlobalVisionFavoritesFilters.map((GlobalVisionFavoritesFilters): any => {
            const externalIds = GlobalVisionFavoritesFilters.refReportingSites.map((refReportingSites) => {
                const edgeExternalId =
                    'GKPIVIEWFILTERREPORTINGSITES-' +
                    GlobalVisionFavoritesFilters.externalId +
                    '-' +
                    refReportingSites.externalId
                return edgeExternalId
            })
            return externalIds
        }).flat()
        const instanceSpace = GetSpace(EntityType.Instance)
        const edgesToDelete: DeleteNodeOptions = {
            externalIds: externalIds,
            space: instanceSpace,
            instanceType: 'edge',
        }
        return edgesToDelete
    }

    const updateGlobalVisionFavoriteFiltersMutationFn = async (entity: FavoriteFiltersMutation[]) => {
        const newEntitiesWithoutEdges = entity.map((e) => {
            return {
                externalId: e.externalId,
                space: e.space,
                refUser: e.refUser,
                name: e.name,
                order: e.order,
            } as unknown as FavoriteFiltersMutation
        })

        const edgeInstances: EdgeInstance[] = []

        edgeInstances.push(...convertUser(entity))
        edgeInstances.push(...convertReportingSite(entity))
        const updateNewNRRTypeMutationResult = await mutationFn(newEntitiesWithoutEdges, edgeInstances)

        return {
            ok: updateNewNRRTypeMutationResult.ok,
            data: updateNewNRRTypeMutationResult.data as unknown as ArrayEntity<MutationDataResult>,
            error: updateNewNRRTypeMutationResult.error,
        }
    }

    const deleteEdgesGlobalVisionFavoriteFiltersMutationFn = async (entity: FavoriteFiltersMutation[]) => {
        const edgesToDelete = getExistingRefReportingSites(entity)
        const deletedNewNRRTypeMutationResult = await mutationFn([], [], edgesToDelete)

        return {
            ok: deletedNewNRRTypeMutationResult.ok,
            data: deletedNewNRRTypeMutationResult.data as unknown as ArrayEntity<MutationDataResult>,
            error: deletedNewNRRTypeMutationResult.error,
        }
    }

    const deleteNodesGlobalVisionFavoriteFiltersMutationFn = async (entity?: any) => {
        const externalIds = []
        externalIds.push(entity.externalId)
        const nodesToDelete: DeleteNodeOptions = {
            externalIds: externalIds,
            space: entity.space ? entity.space : GetSpace(EntityType.Instance),
            instanceType: 'node',
        }

        const updateNewNRRTypeMutationResult = await mutationFn([], [], nodesToDelete)

        return {
            ok: updateNewNRRTypeMutationResult.ok,
            data: updateNewNRRTypeMutationResult.data as unknown as ArrayEntity<MutationDataResult>,
            error: updateNewNRRTypeMutationResult.error,
        }
    }
    return {
        updateGlobalVisionFavoriteFiltersMutationFn,
        deleteNodesGlobalVisionFavoriteFiltersMutationFn,
        deleteEdgesGlobalVisionFavoriteFiltersMutationFn,
    } as const
}
