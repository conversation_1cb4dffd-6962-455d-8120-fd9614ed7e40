import React from 'react'
import { useState } from 'react'
import { SxProps, Theme } from '@mui/material'

type Order = 'asc' | 'desc'

interface TableConfig {
    header: string[]
    headCells: HeaderCells[]
    order: Order
    orderBy: string
    page: number
    rowsPerPage: number[]
    rowPageValue: number
    visibleRows: any[]
    setTableRows: React.Dispatch<React.SetStateAction<any[]>>
    handleChangePage: (event: React.ChangeEvent<unknown>, page: number) => void
    handleChangeRowsPerPage: (event: React.ChangeEvent<HTMLInputElement>) => void
    handleRequestSort: (property: string) => void
}

export interface HeaderCells {
    id: string
    label: string
    isSorted: boolean
}

export const useTableConfig = (dateLabel: string, doneTask?: string, progress?: string) => {
    const header: string[] = []
    const headCells: HeaderCells[] = []
    const [order, setOrder] = React.useState<Order>('asc')
    const [orderBy, setOrderBy] = React.useState<string>('')
    const [page, setPage] = useState(0)
    const rowsPerPage = [5, 10, 25]
    const [rowPageValue, setRowPageValue] = useState(rowsPerPage[0])
    const [tableRows, setTableRows] = useState<any[]>([])

    const visibleRows = React.useMemo(() => {
        return orderBy === ''
            ? tableRows.slice(page * rowPageValue, page * rowPageValue + rowPageValue)
            : tableRows
                  .sort((a, b) => {
                      const columnA = a.columns.find((i: any) => i.name === orderBy)
                      const columnB = b.columns.find((i: any) => i.name === orderBy)

                      if (columnA && columnB) {
                          if (orderBy === dateLabel) {
                              const parseCustomDate = (dateString: any) => {
                                  const [datePart, timePart] = dateString.split(' - ')
                                  const [month, day, year] = datePart.split('/')
                                  let [hours, minutes] = timePart.replace(' PM', '').replace(' AM', '').split(':')

                                  if (dateString.includes('PM') && hours < 12) {
                                      hours = parseInt(hours, 10) + 12
                                      minutes = minutes
                                  }

                                  return new Date(year, month - 1, day, hours, minutes)
                              }

                              const dateTimeA = parseCustomDate(columnA.value)
                              const dateTimeB = parseCustomDate(columnB.value)

                              return order === 'desc'
                                  ? dateTimeB.getTime() - dateTimeA.getTime()
                                  : dateTimeA.getTime() - dateTimeB.getTime()
                          } else if (orderBy === doneTask) {
                              const parseDoneTask = (doneTaskString: string) => {
                                  const [completedA, totalA] = doneTaskString.split('/').map(Number)
                                  const percentageA = (completedA / totalA) * 100

                                  return percentageA
                              }

                              const percentageA = parseDoneTask(columnA.value)
                              const percentageB = parseDoneTask(columnB.value)

                              return order === 'desc' ? percentageB - percentageA : percentageA - percentageB
                          } else if (orderBy === progress) {
                              const progressA = parseInt(columnA.value.replace('%', ''))
                              const progressB = parseInt(columnB.value.replace('%', ''))

                              return order === 'desc' ? progressA - progressB : progressB - progressA
                          } else if (typeof columnA.value === 'string' && typeof columnB.value === 'string') {
                              return order === 'desc'
                                  ? columnB.value.localeCompare(columnA.value)
                                  : columnA.value.localeCompare(columnB.value)
                          } else {
                              return order === 'desc' ? columnB.value - columnA.value : columnA.value - columnB.value
                          }
                      }

                      return 0
                  })
                  .slice(page * rowPageValue, page * rowPageValue + rowPageValue)
    }, [order, tableRows, orderBy, page, rowPageValue])

    const handleChangePage = (event: React.ChangeEvent<unknown>, page: number) => {
        setPage(page - 1)
    }

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
        setRowPageValue(parseInt(event.target.value, 10))
        setPage(0)
    }

    const handleRequestSort = (property: string) => {
        const isAsc = orderBy === property && order === 'asc'
        setOrder(isAsc ? 'desc' : 'asc')
        setOrderBy(property)
    }

    return {
        header,
        headCells,
        order,
        orderBy,
        page,
        rowsPerPage,
        rowPageValue,
        visibleRows,
        setTableRows,
        handleChangePage,
        handleChangeRowsPerPage,
        handleRequestSort,
    } as TableConfig
}

export function getSxProps(sxProps?: SxProps<Theme>) {
    if (sxProps) {
        return Array.isArray(sxProps) ? sxProps : [sxProps]
    } else {
        return []
    }
}
