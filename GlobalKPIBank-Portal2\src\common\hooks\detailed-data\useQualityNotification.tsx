import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import { QualityNotificationKpi } from '@/common/models/qualityNotification'
import { QualityNotificationTableView } from '@/common/models/qualityNotificationTable'
import dayjs from 'dayjs'
import { setExportDateRange } from '@/common/utils/kpis-data-table-export'

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let businessSegmentQ: string = ''
    let startDateQ: string = ''
    let endDateQ: string = ''
    let fixedFilters: string = ''
    let limitQ: string = ''
    let cursorQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{aboutReportingSite: {externalId: {in: [${sites}]}}},`
    }

    if (request && request.kpiFilters.businessSegment !== '') {
        businessSegmentQ = `{businessSegment: {externalId: {eq: "${request.kpiFilters.businessSegment}"}}},`
    }

    if (request && request.kpiFilters.date !== '') {
        let sanitizedInitialDate = dayjs(request.kpiFilters.date).startOf('month').format('YYYY-MM-DD')
        let sanitizedEndDate = dayjs(request.kpiFilters.date).endOf('month').format('YYYY-MM-DD')
        const time = 'T00:00:00+00:00'

        if (request.exportFilters && request.exportFilters.isExport) {
            ;[sanitizedInitialDate, sanitizedEndDate] = setExportDateRange(
                request.exportFilters.range,
                request.kpiFilters.date
            )
        }

        startDateQ = `{notificationDate: {gte: "${sanitizedInitialDate}${time}"}},`
        endDateQ = `{notificationDate: {lte: "${sanitizedEndDate}${time}"}},`
    }

    if (request && request.kpiFilters.kpiName !== '') {
        if (request.kpiFilters.kpiName === 'HS QN1s') {
            fixedFilters = `
                {notificationType: {eq: "Q1"}},
                {or: [{priority: {eq: "1"}}, {priority: {eq: "3"}}]},
                {causeCodeGroup: {eq: "QMMAN500"}},
                {or: [{systemStatusMapping: {eq: "Accepted"}}, {systemStatusMapping: {eq: "Open"}}]},
            `
        }
        if (request.kpiFilters.kpiName === 'QN1s') {
            fixedFilters = `
                {notificationType: {eq: "Q1"}},
                {systemStatusMapping: {eq: "Accepted"}},
                {causeCodeGroup: {eq: "QMMAN500"}},
                {not: {priority: {eq: "E"}}},
            `
        }
        if (request.kpiFilters.kpiName === 'Major Audit Findings - External') {
            fixedFilters = `
                {notificationType: {eq: "Q3"}},
                {priority: {eq: "H"}},
                {or: [{systemStatusMapping: {eq: "Accepted"}}, {systemStatusMapping: {eq: "Open"}}]},
                {qualityManagementCodeGroup: {eq: "QMI00013"}},
                {qualityManagementCode: {eq: "0002"}},
            `
        }
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listQualityNotification (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${businessSegmentQ} ${startDateQ} ${endDateQ} ${fixedFilters}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                aboutReportingSite {
                    externalId
                    name
                }
                notificationDescription
                notificationNumber
                notificationDate
                notificationType
                systemStatusMapping
                systemStatus
                priority
                causeCodeGroup
                qualityManagementCodeGroup
                qualityManagementCode
                severity
                subjectCode
                businessSegment {
                    externalId
                    description
                }
            }
        }
    }`
}

const checkSystemStatus = (kpiName: string, systemStatus: string): boolean => {
    let result: boolean = false
    const systemStatusArray = systemStatus.split(' ')
    const rank = 'RANK'

    if (kpiName === 'HS QN1s') {
        const words = ['NOCO', 'NOPR', 'OSNO']
        const containsAtLeastOne: boolean = words.some((word) => systemStatusArray.includes(word))
        const containsRank: boolean = systemStatusArray.includes(rank)
        if (containsAtLeastOne && containsRank) result = true
    }
    if (kpiName === 'QN1s') {
        const words = ['NOCO']
        const containsAtLeastOne: boolean = words.some((word) => systemStatusArray.includes(word))
        const containsRank: boolean = systemStatusArray.includes(rank)
        if (containsAtLeastOne && containsRank) result = true
    }
    if (kpiName === 'Major Audit Findings - External') result = true

    return result
}

const mapQualityNotification = (data: QualityNotificationKpi[], kpiName: string): QualityNotificationTableView[] => {
    const mappedResult: QualityNotificationTableView[] = []

    if (data && data.length > 0) {
        data.map((item) => {
            const isValidSystemStatus = checkSystemStatus(kpiName, item.systemStatus)

            const result: QualityNotificationTableView = {
                siteName: item.aboutReportingSite.name ?? '',
                notificationDescription: item.notificationDescription ?? '',
                notificationNumber: item.notificationNumber ?? '',
                notificationDate: item.notificationDate?.toString().split('T')[0] ?? '',
                notificationType: item.notificationType ?? '',
                systemStatusMapping: item.systemStatusMapping ?? '',
                priority: item.priority ?? '',
                causeCodeGroup: item.causeCodeGroup ?? '',
                qualityManagementCodeGroup: item.qualityManagementCodeGroup ?? '',
                qualityManagementCode: item.qualityManagementCode ?? '',
                severity: item.severity ?? '',
                subjectCode: item.subjectCode ?? '',
                businessSegment: item.businessSegment.description ?? '',
            }

            if (isValidSystemStatus) mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useQualityNotification = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<QualityNotificationKpi>(gql(query), 'listQualityNotification', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'qualityView',
        },
    })

    const [resultData, setResultData] = useState<{ data: QualityNotificationTableView[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapQualityNotification(fdmData, request.kpiFilters.kpiName)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapQualityNotification(fdmData, request.kpiFilters.kpiName)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
