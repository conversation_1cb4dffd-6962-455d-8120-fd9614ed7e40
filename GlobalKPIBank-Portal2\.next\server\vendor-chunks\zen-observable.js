/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zen-observable";
exports.ids = ["vendor-chunks/zen-observable"];
exports.modules = {

/***/ "(ssr)/./node_modules/zen-observable/index.js":
/*!**********************************************!*\
  !*** ./node_modules/zen-observable/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./lib/Observable.js */ \"(ssr)/./node_modules/zen-observable/lib/Observable.js\").Observable;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvemVuLW9ic2VydmFibGUvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsbUlBQTBEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2xvYmFsLWtwaS1hcHAvLi9ub2RlX21vZHVsZXMvemVuLW9ic2VydmFibGUvaW5kZXguanM/ZjY0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vbGliL09ic2VydmFibGUuanMnKS5PYnNlcnZhYmxlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zen-observable/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/zen-observable/lib/Observable.js":
/*!*******************************************************!*\
  !*** ./node_modules/zen-observable/lib/Observable.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.Observable = void 0;\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n// === Symbol Support ===\nvar hasSymbols = function () {\n  return typeof Symbol === 'function';\n};\n\nvar hasSymbol = function (name) {\n  return hasSymbols() && Boolean(Symbol[name]);\n};\n\nvar getSymbol = function (name) {\n  return hasSymbol(name) ? Symbol[name] : '@@' + name;\n};\n\nif (hasSymbols() && !hasSymbol('observable')) {\n  Symbol.observable = Symbol('observable');\n}\n\nvar SymbolIterator = getSymbol('iterator');\nvar SymbolObservable = getSymbol('observable');\nvar SymbolSpecies = getSymbol('species'); // === Abstract Operations ===\n\nfunction getMethod(obj, key) {\n  var value = obj[key];\n  if (value == null) return undefined;\n  if (typeof value !== 'function') throw new TypeError(value + ' is not a function');\n  return value;\n}\n\nfunction getSpecies(obj) {\n  var ctor = obj.constructor;\n\n  if (ctor !== undefined) {\n    ctor = ctor[SymbolSpecies];\n\n    if (ctor === null) {\n      ctor = undefined;\n    }\n  }\n\n  return ctor !== undefined ? ctor : Observable;\n}\n\nfunction isObservable(x) {\n  return x instanceof Observable; // SPEC: Brand check\n}\n\nfunction hostReportError(e) {\n  if (hostReportError.log) {\n    hostReportError.log(e);\n  } else {\n    setTimeout(function () {\n      throw e;\n    });\n  }\n}\n\nfunction enqueue(fn) {\n  Promise.resolve().then(function () {\n    try {\n      fn();\n    } catch (e) {\n      hostReportError(e);\n    }\n  });\n}\n\nfunction cleanupSubscription(subscription) {\n  var cleanup = subscription._cleanup;\n  if (cleanup === undefined) return;\n  subscription._cleanup = undefined;\n\n  if (!cleanup) {\n    return;\n  }\n\n  try {\n    if (typeof cleanup === 'function') {\n      cleanup();\n    } else {\n      var unsubscribe = getMethod(cleanup, 'unsubscribe');\n\n      if (unsubscribe) {\n        unsubscribe.call(cleanup);\n      }\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n}\n\nfunction closeSubscription(subscription) {\n  subscription._observer = undefined;\n  subscription._queue = undefined;\n  subscription._state = 'closed';\n}\n\nfunction flushSubscription(subscription) {\n  var queue = subscription._queue;\n\n  if (!queue) {\n    return;\n  }\n\n  subscription._queue = undefined;\n  subscription._state = 'ready';\n\n  for (var i = 0; i < queue.length; ++i) {\n    notifySubscription(subscription, queue[i].type, queue[i].value);\n    if (subscription._state === 'closed') break;\n  }\n}\n\nfunction notifySubscription(subscription, type, value) {\n  subscription._state = 'running';\n  var observer = subscription._observer;\n\n  try {\n    var m = getMethod(observer, type);\n\n    switch (type) {\n      case 'next':\n        if (m) m.call(observer, value);\n        break;\n\n      case 'error':\n        closeSubscription(subscription);\n        if (m) m.call(observer, value);else throw value;\n        break;\n\n      case 'complete':\n        closeSubscription(subscription);\n        if (m) m.call(observer);\n        break;\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n\n  if (subscription._state === 'closed') cleanupSubscription(subscription);else if (subscription._state === 'running') subscription._state = 'ready';\n}\n\nfunction onNotify(subscription, type, value) {\n  if (subscription._state === 'closed') return;\n\n  if (subscription._state === 'buffering') {\n    subscription._queue.push({\n      type: type,\n      value: value\n    });\n\n    return;\n  }\n\n  if (subscription._state !== 'ready') {\n    subscription._state = 'buffering';\n    subscription._queue = [{\n      type: type,\n      value: value\n    }];\n    enqueue(function () {\n      return flushSubscription(subscription);\n    });\n    return;\n  }\n\n  notifySubscription(subscription, type, value);\n}\n\nvar Subscription =\n/*#__PURE__*/\nfunction () {\n  function Subscription(observer, subscriber) {\n    _classCallCheck(this, Subscription);\n\n    // ASSERT: observer is an object\n    // ASSERT: subscriber is callable\n    this._cleanup = undefined;\n    this._observer = observer;\n    this._queue = undefined;\n    this._state = 'initializing';\n    var subscriptionObserver = new SubscriptionObserver(this);\n\n    try {\n      this._cleanup = subscriber.call(undefined, subscriptionObserver);\n    } catch (e) {\n      subscriptionObserver.error(e);\n    }\n\n    if (this._state === 'initializing') this._state = 'ready';\n  }\n\n  _createClass(Subscription, [{\n    key: \"unsubscribe\",\n    value: function unsubscribe() {\n      if (this._state !== 'closed') {\n        closeSubscription(this);\n        cleanupSubscription(this);\n      }\n    }\n  }, {\n    key: \"closed\",\n    get: function () {\n      return this._state === 'closed';\n    }\n  }]);\n\n  return Subscription;\n}();\n\nvar SubscriptionObserver =\n/*#__PURE__*/\nfunction () {\n  function SubscriptionObserver(subscription) {\n    _classCallCheck(this, SubscriptionObserver);\n\n    this._subscription = subscription;\n  }\n\n  _createClass(SubscriptionObserver, [{\n    key: \"next\",\n    value: function next(value) {\n      onNotify(this._subscription, 'next', value);\n    }\n  }, {\n    key: \"error\",\n    value: function error(value) {\n      onNotify(this._subscription, 'error', value);\n    }\n  }, {\n    key: \"complete\",\n    value: function complete() {\n      onNotify(this._subscription, 'complete');\n    }\n  }, {\n    key: \"closed\",\n    get: function () {\n      return this._subscription._state === 'closed';\n    }\n  }]);\n\n  return SubscriptionObserver;\n}();\n\nvar Observable =\n/*#__PURE__*/\nfunction () {\n  function Observable(subscriber) {\n    _classCallCheck(this, Observable);\n\n    if (!(this instanceof Observable)) throw new TypeError('Observable cannot be called as a function');\n    if (typeof subscriber !== 'function') throw new TypeError('Observable initializer must be a function');\n    this._subscriber = subscriber;\n  }\n\n  _createClass(Observable, [{\n    key: \"subscribe\",\n    value: function subscribe(observer) {\n      if (typeof observer !== 'object' || observer === null) {\n        observer = {\n          next: observer,\n          error: arguments[1],\n          complete: arguments[2]\n        };\n      }\n\n      return new Subscription(observer, this._subscriber);\n    }\n  }, {\n    key: \"forEach\",\n    value: function forEach(fn) {\n      var _this = this;\n\n      return new Promise(function (resolve, reject) {\n        if (typeof fn !== 'function') {\n          reject(new TypeError(fn + ' is not a function'));\n          return;\n        }\n\n        function done() {\n          subscription.unsubscribe();\n          resolve();\n        }\n\n        var subscription = _this.subscribe({\n          next: function (value) {\n            try {\n              fn(value, done);\n            } catch (e) {\n              reject(e);\n              subscription.unsubscribe();\n            }\n          },\n          error: reject,\n          complete: resolve\n        });\n      });\n    }\n  }, {\n    key: \"map\",\n    value: function map(fn) {\n      var _this2 = this;\n\n      if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n      var C = getSpecies(this);\n      return new C(function (observer) {\n        return _this2.subscribe({\n          next: function (value) {\n            try {\n              value = fn(value);\n            } catch (e) {\n              return observer.error(e);\n            }\n\n            observer.next(value);\n          },\n          error: function (e) {\n            observer.error(e);\n          },\n          complete: function () {\n            observer.complete();\n          }\n        });\n      });\n    }\n  }, {\n    key: \"filter\",\n    value: function filter(fn) {\n      var _this3 = this;\n\n      if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n      var C = getSpecies(this);\n      return new C(function (observer) {\n        return _this3.subscribe({\n          next: function (value) {\n            try {\n              if (!fn(value)) return;\n            } catch (e) {\n              return observer.error(e);\n            }\n\n            observer.next(value);\n          },\n          error: function (e) {\n            observer.error(e);\n          },\n          complete: function () {\n            observer.complete();\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reduce\",\n    value: function reduce(fn) {\n      var _this4 = this;\n\n      if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n      var C = getSpecies(this);\n      var hasSeed = arguments.length > 1;\n      var hasValue = false;\n      var seed = arguments[1];\n      var acc = seed;\n      return new C(function (observer) {\n        return _this4.subscribe({\n          next: function (value) {\n            var first = !hasValue;\n            hasValue = true;\n\n            if (!first || hasSeed) {\n              try {\n                acc = fn(acc, value);\n              } catch (e) {\n                return observer.error(e);\n              }\n            } else {\n              acc = value;\n            }\n          },\n          error: function (e) {\n            observer.error(e);\n          },\n          complete: function () {\n            if (!hasValue && !hasSeed) return observer.error(new TypeError('Cannot reduce an empty sequence'));\n            observer.next(acc);\n            observer.complete();\n          }\n        });\n      });\n    }\n  }, {\n    key: \"concat\",\n    value: function concat() {\n      var _this5 = this;\n\n      for (var _len = arguments.length, sources = new Array(_len), _key = 0; _key < _len; _key++) {\n        sources[_key] = arguments[_key];\n      }\n\n      var C = getSpecies(this);\n      return new C(function (observer) {\n        var subscription;\n        var index = 0;\n\n        function startNext(next) {\n          subscription = next.subscribe({\n            next: function (v) {\n              observer.next(v);\n            },\n            error: function (e) {\n              observer.error(e);\n            },\n            complete: function () {\n              if (index === sources.length) {\n                subscription = undefined;\n                observer.complete();\n              } else {\n                startNext(C.from(sources[index++]));\n              }\n            }\n          });\n        }\n\n        startNext(_this5);\n        return function () {\n          if (subscription) {\n            subscription.unsubscribe();\n            subscription = undefined;\n          }\n        };\n      });\n    }\n  }, {\n    key: \"flatMap\",\n    value: function flatMap(fn) {\n      var _this6 = this;\n\n      if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n      var C = getSpecies(this);\n      return new C(function (observer) {\n        var subscriptions = [];\n\n        var outer = _this6.subscribe({\n          next: function (value) {\n            if (fn) {\n              try {\n                value = fn(value);\n              } catch (e) {\n                return observer.error(e);\n              }\n            }\n\n            var inner = C.from(value).subscribe({\n              next: function (value) {\n                observer.next(value);\n              },\n              error: function (e) {\n                observer.error(e);\n              },\n              complete: function () {\n                var i = subscriptions.indexOf(inner);\n                if (i >= 0) subscriptions.splice(i, 1);\n                completeIfDone();\n              }\n            });\n            subscriptions.push(inner);\n          },\n          error: function (e) {\n            observer.error(e);\n          },\n          complete: function () {\n            completeIfDone();\n          }\n        });\n\n        function completeIfDone() {\n          if (outer.closed && subscriptions.length === 0) observer.complete();\n        }\n\n        return function () {\n          subscriptions.forEach(function (s) {\n            return s.unsubscribe();\n          });\n          outer.unsubscribe();\n        };\n      });\n    }\n  }, {\n    key: SymbolObservable,\n    value: function () {\n      return this;\n    }\n  }], [{\n    key: \"from\",\n    value: function from(x) {\n      var C = typeof this === 'function' ? this : Observable;\n      if (x == null) throw new TypeError(x + ' is not an object');\n      var method = getMethod(x, SymbolObservable);\n\n      if (method) {\n        var observable = method.call(x);\n        if (Object(observable) !== observable) throw new TypeError(observable + ' is not an object');\n        if (isObservable(observable) && observable.constructor === C) return observable;\n        return new C(function (observer) {\n          return observable.subscribe(observer);\n        });\n      }\n\n      if (hasSymbol('iterator')) {\n        method = getMethod(x, SymbolIterator);\n\n        if (method) {\n          return new C(function (observer) {\n            enqueue(function () {\n              if (observer.closed) return;\n              var _iteratorNormalCompletion = true;\n              var _didIteratorError = false;\n              var _iteratorError = undefined;\n\n              try {\n                for (var _iterator = method.call(x)[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n                  var _item = _step.value;\n                  observer.next(_item);\n                  if (observer.closed) return;\n                }\n              } catch (err) {\n                _didIteratorError = true;\n                _iteratorError = err;\n              } finally {\n                try {\n                  if (!_iteratorNormalCompletion && _iterator.return != null) {\n                    _iterator.return();\n                  }\n                } finally {\n                  if (_didIteratorError) {\n                    throw _iteratorError;\n                  }\n                }\n              }\n\n              observer.complete();\n            });\n          });\n        }\n      }\n\n      if (Array.isArray(x)) {\n        return new C(function (observer) {\n          enqueue(function () {\n            if (observer.closed) return;\n\n            for (var i = 0; i < x.length; ++i) {\n              observer.next(x[i]);\n              if (observer.closed) return;\n            }\n\n            observer.complete();\n          });\n        });\n      }\n\n      throw new TypeError(x + ' is not observable');\n    }\n  }, {\n    key: \"of\",\n    value: function of() {\n      for (var _len2 = arguments.length, items = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        items[_key2] = arguments[_key2];\n      }\n\n      var C = typeof this === 'function' ? this : Observable;\n      return new C(function (observer) {\n        enqueue(function () {\n          if (observer.closed) return;\n\n          for (var i = 0; i < items.length; ++i) {\n            observer.next(items[i]);\n            if (observer.closed) return;\n          }\n\n          observer.complete();\n        });\n      });\n    }\n  }, {\n    key: SymbolSpecies,\n    get: function () {\n      return this;\n    }\n  }]);\n\n  return Observable;\n}();\n\nexports.Observable = Observable;\n\nif (hasSymbols()) {\n  Object.defineProperty(Observable, Symbol('extensions'), {\n    value: {\n      symbol: SymbolObservable,\n      hostReportError: hostReportError\n    },\n    configurable: true\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zen-observable/lib/Observable.js\n");

/***/ })

};
;