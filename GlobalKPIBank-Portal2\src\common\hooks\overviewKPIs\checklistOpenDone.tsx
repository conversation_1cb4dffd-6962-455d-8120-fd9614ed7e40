/*

Checklists Open x Done:
A graphic that should show the user the percentage of Open Checklists and Done Checklists

Open Checklists: The number of checklists that were not set as 'Done'
* This number and percentage should consider the checklists with 'To Do', 'In Progress', and 'Overdue' statuses

Done Checklists: The number of checklists that were set as 'Done'

Numbers Indicators:
* Two number indicators; Open and Done that should show the numbers of 'Open' and 'Done' checklists respectively

Percentage Indicators
* Two percentage indicators; Open and Done that should show the percentage of 'Open' and 'Done' checklists respectively
* The Percentage indicator should be under the Number indicator

*/

import { useEffect, useState } from 'react'
import { getLocalUserSite } from '../../utils'
import { EntityType, GetSpace } from '../../utils/space-util'
import { useAgregateResultsFunction } from '../general-functions/useAgregateResultsFunction'

export interface KpisQueryRequest {
    currentStatusValues: string[]
    assignedToValue?: string
    startDate?: string
    endDate?: string
    templateConfig: string[]
    units?: string
    location?: string
    shift?: string
}

const buildActionItemCategoryQuery = (request: KpisQueryRequest): any => {
    const mappedStatusValues = request.currentStatusValues.map((value) => ({
        equals: {
            property: ['status'],
            value: value,
        },
    }))

    const query = {
        and: [
            {
                or: [
                    {
                        equals: {
                            property: ['node', 'space'],
                            value: GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode),
                        },
                    },
                    {
                        equals: {
                            property: ['node', 'space'],
                            value: GetSpace(EntityType.APMATEMP, getLocalUserSite()?.siteCode),
                        },
                    },
                ],
            },
            {
                not: {
                    equals: {
                        property: ['isArchived'],
                        value: true,
                    },
                },
            },
            request.currentStatusValues && {
                in: {
                    property: ['status'],
                    values: request.currentStatusValues,
                },
            },
            request.startDate && {
                or: [
                    {
                        range: {
                            property: ['startTime'],
                            gte: request.startDate,
                            lte: request.endDate,
                        },
                    },
                    {
                        range: {
                            property: ['endTime'],
                            gte: request.startDate,
                            lte: request.endDate,
                        },
                    },
                ],
            },
            request.templateConfig.length > 0 && {
                in: {
                    property: ['sourceId'],
                    values: request.templateConfig.map((item) => item.replace('CTC-', '')),
                },
            },
        ].filter(Boolean),
    }
    return query
}

export const useChecklistOpenDone = (request: KpisQueryRequest) => {
    const unitsByFilter = request.units && request.units?.length > 0 ? true : false
    const locationByFilter = request.location && request.location?.length > 0 ? true : false
    const shiftByFilter = request.shift && request.shift.length > 0 ? true : false

    const filter = buildActionItemCategoryQuery(request)

    const [resultData, setResultData] = useState<{ data: any; loading: boolean }>({
        data: 0,
        loading: true,
    })

    const { getAllResults: getAllData } = useAgregateResultsFunction<any>('Checklist')

    useEffect(() => {
        getAllData(filter).then((res) => {
            setResultData({ data: res, loading: true })
        })
    }, [request])

    return {
        loading: resultData.loading,
        countResult: resultData.data,
    }
}
