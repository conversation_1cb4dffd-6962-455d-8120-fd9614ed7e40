import { translate } from '@celanese/celanese-sdk'
import { Box, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'
import { useEffect, useState, useMemo } from 'react'
import { GlobalViewFilters } from '../GlobalViewFilters'
import ColapseTable from './ColapseTable'
import { LoaderCircular } from '../../../components/Loader'
import { useGlobalVisionKPISite } from '@/common/hooks/useGlobalVisionKPISite'
import { useGlobalVisionKPITarget } from '@/common/hooks/useGlobalVisionKPITarget'
import { useReportingSiteList } from '@/common/hooks/useReportingSiteList'
import { useBusinessSegmentList } from '@/common/hooks/useBusinessSegment'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp'
import { KpiGroup } from '@/common/models/kpiGroup'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { FinancialKpisDom } from '@/common/utils/financial-kpis'
import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import { useGlobalVisionKPISegment } from '@/common/hooks/useGlobalVisionKPISegment'
import dayjs from 'dayjs'
import { FoundationalKpisKpidom } from '@/common/utils/foundational-kpis'
import { PageNames } from '@/common/utils/page-names'
import { mergeSiteValues } from '@/common/utils/kpiSitesValueMerger'

interface GlobalViewTableProps {
    tabGroup: KpiGroup | undefined
}

export const GlobalViewTable: React.FC<GlobalViewTableProps> = ({ tabGroup }) => {
    const [filters, setFilters] = useState<any>({ site: [], businessSegment: '', customFilter: [] })
    const [openAll, setOpenAll] = useState(false)
    const [isBusinessSegmentFilterActive, setIsBusinessSegmentFilterActive] = useState(false)
    const [reportingUnits, setReportingUnits] = useState<string[]>([])
    const { checkPermissionsFromRoutes } = useAuthGuard()
    const [permissionedGlobalViewData, setPermissionedGlobalViewData] = useState<GlobalVisionKpiTableModel[]>([])
    const requestKPISite = useMemo(
        () => ({
            site: filters.site ?? [],
            kpiGroup: tabGroup != undefined ? tabGroup.name : '',
            page: PageNames.GLOBAL_VIEW.NAME,
            granularity: 'MON',
        }),
        [filters.site, tabGroup]
    )
    const requestKPISegment = useMemo(
        () => ({
            site: filters.site ?? [],
            businessSegment: filters.businessSegment,
            kpiGroup: tabGroup != undefined ? tabGroup.name : '',
            page: PageNames.GLOBAL_VIEW.NAME,
            granularity: 'MON',
        }),
        [filters.site, filters.businessSegment, tabGroup]
    )
    const requestKPITarget = useMemo(
        () => ({
            site: filters.site ?? [],
            kpiGroupExternalId: tabGroup != undefined ? tabGroup.externalId : '',
            year: new Date().getFullYear(),
        }),
        [filters.site, tabGroup]
    )
    const {
        dataGlobalVisionKPI: globalViewDataMonth,
        refetchGlobalVisionKPI,
        loadingGlobalVisionKPI,
    } = useGlobalVisionKPISite(requestKPISite)
    const { dataGlobalVisionKPI: globalViewDataQuarter, loadingGlobalVisionKPI: loadingGlobalVisionKPISiteQuarter } =
        useGlobalVisionKPISite({ ...requestKPISite, granularity: 'QRT' })
    const { dataGlobalVisionKPI: globalViewDataAnnual, loadingGlobalVisionKPI: loadingGlobalVisionKPISiteAnnual } =
        useGlobalVisionKPISite({ ...requestKPISite, granularity: 'ANL' })
    const [globalViewData, setGlobalViewData] = useState([])

    useEffect(() => {
        if (globalViewDataMonth.length > 0 && globalViewDataQuarter.length > 0 && globalViewDataAnnual.length > 0) {
            const allIds = new Set<string>()
            globalViewDataMonth.forEach((item) => allIds.add(item.kpi.externalId))
            globalViewDataQuarter.forEach((item) => allIds.add(item.kpi.externalId))
            globalViewDataAnnual.forEach((item) => allIds.add(item.kpi.externalId))

            const merged = mergeSiteValues(
                allIds,
                globalViewDataMonth,
                globalViewDataQuarter,
                globalViewDataAnnual,
                filters.site.length
            )
            const result = merged.sort((a, b) => a.kpi.order - b.kpi.order)
            setGlobalViewData(result)
        }
    }, [globalViewDataMonth, globalViewDataQuarter, globalViewDataAnnual])

    const {
        dataGlobalVisionKPI: globalViewDataSegmentMonth,
        refetchGlobalVisionKPI: refetchGlobalVisionKPISegment,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISegment,
    } = useGlobalVisionKPISegment({ ...requestKPISegment, granularity: 'MON' })
    const {
        dataGlobalVisionKPI: globalViewDataSegmentQuarter,
        refetchGlobalVisionKPI: refetchGlobalVisionKPISegmentQuarter,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISegmentQuarter,
    } = useGlobalVisionKPISegment({ ...requestKPISegment, granularity: 'QRT' })
    const {
        dataGlobalVisionKPI: globalViewDataSegmentAnnual,
        refetchGlobalVisionKPI: refetchGlobalVisionKPISegmentAnnual,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISegmentAnnual,
    } = useGlobalVisionKPISegment({ ...requestKPISegment, granularity: 'ANL' })
    const [globalViewDataSegment, setGlobalViewDataSegment] = useState<GlobalVisionKpiTableModel[]>([])

    useEffect(() => {
        if (
            globalViewDataSegmentMonth.length > 0 &&
            globalViewDataSegmentQuarter.length > 0 &&
            globalViewDataSegmentAnnual.length > 0
        ) {
            const allIds = new Set<string>()
            globalViewDataSegmentMonth.forEach((item) => allIds.add(item.kpi.externalId))
            globalViewDataSegmentQuarter.forEach((item) => allIds.add(item.kpi.externalId))
            globalViewDataSegmentAnnual.forEach((item) => allIds.add(item.kpi.externalId))

            const merged = mergeSiteValues(
                allIds,
                globalViewDataSegmentMonth,
                globalViewDataSegmentQuarter,
                globalViewDataSegmentAnnual,
                filters.site.length
            )
            const result = merged.sort((a, b) => a.kpi.order - b.kpi.order)
            setGlobalViewDataSegment(result)
        }
    }, [globalViewDataSegmentMonth, globalViewDataSegmentQuarter, globalViewDataSegmentAnnual])

    const { dataGlobalVisionKPITarget: globalViewTarget, refetchGlobalVisionKPITarget } =
        useGlobalVisionKPITarget(requestKPITarget)

    const { dataSiteList: sites } = useReportingSiteList(reportingUnits)
    const { dataBusinessSegmentList: businessSegments } = useBusinessSegmentList()

    const formatMonthColumn = (offset: number): string => {
        const month = [
            translate('TABLE_COLS.MONTHS.JANUARY'),
            translate('TABLE_COLS.MONTHS.FEBRUARY'),
            translate('TABLE_COLS.MONTHS.MARCH'),
            translate('TABLE_COLS.MONTHS.APRIL'),
            translate('TABLE_COLS.MONTHS.MAY'),
            translate('TABLE_COLS.MONTHS.JUNE'),
            translate('TABLE_COLS.MONTHS.JULY'),
            translate('TABLE_COLS.MONTHS.AUGUST'),
            translate('TABLE_COLS.MONTHS.SEPTEMBER'),
            translate('TABLE_COLS.MONTHS.OCTOBER'),
            translate('TABLE_COLS.MONTHS.NOVEMBER'),
            translate('TABLE_COLS.MONTHS.DECEMBER'),
        ]

        const currentDate = dayjs().subtract(offset, 'month')
        const monthIndex = currentDate.month()
        const yearValue = currentDate.format('YY')

        return month[monthIndex] + '/' + yearValue
    }

    const applyFilters = (filters: any) => {
        const siteAux: any[] = []
        filters.site.map((site: any) => siteAux.push(site.externalId))
        filters.site = siteAux

        const customFilterAux: any[] = []
        filters.customFilter.map((customFilter: any) => customFilterAux.push(customFilter.externalId))
        filters.customFilter = customFilterAux

        const businessSegmentAux: string = filters.businessSegment.externalId
        businessSegmentAux === '' ? (filters.businessSegment = '') : (filters.businessSegment = businessSegmentAux)

        filters.businessSegment !== ''
            ? setIsBusinessSegmentFilterActive(true)
            : setIsBusinessSegmentFilterActive(false)

        setFilters(filters)
    }

    useEffect(() => {
        refetchGlobalVisionKPI()
        refetchGlobalVisionKPISegment()
        refetchGlobalVisionKPITarget()
    }, [tabGroup, filters])

    useEffect(() => {
        if (!globalViewData || globalViewData.length === 0) return

        function removeForecastProduction(value: GlobalVisionKpiTableModel, index: number, array: any) {
            if (value.kpi.externalId === FoundationalKpisKpidom.FORECAST_PRODUCTION.EXTERNAL_ID) {
                array.splice(index, 1)
                return true
            }
            return false
        }

        function removeForecast(value: GlobalVisionKpiTableModel, index: number, array: any) {
            if (
                value.kpi.externalId === FoundationalKpisKpidom.FORECAST_COST.EXTERNAL_ID ||
                value.kpi.externalId === FoundationalKpisKpidom.FORECAST_PRODUCTION.EXTERNAL_ID
            ) {
                array.splice(index, 1)
                return true
            }
            return false
        }

        function removeActualCost(value: GlobalVisionKpiTableModel, index: number, array: any) {
            if (value.kpi.externalId === FoundationalKpisKpidom.ACTUAL_COST.EXTERNAL_ID) {
                array.splice(index, 1)
                return true
            }
            return false
        }

        function removeActualProduction(value: GlobalVisionKpiTableModel, index: number, array: any) {
            if (value.kpi.externalId === FoundationalKpisKpidom.ACTUAL_PRODUCTION.EXTERNAL_ID) {
                array.splice(index, 1)
                return true
            }
            return false
        }

        globalViewData.filter(removeForecastProduction)
        globalViewData.filter(removeForecast)
        globalViewData.filter(removeActualCost)
        globalViewData.filter(removeActualProduction)

        let filteredKpisByPermission = globalViewData
        if (!checkPermissionsFromRoutes('globalViewFoundationalPlantCash')) {
            filteredKpisByPermission = filteredKpisByPermission.filter(
                (value) => value.kpi.externalId !== FinancialKpisDom.PLANT_CASH
            )
        }
        if (!checkPermissionsFromRoutes('globalViewFoundationalPlantCashKg')) {
            filteredKpisByPermission = filteredKpisByPermission.filter(
                (value) => value.kpi.externalId !== FinancialKpisDom.PLANT_CASH_KG
            )
        }
        setPermissionedGlobalViewData(filteredKpisByPermission)
    }, [globalViewData])

    return (
        <Box sx={{ maxWidth: '100%', height: 'calc(100% - 50px)' }}>
            <GlobalViewFilters
                sites={sites ?? []}
                businessSegments={businessSegments ?? []}
                onSubmit={(filters) => applyFilters(filters)}
                setReportingUnits={setReportingUnits}
            />
            {
                <TableContainer
                    sx={{
                        overflow: 'auto',
                        height: 'calc(100% - 89px)',
                    }}
                >
                    {loadingGlobalVisionKPI ? (
                        LoaderCircular()
                    ) : (
                        <Table>
                            <TableHead
                                sx={{
                                    position: 'sticky',
                                    top: 0,
                                    zIndex: 1,
                                    backgroundColor: '#ffffff',
                                    fontWeight: 'bold',
                                }}
                            >
                                <TableRow>
                                    <TableCell sx={{ width: '5%' }}>
                                        <IconButton
                                            aria-label="expand row"
                                            size="small"
                                            onClick={() => setOpenAll(!openAll)}
                                        >
                                            {openAll ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                                        </IconButton>
                                    </TableCell>
                                    <TableCell align="left" sx={{ width: '17%' }}>
                                        {translate('TABLE_COLS.KPI')}
                                    </TableCell>
                                    <TableCell align="right" sx={{ width: '9%' }}>
                                        {(dayjs().month() == 0 || (dayjs().month() == 1 && dayjs().date() < 11)
                                            ? dayjs().year() - 3
                                            : dayjs().year() - 2
                                        ).toString()}
                                    </TableCell>
                                    <TableCell align="right" sx={{ width: '9%' }}>
                                        {(dayjs().month() == 0 || (dayjs().month() == 1 && dayjs().date() < 11)
                                            ? dayjs().year() - 2
                                            : dayjs().year() - 1
                                        ).toString()}
                                    </TableCell>
                                    <TableCell align="right" sx={{ width: '9%' }}>
                                        {dayjs().month() == 0 || (dayjs().month() == 1 && dayjs().date() < 11)
                                            ? (dayjs().year() - 1).toString()
                                            : translate('TABLE_COLS.YTD')}
                                    </TableCell>
                                    <TableCell align="right" sx={{ width: '9%' }}>
                                        {translate('TABLE_COLS.TARGET')}
                                    </TableCell>
                                    <TableCell align="right" sx={{ width: '9%' }}>
                                        {formatMonthColumn(3)}
                                    </TableCell>
                                    <TableCell align="right" sx={{ width: '9%' }}>
                                        {formatMonthColumn(2)}
                                    </TableCell>
                                    <TableCell align="right" sx={{ width: '9%' }}>
                                        {formatMonthColumn(1)}
                                    </TableCell>
                                    <TableCell align="right" sx={{ width: '9%' }}>
                                        {formatMonthColumn(0)}
                                    </TableCell>
                                    <TableCell align="right" sx={{ width: '6%' }}></TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {isBusinessSegmentFilterActive
                                    ? globalViewDataSegment.map((row, index) => (
                                          <ColapseTable key={index} row={row} target={undefined} openAll={openAll} />
                                      ))
                                    : permissionedGlobalViewData &&
                                      permissionedGlobalViewData.length > 0 &&
                                      globalViewTarget.length > 0 &&
                                      permissionedGlobalViewData.map((row, index) => (
                                          <ColapseTable
                                              key={index}
                                              row={row}
                                              target={globalViewTarget.filter(
                                                  (target) => target.refGlobalVisionKPI.externalId == row.kpi.externalId
                                              )}
                                              openAll={openAll}
                                          />
                                      ))}
                            </TableBody>
                        </Table>
                    )}
                </TableContainer>
            }
        </Box>
    )
}

export default GlobalViewTable
