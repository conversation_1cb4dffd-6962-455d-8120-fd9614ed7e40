import { ExternalEntity } from '.'
import { BusinessSegment } from './business-segment'
import { Site } from './site'

export interface CostVarianceKpi extends ExternalEntity {
    reportingSite: Site
    pid: string
    pidDesc: string
    costType: string
    plantPeriodCost: number | undefined
    energyCost: number | undefined
    month: string
    year: number | undefined
    businessSegment: BusinessSegment | undefined
}
