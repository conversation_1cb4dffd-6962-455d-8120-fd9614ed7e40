import { unique } from '.'
import { TemplateConfig } from '../models'
import { User } from '../models/user'
import { UserComplement } from '../models/userComplement'

export function GetAssignees(config: TemplateConfig, usersComplement: UserComplement[], users: User[]): string[] {
    const assignedTo: string[] = []

    if (config.disciplines?.length) {
        assignedTo.push(...config.disciplines.map((d) => 'discipline:' + d))
    }

    if (usersComplement?.length && users?.length) {
        let userComplementsFiltered = usersComplement

        if (
            config.assignedRoles?.length > 0 ||
            config.reportingUnit?.externalId ||
            config.reportingLocation?.externalId
        ) {
            if (config.assignedRoles?.length) {
                userComplementsFiltered = userComplementsFiltered.filter((u) =>
                    u.userRoleSite?.some((urs) =>
                        config.assignedRoles?.some((r) => urs.role?.externalId == r.externalId)
                    )
                )
            }

            if (config.reportingUnit?.externalId) {
                userComplementsFiltered = userComplementsFiltered.filter((u) =>
                    u.reportingUnits?.some((ru) => ru.externalId == config.reportingUnit?.externalId)
                )
            }

            if (config.reportingLocation?.externalId) {
                userComplementsFiltered = userComplementsFiltered.filter((u) =>
                    u.reportingLocations?.some((ru) => ru.externalId == config.reportingLocation?.externalId)
                )
            }

            if (userComplementsFiltered?.length) {
                const uniqueEmails = unique(
                    userComplementsFiltered.map((u) => u.userAzureAttribute.user.email.toLowerCase())
                )
                const externalIds = unique(
                    users.filter((u) => uniqueEmails.includes(u.email.toLowerCase())).map((u) => u.externalId)
                )

                assignedTo.push(...externalIds)
            }
        } else {
            assignedTo.push(...[])
        }
    }

    return assignedTo
}
