import { gql, QueryHookOptions } from '@apollo/client'
import { KpiGroups } from '@/common/utils/kpi-groups'
import { GlobalVisionKpiTableModel } from '../models/globalVisionKpiTableModel'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useEffect, useMemo, useRef, useState } from 'react'
import { QualityKpisKpidom } from '@/common/utils/quality-kpis'
import { FoundationalKpis } from '@/common/utils/foundational-kpis'
import { CgnFilter } from '../utils/gqlQueryUtils'
import dayjs from 'dayjs'

export interface GlobalVisionKpiSegmentRequest {
    skip?: boolean
    site: string[]
    businessSegment?: string
    kpiGroup: string
    limit?: string
    cursor?: string
    page: string
    granularity?: string
}

const GET_GLOBAL_VIEW_SEGMENTS_KPIDOM = gql`
    query getGlobalView($filter: _ListKpiResultFilter, $start: Int64, $end: Int64, $first: Int, $cursor: String) {
        listKpiResult(first: $first, filter: $filter, after: $cursor) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                reportingSite {
                    externalId
                    name
                }
                kpi {
                    externalId
                    code
                    name
                    description
                    symbol
                    category {
                        externalId
                        code
                        name
                    }
                }
                value {
                    getDataPoints(start: $start, end: $end) {
                        items {
                            value
                            timestamp
                        }
                    }
                    externalId
                }
            }
        }
    }
`

const groupConversionName = ['Foundational', 'Stewardship', 'Quality', 'Reliability', 'Engagement']
const groupConversion = ['FDL', 'STW', 'QLT', 'RLB', 'EGM']
const setVariablesKpidom = (request: GlobalVisionKpiSegmentRequest) => {
    const filter: CgnFilter<any>[] = []
    const groupIndex = groupConversionName.indexOf(request?.kpiGroup)

    filter.push({ businessLevel: { code: { eq: 'BUS' } } })

    if (request?.granularity) {
        filter.push({ timeGranularity: { code: { in: [request.granularity] } } })
    }
    if (request?.businessSegment && request?.businessSegment.length > 0) {
        filter.push({ businessSegment: { externalId: { in: [request.businessSegment] } } })
    }
    if (request?.site?.length) {
        filter.push({ reportingSite: { externalId: { in: request.site } } })
    }
    if (groupIndex !== -1) {
        filter.push({ kpi: { category: { code: { eq: groupConversion[groupIndex] } } } })
    }
    if (request?.kpiGroup === KpiGroups.QUALITY.NAME) {
        filter.push({
            kpi: {
                externalId: {
                    in: [
                        QualityKpisKpidom.HS_QN1S,
                        QualityKpisKpidom.QN1S,
                        QualityKpisKpidom.MAJOR_AUDIT_FINDINGS_EXTERNAL,
                        QualityKpisKpidom.BLOCK_STOCK_FINISHED_GOODS,
                        QualityKpisKpidom.BLOCK_STOCK_GENERATED,
                        QualityKpisKpidom.BLOCK_STOCK_CONSUMED,
                    ],
                },
            },
        })
    }
    const startFilter =
        request.granularity === 'ANL'
            ? dayjs().subtract(2, 'year').startOf('year').valueOf()
            : dayjs().startOf('year').valueOf()
    const endFilter = dayjs().endOf('year').valueOf()

    return {
        filter: { and: filter },
        first: 1_000,
        start: startFilter,
        end: endFilter,
    }
}

export const useGlobalVisionKPISegment = (request: GlobalVisionKpiSegmentRequest) => {
    const filter = setVariablesKpidom(request)
    const cursorRef = useRef(request.cursor ?? '')
    const queryOptions = useMemo<QueryHookOptions>(
        () => ({
            variables: filter,
            fetchPolicy: 'network-only',
            context: {
                clientName: 'kpidomView',
            },
            skip: request.skip,
        }),
        [request]
    )

    const {
        data: fdmData,
        pageInfo: pageInfo,
        refetch,
        fetchMore,
        loading,
    } = useGraphqlQuery<GlobalVisionKpiTableModel>(GET_GLOBAL_VIEW_SEGMENTS_KPIDOM, 'listKpiResult', queryOptions)

    const [resultData, setResultData] = useState<{ data: GlobalVisionKpiTableModel[] }>({
        data: [],
    })

    useEffect(() => {
        if (fdmData === undefined) {
            setResultData((prevState) => ({ ...prevState }))
        } else {
            setResultData((prevState) => ({ ...prevState }))
            if (fdmData.length > 0) {
                if (pageInfo?.hasNextPage && cursorRef.current !== pageInfo.endCursor) {
                    request.cursor = pageInfo.endCursor
                    cursorRef.current = pageInfo.endCursor
                    fetchMore({
                        variables: {
                            cursor: pageInfo.endCursor,
                        },
                    })
                }

                let result_aggregation = GlobalVisionKPIAggregation(fdmData, request?.granularity)
                const listIds = listOrder.map((x) => x.EXTERNAL_ID)
                result_aggregation = result_aggregation.filter((items) => listIds.includes(items.kpi.externalId))
                result_aggregation = result_aggregation.sort((a, b) => a.kpi.order - b.kpi.order)

                if (request.kpiGroup && request.kpiGroup.length > 0) {
                    if (request.kpiGroup == 'Stewardship') {
                        const result_apply_rules = ApplyStewardshipRules(result_aggregation)
                        setResultData({ data: result_apply_rules })
                    } else if (request.kpiGroup == 'Foundational') {
                        const result_apply_rules = ApplyFoundationalRules(result_aggregation)
                        setResultData({ data: result_apply_rules })
                    } else {
                        const result_apply_rules = ApplyDefaultRules(result_aggregation)
                        setResultData({ data: result_apply_rules })
                    }
                } else {
                    setResultData({ data: result_aggregation })
                }
            } else {
                setResultData({ data: [] })
            }
        }
    }, [fdmData, fetchMore, request.kpiGroup])

    return useMemo(
        () => ({
            loadingGlobalVisionKPI: loading,
            refetchGlobalVisionKPI: refetch,
            dataGlobalVisionKPI: resultData.data,
            fetchMore: fetchMore,
        }),
        [loading, refetch, resultData]
    )
}

const listOrder: any[] = [
    { EXTERNAL_ID: 'KPIG-FLD', ORDER: 1 },
    { EXTERNAL_ID: 'KPIG-APF', ORDER: 2 },
    { EXTERNAL_ID: 'KPIG-ACF', ORDER: 3 },
    { EXTERNAL_ID: 'KPIG-AOP', ORDER: 4 },
    { EXTERNAL_ID: 'KPIG-PCM', ORDER: 5 },
    { EXTERNAL_ID: 'KPIG-MKG', ORDER: 6 },
    { EXTERNAL_ID: 'KPIG-KGH', ORDER: 7 },
    { EXTERNAL_ID: 'KPIG-PRD', ORDER: 8 },
    { EXTERNAL_ID: 'KPIG-PNY', ORDER: 9 },
    { EXTERNAL_ID: 'KPIG-PPN', ORDER: 10 },
    { EXTERNAL_ID: 'KPIG-FCS', ORDER: 99 },
    { EXTERNAL_ID: 'KPIG-ACS', ORDER: 99 },
    { EXTERNAL_ID: 'KPIG-APV', ORDER: 99 },
    { EXTERNAL_ID: 'KPIG-FPV', ORDER: 99 },

    { EXTERNAL_ID: 'KPIG-RCI', ORDER: 1 },
    { EXTERNAL_ID: 'KPIG-NFA', ORDER: 2 },
    { EXTERNAL_ID: 'KPIG-PST', ORDER: 3 },
    { EXTERNAL_ID: 'KPIG-EVT', ORDER: 4 },
    { EXTERNAL_ID: 'KPIG-NFT', ORDER: 5 },
    { EXTERNAL_ID: 'KPIG-NNM', ORDER: 6 },
    { EXTERNAL_ID: 'KPIG-HPE', ORDER: 7 },
    { EXTERNAL_ID: 'KPIG-TTL', ORDER: 8 },
    { EXTERNAL_ID: 'KPIG-TEP', ORDER: 9 },
    { EXTERNAL_ID: 'KPIG-TCT', ORDER: 10 },

    { EXTERNAL_ID: 'KPIG-HQS', ORDER: 2 },
    { EXTERNAL_ID: 'KPIG-QN1', ORDER: 3 },
    { EXTERNAL_ID: 'KPIG-MAF', ORDER: 4 },
    { EXTERNAL_ID: 'KPIG-FGB', ORDER: 11 },
    { EXTERNAL_ID: 'KPIG-GBS', ORDER: 13 },
    { EXTERNAL_ID: 'KPIG-CBS', ORDER: 14 },

    { EXTERNAL_ID: 'KPIG-OEE', ORDER: 1 },
]

const getOrder = (kpiExternalId: string): number => {
    const index = listOrder.findIndex((item) => item.EXTERNAL_ID === kpiExternalId)
    if (index !== -1) {
        return listOrder[index].ORDER
    }
    return 99
}

const getMonthValueFromTimestamp = (items: any, monthIndex: number): number => {
    const hasMonth = items.find((x) => dayjs(x.timestamp).month() === monthIndex)
    if (hasMonth) {
        return hasMonth.value
    } else {
        return 0
    }
}

const getYearValueFromTimestamp = (items: any, year: number): number => {
    const hasYear = items.find((x) => dayjs(x.timestamp).year() === year)
    if (hasYear) {
        return hasYear.value
    } else {
        return 0
    }
}

function GlobalVisionKPIAggregation(data: any[], granularity: string) {
    const data_by_kpi = Object.values(
        data.reduce((group, item) => {
            group[item.kpi.name] = group[item.kpi.name] ?? []

            const aux = JSON.stringify(item)
            const newItem = JSON.parse(aux)
            newItem.kpi['order'] = getOrder(item.kpi.externalId)

            group[item.kpi.name].push(newItem)
            return group
        }, {})
    )
    const data_by_kpi_by_site_segment: { kpi: any; data: any[] }[] = []
    data_by_kpi.forEach((x: any) => {
        const result: any[] = []

        if (granularity === 'MON') {
            x.reduce(function (res: any, item: any) {
                if (!res[item.reportingSite.externalId]) {
                    res[item.reportingSite.externalId] = {
                        refReportingSite: item.reportingSite,
                        refGlobalVisionKPI: item.kpi,
                        lastTwoYear: 0,
                        lastYear: 0,
                        ytd: 0,

                        lastThreeMonth: 0,
                        lastTwoMonth: 0,
                        lastMonth: 0,
                        actualMonth: 0,

                        january: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 0),
                        february: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 1),
                        march: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 2),
                        april: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 3),
                        may: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 4),
                        june: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 5),
                        july: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 6),
                        august: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 7),
                        september: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 8),
                        october: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 9),
                        november: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 10),
                        december: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 11),
                    }
                    result.push(res[item.reportingSite.externalId])
                }
                return res
            }, {})
        }
        if (granularity === 'QRT') {
            x.reduce(function (res: any, item: any) {
                if (!res[item.reportingSite.externalId]) {
                    res[item.reportingSite.externalId] = {
                        refReportingSite: item.reportingSite,
                        refGlobalVisionKPI: item.kpi,
                        lastTwoYear: 0,
                        lastYear: 0,
                        ytd: 0,

                        lastThreeMonth: 0,
                        lastTwoMonth: 0,
                        lastMonth: 0,
                        actualMonth: 0,

                        january: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 0),
                        february: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 0),
                        march: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 0),
                        april: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 3),
                        may: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 3),
                        june: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 3),
                        july: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 6),
                        august: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 6),
                        september: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 6),
                        october: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 9),
                        november: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 9),
                        december: getMonthValueFromTimestamp(item.value?.getDataPoints?.items, 9),
                    }
                    result.push(res[item.reportingSite.externalId])
                }
                return res
            }, {})
        }
        if (granularity === 'ANL') {
            x.reduce(function (res: any, item: any) {
                if (!res[item.reportingSite.externalId]) {
                    res[item.reportingSite.externalId] = {
                        refReportingSite: item.reportingSite,
                        refGlobalVisionKPI: item.kpi,
                        lastTwoYear: getYearValueFromTimestamp(item.value?.getDataPoints?.items, dayjs().year() - 2),
                        lastYear: getYearValueFromTimestamp(item.value?.getDataPoints?.items, dayjs().year() - 1),
                        ytd: getYearValueFromTimestamp(item.value?.getDataPoints?.items, dayjs().year()),

                        lastThreeMonth: 0,
                        lastTwoMonth: 0,
                        lastMonth: 0,
                        actualMonth: 0,

                        january: 0,
                        february: 0,
                        march: 0,
                        april: 0,
                        may: 0,
                        june: 0,
                        july: 0,
                        august: 0,
                        september: 0,
                        october: 0,
                        november: 0,
                        december: 0,
                    }
                    result.push(res[item.reportingSite.externalId])
                }
                return res
            }, {})
        }
        data_by_kpi_by_site_segment.push({
            kpi: x[0].kpi,
            data: result.sort(
                (a, b) =>
                    a.refReportingSite.name.localeCompare(b.refReportingSite.name) ||
                    a.refBusinessSegment.name.localeCompare(b.refBusinessSegment.name)
            ),
        })
    })
    return data_by_kpi_by_site_segment as any[]
}

function ApplyStewardshipRules(data: any[]) {
    data.filter((kpi) => {
        return (
            kpi['kpi']['externalId'] == 'KPIG-TTL' ||
            kpi['kpi']['externalId'] == 'KPIG-TEP' ||
            kpi['kpi']['externalId'] == 'KPIG-TCT'
        )
    }).forEach((kpi) => {
        kpi['data'].forEach((site: any) => {
            site.lastTwoYear = site.lastTwoYear.toFixed(4)
            site.lastYear = site.lastYear.toFixed(4)
            site.ytd = site.ytd.toFixed(4)
            site.lastThreeMonth = site.lastThreeMonth.toFixed(4)
            site.lastTwoMonth = site.lastTwoMonth.toFixed(4)
            site.lastMonth = site.lastMonth.toFixed(4)
            site.actualMonth = site.actualMonth.toFixed(4)
            site.january = site.january.toFixed(4)
            site.february = site.february.toFixed(4)
            site.march = site.march.toFixed(4)
            site.april = site.april.toFixed(4)
            site.may = site.may.toFixed(4)
            site.june = site.june.toFixed(4)
            site.july = site.july.toFixed(4)
            site.august = site.august.toFixed(4)
            site.september = site.september.toFixed(4)
            site.october = site.october.toFixed(4)
            site.november = site.november.toFixed(4)
            site.december = site.december.toFixed(4)
        })
    })
    return data
}

function ApplyFoundationalRules(data: any) {
    const foundationalKpisValues = Object.values(FoundationalKpis)
    data.forEach((kpi: any) => {
        kpi['data'].forEach((site: any) => {
            site.lastTwoYear = site.lastTwoYear.toFixed(2)
            site.lastYear = site.lastYear.toFixed(2)
            site.ytd = site.ytd.toFixed(2)
            site.lastThreeMonth = site.lastThreeMonth.toFixed(2)
            site.lastTwoMonth = site.lastTwoMonth.toFixed(2)
            site.lastMonth = site.lastMonth.toFixed(2)
            site.actualMonth = site.actualMonth.toFixed(2)
            site.january = site.january.toFixed(2)
            site.february = site.february.toFixed(2)
            site.march = site.march.toFixed(2)
            site.april = site.april.toFixed(2)
            site.may = site.may.toFixed(2)
            site.june = site.june.toFixed(2)
            site.july = site.july.toFixed(2)
            site.august = site.august.toFixed(2)
            site.september = site.september.toFixed(2)
            site.october = site.october.toFixed(2)
            site.november = site.november.toFixed(2)
            site.december = site.december.toFixed(2)
        })
        const matchingKpi = foundationalKpisValues.find(
            (foundationalKpi) => foundationalKpi.EXTERNAL_ID == kpi.kpi.externalId
        )
        if (matchingKpi) {
            kpi.kpi = { ...kpi.kpi, name: matchingKpi.NAME }
        }
    })
    return data
}

function ApplyDefaultRules(data: any) {
    data.forEach((kpi: any) => {
        kpi['data'].forEach((site: any) => {
            site.lastTwoYear = site.lastTwoYear.toFixed(2)
            site.lastYear = site.lastYear.toFixed(2)
            site.ytd = site.ytd.toFixed(2)
            site.lastThreeMonth = site.lastThreeMonth.toFixed(2)
            site.lastTwoMonth = site.lastTwoMonth.toFixed(2)
            site.lastMonth = site.lastMonth.toFixed(2)
            site.actualMonth = site.actualMonth.toFixed(2)
            site.january = site.january.toFixed(2)
            site.february = site.february.toFixed(2)
            site.march = site.march.toFixed(2)
            site.april = site.april.toFixed(2)
            site.may = site.may.toFixed(2)
            site.june = site.june.toFixed(2)
            site.july = site.july.toFixed(2)
            site.august = site.august.toFixed(2)
            site.september = site.september.toFixed(2)
            site.october = site.october.toFixed(2)
            site.november = site.november.toFixed(2)
            site.december = site.december.toFixed(2)
        })
    })
    return data
}
