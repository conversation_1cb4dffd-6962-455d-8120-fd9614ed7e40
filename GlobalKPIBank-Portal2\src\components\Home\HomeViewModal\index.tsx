import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { ModalWrapper } from '../../Modal/ModalWrapper'
import { Box } from '@mui/material'
import { useEffect } from 'react'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { DataTab } from '../../../components/DataTab'

interface HomeViewModalProps {
    data?: GlobalVisionKpiTableModel
    open: boolean
    handleClose: () => void
    target?: GlobalVisionKpiTarget[]
}

export const HomeViewModal: React.FC<HomeViewModalProps> = ({ open, handleClose, data, target }) => {
    const { selectedKpiData, setSelectedKpi, setSelectedKpiData, setRequestData, selectedSiteId } =
        useGlobalKpiContext()

    useEffect(() => {
        if (data) {
            setSelectedKpiData(data.kpi)
            setSelectedKpi(data.kpi.externalId)

            setRequestData((prevData) => ({
                ...prevData,
                cursor: '',
                kpiFilters: {
                    ...prevData.kpiFilters,
                    refSite: selectedSiteId ? [selectedSiteId] : prevData.kpiFilters.refSite,
                },
            }))
        }
    }, [data, selectedSiteId])

    return (
        <ModalWrapper
            openModal={open}
            closeModal={handleClose}
            title={' '}
            content={
                <Box
                    sx={{
                        width: '1000px',
                        maxHeight: '700px',
                        padding: '0px 20px 35px 20px',
                        overflowX: 'hidden',
                        fontFamily: 'Roboto',
                        overflowY: data !== undefined && data.data.length < 5 ? 'auto' : 'scroll',
                        margin: '0px 10px',
                    }}
                >
                    {selectedKpiData && <DataTab data={data} target={target} />}
                </Box>
            }
        />
    )
}
