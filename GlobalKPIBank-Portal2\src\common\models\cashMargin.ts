import { ExternalEntity } from '.'
import { BusinessSegment } from './business-segment'
import { Site } from './site'

export interface CashMarginKpi extends ExternalEntity {
    reportingSite: Site
    pid: string
    pidDesc: string
    variableMargin: number | undefined
    fixedDistributionCost: number | undefined
    otherFixedCost: number | undefined
    plantPeriodCost: number | undefined
    energyCost: number | undefined
    salesVolume: number | undefined
    businessSegment: BusinessSegment | null
}
