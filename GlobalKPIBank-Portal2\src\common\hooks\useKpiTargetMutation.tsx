import { ArrayEntity, Entity, ExternalEntity } from '../models'
import { useFdmMutation } from './useFdmMutation'

export interface KpiTargetMutation extends Entity {
    value: number
    rangeDirection: string
    year: number
}

interface MutationDataResult extends ExternalEntity {
    createdTime: number
    instanceType: string
    lastUpdatedTime: number
    version: number
    wasModified: boolean
}

export const useKpiTargetMutation = () => {
    const [mutationFn] = useFdmMutation<KpiTargetMutation>('GlobalVisionKPITarget', undefined, false)

    const updateKpiTargetMutationFn = async (entity: KpiTargetMutation[], space: string) => {
        const updateKpiTargetMutationResult = await mutationFn(entity, [], undefined, space, undefined, undefined)

        return {
            ok: updateKpiTargetMutationResult.ok,
            data: updateKpiTargetMutationResult.data as unknown as ArrayEntity<MutationDataResult>,
            error: updateKpiTargetMutationResult.error,
        }
    }

    return { updateKpiTargetMutationFn } as const
}
