import { VisionKpiAggregation } from '@/common/models/visionKpiAggregation'
import { Bar<PERSON>hart, QuantityBarItemChart } from '../../Charts/BarChart'
import { translate } from '@celanese/celanese-sdk'
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react'
import { Box, Typography } from '@celanese/ui-lib'
import KpiDataModal from '../KpiDataModal'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { numberFormat } from '@/common/utils/numberFormat'
import { useDataChartContext } from '@/common/contexts/DataChartContext'
import dayjs from 'dayjs'
import { usePathname } from 'next/navigation'

interface KpiChartProps {
    data: VisionKpiAggregation[]
    range?: number
    target?: GlobalVisionKpiTarget[]
    selectedKpi?: string
    openModal?: boolean
    setOpenModal?: Dispatch<SetStateAction<boolean>>
}

const LAST_YEAR = new Date().getFullYear() - 1
const LAST_TWO_YEAR = new Date().getFullYear() - 2

const Dot = () => (
    <Box
        sx={{
            height: 4,
            width: 4,
            backgroundColor: 'text.secondary',
            borderRadius: '50%',
            display: 'inline-block',
        }}
    />
)

export const KpiDataChart: React.FC<KpiChartProps> = ({
    data,
    range,
    selectedKpi,
    target,
    openModal = false,
    setOpenModal,
}) => {
    const [formattedData, setFormattedData] = useState<QuantityBarItemChart[]>([])
    const [labels, setLabels] = useState<string[]>([])
    const [selectedSitesOnLegend, setSelectedSitesOnLegend] = useState<QuantityBarItemChart[]>([])
    const { setSelectedBar, selectedBar } = useDataChartContext()
    const pathName = usePathname().split('/')[1]

    const lastYearValue = useMemo(() => data?.reduce((acc, cur) => acc + Number(cur.lastYear), 0), [data])
    const lastTwoYearValue = useMemo(() => data?.reduce((acc, cur) => acc + Number(cur.lastTwoYear), 0), [data])
    const ytdValue = useMemo(() => data?.reduce((acc, cur) => acc + Number(cur.ytd), 0), [data])

    useEffect(() => {
        const formattedDataAux: QuantityBarItemChart[] = []
        const labelsAux: string[] = []
        const trirsExternalIds = ['KPIG-TTL', 'KPIG-TCT', 'KPIG-TEP']

        if (selectedKpi !== 'KPIG-PCM' && selectedKpi !== 'KPIG-MKG') {
            data?.map((item) => {
                const auxItem = { ...item }

                if (trirsExternalIds.includes(selectedKpi)) {
                    const isClosedMonth = dayjs().day() >= 15
                    const currentMonth = dayjs().format('MMMM').toLowerCase()

                    if (isClosedMonth) {
                        auxItem[currentMonth] = '0.000'
                    } else {
                        const lastMonth = dayjs().subtract(1, 'month').format('MMMM').toLowerCase()
                        auxItem[lastMonth] = '0.000'
                        auxItem[currentMonth] = '0.000'
                    }
                }

                formattedDataAux.push({
                    label: auxItem.refReportingSite.name,
                    value: [
                        Number(auxItem.january),
                        Number(auxItem.february),
                        Number(auxItem.march),
                        Number(auxItem.april),
                        Number(auxItem.may),
                        Number(auxItem.june),
                        Number(auxItem.july),
                        Number(auxItem.august),
                        Number(auxItem.september),
                        Number(auxItem.october),
                        Number(auxItem.november),
                        Number(auxItem.december),
                    ],
                })
            })

            labelsAux.push(translate('TABLE_COLS.MONTHS.JANUARY'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.FEBRUARY'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.MARCH'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.APRIL'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.MAY'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.JUNE'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.JULY'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.AUGUST'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.SEPTEMBER'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.OCTOBER'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.NOVEMBER'))
            labelsAux.push(translate('TABLE_COLS.MONTHS.DECEMBER'))
        } else {
            data?.map((item) =>
                formattedDataAux.push({
                    label: item.refReportingSite.name,
                    value: [item.january, item.april, item.july, item.october],
                })
            )
            labelsAux.push('Q1')
            labelsAux.push('Q2')
            labelsAux.push('Q3')
            labelsAux.push('Q4')
        }

        if (formattedDataAux) setFormattedData(formattedDataAux)

        setLabels(labelsAux)
    }, [data, selectedSitesOnLegend])

    return (
        selectedSitesOnLegend && (
            <Box flex={1} display="flex" flexDirection="column" overflow={selectedBar ? 'visible' : 'hidden'}>
                <Box flex={1} height="100%">
                    <Box height="calc(100% - 15px)">
                        {selectedSitesOnLegend.length !== 1 && formattedData.length !== 1 ? (
                            <BarChart
                                id={selectedKpi ?? ''}
                                data={formattedData}
                                labels={labels}
                                title={''}
                                selectedKpi={selectedKpi}
                                setSelectedSitesOnLegend={setSelectedSitesOnLegend}
                                onPointSelect={setSelectedBar}
                            />
                        ) : (
                            <BarChart
                                id={selectedKpi ?? ''}
                                data={formattedData}
                                labels={labels}
                                title={''}
                                range={
                                    !selectedSitesOnLegend[0]
                                        ? range
                                        : target &&
                                          (target.length == 0
                                              ? 0
                                              : target.find(
                                                    (item) =>
                                                        item.refGlobalVisionKPI.externalId === selectedKpi &&
                                                        item.refReportingSite.name === selectedSitesOnLegend[0].label
                                                )?.value)
                                }
                                selectedKpi={selectedKpi}
                                setSelectedSitesOnLegend={setSelectedSitesOnLegend}
                                onPointSelect={setSelectedBar}
                            />
                        )}
                    </Box>
                    <Box width="100%" height={20}>
                        <Typography
                            color="text.secondary"
                            display="flex"
                            lineHeight="20px"
                            alignItems="center"
                            textAlign="center"
                            justifyContent="center"
                            columnGap={0.5}
                            variant="caption"
                        >
                            {pathName === 'home' ? (
                                <>
                                    {LAST_TWO_YEAR}: {numberFormat(lastTwoYearValue)} <Dot /> {LAST_YEAR}:{' '}
                                    {numberFormat(lastYearValue)} <Dot /> YTD: {numberFormat(ytdValue)}
                                </>
                            ) : (
                                <>
                                    <Dot /> YTD: {numberFormat(ytdValue)}
                                </>
                            )}
                        </Typography>
                    </Box>
                </Box>
                <KpiDataModal
                    data={data}
                    open={openModal}
                    handleClose={() => setOpenModal(false)}
                    selectedKPI={selectedKpi}
                    range={range}
                    target={target}
                />
            </Box>
        )
    )
}
