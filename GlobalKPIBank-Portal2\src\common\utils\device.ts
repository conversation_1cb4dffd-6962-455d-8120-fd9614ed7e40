export function isNotebook(): boolean {
    if (typeof window === 'object') {
        return window.innerWidth <= 1400
    }
    return false
}

export function isMobile(): boolean {
    if (typeof window === 'object') {
        return window.innerWidth <= 1024
    }
    return false
}

export function isSmartPhone(): boolean {
    if (typeof window === 'object') {
        return window.innerWidth <= 600
    }
    return false
}
