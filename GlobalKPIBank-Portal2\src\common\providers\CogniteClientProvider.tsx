import React, { PropsWithChildren } from 'react'
import { CogniteContext } from '../contexts/CogniteContext'
import { createCogniteClient } from '../factories/cognite-client-factory'
import { createFdmClient } from '../factories/fdm-client-factory'
import { useAuthToken } from '../hooks'

export const CogniteClientProvider = ({ children }: PropsWithChildren) => {
    const { getAuthToken } = useAuthToken()
    const cogniteClient = React.useMemo(() => createCogniteClient(getAuthToken), [getAuthToken])
    const fdmClient = React.useMemo(() => createFdmClient(getAuthToken), [getAuthToken])

    return (
        <CogniteContext.Provider
            value={{
                cogniteClient,
                fdmClient,
            }}
        >
            {children}
        </CogniteContext.Provider>
    )
}
