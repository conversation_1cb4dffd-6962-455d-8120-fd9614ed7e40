import { gql } from '@apollo/client'
import { useMemo } from 'react'
import { useGraphqlQuery } from './useGraphqlQuery'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'
import { Observation } from '../models/observation'

export interface ObservationQueryRequest {
    start?: string
    end?: string
    space?: string
}

const buildObservationQuery = (request: ObservationQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.start) {
        filter.push(`{ createdTime: { gte: ${request.start} }}`)
    }

    if (request.end) {
        filter.push(`{ createdTime: { lte: ${request.end} }}`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetObservation {
            listObservation(
                filter: ${queryFilter}
            , first: 1000) {
                items {
                    externalId
                    description
                    space
                    sourceId
                    source
                    title
                    labels
                    visibility
                    createdBy {
                        externalId
                        email
                    }
                    updatedBy {
                        externalId
                        email
                    }
                    isArchived
                    status
                    asset {
                        externalId
                        space
                        description
                        title
                        lastUpdatedTime
                        createdTime
                        parent {
                            externalId
                        }
                    }
                    rootLocation {
                        externalId
                        space
                        lastUpdatedTime
                        createdTime
                    }
                    priority
                    type
                    lastUpdatedTime
                    createdTime
                }
            }
        }
    `

    return query
}

export const useObservation = (request: ObservationQueryRequest) => {
    const query = useMemo(() => gql(buildObservationQuery(request)), [request])

    return useGraphqlQuery<Observation>(query, 'listObservation')
}
