import { Observation } from './observation'
import { ExternalEntity } from '.'
import { Dayjs } from 'dayjs'
import { User } from './user'
import { ChecklistItemFile } from './checklist-item-file'
import { Measurement } from './measurement'
import { Asset } from './asset'
import { UpdatedBy } from './updated-by'

export type ChecklistItemStatus = 'Ok' | 'Not ok' | 'Blocked' | 'Not applicable'

export const ChecklistItemStatusEnum = {
    Ok: 'Ok',
    NotOk: 'Not ok',
    Blocked: 'Blocked',
    NotApplicable: 'Not applicable',
} as const

export interface ChecklistItem extends ExternalEntity {
    sourceId: string
    title: string
    description: string
    discipline: string
    labels: string[]
    order: number
    status?: ChecklistItemStatus
    note: string
    startTime: Dayjs
    endTime: Dayjs
    checklistExternalId: string
    asset: Asset
    files: ChecklistItemFile[]
    measurements: Measurement[]
    observations: Observation[]
    createdBy: User
    updatedBy: UpdatedBy | User
    lastUpdatedTime: Dayjs
}
