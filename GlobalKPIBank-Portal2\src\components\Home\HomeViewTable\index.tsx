import { translate } from '@celanese/celanese-sdk'
import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { LoaderCircular } from '../../../components/Loader'
import dayjs from 'dayjs'
import { numberFormat } from '@/common/utils/numberFormat'
import { VisionKpiAggregation } from '@/common/models/visionKpiAggregation'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { usePathname } from 'next/navigation'
import { agglomerateValues } from '@/common/utils/agglomerateKpi'
import KpiDataModal from '../../../components/DataTab/KpiDataModal'

interface HomeViewTableProps {
    data: VisionKpiAggregation[]
    target?: GlobalVisionKpiTarget[]
    isInModal?: boolean
    openModal?: boolean
    setOpenModal?: Dispatch<SetStateAction<boolean>>
}

export const HomeViewTable: React.FC<HomeViewTableProps> = ({
    data,
    target,
    isInModal,
    openModal = false,
    setOpenModal,
}) => {
    const [loader, setLoader] = useState(true)
    const { selectedKpi } = useGlobalKpiContext()
    const pathName = usePathname().split('/')[1]
    const formatLastMonths = (count: number): [string[], string[]] => {
        const monthNamesTranslated = [
            translate('TABLE_COLS.MONTHS.JANUARY'),
            translate('TABLE_COLS.MONTHS.FEBRUARY'),
            translate('TABLE_COLS.MONTHS.MARCH'),
            translate('TABLE_COLS.MONTHS.APRIL'),
            translate('TABLE_COLS.MONTHS.MAY'),
            translate('TABLE_COLS.MONTHS.JUNE'),
            translate('TABLE_COLS.MONTHS.JULY'),
            translate('TABLE_COLS.MONTHS.AUGUST'),
            translate('TABLE_COLS.MONTHS.SEPTEMBER'),
            translate('TABLE_COLS.MONTHS.OCTOBER'),
            translate('TABLE_COLS.MONTHS.NOVEMBER'),
            translate('TABLE_COLS.MONTHS.DECEMBER'),
        ]
        const monthNames = [
            'january',
            'february',
            'march',
            'april',
            'may',
            'june',
            'july',
            'august',
            'september',
            'october',
            'november',
            'december',
        ]

        const translatedMonths = []
        const months = []

        Array.from({ length: count }, (_, i) => {
            const currentDate = dayjs().subtract(i, 'month')
            translatedMonths.push(`${monthNamesTranslated[currentDate.month()]}/${currentDate.format('YY')}`)
            months.push(`${monthNames[currentDate.month()]}/${currentDate.format('YY')}`)
        }).reverse()

        return [translatedMonths, months]
    }
    const [last4MonthsTranslated, last4Months] = formatLastMonths(4)

    useEffect(() => {
        setLoader(data?.length === 0)
    }, [data])

    return (
        <Box sx={{ ...{ maxWidth: '100%', marginTop: '16px' }, ...(isInModal ? {} : { height: '50vh' }) }}>
            <TableContainer
                sx={{
                    ...{ overflow: 'auto' },
                    ...(isInModal ? {} : { maxHeight: 'calc(70vh - 50px)' }),
                }}
            >
                {loader ? (
                    LoaderCircular()
                ) : (
                    <Table>
                        <TableHead
                            sx={{
                                position: 'sticky',
                                top: 0,
                                zIndex: 1,
                                backgroundColor: '#ffffff',
                                fontWeight: 'bold',
                                textAlign: 'center',
                                borderBottom: '2px solid #ddd',
                            }}
                        >
                            <TableRow>
                                <TableCell align="left" sx={{ width: '17%', fontWeight: 'bold' }}>
                                    {translate('TABLE_COLS.SITE')}
                                </TableCell>
                                <TableCell align="center" sx={{ width: '9%' }}>
                                    {(dayjs().month() === 0 || (dayjs().month() === 1 && dayjs().date() < 11)
                                        ? dayjs().year() - 2
                                        : dayjs().year() - 1
                                    ).toString()}
                                </TableCell>
                                <TableCell align="center" sx={{ width: '9%' }}>
                                    {translate('TABLE_COLS.YTD')}
                                </TableCell>
                                <TableCell align="center" sx={{ width: '9%' }}>
                                    {translate('TABLE_COLS.TARGET')}
                                </TableCell>
                                {last4MonthsTranslated.map((month, i) => (
                                    <TableCell key={i} align="center" sx={{ width: '9%' }}>
                                        {month}
                                    </TableCell>
                                ))}
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {pathName === 'site-view' ? (
                                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                                    <TableCell sx={{ fontWeight: 'bold' }}>
                                        {translate('TABLE_COLS.SELECTED_SITES_TOTAL')}
                                    </TableCell>
                                    <TableCell align="center">
                                        {numberFormat(
                                            agglomerateValues(
                                                data.map((row) => {
                                                    const value = row.lastYear
                                                    if (typeof value === 'number' && !isNaN(value)) return value
                                                    if (typeof value === 'string' && !isNaN(Number(value)))
                                                        return Number(value)
                                                    return undefined
                                                }),
                                                selectedKpi
                                            )
                                        )}
                                    </TableCell>
                                    <TableCell align="center">
                                        {numberFormat(
                                            agglomerateValues(
                                                data.map((row) => {
                                                    const value = row.ytd
                                                    if (typeof value === 'number' && !isNaN(value)) return value
                                                    if (typeof value === 'string' && !isNaN(Number(value)))
                                                        return Number(value)
                                                    return undefined
                                                }),
                                                selectedKpi
                                            )
                                        )}
                                    </TableCell>
                                    <TableCell align="center">
                                        {numberFormat(
                                            agglomerateValues(
                                                data.map(
                                                    (row) =>
                                                        target?.find(
                                                            (item) =>
                                                                item.refGlobalVisionKPI.externalId ===
                                                                    row.refGlobalVisionKPI?.externalId &&
                                                                item.refReportingSite.externalId ===
                                                                    row.refReportingSite.externalId
                                                        )?.value
                                                ),
                                                selectedKpi
                                            )
                                        )}
                                    </TableCell>
                                    {last4Months.map((month, i) => {
                                        const monthKey = month.split('/')[0].toLowerCase() as keyof VisionKpiAggregation
                                        return (
                                            <TableCell key={i} align="center">
                                                {numberFormat(
                                                    agglomerateValues(
                                                        data.map((row) => {
                                                            const value = row[monthKey]
                                                            if (typeof value === 'number' && !isNaN(value)) return value
                                                            if (typeof value === 'string' && !isNaN(Number(value)))
                                                                return Number(value)
                                                            return undefined
                                                        }),
                                                        selectedKpi
                                                    )
                                                )}
                                            </TableCell>
                                        )
                                    })}
                                </TableRow>
                            ) : null}
                            {data?.map((row: VisionKpiAggregation, index: number) => (
                                <TableRow
                                    key={index}
                                    sx={{
                                        backgroundColor:
                                            pathName === 'site-view'
                                                ? index % 2 === 0
                                                    ? '#fff'
                                                    : '#f9f9f9'
                                                : '#f5f5f5',
                                    }}
                                >
                                    <TableCell align="left" sx={{ width: '9%', fontWeight: 'bold' }}>
                                        {row.refReportingSite.name}
                                    </TableCell>
                                    <TableCell align="center" sx={{ width: '9%' }}>
                                        {numberFormat(row.lastYear)}
                                    </TableCell>
                                    <TableCell align="center" sx={{ width: '9%' }}>
                                        {numberFormat(row.ytd)}
                                    </TableCell>
                                    <TableCell align="center" sx={{ width: '9%' }}>
                                        {
                                            target.filter(
                                                (item) =>
                                                    item.refGlobalVisionKPI.externalId ===
                                                        row.refGlobalVisionKPI?.externalId &&
                                                    item.refReportingSite.externalId === row.refReportingSite.externalId
                                            )[0]?.value
                                        }
                                    </TableCell>
                                    {last4Months.map((month, i) => {
                                        const monthKey = month.split('/')[0].toLowerCase()
                                        return (
                                            <TableCell key={i} align="center" sx={{ width: '9%' }}>
                                                {numberFormat(row[monthKey])}
                                            </TableCell>
                                        )
                                    })}
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}
            </TableContainer>
            <KpiDataModal
                data={data}
                open={openModal}
                handleClose={() => setOpenModal(false)}
                selectedKPI={selectedKpi}
                range={0}
                target={target}
            />
        </Box>
    )
}
