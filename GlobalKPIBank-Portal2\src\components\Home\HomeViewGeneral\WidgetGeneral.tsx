import { useGetKpiGroups } from '@/common/hooks/useGetKpiGroups'
import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { KpiGroup } from '@/common/models/kpiGroup'
import { Box, Card, CardContent, Typography } from '@mui/material'
import { useEffect, useState } from 'react'
import { WidgetGeneralTable } from './WidgetGeneralTable'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { FinancialKpisDom } from '@/common/utils/financial-kpis'
import './styles.css'
import { HomeViewModal } from '../HomeViewModal'
import dayjs from 'dayjs'
import { translate } from '@celanese/celanese-sdk'
import { GeneralFilter } from '@/common/models/homeGeneralFilter'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'

interface WidgetGeneralProps {
    filters: any
    generalFilter: GeneralFilter
    generalViewMore: boolean
    data: GlobalVisionKpiTableModel[]
    globalViewTarget: GlobalVisionKpiTarget[]
}

export const WidgetGeneral: React.FC<WidgetGeneralProps> = ({
    filters,
    generalFilter,
    generalViewMore,
    data,
    globalViewTarget,
}) => {
    const { kpiGroupsList, loadingKpiGroups } = useGetKpiGroups()
    const { checkPermissionsFromRoutes } = useAuthGuard()
    const [filteredData, setFilteredData] = useState<GlobalVisionKpiTableModel[]>([])
    const [openModal, setOpenModal] = useState(false)
    const [selectedKpi, setSelectedKpi] = useState<GlobalVisionKpiTableModel>()
    const { setRequestData } = useGlobalKpiContext()
    const productivity = {
        'Productivity - Year Forecast': `Productivity - ${dayjs().year()}`,
        'Productivity Pipeline - Next Year': `Productivity Pipeline - ${dayjs().add(1, 'year').year()}`,
        'Productivity Pipeline - +2 Years': `Productivity Pipeline - ${dayjs().add(2, 'year').year()}`,
    }

    useEffect(() => {
        if (openModal) {
            setRequestData((prevData) => ({
                ...prevData,
                cursor: '',
                kpiFilters: {
                    ...prevData.kpiFilters,
                    refSite: filters.siteList,
                },
            }))
        }
    }, [openModal])

    const filteredDataByGroup = (groupName: string): GlobalVisionKpiTableModel[] => {
        let categoryCode: string = ''

        switch (groupName) {
            case 'Foundational':
                categoryCode = 'FDL'
                break
            case 'Stewardship':
                categoryCode = 'STW'
                break
            case 'Quality':
                categoryCode = 'QLT'
                break
            case 'Reliability':
                categoryCode = 'RLB'
                break
            default:
                return []
        }

        const filtered = filteredData.filter((kpi) => {
            if (['KPIG-FPV', 'KPIG-APV', 'KPIG-ACS', 'KPIG-FCS'].includes(kpi.kpi.externalId)) {
                return false
            }

            if (groupName === 'Stewardship') {
                const excludeTrirKpis =
                    filters.siteList.length >= 2 &&
                    ['GKPI-SOL-TTL', 'GKPI-SOL-TEP', 'GKPI-SOL-TCT'].includes(kpi.kpi.externalId)
                if (excludeTrirKpis) return false
            }

            return kpi.kpi.category?.code === categoryCode
        })

        return filtered.sort((a, b) => (a.kpi.order || 0) - (b.kpi.order || 0))
    }

    useEffect(() => {
        if (!data || data.length === 0) return

        const selectedKpis = data.map((kpiName) => kpiName.kpi.name)
        const indexToReplace = Object.keys(productivity).map((item) => selectedKpis.indexOf(item))
        const productivityValues = Object.values(productivity)

        let aux: GlobalVisionKpiTableModel[] = data.map((item: GlobalVisionKpiTableModel, index: number) => ({
            externalId: item.externalId,
            space: item.space,
            data: item.data,
            kpi: {
                ...item.kpi,
                name: indexToReplace.includes(index)
                    ? productivityValues[indexToReplace.indexOf(index)]
                    : item.kpi.name,
            },
        }))

        if (!checkPermissionsFromRoutes('globalViewFoundationalPlantCash')) {
            aux = aux.filter((value) => value.kpi.externalId !== FinancialKpisDom.PLANT_CASH)
        }
        if (!checkPermissionsFromRoutes('globalViewFoundationalPlantCashKg')) {
            aux = aux.filter((value) => value.kpi.externalId !== FinancialKpisDom.PLANT_CASH_KG)
        }
        setFilteredData(aux)
    }, [data])

    return loadingKpiGroups ? (
        <div></div>
    ) : (
        <>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', height: 'calc(100% - 19px)' }}>
                {kpiGroupsList
                    .filter((kpiGroup) => kpiGroup.name !== 'Engagement')
                    .map((kpiGroup: KpiGroup, index: number) => (
                        <Card
                            key={index}
                            className="generalCard"
                            sx={{
                                overflow: generalViewMore ? 'visible' : 'auto',
                                borderColor: '#F1F4F6',
                                height: generalViewMore ? '500px' : '50%',
                            }}
                        >
                            <CardContent sx={{ height: '100%', width: '100%', padding: '0px 12px 8px 12px' }}>
                                <>
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            paddingBottom: '8px',
                                            paddingTop: '8px',
                                            flexDirection: 'row',
                                            alignItems: 'baseline',
                                            fontWeight: 'bold',
                                            position: 'sticky',
                                            top: 0,
                                            backgroundColor: '#fff',
                                            zIndex: 1,
                                        }}
                                    >
                                        <Typography
                                            sx={{
                                                fontSize: '20px',
                                            }}
                                        >
                                            {translate('KPI_TAB_LIST.' + kpiGroup.name.toUpperCase())}
                                        </Typography>
                                    </Box>
                                    <Box
                                        sx={{
                                            marginTop: '5px',
                                            height: 'calc(100% - 33px)',
                                            overflowY: generalViewMore ? 'visible' : 'auto',
                                        }}
                                    >
                                        <WidgetGeneralTable
                                            data={filteredDataByGroup(kpiGroup.name)}
                                            target={globalViewTarget}
                                            filters={filters}
                                            generalFilter={generalFilter}
                                            setOpenModal={setOpenModal}
                                            setSelectedKpi={setSelectedKpi}
                                            generalViewMore={generalViewMore}
                                        />
                                    </Box>
                                </>
                            </CardContent>
                        </Card>
                    ))}
            </Box>
            <HomeViewModal
                open={openModal}
                handleClose={() => setOpenModal(false)}
                data={selectedKpi}
                target={globalViewTarget}
            />
        </>
    )
}
