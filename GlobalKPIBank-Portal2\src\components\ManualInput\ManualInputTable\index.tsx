import { translate } from '@celanese/celanese-sdk'
import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField } from '@mui/material'
import { useEffect, useState } from 'react'
import { ManualInputTableModel } from '@/common/models/manualInputDataTableModel'
import { Toastr, AlertData } from '../../Toastr'
import { ClnButton } from '@celanese/ui-lib'
import { useManualInputKpiMutation } from '@/common/hooks/useManualInputKPIMutation'
import './styles.css'
import LoaderCircular from '../../Loader'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import updateLocale from 'dayjs/plugin/updateLocale'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { AuthGuardContext } from '@/common/contexts/AuthGuardContext'
import { useContext } from 'react'
dayjs.extend(utc)
dayjs.extend(updateLocale)

interface ManualInputKPI {
    manualInputData: ManualInputTableModel[]
    loading: boolean
    refetchData: any
}

export const ManualInputKPITable: React.FC<ManualInputKPI> = ({ manualInputData, loading, refetchData }) => {
    const { userInfo } = useContext(AuthGuardContext)
    const [editRowIndex, setEditRowIndex] = useState<number | null>(null)
    const [editColumn, setEditColumn] = useState<string | null>(null)
    const [editValue, setEditValue] = useState<any>('')
    const [isUpdated, setIsUpdated] = useState<boolean>(true)
    const { updateManualInputMutationFn } = useManualInputKpiMutation()
    const { checkPermissionsFromRoutes } = useAuthGuard()
    const [alertData, setAlertData] = useState<AlertData>({
        isOpen: false,
        message: '',
        severity: 'error',
        autoHideDuration: 6000,
    })

    const listMonths = [
        'JANUARY',
        'FEBRUARY',
        'MARCH',
        'APRIL',
        'MAY',
        'JUNE',
        'JULY',
        'AUGUST',
        'SEPTEMBER',
        'OCTOBER',
        'NOVEMBER',
        'DECEMBER',
    ]

    const handleCellClick = (index: number, column: string, value: number) => {
        if (!checkPermissionsFromRoutes('editManualInput')) return
        setEditRowIndex(index)
        setEditColumn(column)
        setEditValue(value)
    }

    const handleInputChange = (event: any) => {
        const newValue = event.target.value

        if (newValue === '') setEditValue(null)

        if (/^[0-9]*$/.test(newValue)) {
            setEditValue(Number(event.target.value))
        }
    }

    const handleKeyPress = (event: any, index: number, month: string) => {
        if (event.key === 'Enter') {
            handleInputBlur(index, month)
        }
    }

    const handleInputBlur = (index: number, month: string) => {
        if (editRowIndex !== null && editColumn !== null) {
            switch (month) {
                case 'january':
                    manualInputData[index].data.january = editValue
                    break
                case 'february':
                    manualInputData[index].data.february = editValue
                    break
                case 'march':
                    manualInputData[index].data.march = editValue
                    break
                case 'april':
                    manualInputData[index].data.april = editValue
                    break
                case 'may':
                    manualInputData[index].data.may = editValue
                    break
                case 'june':
                    manualInputData[index].data.june = editValue
                    break
                case 'july':
                    manualInputData[index].data.july = editValue
                    break
                case 'august':
                    manualInputData[index].data.august = editValue
                    break
                case 'september':
                    manualInputData[index].data.september = editValue
                    break
                case 'october':
                    manualInputData[index].data.october = editValue
                    break
                case 'november':
                    manualInputData[index].data.november = editValue
                    break
                case 'december':
                    manualInputData[index].data.december = editValue
                    break
            }
            setEditRowIndex(null)
            setEditColumn(null)
            setEditValue('')
            setIsUpdated(false)
        }
    }

    const generateDto = (kpi: any, value: number): any => {
        const result = {
            externalId: kpi[0].externalId,
            space: kpi[0].space,
            description: kpi[0].description,
            endDate: kpi[0].endDate,
            kpiValue: value,
            refKPICatogory: {
                space: 'GKPI-COR-ALL-DAT',
                externalId: kpi[0].refKPICatogory.externalId,
                name: kpi[0].refKPICatogory.name,
            },
            refSite: {
                space: 'REF-COR-ALL-DAT',
                externalId: kpi[0].refSite.externalId,
            },
            refUser: {
                space: 'UMG-COR-ALL-DAT',
                externalId: userInfo.email,
                email: userInfo.email,
            },
            startDate: kpi[0].startDate,
            period: kpi[0].period ?? 'Monthly',
            inputType: kpi[0].inputType ?? 'Manual',
        }

        return result
    }

    const getExternalId = (monthIndex: number, data: any): string => {
        const prefix = (): string => {
            if (data.description === 'Flawless days') return `A${(monthIndex + 1).toString().padStart(4, '0')}`
            if (data.description === 'Celanese Employee Headcount')
                return `B000${(monthIndex + 1).toString().padStart(4, '0')}`

            return `C000${(monthIndex + 1).toString().padStart(4, '0')}`
        }
        const date = dayjs().format('MMDDYY')

        return `GKPI-${date}-${prefix()}`
    }

    const getDate = (monthIndex: number, year: string, isStartDate: boolean): string => {
        const currentYear = year

        if (isStartDate) {
            const result = `${currentYear}-${monthIndex + 1}-1`
            return dayjs(result).format('YYYY-MM-DD')
        } else {
            const result = `${currentYear}-${monthIndex + 2}-0`
            return dayjs(result).format('YYYY-MM-DD')
        }
    }

    const generateEmptyDto = (monthIndex: number, value: number, data: any, year: string): any => {
        const result = {
            externalId: getExternalId(monthIndex, data),
            space: `GKPI-${data.name}-ALL-DAT`,
            description: data.description,
            endDate: getDate(monthIndex, year, false),
            kpiValue: value,
            refKPICatogory: {
                space: 'GKPI-COR-ALL-DAT',
                externalId: 'KPCA-091723-A0001',
                name: 'Foundational',
            },
            refSite: {
                space: 'REF-COR-ALL-DAT',
                externalId: data.name,
            },
            refUser: {
                space: 'UMG-COR-ALL-DAT',
                externalId: userInfo.email,
                email: userInfo.email,
            },
            startDate: getDate(monthIndex, year, true),
            period: 'Monthly',
            inputType: 'Manual',
        }
        return result
    }

    const mapManualInputChanges = async (): Promise<any> => {
        const mappedResult: any = []
        let siteCode: string = 'ERR'

        manualInputData.forEach((item: any) => {
            listMonths.forEach((monthName, index) => {
                const month = monthName.toLowerCase()
                if (item.data[month] !== undefined) {
                    let year: string = ''
                    siteCode = item.data.name.slice(-3) ?? siteCode
                    year = dayjs(item.kpi[0].startDate).year().toString()
                    const matchObject = item.kpi.filter((item: { startDate: string | undefined }) => {
                        const date = dayjs(item.startDate).month()
                        return date === index
                    })
                    if (matchObject && matchObject.length == 1) {
                        const partialResult = generateDto(matchObject, item.data[month])
                        mappedResult.push(partialResult)
                    } else {
                        const partialEmptyResult = generateEmptyDto(index, item.data[month], item.data, year)
                        mappedResult.push(partialEmptyResult)
                    }
                }
            })
        })

        if (mappedResult && mappedResult.length > 0) {
            const mutationKpiTargetResult = await updateManualInputMutationFn(mappedResult, `GKPI-${siteCode}-ALL-DAT`)
            if (mutationKpiTargetResult.ok) {
                setAlertData({
                    isOpen: true,
                    message: 'Success',
                    severity: 'success',
                    autoHideDuration: 6000,
                })
                refetchData()
            }
        }
    }

    useEffect(() => {
        if (loading === false && manualInputData.length > 0) {
            setIsUpdated(true)
        }
    }, [loading, manualInputData.length])

    const MonthTableCell = (row: any, index: any, month: string) => {
        return (
            <>
                <TableCell
                    align="center"
                    onClick={() => handleCellClick(index, month, row.data[month])}
                    sx={{
                        width: '5%',
                        border: 'none',
                        padding: '0px 10px !important',
                    }}
                >
                    {editRowIndex == index && editColumn === month ? (
                        <TextField
                            className="text-field"
                            id="outlined-number"
                            value={editValue}
                            type="outline"
                            onChange={handleInputChange}
                            onKeyDown={(event) => handleKeyPress(event, index, month)}
                            onBlur={() => handleInputBlur(index, month)}
                            autoFocus
                            inputProps={{ maxLength: 6 }}
                        />
                    ) : (
                        <Box className="table-cell">{row.data[month]}</Box>
                    )}
                </TableCell>
            </>
        )
    }

    return (
        <>
            <Toastr
                alertData={alertData}
                onClose={() => {
                    setAlertData({ ...alertData, isOpen: false })
                }}
            />
            <TableContainer sx={{ overflow: 'auto', maxHeight: 'calc(70vh - 50px)' }}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell align="left" sx={{ width: '10%' }}>
                                {translate('TABLE_COLS.CATEGORY')}
                            </TableCell>
                            <TableCell align="left" sx={{ width: '10%' }}>
                                {translate('TABLE_COLS.NAME')}
                            </TableCell>
                            {Array.from({ length: 12 }).map((_, index) => (
                                <TableCell key={index} align="center" sx={{ minWidth: '5%', padding: '0px 3px' }}>
                                    {translate(`TABLE_COLS.MONTHS.${listMonths[index]}`)}
                                </TableCell>
                            ))}
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {loading
                            ? LoaderCircular()
                            : manualInputData &&
                              manualInputData.length > 0 &&
                              manualInputData.map((row, index) => {
                                  return (
                                      <>
                                          <TableRow key={index}>
                                              <TableCell align="left" sx={{ width: '7%', border: 'none' }}>
                                                  {row.data.category}
                                              </TableCell>
                                              <TableCell align="left" sx={{ width: '13%', border: 'none' }}>
                                                  {row.data.description}
                                              </TableCell>
                                              {MonthTableCell(row, index, 'january')}
                                              {MonthTableCell(row, index, 'february')}
                                              {MonthTableCell(row, index, 'march')}
                                              {MonthTableCell(row, index, 'april')}
                                              {MonthTableCell(row, index, 'may')}
                                              {MonthTableCell(row, index, 'june')}
                                              {MonthTableCell(row, index, 'july')}
                                              {MonthTableCell(row, index, 'august')}
                                              {MonthTableCell(row, index, 'september')}
                                              {MonthTableCell(row, index, 'october')}
                                              {MonthTableCell(row, index, 'november')}
                                              {MonthTableCell(row, index, 'december')}
                                          </TableRow>
                                      </>
                                  )
                              })}
                    </TableBody>
                </Table>
            </TableContainer>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '15px' }}>
                <ClnButton
                    variant="contained"
                    label={translate('COMMONBUTTON.SAVE')}
                    type="button"
                    disabled={isUpdated}
                    onClick={mapManualInputChanges}
                />
            </Box>
        </>
    )
}
export default ManualInputKPITable
