import { gql } from '@apollo/client'
import { useGraphqlQuery } from './useGraphqlQuery'
import { getLocalUserSite } from '../utils'
import { useEffect, useState } from 'react'
import { ShiftConfiguration } from '../models/shiftConfiguration'

export type UseShiftRequest = {
    reportingSite: string
    reportingUnit?: string
}

interface ShiftRequest {
    reportingUnit?: string
}

const buildShiftUsersQuery = (request: UseShiftRequest): string => {
    const filter = []
    let queryFilter = '{ }'

    filter.push(`{ space: { eq: "SHI-${request.reportingSite}-ALL-DAT" }}`)

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
      query GetShifts {
        listShiftConfiguration(
            filter: ${queryFilter}
            ) {
            items {
                externalId
                space
                shiftName
                refSite {
                    externalId
                }
                refUnit {
                    externalId
                    description
                }
            }
        }
    }
    `

    return query
}

export const useShift = (shiftRequest: ShiftRequest) => {
    const request: UseShiftRequest = {
        reportingSite: getLocalUserSite()?.siteCode!,
        reportingUnit: shiftRequest.reportingUnit,
    }

    const query = buildShiftUsersQuery(request)
    const { data: fdmData } = useGraphqlQuery<ShiftConfiguration>(gql(query), 'listShiftConfiguration', {})

    const [resultData, setResultData] = useState<{ data: ShiftConfiguration[]; loading: boolean }>({
        data: [],
        loading: true,
    })
    useEffect(() => {
        if (fdmData.length === 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        data: resultData.data,
    }
}
