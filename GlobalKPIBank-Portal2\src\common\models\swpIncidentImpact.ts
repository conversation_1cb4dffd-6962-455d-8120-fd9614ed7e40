import { ExternalEntity } from '.'
import { IncidentCategory } from './incidentCategory'
import { Site } from './site'
import { Unit } from './unit'

export interface SwpIncidentImpactKpi extends ExternalEntity {
    reportingSite: Site
    reportingUnit: Unit
    name: string
    description: string
    incidentParentId: number
    category: IncidentCategory
    isHighPotential: boolean
    reportedTime: Date
}
