import { useEffect, useState } from 'react'
import { useQueryResultsFunction } from '../general-functions/useQueryResultsFunction'
import { EntityType, GetSpace } from '@/common/utils/space-util'
import { getLocalUserSite } from '@/common/utils'
import { Dayjs } from 'dayjs'

export interface ChecklistKPIQueryRequest {
    checklistStatus?: string[]
    startDate?: Dayjs
    endDate?: Dayjs
    overdue?: boolean
    title?: string
    units?: string[]
    discipline?: string[]
    location?: string[]
    shift: string[]
    configs: string[]
}

const buildChecklistKPIQuery = (request: ChecklistKPIQueryRequest): any => {
    const mapFilters = (items: string[] | undefined, space: string) => {
        return items?.map((item) => ({
            externalId: item,
            space,
        }))
    }

    const configsFilter = request.configs.length > 0 ? request.configs : undefined

    const query = {
        checklists: {
            nodes: {
                filter: {
                    and: [
                        {
                            or: [
                                {
                                    equals: {
                                        property: ['node', 'space'],
                                        value: GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode),
                                    },
                                },
                            ],
                        },
                        {
                            or: [
                                {
                                    range: {
                                        property: ['cdf_apm', 'Checklist/v4', 'startTime'],
                                        gte: request.startDate,
                                        lte: request.endDate,
                                    },
                                },
                                {
                                    range: {
                                        property: ['cdf_apm', 'Checklist/v4', 'endTime'],
                                        gte: request.startDate,
                                        lte: request.endDate,
                                    },
                                },
                            ],
                        },
                        {
                            and: [
                                {
                                    equals: {
                                        property: ['cdf_apm', 'Checklist/v4', 'status'],
                                        value: 'Done',
                                    },
                                },
                            ],
                        },
                        ...(configsFilter
                            ? [
                                  {
                                      in: {
                                          property: ['cdf_apm', 'Checklist/v4', 'sourceId'],
                                          values: configsFilter.map((item) => item.replace('CTC-', '')),
                                      },
                                  },
                              ]
                            : []),
                    ],
                },
                chainTo: 'destination',
                direction: 'outwards',
            },
            limit: 10000,
        },
    }

    return query
}

export const useChecklistItemsKpis = (request: ChecklistKPIQueryRequest) => {
    const unitsByFilter = request.units && request.units?.length > 0 ? true : false
    const locationByFilter = request.location && request.location?.length > 0 ? true : false
    const disciplineByFilter = request.discipline && request.discipline?.length > 0 ? true : false
    const shiftByFilter = request.shift.length > 0 ? true : false

    const [filter, setFilter] = useState<any>()
    const [resultData, setResultData] = useState<{ data: number; loading: boolean }>({
        data: 0,
        loading: true,
    })

    const select = {
        checklists: {
            sources: [
                {
                    source: {
                        space: 'cdf_apm',
                        externalId: 'Checklist',
                        version: 'v4',
                        type: 'view',
                    },
                    properties: ['*'],
                },
            ],
            limit: 10000,
        },
    }

    const cursors = {
        checklist_items: null,
        checklist_to_items: null,
        checklists: null,
    }

    const { getAllResults: getAllData } = useQueryResultsFunction<any>(select, cursors)

    useEffect(() => {
        setFilter(buildChecklistKPIQuery(request))
    }, [request])

    useEffect(() => {
        filter &&
            getAllData(filter)
                .then((res) => {
                    if (
                        res.items.checklists.length === 0 ||
                        (request.configs.length === 0 &&
                            (unitsByFilter || locationByFilter || disciplineByFilter || shiftByFilter))
                    ) {
                        setResultData({ data: 0, loading: false })
                    } else {
                        setResultData({ data: res.items.checklists, loading: false })
                    }
                })
                .catch((e) => console.log('error', e, 'filter', filter))
    }, [filter])

    return {
        loading: resultData.loading,
        checklistResult: resultData.data,
    }
}
