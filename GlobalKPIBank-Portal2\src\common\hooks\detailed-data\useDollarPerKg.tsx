import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import { DollarPerKgTableView } from '@/common/models/dollarPerKgTable'
import { DollarPerKgKpi } from '@/common/models/dollarPerKg'
import { getCustomMonthAndYearQuery } from '@/common/utils/kpis-data-table-export'

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let businessSegmentQ = ''
    let dateQ: string = ''
    let cursorQ: string = ''
    let limitQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{reportingSite: {externalId: {in: [${sites}]}}},`
    }

    if (request && request.kpiFilters.businessSegment !== '') {
        businessSegmentQ = `{businessSegment: {externalId: {eq: "${request.kpiFilters.businessSegment}"}}},`
    }

    if (request && request.kpiFilters.date !== '') {
        dateQ = getCustomMonthAndYearQuery(request.exportFilters.range, request.kpiFilters.date, 'month', false)
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listDollarPerKG (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${businessSegmentQ} ${dateQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                reportingSite {
                    externalId
                    name
                }
                pid
                pidDesc
                actualPlantPeriodCost
                actualEnergyCost
                actualProductionVolume
                forecastPlantPeriodCost
                forecastEnergyCost
                forecastProductionVolume
                month
                year
                businessSegment {
                    externalId
                    description
                }
            }
        }
    }`
}

const mapDollarPerKg = (data: DollarPerKgKpi[]): DollarPerKgTableView[] => {
    const mappedResult: DollarPerKgTableView[] = []

    if (data && data.length > 0) {
        data.map((item) => {
            const result: DollarPerKgTableView = {
                siteName: item.reportingSite?.name ?? '',
                pid: item.pid ?? '',
                pidDescription: item.pidDesc ?? '',
                actualPlantPeriodCost: item.actualPlantPeriodCost,
                actualEnergyCost: item.actualEnergyCost,
                actualProductionVolume: item.actualProductionVolume,
                forecastPlantPeriodCost: item.forecastPlantPeriodCost,
                forecastEnergyCost: item.forecastEnergyCost,
                forecastProductionVolume: item.forecastProductionVolume,
                month: item.month ?? '',
                year: item.year,
                businessSegment: item.businessSegment?.description ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useDollarPerKg = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<DollarPerKgKpi>(gql(query), 'listDollarPerKG', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'executiveView',
        },
    })

    const [resultData, setResultData] = useState<{ data: DollarPerKgTableView[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapDollarPerKg(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapDollarPerKg(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
