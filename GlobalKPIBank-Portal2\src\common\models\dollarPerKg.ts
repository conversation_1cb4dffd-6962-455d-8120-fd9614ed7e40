import { ExternalEntity } from '.'
import { BusinessSegment } from './business-segment'
import { Site } from './site'

export interface DollarPerKgKpi extends ExternalEntity {
    reportingSite: Site
    pid: string
    pidDesc: string
    actualPlantPeriodCost: number | undefined
    actualEnergyCost: number | undefined
    actualProductionVolume: number | undefined
    forecastPlantPeriodCost: number | undefined
    forecastEnergyCost: number | undefined
    forecastProductionVolume: number | undefined
    month: string
    year: number | undefined
    businessSegment: BusinessSegment | undefined
}
