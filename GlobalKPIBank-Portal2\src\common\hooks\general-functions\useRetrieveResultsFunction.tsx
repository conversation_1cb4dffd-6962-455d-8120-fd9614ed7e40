import { useCallback } from 'react'
import { useFdmRetrieveDatamodel } from '../cognite/useFdmRetrieveDatamodel'

export function useRetrieveResultsFunction<T>(externalId: string) {
    const [retrieveFunctionFdm] = useFdmRetrieveDatamodel()
    return {
        getAllResults: useCallback(async (): Promise<string> => {
            const res = await retrieveFunctionFdm({ entityName: externalId })
            return res
        }, [externalId]),
    }
}
