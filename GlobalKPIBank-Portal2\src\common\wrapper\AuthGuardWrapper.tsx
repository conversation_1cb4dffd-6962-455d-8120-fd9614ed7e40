import React, { ReactNode } from 'react'
import { useAuthGuard } from '../hooks/useAuthGuard'
import { translate } from '@celanese/celanese-sdk'
import ErrorScreen from '../../components/ErrorScreen'
import { FullLoaderCircular } from '../../components/Loader'

interface AuthGuardWrapperProps {
    children: ReactNode
    componentName?: string
}

const AuthGuardWrapper: React.FC<AuthGuardWrapperProps> = ({ children, componentName }) => {
    const { checkPermissionsFromComponents, loading } = useAuthGuard()
    const nameToCheck = componentName || 'UnknownComponent'
    const userPermission = checkPermissionsFromComponents(nameToCheck)

    let permissionResult = {
        isAuthorized: userPermission.isAuthorized,
        message: !userPermission.message ? '' : translate(userPermission.message),
    }

    if (componentName) {
        if (userPermission !== undefined) {
            if (!userPermission.isAuthorized) {
                permissionResult = {
                    isAuthorized: userPermission.isAuthorized,
                    message: userPermission.message ? translate(userPermission.message) : '',
                }
            } else {
                permissionResult = {
                    isAuthorized: userPermission.isAuthorized,
                    message: '',
                }
            }
        } else {
            permissionResult = {
                isAuthorized: true,
                message: '',
            }
        }
    }

    return loading ? FullLoaderCircular(loading) : permissionResult?.isAuthorized ? children : <ErrorScreen />
}

export default AuthGuardWrapper
