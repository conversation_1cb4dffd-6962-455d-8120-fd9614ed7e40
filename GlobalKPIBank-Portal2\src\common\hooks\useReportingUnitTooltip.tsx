import { gql, useApolloClient } from '@apollo/client'
import { useState, useEffect, useMemo, useCallback } from 'react'
import { usePathname } from 'next/navigation'
import dayjs from 'dayjs'
import { numberFormat } from '../utils/numberFormat'

const firstDayCurrentYearEpoch = dayjs().startOf('year').valueOf()

const queryBuilder = (
    reportingSiteId: string,
    kpiCode: string,
    cursor?: string,
    businessLevel: string = 'STS',
    businessSegments?: string[]
) => {
    let cursorQ: string = ''
    if (cursor) {
        cursorQ = `after: "${cursor}"`
    }

    const filterConditions = [
        `{kpi: {code: {eq: "${kpiCode}"}}}`,
        `{reportingSite: {siteCode: {eq: "${reportingSiteId}"}}}`,
        `{businessLevel: {code: {eq: "${businessLevel}"}}}`,
        `{timeGranularity: {code:{eq: "M<PERSON>"}}}`,
    ]

    if (businessSegments && businessSegments.length > 0) {
        if (businessSegments.length === 1) {
            filterConditions.push(`{businessSegment: {externalId: {eq: "${businessSegments[0]}"}}}`)
        } else {
            const segmentList = businessSegments.map((segment) => `"${segment}"`).join(', ')
            filterConditions.push(`{businessSegment: {externalId: {in: [${segmentList}]}}}`)
        }
    }

    return `{
  listKpiResult(
    first: 100
    ${cursorQ}
    filter: {
      and: [
        ${filterConditions.join(',\n        ')}
      ]
    }
  ) {
    items {
      kpi {
        symbol
        code
        name
      }
      value {
        getDataPoints(start:${firstDayCurrentYearEpoch}) {
          items {
            value
            timestamp
          }
        }
      }
      parameterValues {
        items {
          reportingUnit {
            externalId
            name
            description
          }
          parameter {
            name
            code
          }
        }
      }
      reportingSite {
        siteCode
        name
      }
      businessLevel {
        code
        name
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}`
}

interface UnitList {
    unitDescription: string
    value: any
}

interface UnitValue {
    unitDescription: string
    rawValue: number
}

export const useReportingUnitTooltip = (reportingSiteId?: string, kpiCode?: string, month?: string) => {
    const [allData, setAllData] = useState<any[]>([])
    const [loadingAll, setLoadingAll] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const apolloClient = useApolloClient()
    const pathname = usePathname()

    const isGlobalView = pathname === '/global-view'

    const [businessSegmentFilter, setBusinessSegmentFilter] = useState<string[]>([])

    const readBusinessSegmentFromStorage = useCallback(() => {
        if (isGlobalView) {
            try {
                const globalFiltersString = localStorage.getItem('filters')
                if (globalFiltersString) {
                    const globalFilters = JSON.parse(globalFiltersString)
                    if (globalFilters?.businessSegment?.externalId) {
                        return [globalFilters.businessSegment.externalId]
                    }
                }
            } catch (error) {}
            return []
        }

        try {
            const homeFiltersString = localStorage.getItem('homeFilters')
            if (homeFiltersString) {
                const homeFilters = JSON.parse(homeFiltersString)
                if (homeFilters?.businessSegment?.length) {
                    return homeFilters.businessSegment.map((segment: any) => segment.externalId)
                }
            }
        } catch (error) {}
        return []
    }, [isGlobalView])

    useEffect(() => {
        if (isGlobalView) {
            setBusinessSegmentFilter([])
            return
        }

        const updateBusinessSegment = () => {
            setTimeout(() => {
                const newFilters = readBusinessSegmentFromStorage()
                setBusinessSegmentFilter(newFilters)
            }, 200)
        }

        const initialFilters = readBusinessSegmentFromStorage()
        setBusinessSegmentFilter(initialFilters)

        window.addEventListener('storage', updateBusinessSegment)

        window.addEventListener('homeFiltersChanged', updateBusinessSegment)

        return () => {
            window.removeEventListener('storage', updateBusinessSegment)
            window.removeEventListener('homeFiltersChanged', updateBusinessSegment)
        }
    }, [readBusinessSegmentFromStorage, isGlobalView])

    const formattedReportingSiteId = useMemo(
        () => (reportingSiteId ? reportingSiteId.replace(/^STS-/, '') : ''),
        [reportingSiteId]
    )

    const formattedKpiCode = useMemo(() => (kpiCode ? kpiCode.replace(/^KPIG-/, '') : ''), [kpiCode])

    const [resultData, setResultData] = useState<{ data: UnitList[]; loading: boolean }>({
        data: [],
        loading: true,
    })
    const [total, setTotal] = useState<string | undefined>(undefined)

    const fetchData = useCallback(async () => {
        if (!reportingSiteId || !kpiCode) {
            setAllData([])
            setLoadingAll(false)
            setError(null)
            return
        }

        let isMounted = true
        const abortController = new AbortController()

        async function fetchAllPages(
            cursor?: string,
            accumulated: any[] = [],
            businessLevel: string = 'STS'
        ): Promise<any[]> {
            try {
                if (abortController.signal.aborted) {
                    throw new Error('Request was aborted')
                }

                if (!isMounted) return accumulated

                setLoadingAll(true)
                setError(null)

                const queryString = queryBuilder(
                    formattedReportingSiteId,
                    formattedKpiCode,
                    cursor,
                    businessLevel,
                    isGlobalView ? [] : businessSegmentFilter
                )
                const queryGql = gql(queryString)
                const response = await apolloClient.query({
                    query: queryGql,
                    fetchPolicy: 'network-only',
                    context: {
                        clientName: 'kpidomView',
                        signal: abortController.signal,
                    },
                })

                if (abortController.signal.aborted || !isMounted) {
                    return accumulated
                }

                const data = response.data?.listKpiResult
                let items: any[] = []
                let nextCursor: string | undefined = undefined
                let hasNext: boolean | undefined = undefined

                if (Array.isArray(data)) {
                    items = data
                } else if (data && typeof data === 'object') {
                    items = data.items ?? []
                    nextCursor = data.pageInfo?.endCursor
                    hasNext = data.pageInfo?.hasNextPage
                }

                const newAccumulated = [...accumulated, ...items]

                if (hasNext && nextCursor && !abortController.signal.aborted && isMounted) {
                    return fetchAllPages(nextCursor, newAccumulated, businessLevel)
                }

                return newAccumulated
            } catch (err) {
                if (!abortController.signal.aborted && isMounted) {
                    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
                    setError(errorMessage)
                }
                return accumulated
            }
        }

        try {
            const businessLevelsToTry = ['STS', 'UNT', 'PLT', 'ARE', 'SEC']
            let result: any[] = []
            let foundValidUnits = false

            for (const businessLevel of businessLevelsToTry) {
                result = await fetchAllPages(undefined, [], businessLevel)

                const hasValidUnits = result.some((item) => item?.parameterValues?.items?.[0]?.reportingUnit !== null)

                if (hasValidUnits) {
                    foundValidUnits = true
                    break
                }

                if (result.length === 0) {
                    continue
                }
            }

            if (isMounted && !abortController.signal.aborted) {
                setAllData(result)
                setLoadingAll(false)
            }
        } catch (err) {
            if (isMounted && !abortController.signal.aborted) {
                const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
                setError(errorMessage)
                setLoadingAll(false)
            }
        }

        return () => {
            isMounted = false
            abortController.abort()
        }
    }, [
        reportingSiteId,
        kpiCode,
        formattedReportingSiteId,
        formattedKpiCode,
        apolloClient,
        businessSegmentFilter,
        isGlobalView,
    ])

    useEffect(() => {
        fetchData()
    }, [fetchData])

    useEffect(() => {
        if (allData === undefined) {
            setResultData((prevState) => ({ ...prevState, loading: true }))
        } else {
            const kpiSymbol = allData[0]?.kpi?.symbol ?? ''
            const siteName = allData[0]?.reportingSite?.name ?? 'Site'
            const businessLevelName = allData[0]?.businessLevel?.name ?? 'Unit'
            

            
            const deduplicatedData = allData.reduce((acc, item) => {
                const reportingUnitId = item?.parameterValues?.items?.[0]?.reportingUnit?.externalId
                const existingItem = acc.find(existingItem => 
                    existingItem?.parameterValues?.items?.[0]?.reportingUnit?.externalId === reportingUnitId
                )
                
                if (!existingItem && reportingUnitId) {
                    acc.push(item)
                }
                return acc
            }, [] as typeof allData)
            
            const unitValues = deduplicatedData
                .map((item, index) => {
                    const parameterValue = item?.parameterValues?.items?.[0]
                    const reportingUnit = parameterValue?.reportingUnit
                    const parameter = parameterValue?.parameter

                    let unitDescription = ''

                    if (reportingUnit) {
                        unitDescription = reportingUnit.description || reportingUnit.name || reportingUnit.externalId
                    }

                    if (!unitDescription && parameter) {
                        unitDescription = parameter.name || parameter.code
                    }

                    if (!unitDescription) {
                        unitDescription =
                            businessLevelName && businessLevelName !== 'Unit'
                                ? `${siteName} ${businessLevelName} ${index + 1}`
                                : `${siteName} Unit ${index + 1}`
                    }

                    const dataPoints = item.value?.getDataPoints?.items ?? []
                    const monthValueRaw = dataPoints.find((datapoints: { timestamp: string; value: number }) =>
                        datapoints.timestamp.startsWith(month)
                    )?.value ?? 0

                    return {
                        unitDescription,
                        rawValue: monthValueRaw as number,
                    }
                })
                .filter((item) => item.rawValue !== undefined && item.rawValue !== 0)

                        const groupedUnits = unitValues.reduce((acc, item) => {
                if (acc[item.unitDescription]) {
                    acc[item.unitDescription] += item.rawValue
                } else {
                    acc[item.unitDescription] = item.rawValue
                }
                return acc
            }, {} as Record<string, number>)

            const deduplicatedUnits = Object.entries(groupedUnits).map(([unitDescription, rawValue]) => ({
                unitDescription,
                rawValue,
            }))

            const totalSum = numberFormat(deduplicatedUnits.reduce((acc, item) => Number(acc) + Number(item.rawValue), 0))
            const totalSumWithSymbol = `${totalSum}${kpiSymbol}`
            setTotal(totalSumWithSymbol)

            const mappedUnitValues = deduplicatedUnits.map((item) => ({
                unitDescription: item.unitDescription,
                value: numberFormat(Number(Number(item.rawValue).toFixed(2))),
            }))

            const unitValuesWithSymbol = mappedUnitValues.map((item) => ({
                ...item,
                value: item.value !== undefined ? `${item.value}${kpiSymbol}` : undefined,
            }))

            setResultData({ data: unitValuesWithSymbol, loading: false })
        }
    }, [allData, month, reportingSiteId, kpiCode])

    return {
        loadingReportingUnits: loadingAll,
        refetchReportingUnits: fetchData,
        dataReportingUnits: resultData.data,
        totalSiteValue: total,
        error,
    }
}
