import { Template } from './template'
import { Unit } from './unit'
import { Role } from './role'
import { Entity } from '.'
import { NotificationConfig } from './notification-config'
import { ReportingLocation } from './reportingLocation'
import { ShiftConfiguration } from './shiftConfiguration'

export interface TemplateConfig extends Entity {
    space: string
    checklistNotificationConfigs: NotificationConfig[]
    reportingUnit: Unit | undefined
    reportingLocation: ReportingLocation | undefined
    assignedRoles: Role[]
    rolesToBeNotified: Role[]
    template: Template
    disciplines: string[]
    daysUntilDue: string
    shiftConfigurations: ShiftConfiguration[]
}
