import { ExternalEntity } from '.'
import { KpiGroup } from './kpiGroup'

export interface GlobalVisionKpi extends ExternalEntity {
    name: string
    description: string
    refKpiGroup: string
    order: number
    symbol: string
    lastUpdatedTime: Date
    createdTime: Date
    group: KpiGroup
    category?: {
        code: string
        name: string
        externalId: string
    }
}

export interface unitOfMeasurement extends ExternalEntity {
    symbol: string
}
