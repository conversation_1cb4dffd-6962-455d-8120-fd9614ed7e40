import { ExternalEntity } from '.'
import { BusinessSegment } from './business-segment'
import { Site } from './site'

export interface ProductivityKpi extends ExternalEntity {
    reportingSite: Site
    projectName: string
    projectNumber: string
    projectStatus: string
    year: number | undefined
    contributionType: string
    functionCategory: string
    projectType: string
    annualAmount: number | undefined
    janAmount: number | undefined
    febAmount: number | undefined
    marAmount: number | undefined
    aprAmount: number | undefined
    mayAmount: number | undefined
    junAmount: number | undefined
    julAmount: number | undefined
    augAmount: number | undefined
    sepAmount: number | undefined
    octAmount: number | undefined
    novAmount: number | undefined
    decAmount: number | undefined
    businessSegment: BusinessSegment | null
}
