import { Box, Typography } from '@mui/material'
import { translate } from '@celanese/celanese-sdk'
import { ManualInputFilters } from '../ManualInputFilters'

const styles = {
    tabWrapper: {
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: '8px',
        width: 'auto',
        height: 'calc(100% - 44px)',
        padding: '30px',
    },
}

export const ManualInputPage = () => {
    return (
        <div>
            <Typography variant="h3" sx={{ color: '#083D5B', marginBottom: '1rem', fontWeight: 'bold' }}>
                {translate('MANUAL_INPUT.TITLE')}
            </Typography>
            <Box sx={styles.tabWrapper}>
                <ManualInputFilters />
            </Box>
        </div>
    )
}
