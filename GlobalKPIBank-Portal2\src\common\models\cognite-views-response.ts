export interface CogniteViewsResponseItem {
    externalId: string
    space: string
    name: string
    description: string
    version: string
    createdTime: string
    lastUpdatedTime: string
    writable: boolean
    usedFor: 'node' | 'edge' | 'all'
    isGlobal: boolean
    properties: any
    implements: any
    filter: any
}

export interface CogniteViewsResponse {
    items: CogniteViewsResponseItem[]
}
