import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import dayjs from 'dayjs'
import { IncidentKpi } from '@/common/models/incident'
import { IncidentTableView } from '@/common/models/incidentTable'
import { setExportDateRange } from '@/common/utils/kpis-data-table-export'

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let unitQ: string = ''
    let startDateQ: string = ''
    let endDateQ: string = ''
    let enablonTypeQ: string = ''
    let dataSourceQ: string = ''
    let limitQ: string = ''
    let cursorQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{reportingSite: {externalId: {in: [${sites}]}}},`
        unitQ = `{reportingUnit: {not: {externalId: {in: "UNT-${request.kpiFilters.site.replace('STS-', '')}GRN"}}}},`
    }

    if (request && request.kpiFilters.date !== '') {
        let sanitizedInitialDate = dayjs(request.kpiFilters.date).startOf('month').format('YYYY-MM-DD')
        let sanitizedEndDate = dayjs(request.kpiFilters.date).endOf('month').format('YYYY-MM-DD')
        const time = 'T00:00:00+00:00'

        if (request.exportFilters && request.exportFilters.isExport) {
            ;[sanitizedInitialDate, sanitizedEndDate] = setExportDateRange(
                request.exportFilters.range,
                request.kpiFilters.date
            )
        }

        startDateQ = `{reportedTime: {gte: "${sanitizedInitialDate}${time}"}},`
        endDateQ = `{reportedTime: {lte: "${sanitizedEndDate}${time}"}},`
    }

    if (request && request.kpiFilters.kpiName !== '') {
        if (request.kpiFilters.kpiName == 'Near Misses') {
            dataSourceQ = `{dataSource:{eq:"ENA"}}`
            enablonTypeQ = `{enablonType:{externalId:{eq:"EIT-NEM"}}}`
        }
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listIncident (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${unitQ} ${startDateQ} ${endDateQ} ${dataSourceQ} ${enablonTypeQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                externalId
                description
                incidentStartTime
                reportingSite {
                    externalId
                    name
                }
                reportingUnit {
                    name
                }
                enablonType {
                    name
                }
            }
        }
    }`
}

const mapIncident = (data: IncidentKpi[]): IncidentTableView[] => {
    const mappedResult: IncidentTableView[] = []
    if (data && data.length > 0) {
        data.map((item) => {
            const result: IncidentTableView = {
                externalId: item.externalId,
                description: item.description ?? '',
                incidentStartTime: item.incidentStartTime?.toString().slice(0, 10) ?? '',
                siteName: item.reportingSite?.name ?? '',
                unit: item.reportingUnit?.name ?? '',
                enablonType: item.enablonType?.name ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useIncident = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<IncidentKpi>(gql(query), 'listIncident', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'stewardshipView',
        },
    })

    const [resultData, setResultData] = useState<{ data: any[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapIncident(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapIncident(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
