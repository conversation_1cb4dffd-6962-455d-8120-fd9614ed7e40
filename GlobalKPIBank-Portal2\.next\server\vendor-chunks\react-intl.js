"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-intl";
exports.ids = ["vendor-chunks/react-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-intl/index.js":
/*!******************************************!*\
  !*** ./node_modules/react-intl/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FormattedListParts = exports.FormattedNumberParts = exports.FormattedTimeParts = exports.FormattedDateParts = exports.FormattedDisplayName = exports.FormattedList = exports.FormattedNumber = exports.FormattedTime = exports.FormattedDate = exports.ReactIntlError = exports.ReactIntlErrorCode = exports.MissingTranslationError = exports.MessageFormatError = exports.MissingDataError = exports.InvalidConfigError = exports.UnsupportedFormatterError = exports.createIntlCache = exports.useIntl = exports.injectIntl = exports.createIntl = exports.RawIntlProvider = exports.IntlProvider = exports.IntlContext = exports.FormattedRelativeTime = exports.FormattedPlural = exports.FormattedMessage = exports.FormattedDateTimeRange = void 0;\nexports.defineMessages = defineMessages;\nexports.defineMessage = defineMessage;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar createFormattedComponent_1 = __webpack_require__(/*! ./src/components/createFormattedComponent */ \"(ssr)/./node_modules/react-intl/src/components/createFormattedComponent.js\");\nvar injectIntl_1 = tslib_1.__importStar(__webpack_require__(/*! ./src/components/injectIntl */ \"(ssr)/./node_modules/react-intl/src/components/injectIntl.js\"));\nexports.injectIntl = injectIntl_1.default;\nObject.defineProperty(exports, \"RawIntlProvider\", ({ enumerable: true, get: function () { return injectIntl_1.Provider; } }));\nObject.defineProperty(exports, \"IntlContext\", ({ enumerable: true, get: function () { return injectIntl_1.Context; } }));\nvar useIntl_1 = tslib_1.__importDefault(__webpack_require__(/*! ./src/components/useIntl */ \"(ssr)/./node_modules/react-intl/src/components/useIntl.js\"));\nexports.useIntl = useIntl_1.default;\nvar provider_1 = tslib_1.__importDefault(__webpack_require__(/*! ./src/components/provider */ \"(ssr)/./node_modules/react-intl/src/components/provider.js\"));\nexports.IntlProvider = provider_1.default;\nvar createIntl_1 = __webpack_require__(/*! ./src/components/createIntl */ \"(ssr)/./node_modules/react-intl/src/components/createIntl.js\");\nObject.defineProperty(exports, \"createIntl\", ({ enumerable: true, get: function () { return createIntl_1.createIntl; } }));\nvar relative_1 = tslib_1.__importDefault(__webpack_require__(/*! ./src/components/relative */ \"(ssr)/./node_modules/react-intl/src/components/relative.js\"));\nexports.FormattedRelativeTime = relative_1.default;\nvar plural_1 = tslib_1.__importDefault(__webpack_require__(/*! ./src/components/plural */ \"(ssr)/./node_modules/react-intl/src/components/plural.js\"));\nexports.FormattedPlural = plural_1.default;\nvar message_1 = tslib_1.__importDefault(__webpack_require__(/*! ./src/components/message */ \"(ssr)/./node_modules/react-intl/src/components/message.js\"));\nexports.FormattedMessage = message_1.default;\nvar dateTimeRange_1 = tslib_1.__importDefault(__webpack_require__(/*! ./src/components/dateTimeRange */ \"(ssr)/./node_modules/react-intl/src/components/dateTimeRange.js\"));\nexports.FormattedDateTimeRange = dateTimeRange_1.default;\nvar intl_1 = __webpack_require__(/*! @formatjs/intl */ \"(ssr)/./node_modules/@formatjs/intl/index.js\");\nObject.defineProperty(exports, \"createIntlCache\", ({ enumerable: true, get: function () { return intl_1.createIntlCache; } }));\nObject.defineProperty(exports, \"UnsupportedFormatterError\", ({ enumerable: true, get: function () { return intl_1.UnsupportedFormatterError; } }));\nObject.defineProperty(exports, \"InvalidConfigError\", ({ enumerable: true, get: function () { return intl_1.InvalidConfigError; } }));\nObject.defineProperty(exports, \"MissingDataError\", ({ enumerable: true, get: function () { return intl_1.MissingDataError; } }));\nObject.defineProperty(exports, \"MessageFormatError\", ({ enumerable: true, get: function () { return intl_1.MessageFormatError; } }));\nObject.defineProperty(exports, \"MissingTranslationError\", ({ enumerable: true, get: function () { return intl_1.MissingTranslationError; } }));\nObject.defineProperty(exports, \"ReactIntlErrorCode\", ({ enumerable: true, get: function () { return intl_1.IntlErrorCode; } }));\nObject.defineProperty(exports, \"ReactIntlError\", ({ enumerable: true, get: function () { return intl_1.IntlError; } }));\nfunction defineMessages(msgs) {\n    return msgs;\n}\nfunction defineMessage(msg) {\n    return msg;\n}\n// IMPORTANT: Explicit here to prevent api-extractor from outputing `import('./src/types').CustomFormatConfig`\nexports.FormattedDate = (0, createFormattedComponent_1.createFormattedComponent)('formatDate');\nexports.FormattedTime = (0, createFormattedComponent_1.createFormattedComponent)('formatTime');\nexports.FormattedNumber = (0, createFormattedComponent_1.createFormattedComponent)('formatNumber');\nexports.FormattedList = (0, createFormattedComponent_1.createFormattedComponent)('formatList');\nexports.FormattedDisplayName = (0, createFormattedComponent_1.createFormattedComponent)('formatDisplayName');\nexports.FormattedDateParts = (0, createFormattedComponent_1.createFormattedDateTimePartsComponent)('formatDate');\nexports.FormattedTimeParts = (0, createFormattedComponent_1.createFormattedDateTimePartsComponent)('formatTime');\nvar createFormattedComponent_2 = __webpack_require__(/*! ./src/components/createFormattedComponent */ \"(ssr)/./node_modules/react-intl/src/components/createFormattedComponent.js\");\nObject.defineProperty(exports, \"FormattedNumberParts\", ({ enumerable: true, get: function () { return createFormattedComponent_2.FormattedNumberParts; } }));\nObject.defineProperty(exports, \"FormattedListParts\", ({ enumerable: true, get: function () { return createFormattedComponent_2.FormattedListParts; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/components/createFormattedComponent.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-intl/src/components/createFormattedComponent.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FormattedListParts = exports.FormattedNumberParts = void 0;\nexports.createFormattedDateTimePartsComponent = createFormattedDateTimePartsComponent;\nexports.createFormattedComponent = createFormattedComponent;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar useIntl_1 = tslib_1.__importDefault(__webpack_require__(/*! ./useIntl */ \"(ssr)/./node_modules/react-intl/src/components/useIntl.js\"));\nvar DisplayName;\n(function (DisplayName) {\n    DisplayName[\"formatDate\"] = \"FormattedDate\";\n    DisplayName[\"formatTime\"] = \"FormattedTime\";\n    DisplayName[\"formatNumber\"] = \"FormattedNumber\";\n    DisplayName[\"formatList\"] = \"FormattedList\";\n    // Note that this DisplayName is the locale display name, not to be confused with\n    // the name of the enum, which is for React component display name in dev tools.\n    DisplayName[\"formatDisplayName\"] = \"FormattedDisplayName\";\n})(DisplayName || (DisplayName = {}));\nvar DisplayNameParts;\n(function (DisplayNameParts) {\n    DisplayNameParts[\"formatDate\"] = \"FormattedDateParts\";\n    DisplayNameParts[\"formatTime\"] = \"FormattedTimeParts\";\n    DisplayNameParts[\"formatNumber\"] = \"FormattedNumberParts\";\n    DisplayNameParts[\"formatList\"] = \"FormattedListParts\";\n})(DisplayNameParts || (DisplayNameParts = {}));\nvar FormattedNumberParts = function (props) {\n    var intl = (0, useIntl_1.default)();\n    var value = props.value, children = props.children, formatProps = tslib_1.__rest(props, [\"value\", \"children\"]);\n    return children(intl.formatNumberToParts(value, formatProps));\n};\nexports.FormattedNumberParts = FormattedNumberParts;\nexports.FormattedNumberParts.displayName = 'FormattedNumberParts';\nvar FormattedListParts = function (props) {\n    var intl = (0, useIntl_1.default)();\n    var value = props.value, children = props.children, formatProps = tslib_1.__rest(props, [\"value\", \"children\"]);\n    return children(intl.formatListToParts(value, formatProps));\n};\nexports.FormattedListParts = FormattedListParts;\nexports.FormattedNumberParts.displayName = 'FormattedNumberParts';\nfunction createFormattedDateTimePartsComponent(name) {\n    var ComponentParts = function (props) {\n        var intl = (0, useIntl_1.default)();\n        var value = props.value, children = props.children, formatProps = tslib_1.__rest(props, [\"value\", \"children\"]);\n        var date = typeof value === 'string' ? new Date(value || 0) : value;\n        var formattedParts = name === 'formatDate'\n            ? intl.formatDateToParts(date, formatProps)\n            : intl.formatTimeToParts(date, formatProps);\n        return children(formattedParts);\n    };\n    ComponentParts.displayName = DisplayNameParts[name];\n    return ComponentParts;\n}\nfunction createFormattedComponent(name) {\n    var Component = function (props) {\n        var intl = (0, useIntl_1.default)();\n        var value = props.value, children = props.children, formatProps = tslib_1.__rest(props\n        // TODO: fix TS type definition for localeMatcher upstream\n        , [\"value\", \"children\"]);\n        // TODO: fix TS type definition for localeMatcher upstream\n        var formattedValue = intl[name](value, formatProps);\n        if (typeof children === 'function') {\n            return children(formattedValue);\n        }\n        var Text = intl.textComponent || React.Fragment;\n        return React.createElement(Text, null, formattedValue);\n    };\n    Component.displayName = DisplayName[name];\n    return Component;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/components/createFormattedComponent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/components/createIntl.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-intl/src/components/createIntl.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createIntl = void 0;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar intl_1 = __webpack_require__(/*! @formatjs/intl */ \"(ssr)/./node_modules/@formatjs/intl/index.js\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar utils_1 = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-intl/src/utils.js\");\nvar intl_messageformat_1 = __webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/index.js\");\nfunction assignUniqueKeysToFormatXMLElementFnArgument(values) {\n    if (!values) {\n        return values;\n    }\n    return Object.keys(values).reduce(function (acc, k) {\n        var v = values[k];\n        acc[k] = (0, intl_messageformat_1.isFormatXMLElementFn)(v)\n            ? (0, utils_1.assignUniqueKeysToParts)(v)\n            : v;\n        return acc;\n    }, {});\n}\nvar formatMessage = function (config, formatters, descriptor, rawValues) {\n    var rest = [];\n    for (var _i = 4; _i < arguments.length; _i++) {\n        rest[_i - 4] = arguments[_i];\n    }\n    var values = assignUniqueKeysToFormatXMLElementFnArgument(rawValues);\n    var chunks = intl_1.formatMessage.apply(void 0, tslib_1.__spreadArray([config,\n        formatters,\n        descriptor,\n        values], rest, false));\n    if (Array.isArray(chunks)) {\n        return React.Children.toArray(chunks);\n    }\n    return chunks;\n};\n/**\n * Create intl object\n * @param config intl config\n * @param cache cache for formatter instances to prevent memory leak\n */\nvar createIntl = function (_a, cache) {\n    var rawDefaultRichTextElements = _a.defaultRichTextElements, config = tslib_1.__rest(_a, [\"defaultRichTextElements\"]);\n    var defaultRichTextElements = assignUniqueKeysToFormatXMLElementFnArgument(rawDefaultRichTextElements);\n    var coreIntl = (0, intl_1.createIntl)(tslib_1.__assign(tslib_1.__assign(tslib_1.__assign({}, utils_1.DEFAULT_INTL_CONFIG), config), { defaultRichTextElements: defaultRichTextElements }), cache);\n    var resolvedConfig = {\n        locale: coreIntl.locale,\n        timeZone: coreIntl.timeZone,\n        fallbackOnEmptyString: coreIntl.fallbackOnEmptyString,\n        formats: coreIntl.formats,\n        defaultLocale: coreIntl.defaultLocale,\n        defaultFormats: coreIntl.defaultFormats,\n        messages: coreIntl.messages,\n        onError: coreIntl.onError,\n        defaultRichTextElements: defaultRichTextElements,\n    };\n    return tslib_1.__assign(tslib_1.__assign({}, coreIntl), { formatMessage: formatMessage.bind(null, resolvedConfig, \n        // @ts-expect-error fix this\n        coreIntl.formatters), \n        // @ts-expect-error fix this\n        $t: formatMessage.bind(null, resolvedConfig, coreIntl.formatters) });\n};\nexports.createIntl = createIntl;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/components/createIntl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/components/dateTimeRange.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-intl/src/components/dateTimeRange.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar useIntl_1 = tslib_1.__importDefault(__webpack_require__(/*! ./useIntl */ \"(ssr)/./node_modules/react-intl/src/components/useIntl.js\"));\nvar FormattedDateTimeRange = function (props) {\n    var intl = (0, useIntl_1.default)();\n    var from = props.from, to = props.to, children = props.children, formatProps = tslib_1.__rest(props, [\"from\", \"to\", \"children\"]);\n    var formattedValue = intl.formatDateTimeRange(from, to, formatProps);\n    if (typeof children === 'function') {\n        return children(formattedValue);\n    }\n    var Text = intl.textComponent || React.Fragment;\n    return React.createElement(Text, null, formattedValue);\n};\nFormattedDateTimeRange.displayName = 'FormattedDateTimeRange';\nexports[\"default\"] = FormattedDateTimeRange;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaW50bC9zcmMvY29tcG9uZW50cy9kYXRlVGltZVJhbmdlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWMsbUJBQU8sQ0FBQyx1REFBTztBQUM3QixpQ0FBaUMsbUJBQU8sQ0FBQyx3R0FBTztBQUNoRCx3Q0FBd0MsbUJBQU8sQ0FBQyw0RUFBVztBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nbG9iYWwta3BpLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1pbnRsL3NyYy9jb21wb25lbnRzL2RhdGVUaW1lUmFuZ2UuanM/NzUxOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbnZhciB0c2xpYl8xID0gcmVxdWlyZShcInRzbGliXCIpO1xudmFyIFJlYWN0ID0gdHNsaWJfMS5fX2ltcG9ydFN0YXIocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciB1c2VJbnRsXzEgPSB0c2xpYl8xLl9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiLi91c2VJbnRsXCIpKTtcbnZhciBGb3JtYXR0ZWREYXRlVGltZVJhbmdlID0gZnVuY3Rpb24gKHByb3BzKSB7XG4gICAgdmFyIGludGwgPSAoMCwgdXNlSW50bF8xLmRlZmF1bHQpKCk7XG4gICAgdmFyIGZyb20gPSBwcm9wcy5mcm9tLCB0byA9IHByb3BzLnRvLCBjaGlsZHJlbiA9IHByb3BzLmNoaWxkcmVuLCBmb3JtYXRQcm9wcyA9IHRzbGliXzEuX19yZXN0KHByb3BzLCBbXCJmcm9tXCIsIFwidG9cIiwgXCJjaGlsZHJlblwiXSk7XG4gICAgdmFyIGZvcm1hdHRlZFZhbHVlID0gaW50bC5mb3JtYXREYXRlVGltZVJhbmdlKGZyb20sIHRvLCBmb3JtYXRQcm9wcyk7XG4gICAgaWYgKHR5cGVvZiBjaGlsZHJlbiA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICByZXR1cm4gY2hpbGRyZW4oZm9ybWF0dGVkVmFsdWUpO1xuICAgIH1cbiAgICB2YXIgVGV4dCA9IGludGwudGV4dENvbXBvbmVudCB8fCBSZWFjdC5GcmFnbWVudDtcbiAgICByZXR1cm4gUmVhY3QuY3JlYXRlRWxlbWVudChUZXh0LCBudWxsLCBmb3JtYXR0ZWRWYWx1ZSk7XG59O1xuRm9ybWF0dGVkRGF0ZVRpbWVSYW5nZS5kaXNwbGF5TmFtZSA9ICdGb3JtYXR0ZWREYXRlVGltZVJhbmdlJztcbmV4cG9ydHMuZGVmYXVsdCA9IEZvcm1hdHRlZERhdGVUaW1lUmFuZ2U7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/components/dateTimeRange.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/components/injectIntl.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-intl/src/components/injectIntl.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Context = exports.Provider = void 0;\nexports[\"default\"] = injectIntl;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar hoist_non_react_statics_1 = tslib_1.__importDefault(__webpack_require__(/*! hoist-non-react-statics */ \"(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\"));\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar utils_1 = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-intl/src/utils.js\");\nfunction getDisplayName(Component) {\n    return Component.displayName || Component.name || 'Component';\n}\n// This is primarily dealing with packaging systems where multiple copies of react-intl\n// might exist\nvar IntlContext = typeof window !== 'undefined' && !window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__\n    ? window.__REACT_INTL_CONTEXT__ ||\n        (window.__REACT_INTL_CONTEXT__ = React.createContext(null))\n    : React.createContext(null);\nvar IntlConsumer = IntlContext.Consumer, IntlProvider = IntlContext.Provider;\nexports.Provider = IntlProvider;\nexports.Context = IntlContext;\nfunction injectIntl(WrappedComponent, options) {\n    var _a = options || {}, _b = _a.intlPropName, intlPropName = _b === void 0 ? 'intl' : _b, _c = _a.forwardRef, forwardRef = _c === void 0 ? false : _c, _d = _a.enforceContext, enforceContext = _d === void 0 ? true : _d;\n    var WithIntl = function (props) { return (React.createElement(IntlConsumer, null, function (intl) {\n        var _a;\n        if (enforceContext) {\n            (0, utils_1.invariantIntlContext)(intl);\n        }\n        var intlProp = (_a = {}, _a[intlPropName] = intl, _a);\n        return (React.createElement(WrappedComponent, tslib_1.__assign({}, props, intlProp, { ref: forwardRef ? props.forwardedRef : null })));\n    })); };\n    WithIntl.displayName = \"injectIntl(\".concat(getDisplayName(WrappedComponent), \")\");\n    WithIntl.WrappedComponent = WrappedComponent;\n    if (forwardRef) {\n        return (0, hoist_non_react_statics_1.default)(\n        // @ts-expect-error\n        React.forwardRef(function (props, ref) { return (React.createElement(WithIntl, tslib_1.__assign({}, props, { forwardedRef: ref }))); }), WrappedComponent);\n    }\n    return (0, hoist_non_react_statics_1.default)(WithIntl, WrappedComponent);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/components/injectIntl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/components/message.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-intl/src/components/message.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar utils_1 = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-intl/src/utils.js\");\nvar useIntl_1 = tslib_1.__importDefault(__webpack_require__(/*! ./useIntl */ \"(ssr)/./node_modules/react-intl/src/components/useIntl.js\"));\nfunction areEqual(prevProps, nextProps) {\n    var values = prevProps.values, otherProps = tslib_1.__rest(prevProps, [\"values\"]);\n    var nextValues = nextProps.values, nextOtherProps = tslib_1.__rest(nextProps, [\"values\"]);\n    return ((0, utils_1.shallowEqual)(nextValues, values) &&\n        (0, utils_1.shallowEqual)(otherProps, nextOtherProps));\n}\nfunction FormattedMessage(props) {\n    var intl = (0, useIntl_1.default)();\n    var formatMessage = intl.formatMessage, _a = intl.textComponent, Text = _a === void 0 ? React.Fragment : _a;\n    var id = props.id, description = props.description, defaultMessage = props.defaultMessage, values = props.values, children = props.children, _b = props.tagName, Component = _b === void 0 ? Text : _b, ignoreTag = props.ignoreTag;\n    var descriptor = { id: id, description: description, defaultMessage: defaultMessage };\n    var nodes = formatMessage(descriptor, values, {\n        ignoreTag: ignoreTag,\n    });\n    if (typeof children === 'function') {\n        return children(Array.isArray(nodes) ? nodes : [nodes]);\n    }\n    if (Component) {\n        return React.createElement(Component, null, React.Children.toArray(nodes));\n    }\n    return React.createElement(React.Fragment, null, nodes);\n}\nFormattedMessage.displayName = 'FormattedMessage';\nvar MemoizedFormattedMessage = React.memo(FormattedMessage, areEqual);\nMemoizedFormattedMessage.displayName = 'MemoizedFormattedMessage';\nexports[\"default\"] = MemoizedFormattedMessage;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/components/message.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/components/plural.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-intl/src/components/plural.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar useIntl_1 = tslib_1.__importDefault(__webpack_require__(/*! ./useIntl */ \"(ssr)/./node_modules/react-intl/src/components/useIntl.js\"));\nvar FormattedPlural = function (props) {\n    var _a = (0, useIntl_1.default)(), formatPlural = _a.formatPlural, Text = _a.textComponent;\n    var value = props.value, other = props.other, children = props.children;\n    var pluralCategory = formatPlural(value, props);\n    var formattedPlural = props[pluralCategory] || other;\n    if (typeof children === 'function') {\n        return children(formattedPlural);\n    }\n    if (Text) {\n        return React.createElement(Text, null, formattedPlural);\n    }\n    // Work around @types/react where React.FC cannot return string\n    return formattedPlural;\n};\nFormattedPlural.displayName = 'FormattedPlural';\nexports[\"default\"] = FormattedPlural;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/components/plural.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/components/provider.js":
/*!************************************************************!*\
  !*** ./node_modules/react-intl/src/components/provider.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar intl_1 = __webpack_require__(/*! @formatjs/intl */ \"(ssr)/./node_modules/@formatjs/intl/index.js\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar utils_1 = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-intl/src/utils.js\");\nvar injectIntl_1 = __webpack_require__(/*! ./injectIntl */ \"(ssr)/./node_modules/react-intl/src/components/injectIntl.js\");\nvar createIntl_1 = __webpack_require__(/*! ./createIntl */ \"(ssr)/./node_modules/react-intl/src/components/createIntl.js\");\nfunction processIntlConfig(config) {\n    return {\n        locale: config.locale,\n        timeZone: config.timeZone,\n        fallbackOnEmptyString: config.fallbackOnEmptyString,\n        formats: config.formats,\n        textComponent: config.textComponent,\n        messages: config.messages,\n        defaultLocale: config.defaultLocale,\n        defaultFormats: config.defaultFormats,\n        onError: config.onError,\n        onWarn: config.onWarn,\n        wrapRichTextChunksInFragment: config.wrapRichTextChunksInFragment,\n        defaultRichTextElements: config.defaultRichTextElements,\n    };\n}\nvar IntlProvider = /** @class */ (function (_super) {\n    tslib_1.__extends(IntlProvider, _super);\n    function IntlProvider() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.cache = (0, intl_1.createIntlCache)();\n        _this.state = {\n            cache: _this.cache,\n            intl: (0, createIntl_1.createIntl)(processIntlConfig(_this.props), _this.cache),\n            prevConfig: processIntlConfig(_this.props),\n        };\n        return _this;\n    }\n    IntlProvider.getDerivedStateFromProps = function (props, _a) {\n        var prevConfig = _a.prevConfig, cache = _a.cache;\n        var config = processIntlConfig(props);\n        if (!(0, utils_1.shallowEqual)(prevConfig, config)) {\n            return {\n                intl: (0, createIntl_1.createIntl)(config, cache),\n                prevConfig: config,\n            };\n        }\n        return null;\n    };\n    IntlProvider.prototype.render = function () {\n        (0, utils_1.invariantIntlContext)(this.state.intl);\n        return React.createElement(injectIntl_1.Provider, { value: this.state.intl }, this.props.children);\n    };\n    IntlProvider.displayName = 'IntlProvider';\n    IntlProvider.defaultProps = utils_1.DEFAULT_INTL_CONFIG;\n    return IntlProvider;\n}(React.PureComponent));\nexports[\"default\"] = IntlProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaW50bC9zcmMvY29tcG9uZW50cy9wcm92aWRlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsY0FBYyxtQkFBTyxDQUFDLHVEQUFPO0FBQzdCLGFBQWEsbUJBQU8sQ0FBQyxvRUFBZ0I7QUFDckMsaUNBQWlDLG1CQUFPLENBQUMsd0dBQU87QUFDaEQsY0FBYyxtQkFBTyxDQUFDLDhEQUFVO0FBQ2hDLG1CQUFtQixtQkFBTyxDQUFDLGtGQUFjO0FBQ3pDLG1CQUFtQixtQkFBTyxDQUFDLGtGQUFjO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsd0JBQXdCO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELGtCQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2xvYmFsLWtwaS1hcHAvLi9ub2RlX21vZHVsZXMvcmVhY3QtaW50bC9zcmMvY29tcG9uZW50cy9wcm92aWRlci5qcz82YTdjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCAyMDE1LCBZYWhvbyBJbmMuXG4gKiBDb3B5cmlnaHRzIGxpY2Vuc2VkIHVuZGVyIHRoZSBOZXcgQlNEIExpY2Vuc2UuXG4gKiBTZWUgdGhlIGFjY29tcGFueWluZyBMSUNFTlNFIGZpbGUgZm9yIHRlcm1zLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG52YXIgdHNsaWJfMSA9IHJlcXVpcmUoXCJ0c2xpYlwiKTtcbnZhciBpbnRsXzEgPSByZXF1aXJlKFwiQGZvcm1hdGpzL2ludGxcIik7XG52YXIgUmVhY3QgPSB0c2xpYl8xLl9faW1wb3J0U3RhcihyZXF1aXJlKFwicmVhY3RcIikpO1xudmFyIHV0aWxzXzEgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG52YXIgaW5qZWN0SW50bF8xID0gcmVxdWlyZShcIi4vaW5qZWN0SW50bFwiKTtcbnZhciBjcmVhdGVJbnRsXzEgPSByZXF1aXJlKFwiLi9jcmVhdGVJbnRsXCIpO1xuZnVuY3Rpb24gcHJvY2Vzc0ludGxDb25maWcoY29uZmlnKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbG9jYWxlOiBjb25maWcubG9jYWxlLFxuICAgICAgICB0aW1lWm9uZTogY29uZmlnLnRpbWVab25lLFxuICAgICAgICBmYWxsYmFja09uRW1wdHlTdHJpbmc6IGNvbmZpZy5mYWxsYmFja09uRW1wdHlTdHJpbmcsXG4gICAgICAgIGZvcm1hdHM6IGNvbmZpZy5mb3JtYXRzLFxuICAgICAgICB0ZXh0Q29tcG9uZW50OiBjb25maWcudGV4dENvbXBvbmVudCxcbiAgICAgICAgbWVzc2FnZXM6IGNvbmZpZy5tZXNzYWdlcyxcbiAgICAgICAgZGVmYXVsdExvY2FsZTogY29uZmlnLmRlZmF1bHRMb2NhbGUsXG4gICAgICAgIGRlZmF1bHRGb3JtYXRzOiBjb25maWcuZGVmYXVsdEZvcm1hdHMsXG4gICAgICAgIG9uRXJyb3I6IGNvbmZpZy5vbkVycm9yLFxuICAgICAgICBvbldhcm46IGNvbmZpZy5vbldhcm4sXG4gICAgICAgIHdyYXBSaWNoVGV4dENodW5rc0luRnJhZ21lbnQ6IGNvbmZpZy53cmFwUmljaFRleHRDaHVua3NJbkZyYWdtZW50LFxuICAgICAgICBkZWZhdWx0UmljaFRleHRFbGVtZW50czogY29uZmlnLmRlZmF1bHRSaWNoVGV4dEVsZW1lbnRzLFxuICAgIH07XG59XG52YXIgSW50bFByb3ZpZGVyID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKF9zdXBlcikge1xuICAgIHRzbGliXzEuX19leHRlbmRzKEludGxQcm92aWRlciwgX3N1cGVyKTtcbiAgICBmdW5jdGlvbiBJbnRsUHJvdmlkZXIoKSB7XG4gICAgICAgIHZhciBfdGhpcyA9IF9zdXBlciAhPT0gbnVsbCAmJiBfc3VwZXIuYXBwbHkodGhpcywgYXJndW1lbnRzKSB8fCB0aGlzO1xuICAgICAgICBfdGhpcy5jYWNoZSA9ICgwLCBpbnRsXzEuY3JlYXRlSW50bENhY2hlKSgpO1xuICAgICAgICBfdGhpcy5zdGF0ZSA9IHtcbiAgICAgICAgICAgIGNhY2hlOiBfdGhpcy5jYWNoZSxcbiAgICAgICAgICAgIGludGw6ICgwLCBjcmVhdGVJbnRsXzEuY3JlYXRlSW50bCkocHJvY2Vzc0ludGxDb25maWcoX3RoaXMucHJvcHMpLCBfdGhpcy5jYWNoZSksXG4gICAgICAgICAgICBwcmV2Q29uZmlnOiBwcm9jZXNzSW50bENvbmZpZyhfdGhpcy5wcm9wcyksXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiBfdGhpcztcbiAgICB9XG4gICAgSW50bFByb3ZpZGVyLmdldERlcml2ZWRTdGF0ZUZyb21Qcm9wcyA9IGZ1bmN0aW9uIChwcm9wcywgX2EpIHtcbiAgICAgICAgdmFyIHByZXZDb25maWcgPSBfYS5wcmV2Q29uZmlnLCBjYWNoZSA9IF9hLmNhY2hlO1xuICAgICAgICB2YXIgY29uZmlnID0gcHJvY2Vzc0ludGxDb25maWcocHJvcHMpO1xuICAgICAgICBpZiAoISgwLCB1dGlsc18xLnNoYWxsb3dFcXVhbCkocHJldkNvbmZpZywgY29uZmlnKSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBpbnRsOiAoMCwgY3JlYXRlSW50bF8xLmNyZWF0ZUludGwpKGNvbmZpZywgY2FjaGUpLFxuICAgICAgICAgICAgICAgIHByZXZDb25maWc6IGNvbmZpZyxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfTtcbiAgICBJbnRsUHJvdmlkZXIucHJvdG90eXBlLnJlbmRlciA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgKDAsIHV0aWxzXzEuaW52YXJpYW50SW50bENvbnRleHQpKHRoaXMuc3RhdGUuaW50bCk7XG4gICAgICAgIHJldHVybiBSZWFjdC5jcmVhdGVFbGVtZW50KGluamVjdEludGxfMS5Qcm92aWRlciwgeyB2YWx1ZTogdGhpcy5zdGF0ZS5pbnRsIH0sIHRoaXMucHJvcHMuY2hpbGRyZW4pO1xuICAgIH07XG4gICAgSW50bFByb3ZpZGVyLmRpc3BsYXlOYW1lID0gJ0ludGxQcm92aWRlcic7XG4gICAgSW50bFByb3ZpZGVyLmRlZmF1bHRQcm9wcyA9IHV0aWxzXzEuREVGQVVMVF9JTlRMX0NPTkZJRztcbiAgICByZXR1cm4gSW50bFByb3ZpZGVyO1xufShSZWFjdC5QdXJlQ29tcG9uZW50KSk7XG5leHBvcnRzLmRlZmF1bHQgPSBJbnRsUHJvdmlkZXI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/components/provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/components/relative.js":
/*!************************************************************!*\
  !*** ./node_modules/react-intl/src/components/relative.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar ecma402_abstract_1 = __webpack_require__(/*! @formatjs/ecma402-abstract */ \"(ssr)/./node_modules/@formatjs/ecma402-abstract/index.js\");\nvar useIntl_1 = tslib_1.__importDefault(__webpack_require__(/*! ./useIntl */ \"(ssr)/./node_modules/react-intl/src/components/useIntl.js\"));\nvar MINUTE = 60;\nvar HOUR = 60 * 60;\nvar DAY = 60 * 60 * 24;\nfunction selectUnit(seconds) {\n    var absValue = Math.abs(seconds);\n    if (absValue < MINUTE) {\n        return 'second';\n    }\n    if (absValue < HOUR) {\n        return 'minute';\n    }\n    if (absValue < DAY) {\n        return 'hour';\n    }\n    return 'day';\n}\nfunction getDurationInSeconds(unit) {\n    switch (unit) {\n        case 'second':\n            return 1;\n        case 'minute':\n            return MINUTE;\n        case 'hour':\n            return HOUR;\n        default:\n            return DAY;\n    }\n}\nfunction valueToSeconds(value, unit) {\n    if (!value) {\n        return 0;\n    }\n    switch (unit) {\n        case 'second':\n            return value;\n        case 'minute':\n            return value * MINUTE;\n        default:\n            return value * HOUR;\n    }\n}\nvar INCREMENTABLE_UNITS = [\n    'second',\n    'minute',\n    'hour',\n];\nfunction canIncrement(unit) {\n    if (unit === void 0) { unit = 'second'; }\n    return INCREMENTABLE_UNITS.indexOf(unit) > -1;\n}\nvar SimpleFormattedRelativeTime = function (props) {\n    var _a = (0, useIntl_1.default)(), formatRelativeTime = _a.formatRelativeTime, Text = _a.textComponent;\n    var children = props.children, value = props.value, unit = props.unit, otherProps = tslib_1.__rest(props, [\"children\", \"value\", \"unit\"]);\n    var formattedRelativeTime = formatRelativeTime(value || 0, unit, otherProps);\n    if (typeof children === 'function') {\n        return children(formattedRelativeTime);\n    }\n    if (Text) {\n        return React.createElement(Text, null, formattedRelativeTime);\n    }\n    return React.createElement(React.Fragment, null, formattedRelativeTime);\n};\nvar FormattedRelativeTime = function (_a) {\n    var _b = _a.value, value = _b === void 0 ? 0 : _b, _c = _a.unit, unit = _c === void 0 ? 'second' : _c, updateIntervalInSeconds = _a.updateIntervalInSeconds, otherProps = tslib_1.__rest(_a, [\"value\", \"unit\", \"updateIntervalInSeconds\"]);\n    (0, ecma402_abstract_1.invariant)(!updateIntervalInSeconds ||\n        !!(updateIntervalInSeconds && canIncrement(unit)), 'Cannot schedule update with unit longer than hour');\n    var _d = React.useState(), prevUnit = _d[0], setPrevUnit = _d[1];\n    var _e = React.useState(0), prevValue = _e[0], setPrevValue = _e[1];\n    var _f = React.useState(0), currentValueInSeconds = _f[0], setCurrentValueInSeconds = _f[1];\n    var updateTimer;\n    if (unit !== prevUnit || value !== prevValue) {\n        setPrevValue(value || 0);\n        setPrevUnit(unit);\n        setCurrentValueInSeconds(canIncrement(unit) ? valueToSeconds(value, unit) : 0);\n    }\n    React.useEffect(function () {\n        function clearUpdateTimer() {\n            clearTimeout(updateTimer);\n        }\n        clearUpdateTimer();\n        // If there's no interval and we cannot increment this unit, do nothing\n        if (!updateIntervalInSeconds || !canIncrement(unit)) {\n            return clearUpdateTimer;\n        }\n        // Figure out the next interesting time\n        var nextValueInSeconds = currentValueInSeconds - updateIntervalInSeconds;\n        var nextUnit = selectUnit(nextValueInSeconds);\n        // We've reached the max auto incrementable unit, don't schedule another update\n        if (nextUnit === 'day') {\n            return clearUpdateTimer;\n        }\n        var unitDuration = getDurationInSeconds(nextUnit);\n        var remainder = nextValueInSeconds % unitDuration;\n        var prevInterestingValueInSeconds = nextValueInSeconds - remainder;\n        var nextInterestingValueInSeconds = prevInterestingValueInSeconds >= currentValueInSeconds\n            ? prevInterestingValueInSeconds - unitDuration\n            : prevInterestingValueInSeconds;\n        var delayInSeconds = Math.abs(nextInterestingValueInSeconds - currentValueInSeconds);\n        if (currentValueInSeconds !== nextInterestingValueInSeconds) {\n            updateTimer = setTimeout(function () { return setCurrentValueInSeconds(nextInterestingValueInSeconds); }, delayInSeconds * 1e3);\n        }\n        return clearUpdateTimer;\n    }, [currentValueInSeconds, updateIntervalInSeconds, unit]);\n    var currentValue = value || 0;\n    var currentUnit = unit;\n    if (canIncrement(unit) &&\n        typeof currentValueInSeconds === 'number' &&\n        updateIntervalInSeconds) {\n        currentUnit = selectUnit(currentValueInSeconds);\n        var unitDuration = getDurationInSeconds(currentUnit);\n        currentValue = Math.round(currentValueInSeconds / unitDuration);\n    }\n    return (React.createElement(SimpleFormattedRelativeTime, tslib_1.__assign({ value: currentValue, unit: currentUnit }, otherProps)));\n};\nFormattedRelativeTime.displayName = 'FormattedRelativeTime';\nexports[\"default\"] = FormattedRelativeTime;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/components/relative.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/components/useIntl.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-intl/src/components/useIntl.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports[\"default\"] = useIntl;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar utils_1 = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-intl/src/utils.js\");\nvar injectIntl_1 = __webpack_require__(/*! ./injectIntl */ \"(ssr)/./node_modules/react-intl/src/components/injectIntl.js\");\nfunction useIntl() {\n    var intl = React.useContext(injectIntl_1.Context);\n    (0, utils_1.invariantIntlContext)(intl);\n    return intl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaW50bC9zcmMvY29tcG9uZW50cy91c2VJbnRsLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGtCQUFlO0FBQ2YsY0FBYyxtQkFBTyxDQUFDLHVEQUFPO0FBQzdCLGlDQUFpQyxtQkFBTyxDQUFDLHdHQUFPO0FBQ2hELGNBQWMsbUJBQU8sQ0FBQyw4REFBVTtBQUNoQyxtQkFBbUIsbUJBQU8sQ0FBQyxrRkFBYztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2xvYmFsLWtwaS1hcHAvLi9ub2RlX21vZHVsZXMvcmVhY3QtaW50bC9zcmMvY29tcG9uZW50cy91c2VJbnRsLmpzPzBjNWYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmRlZmF1bHQgPSB1c2VJbnRsO1xudmFyIHRzbGliXzEgPSByZXF1aXJlKFwidHNsaWJcIik7XG52YXIgUmVhY3QgPSB0c2xpYl8xLl9faW1wb3J0U3RhcihyZXF1aXJlKFwicmVhY3RcIikpO1xudmFyIHV0aWxzXzEgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG52YXIgaW5qZWN0SW50bF8xID0gcmVxdWlyZShcIi4vaW5qZWN0SW50bFwiKTtcbmZ1bmN0aW9uIHVzZUludGwoKSB7XG4gICAgdmFyIGludGwgPSBSZWFjdC51c2VDb250ZXh0KGluamVjdEludGxfMS5Db250ZXh0KTtcbiAgICAoMCwgdXRpbHNfMS5pbnZhcmlhbnRJbnRsQ29udGV4dCkoaW50bCk7XG4gICAgcmV0dXJuIGludGw7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/components/useIntl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-intl/src/utils.js":
/*!**********************************************!*\
  !*** ./node_modules/react-intl/src/utils.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_INTL_CONFIG = void 0;\nexports.invariantIntlContext = invariantIntlContext;\nexports.assignUniqueKeysToParts = assignUniqueKeysToParts;\nexports.shallowEqual = shallowEqual;\nvar tslib_1 = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar React = tslib_1.__importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar ecma402_abstract_1 = __webpack_require__(/*! @formatjs/ecma402-abstract */ \"(ssr)/./node_modules/@formatjs/ecma402-abstract/index.js\");\nvar intl_1 = __webpack_require__(/*! @formatjs/intl */ \"(ssr)/./node_modules/@formatjs/intl/index.js\");\nfunction invariantIntlContext(intl) {\n    (0, ecma402_abstract_1.invariant)(intl, '[React Intl] Could not find required `intl` object. ' +\n        '<IntlProvider> needs to exist in the component ancestry.');\n}\nexports.DEFAULT_INTL_CONFIG = tslib_1.__assign(tslib_1.__assign({}, intl_1.DEFAULT_INTL_CONFIG), { textComponent: React.Fragment });\n/**\n * Takes a `formatXMLElementFn`, and composes it in function, which passes\n * argument `parts` through, assigning unique key to each part, to prevent\n * \"Each child in a list should have a unique \"key\"\" React error.\n * @param formatXMLElementFn\n */\nfunction assignUniqueKeysToParts(formatXMLElementFn) {\n    return function (parts) {\n        // eslint-disable-next-line prefer-rest-params\n        return formatXMLElementFn(React.Children.toArray(parts));\n    };\n}\nfunction shallowEqual(objA, objB) {\n    if (objA === objB) {\n        return true;\n    }\n    if (!objA || !objB) {\n        return false;\n    }\n    var aKeys = Object.keys(objA);\n    var bKeys = Object.keys(objB);\n    var len = aKeys.length;\n    if (bKeys.length !== len) {\n        return false;\n    }\n    for (var i = 0; i < len; i++) {\n        var key = aKeys[i];\n        if (objA[key] !== objB[key] ||\n            !Object.prototype.hasOwnProperty.call(objB, key)) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-intl/src/utils.js\n");

/***/ })

};
;