import { enviroment } from '../configurations/enviroment'
import { IPublicClientApplication } from '@azure/msal-browser'
import { getAuthenticationResult, safeStringify } from '../utils/general'
import { fetchFromCache, setCache } from './cache'
import { getReportingSites } from './asset-hierarchy-client'

const userManagementBase = enviroment.userManagementUrl ?? ''
const applicationCode = enviroment.userManagementAppCode ?? ''

export interface UserRolesPermission {
    displayName: string
    firstName: string
    lastName: string
    email: string
    lanId: string
    companyName: string
    jobTitle: string
    department: string
    avatar: string
    applications: UserApplication[]
    sites: UserSite[]
    selectedSite?: UserSite
    availableReportingSites?: UserSite[]
    units: UserUnit[]
}

export interface FeatureSitesMapping {
    featureCode: string
    reportingSites: UserSite[]
}

interface CacheData {
    user: UserRolesPermission
    featureSitesMapping: FeatureSitesMapping[]
}

export interface UserApplication {
    applicationCode: string
    roles: UserRole[]
    userSites: UserSite[]
}

export interface UserUnit {
    unitName: string
    unitCode: string
}

export interface UserSite {
    siteId: string
    siteName: string
    siteCode: string
}

export interface UserRole {
    roleName: string
    roleCode: string
    siteCodes: string[]
    features: FeaturePermission[]
}

export interface FeaturePermission {
    featureCode: string
    featureName: string
    featureAccessLevel: string
    featureAccessLevelCode: string
}

async function fetchFromUserManagement(token: string, bodyRequest: { site_code: string[]; application_code: string }) {
    const userIntegration = await fetch(userManagementBase + '/integration/user', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${token}` },
        body: safeStringify(bodyRequest),
    })

    if (userIntegration.status !== 200) {
        throw new Error(userIntegration.statusText)
    }

    return await userIntegration.json()
}

function mapResult(result: any) {
    return Object.keys(result).length !== 0 && result.constructor === Object
        ? (result as UserRolesPermission)
        : ({} as UserRolesPermission)
}

function mapFeatureSitesMapping(userPermissions: UserRolesPermission) {
    const roles = userPermissions?.applications?.find((a) => a.applicationCode === applicationCode)?.roles ?? []

    const mapping = new Map<string, Set<string>>()
    for (const role of roles) {
        for (const feature of role.features) {
            const featureCode = feature.featureCode
            const siteCodes = role.siteCodes

            const existingSiteCodes = mapping.get(featureCode) ?? new Set<string>()
            siteCodes.forEach((item) => existingSiteCodes.add(item))

            mapping.set(featureCode, existingSiteCodes)
        }
    }

    const result: FeatureSitesMapping[] = []
    for (const [featureKey, siteExternalIds] of mapping) {
        const entrySites = userPermissions.sites.filter((s) => siteExternalIds.has(s.siteId))

        result.push({ featureCode: featureKey, reportingSites: entrySites })
    }

    return result
}

async function getCacheData(msal: IPublicClientApplication): Promise<CacheData> {
    const { account, accessToken } = await getAuthenticationResult(msal)
    const userName = account?.username ?? ''

    const cachedData = fetchFromCache({ userName }) as CacheData | undefined

    if (cachedData) {
        return cachedData
    }

    const sites = await getReportingSites(accessToken)
    const siteCodes = sites.map((s) => s.externalId)

    const bodyRequest = { site_code: siteCodes, application_code: applicationCode }
    const data = await fetchFromUserManagement(accessToken, bodyRequest)

    const user = mapResult(data)
    const featureSitesMapping = mapFeatureSitesMapping(user)

    const entry = { user, featureSitesMapping } as CacheData
    setCache({ userName, data: entry })

    return entry
}

export async function getUserPermissions(msal: IPublicClientApplication): Promise<CacheData> {
    return await getCacheData(msal)
}

export async function getSitesByFeatures(msal: IPublicClientApplication, featureCodes: string[]) {
    const { featureSitesMapping } = await getCacheData(msal)

    const sites = featureSitesMapping
        .filter((p) => featureCodes.includes(p.featureCode))
        .flatMap((p) => p.reportingSites)

    const map = new Map(sites.map((p) => [p.siteId, p]))

    return Array.from(map.values())
}
