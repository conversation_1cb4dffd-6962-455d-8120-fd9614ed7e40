import { BusinessSegment } from '@/common/models/business-segment'
import { DescribableEntity, HomeFilterValue, HomeTabsEnum } from '@/common/models/home-models'
import { KpiGroup } from '@/common/models/kpiGroup'
import { KpiList } from '@/common/models/kpiList'
import { Site } from '@/common/models/site'
import { QualityKpis } from '@/common/utils/quality-kpis'
import { translate } from '@celanese/celanese-sdk'
import dayjs from 'dayjs'

export const referenceFoundational = ['GKPI-SOL-APC', 'GKPI-SOL-APV', 'GKPI-SOL-FPC', 'GKPI-SOL-FPV']
export const allKpisItem = { externalId: 'ALL_KPIS', name: translate('COMMONFILTER.ALL') }

export function getDefaultFilterState(
    currentTab: number,
    sites: Site[],
    segments: BusinessSegment[],
    kpis?: KpiList[],
    kpiGroups?: KpiGroup[],
    defaultValues?: HomeFilterValue,
    disableSegmentSelector?: boolean
) {
    let segmentsState = []
    if (disableSegmentSelector) {
        segmentsState = [...segments]
    } else {
        segmentsState = defaultValues?.businessSegment?.length
            ? [...defaultValues.businessSegment]
            : segments?.length
            ? [...segments]
            : []
    }
    const sitesState = defaultValues?.siteList?.length ? [...defaultValues.siteList] : sites?.length ? [sites[0]] : []
    const newSiteList = getSelectedSites(segmentsState, sites, sitesState)
    const kpisSelected = defaultValues?.kpiList?.length ? [...defaultValues.kpiList] : kpis?.length ? [...kpis] : []
    return {
        businessSegment: segmentsState,
        siteList: newSiteList,
        kpiList: kpis?.length ? getSelectedKpis(currentTab, kpis, kpiGroups, kpisSelected) : [],
        period: defaultValues?.period ?? dayjs().format('YYYY-MM-DD'),
    }
}

export function getSelectedSites(
    businessSegments: DescribableEntity[],
    sities: Site[],
    currentSites?: DescribableEntity[]
) {
    const sitesFromSegments = getSitesBySegment(sities, businessSegments)
    if (!sitesFromSegments.length) {
        return []
    }
    const currentSitesIds = currentSites?.map((s) => s.externalId)
    const filteredSites = sitesFromSegments.filter(
        (site) => !currentSitesIds || currentSitesIds.includes(site.externalId)
    )
    return filteredSites.length ? filteredSites : [sitesFromSegments[0]]
}

export function getSitesBySegment(sites: Site[], businessSegments: DescribableEntity[]) {
    const segments = businessSegments.map((bs) => bs.externalId)
    return sites.filter((s) => s.businessSegments.some((bs) => segments.includes(bs)))
}

export function getSelectedKpis(
    currentTab: number,
    kpis: KpiList[],
    kpiGroups: KpiGroup[],
    selectedKpis?: DescribableEntity[]
) {
    if (currentTab === HomeTabsEnum.General) {
        return [...kpis]
    }
    if (currentTab === HomeTabsEnum.Engagement) {
        return [allKpisItem as KpiList]
    }
    const kpiOptions = getKpisByGroup(currentTab, kpis, kpiGroups)
    if (!selectedKpis?.length) {
        return kpiOptions
    }
    const selectedIds = selectedKpis?.map((s) => s.externalId)
    const keep = kpiOptions.filter((o) => selectedIds.includes(o.externalId))
    return keep.length ? keep : [...kpiOptions]
}

export function getKpisByGroup(currentTab: number, kpis?: KpiList[], kpiGroups?: KpiGroup[]): KpiList[] {
    if (!kpis?.length) {
        return []
    }
    if (currentTab === HomeTabsEnum.General) {
        return [...kpis]
    }
    if (currentTab === HomeTabsEnum.Engagement) {
        return [allKpisItem as KpiList]
    }
    const currentGroupKpis = kpis.filter((kpi) => kpiGroups[currentTab]?.name === kpi.group?.name)
    if (kpiGroups[currentTab]?.name === 'Foundational') {
        return currentGroupKpis.filter((kpi) => !referenceFoundational.includes(kpi.externalId))
    }
    if (kpiGroups[currentTab]?.name === 'Quality') {
        const enumValues = Object.values(QualityKpis)
        return currentGroupKpis.filter((kpi) => Object.values(enumValues).includes(kpi.externalId as QualityKpis))
    }
    return currentGroupKpis
}
