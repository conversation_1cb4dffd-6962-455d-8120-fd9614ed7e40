"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@loadable";
exports.ids = ["vendor-chunks/@loadable"];
exports.modules = {

/***/ "(ssr)/./node_modules/@loadable/component/dist/cjs/loadable.cjs.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@loadable/component/dist/cjs/loadable.cjs.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }\n\nvar React = _interopDefault(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _objectWithoutPropertiesLoose = _interopDefault(__webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\"));\nvar _extends = _interopDefault(__webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/extends.js\"));\nvar _assertThisInitialized = _interopDefault(__webpack_require__(/*! @babel/runtime/helpers/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/assertThisInitialized.js\"));\nvar _inheritsLoose = _interopDefault(__webpack_require__(/*! @babel/runtime/helpers/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/inheritsLoose.js\"));\nvar hoistNonReactStatics = _interopDefault(__webpack_require__(/*! hoist-non-react-statics */ \"(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\"));\n\n/* eslint-disable import/prefer-default-export */\nfunction invariant(condition, message) {\n  if (condition) return;\n  var error = new Error(\"loadable: \" + message);\n  error.framesToPop = 1;\n  error.name = 'Invariant Violation';\n  throw error;\n}\nfunction warn(message) {\n  // eslint-disable-next-line no-console\n  console.warn(\"loadable: \" + message);\n}\n\nvar Context = /*#__PURE__*/\nReact.createContext();\n\nvar LOADABLE_REQUIRED_CHUNKS_KEY = '__LOADABLE_REQUIRED_CHUNKS__';\nfunction getRequiredChunkKey(namespace) {\n  return \"\" + namespace + LOADABLE_REQUIRED_CHUNKS_KEY;\n}\n\nvar sharedInternals = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  getRequiredChunkKey: getRequiredChunkKey,\n  invariant: invariant,\n  Context: Context\n});\n\nvar LOADABLE_SHARED = {\n  initialChunks: {}\n};\n\nvar STATUS_PENDING = 'PENDING';\nvar STATUS_RESOLVED = 'RESOLVED';\nvar STATUS_REJECTED = 'REJECTED';\n\nfunction resolveConstructor(ctor) {\n  if (typeof ctor === 'function') {\n    return {\n      requireAsync: ctor,\n      resolve: function resolve() {\n        return undefined;\n      },\n      chunkName: function chunkName() {\n        return undefined;\n      }\n    };\n  }\n\n  return ctor;\n}\n\nvar withChunkExtractor = function withChunkExtractor(Component) {\n  var LoadableWithChunkExtractor = function LoadableWithChunkExtractor(props) {\n    return React.createElement(Context.Consumer, null, function (extractor) {\n      return React.createElement(Component, Object.assign({\n        __chunkExtractor: extractor\n      }, props));\n    });\n  };\n\n  if (Component.displayName) {\n    LoadableWithChunkExtractor.displayName = Component.displayName + \"WithChunkExtractor\";\n  }\n\n  return LoadableWithChunkExtractor;\n};\n\nvar identity = function identity(v) {\n  return v;\n};\n\nfunction createLoadable(_ref) {\n  var _ref$defaultResolveCo = _ref.defaultResolveComponent,\n      defaultResolveComponent = _ref$defaultResolveCo === void 0 ? identity : _ref$defaultResolveCo,\n      _render = _ref.render,\n      onLoad = _ref.onLoad;\n\n  function loadable(loadableConstructor, options) {\n    if (options === void 0) {\n      options = {};\n    }\n\n    var ctor = resolveConstructor(loadableConstructor);\n    var cache = {};\n    /**\n     * Cachekey represents the component to be loaded\n     * if key changes - component has to be reloaded\n     * @param props\n     * @returns {null|Component}\n     */\n\n    function _getCacheKey(props) {\n      if (options.cacheKey) {\n        return options.cacheKey(props);\n      }\n\n      if (ctor.resolve) {\n        return ctor.resolve(props);\n      }\n\n      return 'static';\n    }\n    /**\n     * Resolves loaded `module` to a specific `Component\n     * @param module\n     * @param props\n     * @param Loadable\n     * @returns Component\n     */\n\n\n    function resolve(module, props, Loadable) {\n      var Component = options.resolveComponent ? options.resolveComponent(module, props) : defaultResolveComponent(module); // FIXME: suppressed due to https://github.com/gregberge/loadable-components/issues/990\n      // if (options.resolveComponent && !ReactIs.isValidElementType(Component)) {\n      //   throw new Error(\n      //     `resolveComponent returned something that is not a React component!`,\n      //   )\n      // }\n\n      hoistNonReactStatics(Loadable, Component, {\n        preload: true\n      });\n      return Component;\n    }\n\n    var cachedLoad = function cachedLoad(props) {\n      var cacheKey = _getCacheKey(props);\n\n      var promise = cache[cacheKey];\n\n      if (!promise || promise.status === STATUS_REJECTED) {\n        promise = ctor.requireAsync(props);\n        promise.status = STATUS_PENDING;\n        cache[cacheKey] = promise;\n        promise.then(function () {\n          promise.status = STATUS_RESOLVED;\n        }, function (error) {\n          console.error('loadable-components: failed to asynchronously load component', {\n            fileName: ctor.resolve(props),\n            chunkName: ctor.chunkName(props),\n            error: error ? error.message : error\n          });\n          promise.status = STATUS_REJECTED;\n        });\n      }\n\n      return promise;\n    };\n\n    var InnerLoadable =\n    /*#__PURE__*/\n    function (_React$Component) {\n      _inheritsLoose(InnerLoadable, _React$Component);\n\n      InnerLoadable.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {\n        var cacheKey = _getCacheKey(props);\n\n        return _extends({}, state, {\n          cacheKey: cacheKey,\n          // change of a key triggers loading state automatically\n          loading: state.loading || state.cacheKey !== cacheKey\n        });\n      };\n\n      function InnerLoadable(props) {\n        var _this;\n\n        _this = _React$Component.call(this, props) || this;\n        _this.state = {\n          result: null,\n          error: null,\n          loading: true,\n          cacheKey: _getCacheKey(props)\n        };\n        invariant(!props.__chunkExtractor || ctor.requireSync, 'SSR requires `@loadable/babel-plugin`, please install it'); // Server-side\n\n        if (props.__chunkExtractor) {\n          // This module has been marked with no SSR\n          if (options.ssr === false) {\n            return _assertThisInitialized(_this);\n          } // We run load function, we assume that it won't fail and that it\n          // triggers a synchronous loading of the module\n\n\n          ctor.requireAsync(props)[\"catch\"](function () {\n            return null;\n          }); // So we can require now the module synchronously\n\n          _this.loadSync();\n\n          props.__chunkExtractor.addChunk(ctor.chunkName(props));\n\n          return _assertThisInitialized(_this);\n        } // Client-side with `isReady` method present (SSR probably)\n        // If module is already loaded, we use a synchronous loading\n        // Only perform this synchronous loading if the component has not\n        // been marked with no SSR, else we risk hydration mismatches\n\n\n        if (options.ssr !== false && ( // is ready - was loaded in this session\n        ctor.isReady && ctor.isReady(props) || // is ready - was loaded during SSR process\n        ctor.chunkName && LOADABLE_SHARED.initialChunks[ctor.chunkName(props)])) {\n          _this.loadSync();\n        }\n\n        return _this;\n      }\n\n      var _proto = InnerLoadable.prototype;\n\n      _proto.componentDidMount = function componentDidMount() {\n        this.mounted = true; // retrieve loading promise from a global cache\n\n        var cachedPromise = this.getCache(); // if promise exists, but rejected - clear cache\n\n        if (cachedPromise && cachedPromise.status === STATUS_REJECTED) {\n          this.setCache();\n        } // component might be resolved synchronously in the constructor\n\n\n        if (this.state.loading) {\n          this.loadAsync();\n        }\n      };\n\n      _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n        // Component has to be reloaded on cacheKey change\n        if (prevState.cacheKey !== this.state.cacheKey) {\n          this.loadAsync();\n        }\n      };\n\n      _proto.componentWillUnmount = function componentWillUnmount() {\n        this.mounted = false;\n      };\n\n      _proto.safeSetState = function safeSetState(nextState, callback) {\n        if (this.mounted) {\n          this.setState(nextState, callback);\n        }\n      }\n      /**\n       * returns a cache key for the current props\n       * @returns {Component|string}\n       */\n      ;\n\n      _proto.getCacheKey = function getCacheKey() {\n        return _getCacheKey(this.props);\n      }\n      /**\n       * access the persistent cache\n       */\n      ;\n\n      _proto.getCache = function getCache() {\n        return cache[this.getCacheKey()];\n      }\n      /**\n       * sets the cache value. If called without value sets it as undefined\n       */\n      ;\n\n      _proto.setCache = function setCache(value) {\n        if (value === void 0) {\n          value = undefined;\n        }\n\n        cache[this.getCacheKey()] = value;\n      };\n\n      _proto.triggerOnLoad = function triggerOnLoad() {\n        var _this2 = this;\n\n        if (onLoad) {\n          setTimeout(function () {\n            onLoad(_this2.state.result, _this2.props);\n          });\n        }\n      }\n      /**\n       * Synchronously loads component\n       * target module is expected to already exists in the module cache\n       * or be capable to resolve synchronously (webpack target=node)\n       */\n      ;\n\n      _proto.loadSync = function loadSync() {\n        // load sync is expecting component to be in the \"loading\" state already\n        // sounds weird, but loading=true is the initial state of InnerLoadable\n        if (!this.state.loading) return;\n\n        try {\n          var loadedModule = ctor.requireSync(this.props);\n          var result = resolve(loadedModule, this.props, Loadable);\n          this.state.result = result;\n          this.state.loading = false;\n        } catch (error) {\n          console.error('loadable-components: failed to synchronously load component, which expected to be available', {\n            fileName: ctor.resolve(this.props),\n            chunkName: ctor.chunkName(this.props),\n            error: error ? error.message : error\n          });\n          this.state.error = error;\n        }\n      }\n      /**\n       * Asynchronously loads a component.\n       */\n      ;\n\n      _proto.loadAsync = function loadAsync() {\n        var _this3 = this;\n\n        var promise = this.resolveAsync();\n        promise.then(function (loadedModule) {\n          var result = resolve(loadedModule, _this3.props, Loadable);\n\n          _this3.safeSetState({\n            result: result,\n            loading: false\n          }, function () {\n            return _this3.triggerOnLoad();\n          });\n        })[\"catch\"](function (error) {\n          return _this3.safeSetState({\n            error: error,\n            loading: false\n          });\n        });\n        return promise;\n      }\n      /**\n       * Asynchronously resolves(not loads) a component.\n       * Note - this function does not change the state\n       */\n      ;\n\n      _proto.resolveAsync = function resolveAsync() {\n        var _this$props = this.props,\n            __chunkExtractor = _this$props.__chunkExtractor,\n            forwardedRef = _this$props.forwardedRef,\n            props = _objectWithoutPropertiesLoose(_this$props, [\"__chunkExtractor\", \"forwardedRef\"]);\n\n        return cachedLoad(props);\n      };\n\n      _proto.render = function render() {\n        var _this$props2 = this.props,\n            forwardedRef = _this$props2.forwardedRef,\n            propFallback = _this$props2.fallback,\n            __chunkExtractor = _this$props2.__chunkExtractor,\n            props = _objectWithoutPropertiesLoose(_this$props2, [\"forwardedRef\", \"fallback\", \"__chunkExtractor\"]);\n\n        var _this$state = this.state,\n            error = _this$state.error,\n            loading = _this$state.loading,\n            result = _this$state.result;\n\n        if (options.suspense) {\n          var cachedPromise = this.getCache() || this.loadAsync();\n\n          if (cachedPromise.status === STATUS_PENDING) {\n            throw this.loadAsync();\n          }\n        }\n\n        if (error) {\n          throw error;\n        }\n\n        var fallback = propFallback || options.fallback || null;\n\n        if (loading) {\n          return fallback;\n        }\n\n        return _render({\n          fallback: fallback,\n          result: result,\n          options: options,\n          props: _extends({}, props, {\n            ref: forwardedRef\n          })\n        });\n      };\n\n      return InnerLoadable;\n    }(React.Component);\n\n    var EnhancedInnerLoadable = withChunkExtractor(InnerLoadable);\n    var Loadable = React.forwardRef(function (props, ref) {\n      return React.createElement(EnhancedInnerLoadable, Object.assign({\n        forwardedRef: ref\n      }, props));\n    });\n    Loadable.displayName = 'Loadable'; // In future, preload could use `<link rel=\"preload\">`\n\n    Loadable.preload = function (props) {\n      Loadable.load(props);\n    };\n\n    Loadable.load = function (props) {\n      return cachedLoad(props);\n    };\n\n    return Loadable;\n  }\n\n  function lazy(ctor, options) {\n    return loadable(ctor, _extends({}, options, {\n      suspense: true\n    }));\n  }\n\n  return {\n    loadable: loadable,\n    lazy: lazy\n  };\n}\n\nfunction defaultResolveComponent(loadedModule) {\n  // eslint-disable-next-line no-underscore-dangle\n  return loadedModule.__esModule ? loadedModule[\"default\"] : loadedModule[\"default\"] || loadedModule;\n}\n\n/* eslint-disable no-use-before-define, react/no-multi-comp */\n\nvar _createLoadable =\n/*#__PURE__*/\ncreateLoadable({\n  defaultResolveComponent: defaultResolveComponent,\n  render: function render(_ref) {\n    var Component = _ref.result,\n        props = _ref.props;\n    return React.createElement(Component, props);\n  }\n}),\n    loadable = _createLoadable.loadable,\n    lazy = _createLoadable.lazy;\n\n/* eslint-disable no-use-before-define, react/no-multi-comp */\n\nvar _createLoadable$1 =\n/*#__PURE__*/\ncreateLoadable({\n  onLoad: function onLoad(result, props) {\n    if (result && props.forwardedRef) {\n      if (typeof props.forwardedRef === 'function') {\n        props.forwardedRef(result);\n      } else {\n        props.forwardedRef.current = result;\n      }\n    }\n  },\n  render: function render(_ref) {\n    var result = _ref.result,\n        props = _ref.props;\n\n    if (props.children) {\n      return props.children(result);\n    }\n\n    return null;\n  }\n}),\n    loadable$1 = _createLoadable$1.loadable,\n    lazy$1 = _createLoadable$1.lazy;\n\n/* eslint-disable no-underscore-dangle, camelcase */\nvar BROWSER = typeof window !== 'undefined';\nfunction loadableReady(done, _temp) {\n  if (done === void 0) {\n    done = function done() {};\n  }\n\n  var _ref = _temp === void 0 ? {} : _temp,\n      _ref$namespace = _ref.namespace,\n      namespace = _ref$namespace === void 0 ? '' : _ref$namespace,\n      _ref$chunkLoadingGlob = _ref.chunkLoadingGlobal,\n      chunkLoadingGlobal = _ref$chunkLoadingGlob === void 0 ? '__LOADABLE_LOADED_CHUNKS__' : _ref$chunkLoadingGlob;\n\n  if (!BROWSER) {\n    warn('`loadableReady()` must be called in browser only');\n    done();\n    return Promise.resolve();\n  }\n\n  var requiredChunks = null;\n\n  if (BROWSER) {\n    var id = getRequiredChunkKey(namespace);\n    var dataElement = document.getElementById(id);\n\n    if (dataElement) {\n      requiredChunks = JSON.parse(dataElement.textContent);\n      var extElement = document.getElementById(id + \"_ext\");\n\n      if (extElement) {\n        var _JSON$parse = JSON.parse(extElement.textContent),\n            namedChunks = _JSON$parse.namedChunks;\n\n        namedChunks.forEach(function (chunkName) {\n          LOADABLE_SHARED.initialChunks[chunkName] = true;\n        });\n      } else {\n        // version mismatch\n        throw new Error('loadable-component: @loadable/server does not match @loadable/component');\n      }\n    }\n  }\n\n  if (!requiredChunks) {\n    warn('`loadableReady()` requires state, please use `getScriptTags` or `getScriptElements` server-side');\n    done();\n    return Promise.resolve();\n  }\n\n  var resolved = false;\n  return new Promise(function (resolve) {\n    window[chunkLoadingGlobal] = window[chunkLoadingGlobal] || [];\n    var loadedChunks = window[chunkLoadingGlobal];\n    var originalPush = loadedChunks.push.bind(loadedChunks);\n\n    function checkReadyState() {\n      if (requiredChunks.every(function (chunk) {\n        return loadedChunks.some(function (_ref2) {\n          var chunks = _ref2[0];\n          return chunks.indexOf(chunk) > -1;\n        });\n      })) {\n        if (!resolved) {\n          resolved = true;\n          resolve();\n        }\n      }\n    }\n\n    loadedChunks.push = function () {\n      originalPush.apply(void 0, arguments);\n      checkReadyState();\n    };\n\n    checkReadyState();\n  }).then(done);\n}\n\n/* eslint-disable no-underscore-dangle */\nvar loadable$2 = loadable;\nloadable$2.lib = loadable$1;\nvar lazy$2 = lazy;\nlazy$2.lib = lazy$1;\nvar __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = sharedInternals;\n\nexports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\nexports[\"default\"] = loadable$2;\nexports.lazy = lazy$2;\nexports.loadableReady = loadableReady;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@loadable/component/dist/cjs/loadable.cjs.js\n");

/***/ })

};
;