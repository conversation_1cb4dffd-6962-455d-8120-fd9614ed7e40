import { cognite } from '../configurations/cognite'
import { safeStringify } from '../utils'
import { EntityType, GetSpace } from '../utils/space-util'

const base_api_url = `${cognite.baseUrl}/api/v1/projects/${cognite.project}`

export class CdfApiClient {
    private versions: { [k: string]: number } = {}

    constructor(private bearer: string) {}

    private buildHeaders(): HeadersInit {
        return {
            'Content-Type': 'application/json',
            Authorization: this.bearer,
            'X-Cdp-App': cognite.cogniteXCdpApp,
        }
    }

    async getInstancesById<T>(entity: string, externalIds: string[]): Promise<T> {
        await this.loadViewVersions()
        const response = await fetch(`${base_api_url}/models/instances/byids`, {
            method: 'POST',
            headers: this.buildHeaders(),
            body: safeStringify({
                sources: [
                    {
                        source: {
                            type: 'view',
                            space: GetSpace(EntityType.Model),
                            externalId: entity,
                            version: this.versions[entity],
                        },
                    },
                ],
                items: externalIds.map((e) => ({
                    externalId: e,
                    instanceType: 'node',
                    space: GetSpace(EntityType.Instance),
                })),
            }),
        })
        if (!response.ok) {
            throw new Error(response.statusText)
        }
        const data = await response.json()
        const result = data?.items.map((item: any) => {
            const properties: any = item?.properties[GetSpace(EntityType.Model)][`${entity}/${this.versions[entity]}`]
            properties.externalId = item.externalId
            return properties
        })
        return result as T
    }

    private async loadViewVersions() {
        if (Object.keys(this.versions).length) {
            return this.versions
        }
        const response = await fetch(`${base_api_url}/models/views?limit=1000&space=${GetSpace(EntityType.Model)}`, {
            method: 'GET',
            headers: this.buildHeaders(),
        })
        if (!response.ok) {
            throw new Error(response.statusText)
        }
        const data = await response.json()
        const result: any = {}
        data?.items?.forEach((view: any) => {
            result[view.externalId] = view.version
        })
        this.versions = result
    }
}
