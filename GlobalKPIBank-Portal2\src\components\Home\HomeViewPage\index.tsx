import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { useBusinessSegmentList } from '@/common/hooks/useBusinessSegment'
import { useGetKpiGroups } from '@/common/hooks/useGetKpiGroups'
import { useGetKpiList } from '@/common/hooks/useGetKpiList'
import { useLocalStorage } from '@/common/hooks/useLocalStorage'
import { useReportingSiteList } from '@/common/hooks/useReportingSiteList'
import { HomeFilterValue } from '@/common/models/home-models'
import { KpiGroup } from '@/common/models/kpiGroup'
import { translate } from '@celanese/celanese-sdk'
import { ClnTabs, TabProps } from '@celanese/ui-lib'
import { Box, Typography } from '@mui/material'
import dayjs from 'dayjs'
import { useCallback, useMemo, useState } from 'react'
import HomeViewFilters from '../HomeViewFilters'
import { HomeViewWidgets } from '../HomeViewWidgets'
import './styles.css'

const styles = {
    tabWrapper: {
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: '8px',
        width: 'auto',
        height: 'calc(100% - 59px)',
        padding: '20px',
        margin: '15px 0px 0px 0px',
    },
}
const tabGroupsPermission = [
    'homeViewFoundationalKpi',
    'homeViewFoundationalKpi',
    'homeViewStewardshipKpi',
    'homeViewQualityKpi',
    'homeViewReliabilityKpi',
    'homeViewEngagementKpi',
]

export const HomeViewPage = () => {
    const [parsedFilters, setParsedFilters] = useLocalStorage<HomeFilterValue | undefined>('homeFilters', undefined)
    const [currentTab, setCurrentTab] = useState<number>(parsedFilters?.currentTab ?? 0)
    const { kpiGroupsList, loadingKpiGroups } = useGetKpiGroups()
    const [updatedKpiGroupList, setUpdatedKpiGroupList] = useState<KpiGroup[]>([])
    const { kpiList, loadingKpiList } = useGetKpiList()
    const { dataBusinessSegmentList, loadingBusinessSegmentList } = useBusinessSegmentList()
    const { checkPermissionsFromRoutes } = useAuthGuard()
    const { dataSiteList: sites, loadingSiteList } = useReportingSiteList([])
    const [generalFilterTag, setGeneralFilterTag] = useState<boolean>(false)
    const [filters, setFilters] = useState<any>(null)
    const { setRequestData } = useGlobalKpiContext()

    const tabsContent: React.ReactNode[] = useMemo(() => {
        const general = { name: 'General' } as KpiGroup
        const aux = [...kpiGroupsList]
        aux.unshift(general)
        setUpdatedKpiGroupList(aux)

        return aux.map((groupName) => {
            return groupName?.name === 'Engagement' ? (
                <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" sx={{ color: '#083D5B', marginTop: '2rem' }}>
                        {translate('COMING_SOON')}
                    </Typography>
                </Box>
            ) : (
                <HomeViewWidgets tabGroup={groupName} filters={filters} setGeneralFilterTag={setGeneralFilterTag} />
            )
        })
    }, [kpiGroupsList, filters])

    const tabs: TabProps[] = updatedKpiGroupList.map((groupName, index) => ({
        label: translate('KPI_TAB_LIST.' + groupName?.name.toUpperCase()).toUpperCase(),
        content: tabsContent[index],
        disabled: !checkPermissionsFromRoutes(tabGroupsPermission[index]),
    }))

    const applyFilters = useCallback(
        (updatedFilters: HomeFilterValue) => {
            const mappedFilters = {
                ...updatedFilters,
                businessSegment: updatedFilters.businessSegment?.length == 1 ? updatedFilters.businessSegment : [],
                siteList: updatedFilters.siteList.map((site: any) => site.externalId),
            }
            const hasFilterChanged = JSON.stringify(filters) !== JSON.stringify(mappedFilters)
            if (hasFilterChanged) {
                setFilters(mappedFilters)

                setParsedFilters({ ...updatedFilters, currentTab })

                window.dispatchEvent(new CustomEvent('homeFiltersChanged', { detail: updatedFilters }))
            } else {
                return
            }

            setRequestData((prevData: any) => ({
                ...prevData,
                kpiFilters: {
                    ...prevData.kpiFilters,
                    date: dayjs(updatedFilters.period).format('YYYY-MM-DD'),
                },
            }))
        },
        [currentTab, filters]
    )

    const isLoading = loadingKpiList || loadingBusinessSegmentList || loadingKpiGroups || loadingSiteList
    const allDataIsLoaded =
        updatedKpiGroupList.length > 0 && kpiList.length > 0 && dataBusinessSegmentList.length > 0 && sites.length > 0

    if (isLoading || !allDataIsLoaded) {
        return <></>
    }

    return (
        <Box sx={{ height: '100%', overflow: 'hidden', paddingTop: '5px' }}>
            <div className="align-test">
                <div>
                    <Typography variant="h3" sx={{ color: '#083D5B', marginBottom: '1rem', fontWeight: 'bold' }}>
                        {translate('MENU.HOME')}
                    </Typography>
                </div>
                <div>
                    <HomeViewFilters
                        kpiGroups={updatedKpiGroupList}
                        kpis={kpiList}
                        businessSegments={dataBusinessSegmentList}
                        sites={sites}
                        currentTab={currentTab}
                        defaultValues={parsedFilters}
                        onSubmit={applyFilters}
                        generalFilterTag={generalFilterTag}
                    />
                </div>
            </div>

            <Box sx={styles.tabWrapper}>
                <ClnTabs
                    value={currentTab}
                    tabs={tabs}
                    onChange={(_e, value) => {
                        setCurrentTab(value)
                    }}
                />
            </Box>
        </Box>
    )
}
