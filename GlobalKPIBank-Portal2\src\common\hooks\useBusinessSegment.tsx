import { gql } from '@apollo/client'
import { BusinessSegment } from '../models/business-segment'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useState, useEffect, useMemo } from 'react'

const GET_SEGMENTS_QUERY = gql`
    query getBusinessSegmentList {
        listBusinessSegment(
            first: 1000
            filter: { and: [{ isActive: { eq: true } }, { externalId: { in: ["BUS-EM", "BUS-AC"] } }] }
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                externalId
                space
                name
                description
                code
            }
        }
    }
`

export const useBusinessSegmentList = () => {
    const { data: fdmData, refetch } = useGraphqlQuery<BusinessSegment>(GET_SEGMENTS_QUERY, 'listBusinessSegment', {
        context: {
            clientName: 'assetHierarchy',
        },
    })

    const [resultData, setResultData] = useState<{ data: BusinessSegment[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData === undefined) {
            setResultData((prevState) => ({ ...prevState, loading: true }))
        } else {
            const businessSegmentOrdered = fdmData
                .filter((x) => {
                    return x.externalId !== null
                })
                .sort((a, b) => a.name.localeCompare(b.name))
            setResultData({ data: businessSegmentOrdered, loading: false })
        }
    }, [fdmData])

    return useMemo(
        () => ({
            loadingBusinessSegmentList: resultData.loading,
            refetchBusinessSegmentList: refetch,
            dataBusinessSegmentList: resultData.data,
        }),
        [resultData, refetch]
    )
}
