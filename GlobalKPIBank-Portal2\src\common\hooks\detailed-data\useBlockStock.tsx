import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import dayjs from 'dayjs'
import { BlockStockKpi } from '@/common/models/blockStock'
import { BlockStockTableView } from '@/common/models/blockStockTable'
import { setExportDateRange } from '@/common/utils/kpis-data-table-export'

const fgbFilters: string = `
    {blockStockType: {eq: "FGBS"}},
    {unitOfMeasurement: {externalId: {in: ["UOM_GRM", "UOM_KGM"]}}}
`
const gbsFilters = `
    {blockStockType: {eq: "GBS"}},
    {materialNumber: {gte: 20000000}}
    {materialNumber: {lte: 60000000}}
    {or: [{movementType: {eq: "Z95"}}, {movementType: {eq: "350"}}]}
`
const cbsFilters = `
    {blockStockType: {eq: "CBS"}},
    {materialNumber: {gte: 20000000}}
    {materialNumber: {lte: 60000000}}
    {or: [{movementType: {eq: "Z61"}}, {movementType: {eq: "Z62"}}]}
`

const refKpiBlockStock = {
    fgb: fgbFilters,
    gbs: gbsFilters,
    cbs: cbsFilters,
}

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let businessSegmentQ: string = ''
    let startDateQ: string = ''
    let endDateQ: string = ''
    let fixedFilters: string = ''
    fixedFilters = getBlockStockFilter(request.kpiFilters?.info)
    let limitQ: string = ''
    let cursorQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{manufacturingReportingSite: {externalId: {in: [${sites}]}}},`
    }

    if (request && request.kpiFilters.businessSegment !== '') {
        businessSegmentQ = `{businessSegment: {externalId: {eq: "${request.kpiFilters.businessSegment}"}}},`
    }

    if (request && request.kpiFilters.date !== '') {
        let sanitizedInitialDate = dayjs(request.kpiFilters.date).startOf('month').format('YYYY-MM-DD')
        let sanitizedEndDate = dayjs(request.kpiFilters.date).endOf('month').format('YYYY-MM-DD')
        const time = 'T00:00:00+00:00'

        if (request.exportFilters && request.exportFilters.isExport) {
            [sanitizedInitialDate, sanitizedEndDate] = setExportDateRange(
                request.exportFilters.range,
                request.kpiFilters.date
            )
        }

        startDateQ = `{postingDate: {gte: "${sanitizedInitialDate}${time}"}},`
        endDateQ = `{postingDate: {lte: "${sanitizedEndDate}${time}"}},`
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listBlockStock (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${startDateQ} ${endDateQ} ${fixedFilters}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                manufacturingReportingSite {
                    externalId
                    name
                }
                materialNumber
                blockedQuantity
                blockStockType
                unitOfMeasurement {
                    symbol
                }
                businessSegment {
                    externalId
                    description
                }
            }
        }
    }`
}

const getBlockStockFilter = (kpiInfo: string): string => {
    const kpiRef = kpiInfo.toLowerCase()
    const keys = Object.keys(refKpiBlockStock)
    const isValidKpi = keys.includes(kpiRef)

    if (isValidKpi) {
        const values = Object.values(refKpiBlockStock)
        const index = keys.indexOf(kpiRef)
        return values[index]
    } else {
        return ''
    }
}

const mapBlockStock = (data: BlockStockKpi[]): BlockStockTableView[] => {
    const mappedResult: BlockStockTableView[] = []

    if (data && data.length > 0) {
        data.map((item) => {
            const result: BlockStockTableView = {
                siteName: item.manufacturingReportingSite.name ?? '',
                materialNumber: item.materialNumber?.toString() ?? '',
                blockedQuantity: item.blockedQuantity?.toString() ?? '',
                blockStockType: item.blockStockType ?? '',
                unitOfMeasurement: item.unitOfMeasurement?.symbol ?? '',
                businessSegment: item.businessSegment?.description ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useBlockStock = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<BlockStockKpi>(gql(query), 'listBlockStock', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'qualityView',
        },
    })

    const [resultData, setResultData] = useState<{ data: BlockStockTableView[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapBlockStock(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapBlockStock(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
