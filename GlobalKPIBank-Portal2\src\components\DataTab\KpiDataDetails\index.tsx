import { Box, TableCell, TableContainer, TableRow, Tooltip, Typography } from '@mui/material'
import { useEffect, useState } from 'react'
import { translate } from '@celanese/celanese-sdk'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import dayjs from 'dayjs'
import { KpisDataViews } from '@/common/utils/kpis-data-table'
import LoaderCircular, { LoaderCircularTable } from '../../Loader'
import { useGenericDataViewHook } from '@/common/hooks/detailed-data/useGenericDataView'
import { StripedCell } from '../KpiDataTable/styles'
import { numberFormat } from '@/common/utils/numberFormat'
import { formatVariableName } from '@/common/utils/stringFormat'
import { TableVirtuoso } from 'react-virtuoso'
import { useDataChartContext } from '@/common/contexts/DataChartContext'

export default function KpiDataDetails() {
    const { requestData, selectedKpi, selectedKpiData } = useGlobalKpiContext()
    const { selectedBar, setSelectedBar } = useDataChartContext()
    const [localRequestData, setLocalRequestData] = useState(requestData)
    const {
        kpiDataModelView: result,
        pageInfoDataModelView: page,
        loadingDataModelView,
        refetchDataModelView,
        loadMoreExport: localLoadMore,
    } = useGenericDataViewHook(localRequestData)
    const [headerCells, setHeaderCells] = useState<string[]>([])
    const [headerCount, setHeaderCount] = useState<number>(0)
    const [resultLength, setResultLength] = useState<number>(0)
    const kpisModels = Object.values(KpisDataViews)
    const kpiName = selectedKpiData.name
    const kpiTabs = (kpisModels.find((kpi) => kpi.NAME === kpiName)?.TAB_NAME || []).filter(
        (tab) => tab !== 'Definition'
    )
    const [selectedView, setSelectedView] = useState<string>(kpiTabs.length > 0 ? kpiTabs[0] : '')

    useEffect(() => {
        setSelectedBar(null)
        setHeaderCells([])
        setHeaderCount(0)
    }, [selectedKpi, kpiName])

    const handleBarSelection = (bar) => {
        if (!bar || !selectedKpiData?.name) return

        const monthIndex = bar.dataPointIndex ?? null
        if (monthIndex === null) return

        const newFormattedDate = dayjs().month(monthIndex).startOf('month').format('YYYY-MM-DD')
        const selectedSiteIndex = bar.seriesIndex ?? null

        const sortedSiteList = [...requestData.kpiFilters.refSite].sort((a, b) => {
            const exception = ['STS-BIS', 'STS-BCH']
            if (exception.includes(a) && exception.includes(b)) {
                return a === 'STS-BIS' ? -1 : 1
            }

            return a.localeCompare(b, 'en-US', { sensitivity: 'base' })
        })
        const selectedSiteId = sortedSiteList[selectedSiteIndex]

        setLocalRequestData((prev) => ({
            ...prev,
            kpiFilters: {
                ...prev.kpiFilters,
                date: newFormattedDate,
                refSite: [selectedSiteId],
            },
            cursor: '',
        }))

        refetchDataModelView()
    }

    useEffect(() => {
        if (selectedBar) {
            handleBarSelection(selectedBar)
        }
    }, [selectedBar?.dataPointIndex, selectedBar?.seriesIndex, selectedKpiData?.name])

    useEffect(() => {
        setLocalRequestData(requestData)
    }, [requestData])

    useEffect(() => {
        if (result && result.length > 0 && page?.endCursor) {
            const headerCells = Object.keys(result[0]).map((columnName) => formatVariableName(columnName))
            setHeaderCells(headerCells)
            setHeaderCount(headerCells.length)
        }
    }, [result])

    useEffect(() => {
        setResultLength(result.length)
    }, [selectedBar, result])

    useEffect(() => {
        if (!kpiTabs.includes(selectedView)) {
            setSelectedView(kpiTabs.length > 0 ? kpiTabs[0] : '')
        }
    }, [kpiTabs, selectedView])

    const LoadingFooter = () => {
        return (
            <TableRow>
                <TableCell align="center" colSpan={headerCount}>
                    <LoaderCircularTable
                        sxProps={{ width: '100%', display: 'flex', justifyContent: 'center !important' }}
                    />
                </TableCell>
            </TableRow>
        )
    }

    const kpiIsOEE = requestData?.kpiFilters?.kpiName === 'Overall Equipment Effectiveness'

    if (!selectedBar || kpiIsOEE) return null
    return (
        <Box
            sx={{
                display: 'flex',
                width: '100%',
                gap: '1rem',
                flexDirection: 'column',
            }}
        >
            {result.length === 0 && headerCells.length === 0 && loadingDataModelView ? (
                LoaderCircular()
            ) : (
                <Box sx={{ height: `calc(130px + (130px * ${resultLength}))`, maxHeight: '420px', overflow: 'hidden' }}>
                    <TableContainer
                        sx={{
                            overflow: 'auto',
                            height: '100%',
                        }}
                    >
                        {result.length === 0 && headerCells?.length === 0 && !loadingDataModelView ? (
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontWeight: 'bold',
                                    fontSize: '1.5rem',
                                    textAlign: 'center',
                                    margin: '2rem',
                                }}
                            >
                                {translate('DATA_TAB.NO_DATA')}
                            </Typography>
                        ) : (
                            <TableVirtuoso
                                data={result}
                                endReached={() => {
                                    if (page.endCursor && page.endCursor !== '') {
                                        setLocalRequestData((prevData) => ({
                                            ...prevData,
                                            cursor: page.endCursor,
                                        }))

                                        localLoadMore()
                                    }
                                }}
                                fixedFooterContent={() => (loadingDataModelView ? <LoadingFooter /> : null)}
                                components={{
                                    Table: ({ style, ...props }) => (
                                        <table {...props} style={{ ...style, width: '100%' }} />
                                    ),
                                    TableFoot: ({ style, ...props }) => (
                                        <tfoot {...props} style={{ ...style, position: 'relative' }} />
                                    ),
                                }}
                                fixedHeaderContent={() => (
                                    <TableRow>
                                        {headerCells.map((columnName, index) => (
                                            <TableCell
                                                key={index}
                                                align="center"
                                                sx={{
                                                    maxWidth: '85px',
                                                    fontWeight: 'bold',
                                                    backgroundColor: '#ffffff',
                                                }}
                                            >
                                                {columnName}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                )}
                                itemContent={(rowIndex: number, row: any) => {
                                    return (
                                        <>
                                            {Object.keys(row).map((key, cellIndex) => (
                                                <StripedCell
                                                    index={rowIndex}
                                                    key={cellIndex}
                                                    align="center"
                                                    sx={{
                                                        maxWidth: '120px',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                    }}
                                                >
                                                    {!row[key] || row[key] === '' ? (
                                                        '--'
                                                    ) : (
                                                        <Tooltip title={row[key]} arrow>
                                                            <span style={{ display: 'inline-block' }}>
                                                                {typeof row[key] === 'number' && key !== 'year'
                                                                    ? numberFormat(row[key])
                                                                    : row[key]}
                                                            </span>
                                                        </Tooltip>
                                                    )}
                                                </StripedCell>
                                            ))}
                                        </>
                                    )
                                }}
                            ></TableVirtuoso>
                        )}
                    </TableContainer>
                </Box>
            )}
        </Box>
    )
}
