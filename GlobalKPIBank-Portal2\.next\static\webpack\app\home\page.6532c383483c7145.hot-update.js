"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/components/Home/HomeViewGeneral/WidgetGeneralTable.tsx":
/*!********************************************************************!*\
  !*** ./src/components/Home/HomeViewGeneral/WidgetGeneralTable.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WidgetGeneralTable: function() { return /* binding */ WidgetGeneralTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _common_utils_stripedRowTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/common/utils/stripedRowTable */ \"(app-pages-browser)/./src/common/utils/stripedRowTable.ts\");\n/* harmony import */ var _common_utils_numberFormat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/common/utils/numberFormat */ \"(app-pages-browser)/./src/common/utils/numberFormat.ts\");\n/* harmony import */ var _common_contexts_GlobalKpiContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/common/contexts/GlobalKpiContext */ \"(app-pages-browser)/./src/common/contexts/GlobalKpiContext.tsx\");\n/* harmony import */ var _common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/common/utils/targetCalculator */ \"(app-pages-browser)/./src/common/utils/targetCalculator.ts\");\n/* harmony import */ var _celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @celanese/celanese-sdk */ \"(app-pages-browser)/./node_modules/@celanese/celanese-sdk/build/esm/index.ts\");\n/* harmony import */ var _common_utils_kpiCalculator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/common/utils/kpiCalculator */ \"(app-pages-browser)/./src/common/utils/kpiCalculator.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst WidgetGeneralTable = (param)=>{\n    let { data, target, filters, generalFilter, generalViewMore, setOpenModal, setSelectedKpi } = param;\n    _s();\n    const { setSelectedSiteId, setSelectedMultipleSiteIds } = (0,_common_contexts_GlobalKpiContext__WEBPACK_IMPORTED_MODULE_4__.useGlobalKpiContext)();\n    const [sanitizedData, setSanitizedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getTargetValue = (kpi)=>{\n        const targetObject = getTargetObject(kpi);\n        if (!targetObject || targetObject.length === 0 || !targetObject[0]) {\n            return 0;\n        }\n        if (filters.siteList.length === 1) {\n            return (0,_common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__.calculateSingleTarget)(kpi.kpi.externalId, targetObject[0], generalFilter);\n        } else {\n            return (0,_common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__.calculateMultiTarget)(kpi, targetObject, generalFilter);\n        }\n    };\n    const getTargetObject = (kpi)=>{\n        if (!target || target.length === 0) {\n            return [];\n        }\n        if (filters.siteList.length === 1) {\n            const matchedKpi = target.filter((item)=>item && item.refGlobalVisionKPI && item.refGlobalVisionKPI.externalId === kpi.kpi.externalId);\n            const matchedSite = matchedKpi === null || matchedKpi === void 0 ? void 0 : matchedKpi.filter((item)=>item && item.refReportingSite && kpi.data && kpi.data[0] && kpi.data[0].refReportingSite && item.refReportingSite.externalId === kpi.data[0].refReportingSite.externalId);\n            return matchedSite || [];\n        } else {\n            const matchedKpi = target.filter((item)=>item && item.refGlobalVisionKPI && item.refGlobalVisionKPI.externalId === kpi.kpi.externalId);\n            const matchedSites = matchedKpi.filter((item)=>item && item.refReportingSite && filters.siteList.includes(item.refReportingSite.externalId));\n            return matchedSites || [];\n        }\n    };\n    const isRedKpi = (kpi)=>{\n        var _currentTarget_;\n        const currentTarget = getTargetObject(kpi);\n        let targetValue = 0;\n        let kpiValue = 0;\n        if (!currentTarget || currentTarget.length === 0 || !currentTarget[0]) {\n            return false;\n        }\n        if (filters.siteList.length === 1) {\n            targetValue = (0,_common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__.calculateSingleTarget)(kpi.kpi.externalId, currentTarget[0], generalFilter);\n            kpiValue = (0,_common_utils_kpiCalculator__WEBPACK_IMPORTED_MODULE_7__.getKpiValue)(kpi, filters.period, generalFilter);\n        } else {\n            targetValue = (0,_common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__.calculateMultiTarget)(kpi, currentTarget, generalFilter);\n            kpiValue = (0,_common_utils_kpiCalculator__WEBPACK_IMPORTED_MODULE_7__.getKpiValue)(kpi, filters.period, generalFilter);\n        }\n        return ((_currentTarget_ = currentTarget[0]) === null || _currentTarget_ === void 0 ? void 0 : _currentTarget_.rangeDirection) === \"Above\" ? kpiValue < targetValue : kpiValue > targetValue;\n    };\n    const widgetClick = (kpi)=>{\n        setOpenModal(true);\n        setSelectedKpi(kpi);\n        if (kpi.data.length === 1) {\n            setSelectedSiteId(kpi.data[0].refReportingSite.externalId);\n            setSelectedMultipleSiteIds([]);\n        } else {\n            setSelectedSiteId(\"\");\n            setSelectedMultipleSiteIds(filters.siteList);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (data && data.length > 0) {\n            try {\n                data.map((kpi)=>{\n                    try {\n                        kpi.redKpi = isRedKpi(kpi);\n                    } catch (error) {\n                        kpi.redKpi = false;\n                    }\n                });\n                const mappedResult = [\n                    ...data\n                ];\n                mappedResult.sort((a, b)=>{\n                    if (a.redKpi === b.redKpi) {\n                        const orderA = a.kpi.order || 0;\n                        const orderB = b.kpi.order || 0;\n                        return orderA - orderB;\n                    }\n                    return a.redKpi ? -1 : 1;\n                });\n                setSanitizedData(mappedResult);\n                console.log(\"**********\", mappedResult);\n            } catch (error) {\n                setSanitizedData([\n                    ...data\n                ]);\n                console.log(\"**********\", [\n                    ...data\n                ]);\n            }\n        } else {\n            setSanitizedData([]);\n        }\n    }, [\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        sx: {\n            overflowX: \"initial\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    sx: {\n                        position: \"sticky\",\n                        top: generalViewMore ? \"45.97px\" : 0,\n                        backgroundColor: \"#ffffff\",\n                        fontWeight: \"bold\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                align: \"left\",\n                                sx: {\n                                    width: \"60%\",\n                                    padding: \"8px 16px\"\n                                },\n                                children: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_6__.translate)(\"TABLE_COLS.KPI\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                align: \"right\",\n                                sx: {\n                                    width: \"20%\",\n                                    padding: \"8px 16px\"\n                                },\n                                children: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_6__.translate)(\"TABLE_COLS.TARGET\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                align: \"right\",\n                                sx: {\n                                    width: \"20%\",\n                                    padding: \"8px 16px\"\n                                },\n                                children: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_6__.translate)(\"TABLE_COLS.ACTUAL\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    children: sanitizedData.map((kpi, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_utils_stripedRowTable__WEBPACK_IMPORTED_MODULE_2__.StripedRow, {\n                            index: index,\n                            sx: {\n                                borderBottom: kpi.redKpi ? \"1px solid #D32F2F\" : \"1px solid rgba(224, 224, 224, 1)\",\n                                backgroundColor: kpi.redKpi ? \"#FCF1EE !important\" : \"inherit\"\n                            },\n                            onClick: ()=>widgetClick(kpi),\n                            style: {\n                                cursor: \"pointer\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    align: \"left\",\n                                    sx: {\n                                        padding: \"8px 16px\",\n                                        borderBottom: \"unset\",\n                                        width: \"60%\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            fontWeight: \"500\",\n                                            color: kpi.redKpi ? \"#D32F2F\" : \"inherit\"\n                                        },\n                                        children: kpi.kpi.name + (kpi.kpi.symbol != null && kpi.kpi.symbol != \"\" ? \" (\" + kpi.kpi.symbol + \")\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    align: \"right\",\n                                    sx: {\n                                        width: \"20%\",\n                                        color: kpi.redKpi ? \"#D32F2F\" : \"#1F8248\",\n                                        fontWeight: \"600\",\n                                        borderBottom: \"inherit\",\n                                        padding: \"8px 16px\"\n                                    },\n                                    children: (()=>{\n                                        try {\n                                            return (0,_common_utils_numberFormat__WEBPACK_IMPORTED_MODULE_3__.numberFormat)(getTargetValue(kpi));\n                                        } catch (error) {\n                                            return \"-\";\n                                        }\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    align: \"right\",\n                                    sx: {\n                                        width: \"20%\",\n                                        color: kpi.redKpi ? \"#D32F2F\" : \"#1F8248\",\n                                        fontWeight: \"600\",\n                                        borderBottom: \"inherit\",\n                                        padding: \"8px 16px\"\n                                    },\n                                    children: (()=>{\n                                        try {\n                                            return (0,_common_utils_numberFormat__WEBPACK_IMPORTED_MODULE_3__.numberFormat)((0,_common_utils_kpiCalculator__WEBPACK_IMPORTED_MODULE_7__.getKpiValue)(kpi, filters.period, generalFilter));\n                                        } catch (error) {\n                                            return \"-\";\n                                        }\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 25\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n            lineNumber: 142,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n        lineNumber: 141,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WidgetGeneralTable, \"osYg/5hMiVxq8aTFhiwtaDWu0BY=\", false, function() {\n    return [\n        _common_contexts_GlobalKpiContext__WEBPACK_IMPORTED_MODULE_4__.useGlobalKpiContext\n    ];\n});\n_c = WidgetGeneralTable;\nvar _c;\n$RefreshReg$(_c, \"WidgetGeneralTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Home/HomeViewGeneral/WidgetGeneralTable.tsx\n"));

/***/ })

});