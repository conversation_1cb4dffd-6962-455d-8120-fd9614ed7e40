import { useState, useEffect, useMemo, useCallback } from 'react'
import { useGlobalVisionKPISite } from './useGlobalVisionKPISite'
import { useGlobalVisionKPISegment } from './useGlobalVisionKPISegment'
import { GlobalVisionKpiTableModel } from '../models/globalVisionKpiTableModel'
import { KpiGroup } from '../models/kpiGroup'
import { PageNames } from '../utils/page-names'

interface UsePaginatedKpiDataProps {
    filters: any
    tabGroup: KpiGroup | undefined
    batchSize?: number
    debounceDelay?: number
}

interface PaginatedKpiData {
    siteData: GlobalVisionKpiTableModel[]
    segmentData: GlobalVisionKpiTableModel[]
    loading: boolean
    loadingProgress: number
    error?: string
    hasMore: boolean
    loadMore: () => void
    refetch: () => void
}

// Debounce utility
const useDebounce = (value: any, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value)

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value)
        }, delay)

        return () => clearTimeout(handler)
    }, [value, delay])

    return debouncedValue
}

// Utility to chunk array into smaller batches
const chunkArray = <T,>(array: T[], size: number): T[][] => {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size))
    }
    return chunks
}

export const usePaginatedKpiData = ({
    filters,
    tabGroup,
    batchSize = 10,
    debounceDelay = 500,
}: UsePaginatedKpiDataProps): PaginatedKpiData => {
    const debouncedFilters = useDebounce(filters, debounceDelay)

    const [siteData, setSiteData] = useState<GlobalVisionKpiTableModel[]>([])
    const [segmentData, setSegmentData] = useState<GlobalVisionKpiTableModel[]>([])
    const [currentBatch, setCurrentBatch] = useState(0)
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string>()
    const [siteBatches, setSiteBatches] = useState<string[][]>([])
    const [loadedBatches, setLoadedBatches] = useState<Set<number>>(new Set())

    // Create batches from site list
    useEffect(() => {
        if (debouncedFilters?.siteList?.length > 0) {
            const batches = chunkArray(debouncedFilters.siteList as string[], batchSize)
            setSiteBatches(batches)
            setCurrentBatch(0)
            setLoadedBatches(new Set())
            setSiteData([])
            setSegmentData([])
        }
    }, [debouncedFilters?.siteList, batchSize])

    // Progress calculation
    const loadingProgress = useMemo(() => {
        if (siteBatches.length === 0) return 0
        return Math.round((loadedBatches.size / siteBatches.length) * 100)
    }, [siteBatches.length, loadedBatches.size])

    // Current batch sites
    const currentSites = useMemo(() => {
        if (siteBatches.length === 0 || currentBatch >= siteBatches.length) return []
        return siteBatches.slice(0, currentBatch + 1).flat()
    }, [siteBatches, currentBatch])

    // Request configuration for current batch
    const baseRequest = useMemo(
        () => ({
            site: currentSites,
            kpiGroup: tabGroup !== undefined && tabGroup.name !== 'General' ? tabGroup.name : '',
            page: PageNames.HOME_VIEW.NAME,
            skip: currentSites.length === 0,
        }),
        [currentSites, tabGroup]
    )

    const segmentRequest = useMemo(
        () => ({
            ...baseRequest,
            businessSegment: debouncedFilters?.businessSegment?.length
                ? debouncedFilters.businessSegment[0].externalId
                : [],
        }),
        [baseRequest, debouncedFilters]
    )

    // Site data hooks for all granularities
    const siteHooks = {
        MON: useGlobalVisionKPISite({ ...baseRequest, granularity: 'MON' }),
        QRT: useGlobalVisionKPISite({ ...baseRequest, granularity: 'QRT' }),
        ANL: useGlobalVisionKPISite({ ...baseRequest, granularity: 'ANL' }),
    }

    // Segment data hooks for all granularities
    const segmentHooks = {
        MON: useGlobalVisionKPISegment({ ...segmentRequest, granularity: 'MON' }),
        QRT: useGlobalVisionKPISegment({ ...segmentRequest, granularity: 'QRT' }),
        ANL: useGlobalVisionKPISegment({ ...segmentRequest, granularity: 'ANL' }),
    }

    // Update loading state
    useEffect(() => {
        const isLoading =
            Object.values(siteHooks).some((hook) => hook.loadingGlobalVisionKPI) ||
            Object.values(segmentHooks).some((hook) => hook.loadingGlobalVisionKPI)
        setLoading(isLoading)
    }, [siteHooks, segmentHooks])

    // Update site data when hooks complete
    useEffect(() => {
        const monthData = siteHooks.MON.dataGlobalVisionKPI
        const quarterData = siteHooks.QRT.dataGlobalVisionKPI
        const annualData = siteHooks.ANL.dataGlobalVisionKPI

        if (monthData?.length > 0 && quarterData?.length > 0 && annualData?.length > 0) {
            try {
                // Simple merge logic - you might want to use your existing mergeSiteValues function
                const mergedData = monthData.map((monthItem, index) => ({
                    ...monthItem,
                    quarterData: quarterData[index],
                    annualData: annualData[index],
                }))

                setSiteData(mergedData)
                setLoadedBatches((prev) => new Set([...prev, currentBatch]))
                setError(undefined)
            } catch (err) {
                setError('Error processing site data')
                console.error('Error processing site data:', err)
            }
        }
    }, [
        siteHooks.MON.dataGlobalVisionKPI,
        siteHooks.QRT.dataGlobalVisionKPI,
        siteHooks.ANL.dataGlobalVisionKPI,
        currentBatch,
    ])

    // Update segment data when hooks complete
    useEffect(() => {
        const monthData = segmentHooks.MON.dataGlobalVisionKPI
        const quarterData = segmentHooks.QRT.dataGlobalVisionKPI
        const annualData = segmentHooks.ANL.dataGlobalVisionKPI

        if (monthData?.length > 0 && quarterData?.length > 0 && annualData?.length > 0) {
            try {
                // Simple merge logic - you might want to use your existing mergeSiteValues function
                const mergedData = monthData.map((monthItem, index) => ({
                    ...monthItem,
                    quarterData: quarterData[index],
                    annualData: annualData[index],
                }))

                setSegmentData(mergedData)
                setError(undefined)
            } catch (err) {
                setError('Error processing segment data')
                console.error('Error processing segment data:', err)
            }
        }
    }, [
        segmentHooks.MON.dataGlobalVisionKPI,
        segmentHooks.QRT.dataGlobalVisionKPI,
        segmentHooks.ANL.dataGlobalVisionKPI,
    ])

    // Load more function
    const loadMore = useCallback(() => {
        if (currentBatch < siteBatches.length - 1 && !loading) {
            setCurrentBatch((prev) => prev + 1)
        }
    }, [currentBatch, siteBatches.length, loading])

    // Has more data to load
    const hasMore = useMemo(() => {
        return currentBatch < siteBatches.length - 1
    }, [currentBatch, siteBatches.length])

    // Refetch function
    const refetch = useCallback(() => {
        setCurrentBatch(0)
        setLoadedBatches(new Set())
        setSiteData([])
        setSegmentData([])
        setError(undefined)

        // Refetch all hooks
        Object.values(siteHooks).forEach((hook) => hook.refetchGlobalVisionKPI())
        Object.values(segmentHooks).forEach((hook) => hook.refetchGlobalVisionKPI())
    }, [siteHooks, segmentHooks])

    // Auto-load first batch
    useEffect(() => {
        if (siteBatches.length > 0 && loadedBatches.size === 0 && !loading) {
            // First batch will be loaded automatically by the hooks
        }
    }, [siteBatches, loadedBatches.size, loading])

    return {
        siteData,
        segmentData,
        loading,
        loadingProgress,
        error,
        hasMore,
        loadMore,
        refetch,
    }
}
