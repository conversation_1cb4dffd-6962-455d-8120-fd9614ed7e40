import { gql } from '@apollo/client'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { useEffect, useState } from 'react'
import { User } from '../models/user'

export interface CDFUsersQueryRequest {
    emails?: string[]
    userExternalIds?: string[]
    nextPage?: string
}

const buildCDFUsersQuery = (request: CDFUsersQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.emails?.length) {
        filter.push(`{ email: { in: [${request.emails.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.userExternalIds?.length) {
        filter.push(
            `{
                externalId: {
                    in: [${request.userExternalIds.map((e) => `"${e}"`).join(',')}]
                }
            }`
        )
    }

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
    query GetCDFUsers {
        listCDF_User(
            filter: ${queryFilter}
        , first: 1000, after: ${request.nextPage ? `"${request.nextPage}"` : 'null'}) {
          items {
            externalId
            space
            name
            email
          }
          pageInfo {
            hasPreviousPage
            hasNextPage
            startCursor
            endCursor
          }
        }
      }
    `

    return query
}

export const useCDFUser = (request: CDFUsersQueryRequest) => {
    const query = buildCDFUsersQuery(request)

    const { data: fdmData, pageInfo } = useGraphqlQuery<User>(gql(query), 'listCDF_User', {})

    const [resultData, setResultData] = useState<{
        data: User[]
        loading: boolean
        pagging: boolean
        pageInfo: PageInfo
    }>({
        data: [],
        loading: true,
        pagging: false,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false, pageInfo, pagging: false })
        } else {
            setResultData({ data: fdmData, loading: false, pageInfo, pagging: Boolean(request.nextPage) })
        }
    }, [fdmData, pageInfo])

    return {
        loading: resultData.loading,
        users: resultData.data,
        pageInfo: resultData.pageInfo,
        pagging: resultData.pagging,
    }
}
