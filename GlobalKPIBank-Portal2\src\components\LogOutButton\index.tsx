import { translate } from '@celanese/celanese-sdk'
import { useMsal } from '@azure/msal-react'
import { MatIcon } from '@celanese/ui-lib'
import { Box, Typography } from '@mui/material'

export const LogOutButton = () => {
    const { instance: msalInstance } = useMsal()

    function signOutClickHandler() {
        msalInstance.logoutRedirect()
    }

    return (
        <Box
            sx={{
                fontSize: '12px',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                cursor: 'pointer',
            }}
            onClick={() => signOutClickHandler()}
        >
            <MatIcon
                icon="logout"
                fontSize="12px"
                sx={{
                    fontSize: '12px',
                    marginRight: '5px',
                }}
            />
            <Typography
                sx={{
                    fontSize: 12,
                }}
            >
                {translate('COMMONBUTTON.LOG_OUT')}
            </Typography>
        </Box>
    )
}

export default LogOutButton
