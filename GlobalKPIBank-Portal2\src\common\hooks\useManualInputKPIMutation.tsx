import { ArrayEntity, Entity, ExternalEntity } from '../models'
import { KPICategory } from '../models/manualInputKpi'
import { Site } from '../models/site'
import { useFdmMutation } from './useFdmMutation'

interface MutationDataResult extends ExternalEntity {
    createdTime: number
    instanceType: string
    lastUpdatedTime: number
    version: number
    wasModified: boolean
}

export interface ManualInputKpiMutation extends Entity {
    description: string
    endDate: string
    kpiValue: number
    refKPICatogory: KPICategory
    refSite: Site
    startDate: string
    period: string
    inputType: string
}

export const useManualInputKpiMutation = () => {
    const [mutationFn] = useFdmMutation<ManualInputKpiMutation>('KPIManualInput', undefined, false)

    const updateManualInputMutationFn = async (entity: ManualInputKpiMutation[], space: string) => {
        const updateKpiTargetMutationResult = await mutationFn(entity, [], undefined, space, undefined, undefined)

        return {
            ok: updateKpiTargetMutationResult.ok,
            data: updateKpiTargetMutationResult.data as unknown as ArrayEntity<MutationDataResult>,
            error: updateKpiTargetMutationResult.error,
        }
    }

    return { updateManualInputMutationFn } as const
}
