import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { useGlobalVisionKPISegment } from '@/common/hooks/useGlobalVisionKPISegment'
import { useGlobalVisionKPISite } from '@/common/hooks/useGlobalVisionKPISite'
import { useGlobalVisionKPITarget } from '@/common/hooks/useGlobalVisionKPITarget'
import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import { KpiGroup } from '@/common/models/kpiGroup'
import { FinancialKpisDom } from '@/common/utils/financial-kpis'
import { FoundationalKpisKpidom } from '@/common/utils/foundational-kpis'
import { PageNames } from '@/common/utils/page-names'
import { translate } from '@celanese/celanese-sdk'
import { ClnToggle, MatIcon } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import '@material-symbols/font-400/outlined.css'
import WarningSharpIcon from '@mui/icons-material/WarningSharp'
import { Box, Button, ButtonGroup, Grid, Tooltip, Typography, styled } from '@mui/material'
import { useEffect, useMemo, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'
import { DataTab } from '../../DataTab'
import { LoaderCircular } from '../../Loader'
import dayjs from 'dayjs'
import { mergeSiteValues } from '@/common/utils/kpiSitesValueMerger'

interface SiteViewTabProps {
    tabGroup?: KpiGroup
    filters?: any
}

const siteViewFilterSchema = z.object({
    statusFilter: z.string(),
})

type SiteViewFilterSchema = z.infer<typeof siteViewFilterSchema>

const Form = styled('form')({})

const styles = {
    topWrapper: {
        display: 'flex',
        justifyContent: 'space-between',
    },
    tabWrapperLeft: {
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: '8px',
        width: '100%',
        height: 'calc(100vh - 230px)',
        padding: '30px',
        overflow: 'auto',
    },
    tabWrapperRight: {
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: '8px',
        width: '100%',
        height: 'calc(100vh - 230px)',
        padding: '30px',
    },
}

const statusFilterOptions = ['All', 'Critical']

export const SiteViewTab: React.FC<SiteViewTabProps> = ({ tabGroup, filters }) => {
    const {
        setSelectedKpiData,
        setDoesKPIHasBusinessSegmentFilter,
        isBusinessSegmentFilterActive,
        selectedKpi,
        setSelectedKpi,
        selectedMultipleSiteIds,
        setSelectedMultipleSiteIds,
        setIsProductivityKpi,
        setIsFutureProductivityKpi,
    } = useGlobalKpiContext()

    const [filteredKpis, setFilteredKpis] = useState<GlobalVisionKpiTableModel[]>([])
    const filteredKpisRef = useRef<Number>(0)
    const [loadingKpis, setLoadingKpis] = useState<boolean>(true)
    const [selectedStatus, setSelectedStatus] = useState<string>('All')
    const [selectedKpiButton, setSelectedKpiButton] = useState<number>(null)
    const [range, setRange] = useState<number>()
    const { checkPermissionsFromRoutes } = useAuthGuard()

    const getGlobalVisionKpiTargetRequest = useMemo(
        () => ({
            skip: !filters,
            site: filters?.siteList ?? [],
            kpiGroupExternalId: tabGroup != undefined ? tabGroup.externalId : '',
            year: new Date().getFullYear(),
        }),
        [filters, tabGroup]
    )
    const { dataGlobalVisionKPITarget: kpiTargetsData, loadingGlobalVisionKPITarget } = useGlobalVisionKPITarget(
        getGlobalVisionKpiTargetRequest
    )

    const getGlobalVisionKpisSiteRequest = useMemo(
        () => ({
            skip: !filters,
            site: filters?.siteList ?? [],
            kpiGroup: tabGroup != undefined ? tabGroup.name : '',
            page: PageNames.SITE_VIEW.NAME,
            granularity: 'MON',
            date: dayjs(filters?.period).year().toString(),
        }),
        [filters, tabGroup]
    )
    const {
        dataGlobalVisionKPI: globalViewDataMonth,
        refetchGlobalVisionKPI,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISite,
    } = useGlobalVisionKPISite({ ...getGlobalVisionKpisSiteRequest, granularity: 'MON' })
    const { dataGlobalVisionKPI: globalViewDataQuarter, loadingGlobalVisionKPI: loadingGlobalVisionKPISiteQuarter } =
        useGlobalVisionKPISite({ ...getGlobalVisionKpisSiteRequest, granularity: 'QRT' })
    const { dataGlobalVisionKPI: globalViewDataAnnual, loadingGlobalVisionKPI: loadingGlobalVisionKPISiteAnnual } =
        useGlobalVisionKPISite({ ...getGlobalVisionKpisSiteRequest, granularity: 'ANL' })
    const [globalViewData, setGlobalViewData] = useState([])

    useEffect(() => {
        if (globalViewDataMonth.length > 0 && globalViewDataQuarter.length > 0 && globalViewDataAnnual.length > 0) {
            const allIds = new Set<string>()
            globalViewDataMonth.forEach((item) => allIds.add(item.kpi.externalId))
            globalViewDataQuarter.forEach((item) => allIds.add(item.kpi.externalId))
            globalViewDataAnnual.forEach((item) => allIds.add(item.kpi.externalId))

            const merged = mergeSiteValues(
                allIds,
                globalViewDataMonth,
                globalViewDataQuarter,
                globalViewDataAnnual,
                filters.siteList.length
            )
            const result = merged.sort((a, b) => a.kpi.order - b.kpi.order)
            setGlobalViewData(result)
        }
    }, [globalViewDataMonth, globalViewDataQuarter, globalViewDataAnnual])

    const getGlobalVisionKpiSegmentRequest = useMemo(
        () => ({
            skip: !filters,
            site: filters?.siteList ?? [],
            businessSegment: filters?.businessSegment?.length ? filters.businessSegment[0].externalId : [],
            kpiGroup: tabGroup != undefined ? tabGroup.name : '',
            page: PageNames.SITE_VIEW.NAME,
            granularity: 'MON',
        }),
        [filters, tabGroup]
    )
    const {
        dataGlobalVisionKPI: globalViewDataSegmentMonth,
        refetchGlobalVisionKPI: refetchGlobalVisionKPISegment,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISegment,
    } = useGlobalVisionKPISegment({ ...getGlobalVisionKpiSegmentRequest, granularity: 'MON' })
    const {
        dataGlobalVisionKPI: globalViewDataSegmentQuarter,
        refetchGlobalVisionKPI: refetchGlobalVisionKPISegmentQuarter,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISegmentQuarter,
    } = useGlobalVisionKPISegment({ ...getGlobalVisionKpiSegmentRequest, granularity: 'QRT' })
    const {
        dataGlobalVisionKPI: globalViewDataSegmentAnnual,
        refetchGlobalVisionKPI: refetchGlobalVisionKPISegmentAnnual,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISegmentAnnual,
    } = useGlobalVisionKPISegment({ ...getGlobalVisionKpiSegmentRequest, granularity: 'ANL' })
    const [globalViewDataSegment, setGlobalViewDataSegment] = useState<GlobalVisionKpiTableModel[]>([])

    useEffect(() => {
        if (
            globalViewDataSegmentMonth.length > 0 &&
            globalViewDataSegmentQuarter.length > 0 &&
            globalViewDataSegmentAnnual.length > 0
        ) {
            const allIds = new Set<string>()
            globalViewDataSegmentMonth.forEach((item) => allIds.add(item.kpi.externalId))
            globalViewDataSegmentQuarter.forEach((item) => allIds.add(item.kpi.externalId))
            globalViewDataSegmentAnnual.forEach((item) => allIds.add(item.kpi.externalId))

            const merged = mergeSiteValues(
                allIds,
                globalViewDataSegmentMonth,
                globalViewDataSegmentQuarter,
                globalViewDataSegmentAnnual,
                filters.siteList.length
            )
            const result = merged.sort((a, b) => a.kpi.order - b.kpi.order)
            setGlobalViewDataSegment(result)
        }
    }, [globalViewDataSegmentMonth, globalViewDataSegmentQuarter, globalViewDataSegmentAnnual])

    useEffect(() => {
        FoundationalKpisKpidom.FLAWLESS_DAYS.EXTERNAL_ID == selectedKpi ||
        FoundationalKpisKpidom.KG_PER_HEADCOUNT.EXTERNAL_ID == selectedKpi
            ? setDoesKPIHasBusinessSegmentFilter(false)
            : setDoesKPIHasBusinessSegmentFilter(true)

        FoundationalKpisKpidom.PRODUCTIVITY_YEAR_FORECAST.EXTERNAL_ID == selectedKpi ||
        FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_NEXT_YEAR.EXTERNAL_ID == selectedKpi ||
        FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_PLUS_TWO_YEARS.EXTERNAL_ID == selectedKpi
            ? setIsProductivityKpi(true)
            : setIsProductivityKpi(false)

        FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_NEXT_YEAR.EXTERNAL_ID == selectedKpi ||
        FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_PLUS_TWO_YEARS.EXTERNAL_ID == selectedKpi
            ? setIsFutureProductivityKpi(true)
            : setIsFutureProductivityKpi(false)
    }, [selectedKpi])

    useEffect(() => {
        if (loadingGlobalVisionKPISite || loadingGlobalVisionKPITarget) return
        if (globalViewData.length === 0 || kpiTargetsData.length === 0) return

        let multipleSitesTargets = kpiTargetsData.reduce((acc, current) => {
            if (!current.refGlobalVisionKPI.order) return

            if (selectedMultipleSiteIds.length === 1) {
                acc.push(current)
            } else {
                const index = current.refGlobalVisionKPI.order - 1
                if (!acc[index]) {
                    acc[index] = []
                }
                acc[index].push(current)
            }
            return acc
        }, [])
        multipleSitesTargets = multipleSitesTargets.filter((item) => item)

        globalViewData.filter(removeForecastCost)
        globalViewData.filter(removeForecastProduction)
        globalViewData.filter(removeActualCost)
        globalViewData.filter(removeActualProduction)

        const aux = Array.isArray(selectedMultipleSiteIds) ? selectedMultipleSiteIds : [selectedMultipleSiteIds]
        setSelectedMultipleSiteIds(aux)
        const kpiMonthsValues = aux.map((site) =>
            globalViewData.flatMap((kpi) =>
                kpi.data
                    .filter((data) => data.refReportingSite.externalId === site)
                    .map(
                        ({
                            january,
                            february,
                            march,
                            april,
                            may,
                            june,
                            july,
                            august,
                            september,
                            october,
                            november,
                            december,
                            ...data
                        }) => {
                            return [
                                january,
                                february,
                                march,
                                april,
                                may,
                                june,
                                july,
                                august,
                                september,
                                october,
                                november,
                                december,
                            ]
                        }
                    )
            )
        )

        const currentMonth = dayjs().month()
        const groupedResult = []
        multipleSitesTargets.map((target, index) => {
            const groupedValidation = []
            if (selectedMultipleSiteIds.length === 1) {
                const sumTarget = Number(target.value) * currentMonth
                const closedMonthsArrays = kpiMonthsValues[0] || []
                const closedMonths = closedMonthsArrays[index]?.slice(0, currentMonth)
                const sumValues = closedMonths.reduce(
                    (acc, monthValue) => acc + (isNaN(Number(monthValue)) ? 0 : Number(monthValue)),
                    0
                )

                const isCritical =
                    (target.rangeDirection === 'Above' && Number(sumValues) < sumTarget) ||
                    (target.rangeDirection === 'Below' && Number(sumValues) > sumTarget)

                groupedValidation.push(!isCritical)
            } else if (selectedMultipleSiteIds.length > 1) {
                let totalSumValues = 0
                const targetArray = Array.isArray(target) ? target : [target]

                targetArray.map((kpi, indexSite) => {
                    const sumTarget = Number(kpi.value) * currentMonth
                    const closedMonths = kpiMonthsValues[indexSite][index]?.slice(0, currentMonth)
                    const sumValues = closedMonths
                        ? closedMonths?.reduce((acc, monthValue) => acc + Number(monthValue), 0)
                        : 0

                    totalSumValues += sumValues

                    const isCritical =
                        (kpi.rangeDirection === 'Above' && totalSumValues < sumTarget) ||
                        (kpi.rangeDirection === 'Below' && totalSumValues > sumTarget)

                    groupedValidation.push(!isCritical)
                })
            }
            groupedValidation.includes(true) ? groupedResult.push(true) : groupedResult.push(false)
        })

        const filteredKpis = globalViewData.filter((value, index) => {
            if (selectedStatus === statusFilterOptions[0]) {
                value.redKpi = groupedResult[index]
                return value
            }
            if (selectedStatus === statusFilterOptions[1] && groupedResult[index] === false) {
                value.redKpi = groupedResult[index]
                return value
            }
        })

        function removeForecastCost(value: GlobalVisionKpiTableModel, index: number, array: any) {
            if (value.kpi.externalId === FoundationalKpisKpidom.FORECAST_COST.EXTERNAL_ID) {
                array.splice(index, 1)
                return true
            }
            return false
        }

        function removeForecastProduction(value: GlobalVisionKpiTableModel, index: number, array: any) {
            if (value.kpi.externalId === FoundationalKpisKpidom.FORECAST_PRODUCTION.EXTERNAL_ID) {
                array.splice(index, 1)
                return true
            }
            return false
        }

        function removeActualCost(value: GlobalVisionKpiTableModel, index: number, array: any) {
            if (value.kpi.externalId === FoundationalKpisKpidom.ACTUAL_COST.EXTERNAL_ID) {
                array.splice(index, 1)
                return true
            }
            return false
        }

        function removeActualProduction(value: GlobalVisionKpiTableModel, index: number, array: any) {
            if (value.kpi.externalId === FoundationalKpisKpidom.ACTUAL_PRODUCTION.EXTERNAL_ID) {
                array.splice(index, 1)
                return true
            }
            return false
        }

        let filteredKpisByPermission = filteredKpis

        if (!checkPermissionsFromRoutes('siteViewFoundationalPlantCash')) {
            filteredKpisByPermission = filteredKpisByPermission.filter(
                (value) => value.kpi.externalId !== FinancialKpisDom.PLANT_CASH
            )
        }
        if (!checkPermissionsFromRoutes('siteViewFoundationalPlantCashKg')) {
            filteredKpisByPermission = filteredKpisByPermission.filter(
                (value) => value.kpi.externalId !== FinancialKpisDom.PLANT_CASH_KG
            )
        }

        setFilteredKpis(filteredKpisByPermission)
    }, [loadingGlobalVisionKPISite, loadingGlobalVisionKPITarget, globalViewData, kpiTargetsData])

    useEffect(() => {
        refetchGlobalVisionKPI()
        refetchGlobalVisionKPISegment()
    }, [tabGroup, selectedStatus, isBusinessSegmentFilterActive, selectedMultipleSiteIds])

    useEffect(() => {
        if (filteredKpisRef.current === filteredKpis.length) return

        if (tabGroup && filteredKpis.length > 0) {
            setLoadingKpis(false)
            filteredKpisRef.current = filteredKpis.length
            setSelectedKpi(filteredKpis[0].kpi.externalId)
            setSelectedKpiData(filteredKpis[0].kpi)
            setSelectedKpiButton(0)
        }
    }, [tabGroup, filteredKpis])

    useEffect(() => {
        if (kpiTargetsData.length > 0) {
            setRange(kpiTargetsData[0].value)
        }
    }, [kpiTargetsData, isBusinessSegmentFilterActive, selectedMultipleSiteIds])

    useEffect(() => {
        if (selectedKpi !== undefined) {
            kpiTargetsData.map((ele) => {
                if (ele.refGlobalVisionKPI.externalId == selectedKpi) {
                    setRange(ele.value)
                }
            })
        }
    }, [selectedKpi, isBusinessSegmentFilterActive, selectedMultipleSiteIds])

    const { reset, setValue, handleSubmit, control } = useForm<SiteViewFilterSchema>({
        defaultValues: {
            statusFilter: 'All',
        },
        resolver: zodResolver(siteViewFilterSchema),
    })

    const handleStatusFiltersSubmit = (data: SiteViewFilterSchema) => {
        setSelectedStatus(data.statusFilter)
    }

    if (!filters) {
        return <></>
    }

    return (
        <Grid container spacing={2} sx={{ height: '100%', marginTop: '5px' }}>
            <Grid item xs={4}>
                <Box sx={styles.tabWrapperLeft}>
                    <Box sx={styles.topWrapper}>
                        <Typography variant="h6" sx={{ color: '#083D5B', marginBottom: '1rem' }}>
                            {translate('SITE_VIEW.KPI_LIST')}
                        </Typography>

                        <Form>
                            <Controller
                                control={control}
                                name="statusFilter"
                                render={({ field }) => (
                                    <ClnToggle
                                        {...field}
                                        label={translate('COMMONFILTER.CRITICAL_ONLY')}
                                        onChange={() => {
                                            const newValue = selectedStatus === 'Critical' ? null : 'Critical'
                                            if (newValue === null) {
                                                reset()
                                            } else {
                                                setValue('statusFilter', newValue)
                                            }
                                            handleSubmit(handleStatusFiltersSubmit)()
                                        }}
                                    />
                                )}
                            />
                        </Form>
                    </Box>
                    {loadingKpis ? (
                        LoaderCircular()
                    ) : (
                        <ButtonGroup
                            orientation="vertical"
                            variant="text"
                            sx={{
                                width: '100%',
                                '& .MuiButton-root': {
                                    textTransform: 'none',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    justifyContent: 'flex-start',
                                    textAlign: 'left',
                                    padding: '12px',
                                    backgroundColor: '#F1F4F6',
                                },
                            }}
                        >
                            {filteredKpis.map((kpi, index) => (
                                <Button
                                    key={index}
                                    onClick={() => {
                                        setSelectedKpi(kpi.kpi.externalId)
                                        setSelectedKpiData(kpi.kpi)
                                        setSelectedKpiButton(index)
                                    }}
                                    fullWidth={true}
                                    sx={{
                                        backgroundColor:
                                            selectedKpiButton === index ? '#083D5B !important' : '#F1F4F6 !important',
                                        borderBottom: 'unset !important',
                                        borderRadius: '5px',
                                        margin: '5px 0px',
                                        color: selectedKpiButton === index ? '#FFFFFF' : '#083D5B',
                                    }}
                                >
                                    <Tooltip title={kpi?.kpi.name} placement="top">
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'space-between',
                                                width: '100%',
                                                overflow: 'hidden',
                                            }}
                                        >
                                            <Typography
                                                sx={{
                                                    fontWeight: 'inherit',
                                                    whiteSpace: 'nowrap',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    maxWidth: 'calc(100% - 40px)',
                                                }}
                                            >
                                                {kpi?.kpi.symbol
                                                    ? String(kpi?.kpi.name).concat(' ', `(${kpi?.kpi.symbol})`)
                                                    : kpi?.kpi.name}
                                            </Typography>
                                            <Box sx={{ display: 'flex', alignContent: 'center' }}>
                                                {!kpi.redKpi ? (
                                                    <WarningSharpIcon
                                                        sx={{
                                                            color: selectedKpiButton === index ? '#FFFFFF' : '#D32F2F',
                                                        }}
                                                    />
                                                ) : (
                                                    <></>
                                                )}
                                                <MatIcon icon="arrow_right" />
                                            </Box>
                                        </Box>
                                    </Tooltip>
                                </Button>
                            ))}
                        </ButtonGroup>
                    )}
                </Box>
            </Grid>
            <Grid item xs={8}>
                {selectedKpi && selectedKpi.length > 0 && (
                    <Box sx={styles.tabWrapperRight}>
                        {selectedKpi &&
                            selectedKpi.length > 0 &&
                            isBusinessSegmentFilterActive &&
                            (loadingGlobalVisionKPISegment ? (
                                LoaderCircular()
                            ) : (
                                <DataTab globalViewData={selectedKpi !== 'KPIG-FLD' ? globalViewDataSegment : globalViewData} target={kpiTargetsData} />
                            ))}
                        {selectedKpi &&
                            selectedKpi.length > 0 &&
                            !isBusinessSegmentFilterActive &&
                            (loadingGlobalVisionKPISite ? (
                                LoaderCircular()
                            ) : (
                                <DataTab globalViewData={globalViewData} target={kpiTargetsData} />
                            ))}
                    </Box>
                )}
            </Grid>
        </Grid>
    )
}
