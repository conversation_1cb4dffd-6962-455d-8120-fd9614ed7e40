import React, { ReactNode, useContext, useState, useMemo, createContext, useEffect, useCallback } from 'react'
import { useReportingUnitTooltip } from '../hooks/useReportingUnitTooltip'
import dayjs from 'dayjs'
import { useGlobalKpiContext } from './GlobalKpiContext'
import { usePathname } from 'next/navigation'

type Bar = { seriesIndex: number; dataPointIndex: number } | null
interface TooltipRow {
    unit: string
    value: string
}

interface DataChartContextType {
    selectedBar: Bar
    setSelectedBar: React.Dispatch<React.SetStateAction<Bar>>
    disableSelection: boolean
    setDisableSelection: React.Dispatch<React.SetStateAction<boolean>>
    hoveredBar: Bar
    setHoveredBar: React.Dispatch<React.SetStateAction<Bar>>
    tooltipRows: TooltipRow[]
    setTooltipRows: React.Dispatch<React.SetStateAction<TooltipRow[]>>
    kpiHasUnit: boolean
    setKpiHasUnit: React.Dispatch<React.SetStateAction<boolean>>
    loadingReportingUnits: boolean
    xLabel: string
    setXLabel: React.Dispatch<React.SetStateAction<string>>
    columnValue: any
    setColumnValue: React.Dispatch<React.SetStateAction<any>>
    seriesColor: string
    setSeriesColor: React.Dispatch<React.SetStateAction<string>>
    seriesName: string
    setSeriesName: React.Dispatch<React.SetStateAction<string>>
    totalSiteValue: string
    hoveredSiteIdOverride: string
    setHoveredSiteIdOverride: React.Dispatch<React.SetStateAction<string>>
    hoveredKpiOverride: string
    setHoveredKpiOverride: React.Dispatch<React.SetStateAction<string>>
    hoveredPeriodOverride: string
    setHoveredPeriodOverride: React.Dispatch<React.SetStateAction<string>>
}

const DataChartContext = createContext<DataChartContextType | undefined>(undefined)

export const DataChartProvider = ({ children }: { children: ReactNode }) => {
    const [selectedBar, setSelectedBar] = useState<Bar>(null)
    const [disableSelection, setDisableSelection] = useState<boolean>(false)
    const [hoveredBar, setHoveredBar] = useState<Bar>(null)
    const [tooltipRows, setTooltipRows] = useState<TooltipRow[]>([])
    const [hoveredPeriod, setHoveredPeriod] = useState<string>()
    const [hoveredSite, setHoveredSite] = useState<string>()
    const [kpiHasUnit, setKpiHasUnit] = useState<boolean>(false)
    const [xLabel, setXLabel] = useState<string>('')
    const [columnValue, setColumnValue] = useState<any>()
    const [seriesColor, setSeriesColor] = useState<string>()
    const [seriesName, setSeriesName] = useState<string>('')
    const [hoveredSiteIdOverride, setHoveredSiteIdOverride] = useState<string>('')
    const [hoveredKpiOverride, setHoveredKpiOverride] = useState<string>('')
    const [hoveredPeriodOverride, setHoveredPeriodOverride] = useState<string>('')
    const { requestData, selectedKpi, selectedKpiData } = useGlobalKpiContext()
    const pathname = usePathname()
    const isGlobalView = pathname === '/global-view'

    const {
        dataReportingUnits: reportingUnitsData,
        loadingReportingUnits,
        totalSiteValue,
    } = useReportingUnitTooltip(
        isGlobalView && hoveredSiteIdOverride ? hoveredSiteIdOverride : hoveredSite,
        isGlobalView && hoveredKpiOverride ? hoveredKpiOverride : selectedKpi,
        isGlobalView && hoveredPeriodOverride ? hoveredPeriodOverride : hoveredPeriod
    )

    const handleBarHovering = useCallback(
        (bar: Bar) => {
            if (!bar || !selectedKpiData?.name) return

            const monthIndex = bar.dataPointIndex ?? null
            if (monthIndex === null) return

            const newFormattedDate = dayjs().month(monthIndex).startOf('month').format('YYYY-MM')

            if (isGlobalView) {
                setHoveredSite(hoveredSiteIdOverride || '')
                setHoveredPeriod(newFormattedDate)
                return
            }

            const hoveredSiteIndex = bar.seriesIndex ?? null
            const sortedSiteList = [...requestData.kpiFilters.refSite].sort((a, b) => {
                const exception = ['STS-BIS', 'STS-BCH']
                if (exception.includes(a) && exception.includes(b)) {
                    return a === 'STS-BIS' ? -1 : 1
                }

                return a.localeCompare(b, 'en-US', { sensitivity: 'base' })
            })

            const hoveredSiteId = sortedSiteList[hoveredSiteIndex]

            setHoveredSite(hoveredSiteId)
            setHoveredPeriod(newFormattedDate)
        },
        [selectedKpiData?.name, requestData.kpiFilters.refSite, isGlobalView]
    )

    useEffect(() => {
        if (hoveredBar) {
            handleBarHovering(hoveredBar)
        }
    }, [hoveredBar, handleBarHovering])

    useEffect(() => {
        if (reportingUnitsData && reportingUnitsData.length > 0) {
            const rows: TooltipRow[] = reportingUnitsData.map((unit) => ({
                unit: unit.unitDescription,
                value: unit.value,
            }))
            setTooltipRows(rows)
        } else {
            setTooltipRows([])
        }
    }, [reportingUnitsData, isGlobalView])

    const kpisWithUnit = useMemo(() => ['PST', 'RCI', 'EVT', 'NFT', 'HPE', 'NFA', 'NNM'], [])

    useEffect(() => {
        const effectiveKpi = isGlobalView && hoveredKpiOverride ? hoveredKpiOverride : selectedKpi
        const formattedKpiCode: string = effectiveKpi ? effectiveKpi.replace(/^KPIG-/, '') : ''
        const hasUnit = kpisWithUnit.includes(formattedKpiCode)
        setKpiHasUnit(hasUnit)
    }, [selectedKpi, kpisWithUnit, isGlobalView, hoveredKpiOverride])

    const value = useMemo(
        () => ({
            selectedBar,
            setSelectedBar,
            disableSelection,
            setDisableSelection,
            hoveredBar,
            setHoveredBar,
            tooltipRows,
            setTooltipRows,
            kpiHasUnit,
            setKpiHasUnit,
            loadingReportingUnits,
            columnValue,
            setColumnValue,
            xLabel,
            setXLabel,
            seriesColor,
            setSeriesColor,
            seriesName,
            setSeriesName,
            hoveredSiteIdOverride,
            setHoveredSiteIdOverride,
            hoveredKpiOverride,
            setHoveredKpiOverride,
            hoveredPeriodOverride,
            setHoveredPeriodOverride,
            totalSiteValue,
        }),
        [
            selectedBar,
            setSelectedBar,
            disableSelection,
            setDisableSelection,
            hoveredBar,
            setHoveredBar,
            tooltipRows,
            setTooltipRows,
            kpiHasUnit,
            setKpiHasUnit,
            loadingReportingUnits,
            columnValue,
            setColumnValue,
            xLabel,
            setXLabel,
            seriesColor,
            setSeriesColor,
            seriesName,
            setSeriesName,
            hoveredSiteIdOverride,
            setHoveredSiteIdOverride,
            hoveredKpiOverride,
            setHoveredKpiOverride,
            hoveredPeriodOverride,
            setHoveredPeriodOverride,
            totalSiteValue,
        ]
    )

    return <DataChartContext.Provider value={value}>{children}</DataChartContext.Provider>
}

export const useDataChartContext = () => {
    const context = useContext(DataChartContext)
    if (!context) {
        throw new Error('useDataChartContext must be used within a DataChartProvider')
    }
    return context
}
