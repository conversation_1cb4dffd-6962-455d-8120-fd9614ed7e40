import { CSSObject } from '@emotion/react'
export const baseLayoutContainer = (): CSSObject => {
    return {
        height: '100vh',
    }
}
export const menuContainer: CSSObject = {
    marginTop: '5px',
    '& .MuiPaper-root ul': { width: '225px' },
    '& .MuiPaper-root': { borderRadius: '20px' },
}
export const menuContentContainer: CSSObject = {
    padding: '16px',
    display: 'flex',
    flexDirection: 'column',
    gap: '5px',
}
export const avatarAndNameContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'row',
    gap: '10px',
    height: '50px',
    '& .MuiAvatar-root': {
        backgroundColor: 'primary.main',
    },
}
export const pageContainer: CSSObject = {
    marginLeft: '92px',
    height: 'calc(100% - 125px)',
    marginTop: '92px',
    marginRight: '44px',
    marginBottom: '33px',
}
export const dynamicTranslationOn: CSSObject = {
    fontSize: '1.5rem',
}
export const dynamicTranslationOff: CSSObject = {
    color: 'warning.dark',
    fontSize: '1.5rem',
}
export const dynamicTranslationDisabled: CSSObject = {
    color: '#00000042',
    fontSize: '1.5rem',
}
