import { ExternalEntity } from '.'
import { BusinessSegment } from './business-segment'
import { Site } from './site'

export interface QualityNotificationKpi extends ExternalEntity {
    aboutReportingSite: Site
    notificationDescription: string
    notificationNumber: string
    notificationDate?: Date | null
    notificationType: string
    systemStatusMapping: string
    systemStatus: string
    priority: string
    causeCodeGroup: string
    qualityManagementCodeGroup: string
    qualityManagementCode: string
    severity: string
    subjectCode: string
    businessSegment: BusinessSegment | null
}
