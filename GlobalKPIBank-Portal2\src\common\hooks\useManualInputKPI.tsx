import { gql } from '@apollo/client'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useEffect, useState } from 'react'
import { ManualInputKpi } from '../models/manualInputKpi'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import updateLocale from 'dayjs/plugin/updateLocale'
import { ManualInputTableModel } from '../models/manualInputDataTableModel'
dayjs.extend(utc)
dayjs.extend(updateLocale)

export interface ManualInputKpiRequest {
    site: {
        externalId: string
        description: string
    }
    year: string
}

const queryBuilder = (request: ManualInputKpiRequest) => {
    let siteQ: string = ''
    let startDateQ: string = ''
    let endDateQ: string = ''

    if (request.site !== undefined || request.site !== null) {
        siteQ = `{refSite: {externalId: {eq: "${request.site.externalId}"}}}`
    }

    if (request.year !== undefined || request.year != null) {
        startDateQ = `{startDate: {gte: "${request.year}-01-01"}}`
        endDateQ = `{endDate: {lte: "${request.year}-12-31"}}`
    }

    return `query getManualInputList {
        listKPIManualInput (
            first: 1000
            filter: {and: [${siteQ}, ${startDateQ}, ${endDateQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                externalId
                space
                refSite {
                    externalId
                }
                refKPICatogory {
                    externalId
                    name
                }
                kpiValue
                startDate
                endDate
                description
                period
                inputType
            }
        }
    }`
}

export const useManualInputKPI = (request: ManualInputKpiRequest) => {
    const [resultData, setResultData] = useState<{ data: ManualInputTableModel[] }>({
        data: [],
    })
    const descriptionList = ['Flawless days', 'Celanese Employee Headcount', 'Contractor Headcount']

    const query = queryBuilder(request)
    const {
        data: fdmData,
        refetch,
        loading,
    } = useGraphqlQuery<ManualInputKpi>(gql(query), 'listKPIManualInput', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'gkpisol',
        },
    })

    useEffect(() => {
        if (request.site.externalId !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (fdmData.length == 0) {
                    const emptyGroup: any = []
                    emptyGroup.push(generateEmptyResult('Flawless days', request.site.externalId))
                    emptyGroup.push(generateEmptyResult('Celanese Employee Headcount', request.site.externalId))
                    emptyGroup.push(generateEmptyResult('Contractor Headcount', request.site.externalId))
                    const groupedResult = groupByKPI(emptyGroup)
                    const sanitizedResult = mapResult(groupedResult)
                    setResultData({ data: sanitizedResult })
                }
                if (fdmData.length > 0) {
                    const groupedResult = groupByKPI(fdmData)
                    createMissingGroup(groupedResult, descriptionList, request.site.externalId)
                    const sanitizedResult = mapResult(groupedResult)
                    setResultData({ data: sanitizedResult })
                }
            }
        }
    }, [fdmData, request.site, request.year])

    return {
        loadingManualInput: loading,
        refetchManualInput: refetch,
        dataManualInput: resultData.data,
    }
}

const groupByKPI = (resultData: any[]): any => {
    const groupedByKPI = Object.values(
        resultData.reduce((group, item) => {
            group[item.description] = group[item.description] ?? []
            group[item.description].push(item)
            return group
        }, {})
    )
    return groupedByKPI
}

const createMissingGroup = (groupedResult: any[], descriptionList: string[], siteExternalId: string): void => {
    if (groupedResult.length === 3) return

    const collectedKpis: string[] = []
    groupedResult.forEach((kpi) => {
        if (kpi.length > 0) {
            collectedKpis.push(kpi[0].description)
        }
    })

    descriptionList.forEach((description) => {
        const emptyGroup: any[] = []
        if (!collectedKpis.includes(description)) {
            emptyGroup.push(generateEmptyResult(description, siteExternalId))
            groupedResult.push(emptyGroup)
        }
    })
}

const getOrder = (categoryName: string): number => {
    if (categoryName === 'Flawless days') return 1

    if (categoryName === 'Celanese Employee Headcount') return 2

    return 3
}

const getMonthValue = (res: any, item: any) => {
    if (!item.startDate) return

    const monthName = dayjs(item.startDate).format('MMMM').toLowerCase()

    if (res[item.description][monthName] === undefined) {
        res[item.description][monthName] = item.kpiValue
    }
}

const mapResult = (groupedResult: any[]): any => {
    const mappedResult: { kpi: any; data: any }[] = []
    groupedResult.forEach((kpi) => {
        let kpiDescription = ''
        const allKpiItems: any = []
        const accumulator = kpi.reduce(function (res: any, item: any) {
            if (!res[item.description]) {
                res[item.description] = {
                    category: item?.refKPICatogory?.name ?? '',
                    name: item?.refSite?.externalId ?? '',
                    description: item.description,
                    order: getOrder(item.description),
                    january: undefined,
                    february: undefined,
                    march: undefined,
                    april: undefined,
                    may: undefined,
                    june: undefined,
                    july: undefined,
                    august: undefined,
                    september: undefined,
                    october: undefined,
                    november: undefined,
                    december: undefined,
                }
            }
            getMonthValue(res, item)
            kpiDescription = item.description
            allKpiItems.push(item)
            return res
        }, {})
        mappedResult.push({
            kpi: allKpiItems,
            data: accumulator[kpiDescription],
        })
    })
    mappedResult.sort((a, b) => a.data.order - b.data.order)
    return mappedResult
}

const generateEmptyResult = (kpiDescription: string, site: string): ManualInputKpi => {
    const partialEmpty: any = {
        refSite: {
            externalId: site,
            space: 'REF-COR-ALL-DAT',
        },
        refKPICatogory: {
            externalId: '',
            space: 'GKPI-COR-ALL-DAT',
            name: 'Foundational',
        },
        kpiValue: 0,
        startDate: undefined,
        endDate: undefined,
        description: kpiDescription,
        space: '',
        externalId: '',
        period: 'Monthly',
        inputType: 'Manual',
    }
    return partialEmpty
}
