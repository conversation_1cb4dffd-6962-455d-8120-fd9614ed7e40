import { Box, Typography } from '@mui/material'
import { translate } from '@celanese/celanese-sdk'
import KPITargetTable from '../KPITargetTable'

const styles = {
    tabWrapper: {
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: '8px',
        width: 'auto',
        height: 'auto',
        padding: '30px',
    },
}

export const KPITargetPage = () => {
    return (
        <div>
            <Typography variant="h3" sx={{ color: '#083D5B', marginBottom: '1rem', fontWeight: 'bold' }}>
                {translate('KPI_TARGET.TITLE')}
            </Typography>
            <Box sx={styles.tabWrapper}>
                <KPITargetTable />
            </Box>
        </div>
    )
}
