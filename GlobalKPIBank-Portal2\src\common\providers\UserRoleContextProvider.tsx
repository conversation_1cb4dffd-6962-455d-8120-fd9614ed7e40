import { ReactNode, useEffect, useState } from 'react'
import {
    UserRolesPermission,
    FeatureSitesMapping,
    UserSite,
    getUserPermissions,
} from '../clients/user-management-client'
import { AuthGuardContext } from '../contexts/AuthGuardContext'
import { saveLocalUserSite } from '../utils'
import { enviroment } from '../configurations/enviroment'
import { useMsal } from '@azure/msal-react'

type UserRoleContextProps = {
    children: ReactNode
}
const ALL_SITES_CODE = 'STS-COR'
const EXCLUDE_ROLES = new Set(['CLN Business User', 'CLN Business User 2', 'COR Global Application Site Admins'])

export const UserRoleContextProvider = ({ children }: UserRoleContextProps) => {
    const { instance: msalInstance } = useMsal()
    const [userInfo, setUserInfo] = useState<UserRolesPermission>({} as UserRolesPermission)
    const [featureSitesMapping, setFeatureSitesMapping] = useState<FeatureSitesMapping[]>([])
    const [loading, setLoading] = useState<boolean>(true)
    const [loadedPermissions, setLoadedPermissions] = useState<boolean>(false)
    const [countGetPermissionRetry, setCountGetPermissionRetry] = useState<number>(0)
    const MAX_RETRY_ATTEMPTS = 5
    const RETRY_COOLDOWN_MS = 500

    const updateSiteInfo = (siteInfo: UserSite) => {
        setLoading(true)
        setUserInfo((prev) => ({ ...prev, applications: [], selectedSite: siteInfo }))
        saveLocalUserSite(siteInfo)
        setLoadedPermissions(false)
    }

    const retryGetPermission = () => {
        if (countGetPermissionRetry < MAX_RETRY_ATTEMPTS) {
            setTimeout(() => {
                setCountGetPermissionRetry(countGetPermissionRetry + 1)
            }, Math.pow(2, countGetPermissionRetry) * RETRY_COOLDOWN_MS)
        }
    }

    useEffect(() => {
        if (loadedPermissions) {
            return
        }

        getUserPermissions(msalInstance)
            .then(({ user: userResponse, featureSitesMapping: featureSitesMappingResponse }) => {
                if (!userResponse) {
                    retryGetPermission()
                    return
                }

                const appGkpi = userResponse.applications?.find(
                    (a) => a.applicationCode == enviroment.userManagementAppCode
                )
                userResponse.availableReportingSites = appGkpi?.userSites ?? []

                if (appGkpi && appGkpi.roles.length > 0) {
                    const isAllSitesPermission = appGkpi.roles[0].siteCodes?.includes(ALL_SITES_CODE)

                    if (userResponse.availableReportingSites.length) {
                        if (isAllSitesPermission) {
                            appGkpi.roles = appGkpi.roles.filter((r) => !EXCLUDE_ROLES.has(r.roleName))
                        }

                        if (appGkpi.roles.length === 0) {
                            userResponse.availableReportingSites = userResponse.availableReportingSites.filter(
                                (x) => x.siteId !== ALL_SITES_CODE
                            )
                        }
                    }

                    userResponse.applications = [appGkpi]
                    if (userResponse.availableReportingSites) {
                        const targetSiteCode = appGkpi.roles[0].siteCodes.find((p) => p)

                        const reportingSiteFound = userResponse.availableReportingSites.find(
                            (s) => s.siteId === targetSiteCode
                        )

                        userResponse.selectedSite = reportingSiteFound
                        saveLocalUserSite(reportingSiteFound)
                    } else {
                        const defaultEmpty: UserSite = {
                            siteId: '',
                            siteName: '',
                            siteCode: '',
                        }
                        userResponse.selectedSite = defaultEmpty
                        saveLocalUserSite(defaultEmpty)
                    }
                }

                setLoadedPermissions(true)
                setUserInfo(userResponse)
                setFeatureSitesMapping(featureSitesMappingResponse)
            })
            .catch(() => {
                retryGetPermission()
            })
            .finally(() => {
                setLoading(false)
            })
    }, [msalInstance, userInfo?.selectedSite, loadedPermissions])

    return (
        <AuthGuardContext.Provider value={{ userInfo, featureSitesMapping, updateSiteInfo, loading }}>
            {children}
        </AuthGuardContext.Provider>
    )
}
