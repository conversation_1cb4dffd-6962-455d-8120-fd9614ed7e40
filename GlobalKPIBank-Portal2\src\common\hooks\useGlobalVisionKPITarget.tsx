import { gql, QueryHookOptions } from '@apollo/client'
import { GlobalVisionKpiTarget } from '../models/globalVisionKpiTarget'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useEffect, useRef, useMemo, useState } from 'react'
import { QualityKpis } from '../utils/quality-kpis'
import { CgnFilter } from '../utils/gqlQueryUtils'

export interface GlobalVisionKpiTargetRequest {
    skip?: boolean
    site: string[]
    kpiGroupExternalId: string
    year: number
    limit?: string
    cursor?: string
}

const GET_GLOBAL_VIEW_TARGET = gql`
    query getGlobalViewTarget($filter: _ListGlobalVisionKPITargetFilter, $first: Int, $cursor: String) {
        listGlobalVisionKPITarget(first: $first, filter: $filter, after: $cursor) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                externalId
                space
                refGlobalVisionKPI {
                    externalId
                    name
                    order
                }
                refReportingSite {
                    externalId
                    name
                }
                rangeDirection
                value
                year
            }
        }
    }
`

const setVariables = (request: GlobalVisionKpiTargetRequest) => {
    const filter: CgnFilter<any>[] = []
    if (request?.site?.length) {
        filter.push({ refReportingSite: { externalId: { in: request.site } } })
    }
    if (request?.year) {
        filter.push({ year: { eq: request.year } })
    }
    if (
        request?.kpiGroupExternalId !== '' &&
        request.kpiGroupExternalId !== undefined &&
        request.kpiGroupExternalId !== null
    ) {
        if (request?.kpiGroupExternalId === 'GKPI-SOL-QLT') {
            filter.push({
                refGlobalVisionKPI: {
                    externalId: {
                        in: [
                            QualityKpis.HS_QN1S,
                            QualityKpis.QN1S,
                            QualityKpis.MAJOR_AUDIT_FINDINGS_EXTERNAL,
                            QualityKpis.BLOCK_STOCK_FINISHED_GOODS,
                            QualityKpis.BLOCK_STOCK_GENERATED,
                            QualityKpis.BLOCK_STOCK_CONSUMED,
                        ],
                    },
                },
            })
        }
        filter.push({ refGlobalVisionKPI: { group: { externalId: { eq: request.kpiGroupExternalId } } } })
    }

    return {
        filter: { and: filter },
        first: 1_000,
    }
}

export const useGlobalVisionKPITarget = (request: GlobalVisionKpiTargetRequest) => {
    const filter = setVariables(request)
    const cursorRef = useRef(request.cursor ?? '')
    const currentYear =
        new Date().getMonth() == 0 || (new Date().getMonth() == 1 && new Date().getDate() < 11)
            ? new Date().getFullYear() - 1
            : new Date().getFullYear()

    const queryOptions = useMemo<QueryHookOptions>(
        () => ({
            variables: filter,
            fetchPolicy: 'network-only',
            context: {
                clientName: 'globalView',
            },
            skip: request.skip,
        }),
        [request]
    )
    const {
        data: fdmData,
        pageInfo: pageInfo,
        refetch,
        fetchMore,
        loading,
    } = useGraphqlQuery<GlobalVisionKpiTarget>(GET_GLOBAL_VIEW_TARGET, 'listGlobalVisionKPITarget', queryOptions)

    const kpiList = [
        { EXTERNAL_ID: 'KPIG-FLD', OLD_EXTERNAL_ID: 'GKPI-SOL-FLD', NAME: 'Flawless Days' },
        { EXTERNAL_ID: 'KPIG-APF', OLD_EXTERNAL_ID: 'GKPI-SOL-APF', NAME: 'Actual Production Plan to forecast' },
        { EXTERNAL_ID: 'KPIG-ACF', OLD_EXTERNAL_ID: 'GKPI-SOL-ACF', NAME: 'Actual Cost to Forecast' },
        { EXTERNAL_ID: 'KPIG-AOP', OLD_EXTERNAL_ID: 'GKPI-SOL-AOP', NAME: '$/KG Actual to Forecast' },
        { EXTERNAL_ID: 'KPIG-PCM', OLD_EXTERNAL_ID: 'GKPI-SOL-PCM', NAME: 'Plant Cash Margin' },
        { EXTERNAL_ID: 'KPIG-MKG', OLD_EXTERNAL_ID: 'GKPI-SOL-MKG', NAME: 'Plant Cash Margin/KG' },
        { EXTERNAL_ID: 'KPIG-KGH', OLD_EXTERNAL_ID: 'GKPI-SOL-KGH', NAME: 'KG/Headcount*' },
        { EXTERNAL_ID: 'KPIG-PRD', OLD_EXTERNAL_ID: 'GKPI-SOL-PRD', NAME: `Productivity - ${currentYear}` },
        {
            EXTERNAL_ID: 'KPIG-PNY',
            OLD_EXTERNAL_ID: 'GKPI-SOL-PNY',
            NAME: `Productivity Pipeline - ${currentYear + 1}`,
        },
        {
            EXTERNAL_ID: 'KPIG-PPN',
            OLD_EXTERNAL_ID: 'GKPI-SOL-PPN',
            NAME: `Productivity Pipeline - ${currentYear + 2}`,
        },
        { EXTERNAL_ID: 'KPIG-FCS', OLD_EXTERNAL_ID: 'GKPI-SOL-FPC', NAME: 'Forecast Cost' },
        { EXTERNAL_ID: 'KPIG-ACS', OLD_EXTERNAL_ID: 'GKPI-SOL-APC', NAME: 'Actual Cost' },
        { EXTERNAL_ID: 'KPIG-APV', OLD_EXTERNAL_ID: 'GKPI-SOL-APV', NAME: 'Actual Production' },
        { EXTERNAL_ID: 'KPIG-FPV', OLD_EXTERNAL_ID: 'GKPI-SOL-FPV', NAME: 'Forecast Production' },

        { EXTERNAL_ID: 'KPIG-RCI', OLD_EXTERNAL_ID: 'GKPI-SOL-RCI', NAME: 'Peolple Safety - Tier 1/2 Recordables' },
        { EXTERNAL_ID: 'KPIG-NFA', OLD_EXTERNAL_ID: 'GKPI-SOL-NFA', NAME: 'People Safety - Tier 3 First Aids' },
        { EXTERNAL_ID: 'KPIG-PST', OLD_EXTERNAL_ID: 'GKPI-SOL-PST', NAME: 'Process Safety - Tier 1/2' },
        { EXTERNAL_ID: 'KPIG-EVT', OLD_EXTERNAL_ID: 'GKPI-SOL-EVT', NAME: 'Environmental - Tier 1/2' },
        { EXTERNAL_ID: 'KPIG-NFT', OLD_EXTERNAL_ID: 'GKPI-SOL-NFT', NAME: 'Fire - Tier 1/2' },
        { EXTERNAL_ID: 'KPIG-NNM', OLD_EXTERNAL_ID: 'GKPI-SOL-NNM', NAME: 'Near Misses' },
        { EXTERNAL_ID: 'KPIG-HPE', OLD_EXTERNAL_ID: 'GKPI-SOL-HPE', NAME: 'High Potential Events' },
        { EXTERNAL_ID: 'KPIG-TTL', OLD_EXTERNAL_ID: 'GKPI-SOL-TTL', NAME: 'TRIR Combined*' },
        { EXTERNAL_ID: 'KPIG-TEP', OLD_EXTERNAL_ID: 'GKPI-SOL-TEP', NAME: 'TRIR Employee*' },
        { EXTERNAL_ID: 'KPIG-TCT', OLD_EXTERNAL_ID: 'GKPI-SOL-TCT', NAME: 'TRIR Contractor*' },

        { EXTERNAL_ID: 'KPIG-HQS', OLD_EXTERNAL_ID: 'GKPI-SOL-HQN', NAME: 'HS QN1s' },
        { EXTERNAL_ID: 'KPIG-QN1', OLD_EXTERNAL_ID: 'GKPI-SOL-QN1', NAME: 'QN1s' },
        { EXTERNAL_ID: 'KPIG-MAF', OLD_EXTERNAL_ID: 'GKPI-SOL-MAF', NAME: 'Major Audit Findings - External' },
        { EXTERNAL_ID: 'KPIG-FGB', OLD_EXTERNAL_ID: 'GKPI-SOL-FGB', NAME: 'Block Stock - Finished Goods *' },
        { EXTERNAL_ID: 'KPIG-GBS', OLD_EXTERNAL_ID: 'GKPI-SOL-GBS', NAME: 'Block Stock - Generated*' },
        { EXTERNAL_ID: 'KPIG-CBS', OLD_EXTERNAL_ID: 'GKPI-SOL-CBS', NAME: 'Block Stock - Consumed*' },

        { EXTERNAL_ID: 'KPIG-OEE', OLD_EXTERNAL_ID: 'GKPI-SOL-OEE', NAME: 'OEE' },
    ]

    const updateTargetKpis = (data: GlobalVisionKpiTarget[]) => {
        const result = data.map((data) => {
            const matchingKpi = kpiList.find(
                (foundationalKpi) => foundationalKpi.OLD_EXTERNAL_ID === data.refGlobalVisionKPI.externalId
            )
            if (matchingKpi) {
                return {
                    ...data,
                    refGlobalVisionKPI: {
                        ...data.refGlobalVisionKPI,
                        name: matchingKpi.NAME,
                        externalId: matchingKpi.EXTERNAL_ID,
                    },
                }
            }
        })
        return result
    }

    const [resultData, setResultData] = useState<{ data: GlobalVisionKpiTarget[] }>({
        data: [],
    })

    useEffect(() => {
        if (fdmData === undefined) {
            setResultData((prevState) => ({ ...prevState }))
        } else {
            if (fdmData && fdmData.length > 0) {
                if (pageInfo?.hasNextPage && cursorRef.current !== pageInfo.endCursor) {
                    request.cursor = pageInfo.endCursor
                    cursorRef.current = pageInfo.endCursor
                    fetchMore({
                        variables: {
                            cursor: pageInfo.endCursor,
                        },
                    })
                }

                const updatedTargets = updateTargetKpis(fdmData)
                const result_aggregation = [...updatedTargets].sort(
                    (a, b) => a.refGlobalVisionKPI.order - b.refGlobalVisionKPI.order
                )

                setResultData({ data: result_aggregation })
            } else {
                setResultData({ data: [] })
            }
        }
    }, [fdmData, fetchMore, request.kpiGroupExternalId])

    return {
        loadingGlobalVisionKPITarget: loading,
        refetchGlobalVisionKPITarget: refetch,
        dataGlobalVisionKPITarget: resultData.data,
        fetchMore: fetchMore,
    }
}
