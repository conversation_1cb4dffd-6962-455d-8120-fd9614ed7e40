import { useGraphAuthToken } from './useAuthToken'
import { useEffect, useState } from 'react'

const GRAPH_ENDPOINTS = {
    graphMeEndpoint: 'https://graph.microsoft.com/v1.0/me',
    graphProfilePic: 'https://graph.microsoft.com/v1.0/me/photo/$value',
}

interface MsGraphResult {
    accountInformation?: any
    profilePicture?: any
}

export function useMsGraph() {
    const { getAuthToken } = useGraphAuthToken()
    const [state, setState] = useState<MsGraphResult>({})

    useEffect(() => {
        const makeGetRequest = async (url: string): Promise<any> => {
            const headers = new Headers()
            headers.append('Authorization', `Bearer ${await getAuthToken()}`)

            const options = {
                method: 'GET',
                headers: headers,
            }

            return await fetch(url, options)
        }

        const fetchAccountInformation = () => {
            return makeGetRequest(GRAPH_ENDPOINTS.graphMeEndpoint)
        }

        const fetchProfilePicture = () => {
            return makeGetRequest(GRAPH_ENDPOINTS.graphProfilePic)
                .then((response) => response.blob())
                .then((blobData) => {
                    let profilePicture = ''

                    if (blobData) {
                        const url = window.URL || window.webkitURL
                        profilePicture = url.createObjectURL(blobData)
                    }

                    return profilePicture
                })
        }

        Promise.all([fetchAccountInformation(), fetchProfilePicture()]).then(([accountInformation, profilePicture]) => {
            setState({ accountInformation, profilePicture })
        })
    }, [getAuthToken])

    return state
}
