import dayjs from 'dayjs'
import { Entity } from '../models'

export type CogniteSdkEntity = Entity & { __typename?: string; [key: string]: any }

export function flattenNestedModels<T extends CogniteSdkEntity>(entity: T): any {
    const result = {} as any
    for (const key in entity) {
        if (entity[key]?.externalId) {
            result[key] = entity[key].externalId
            continue
        }
        result[key] = entity[key]
    }
    return result
}

export function IdGenerator(tableAcronym: string): string {
    const timeStamp = dayjs().format('YYYYMMDDHHmmssSSS')
    const digit = ''

    return [tableAcronym, timeStamp, digit].join('-')
}
