"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-loadable-visibility";
exports.ids = ["vendor-chunks/react-loadable-visibility"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-loadable-visibility/capacities.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-loadable-visibility/capacities.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nexports.__esModule = true;\nexports.IntersectionObserver = void 0;\nvar IntersectionObserver = typeof window !== 'undefined' && window.IntersectionObserver;\nexports.IntersectionObserver = IntersectionObserver;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbG9hZGFibGUtdmlzaWJpbGl0eS9jYXBhY2l0aWVzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGtCQUFrQjtBQUNsQiw0QkFBNEI7QUFDNUI7QUFDQSw0QkFBNEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nbG9iYWwta3BpLWFwcC8uL25vZGVfbW9kdWxlcy9yZWFjdC1sb2FkYWJsZS12aXNpYmlsaXR5L2NhcGFjaXRpZXMuanM/ZGE2NSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuSW50ZXJzZWN0aW9uT2JzZXJ2ZXIgPSB2b2lkIDA7XG52YXIgSW50ZXJzZWN0aW9uT2JzZXJ2ZXIgPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cuSW50ZXJzZWN0aW9uT2JzZXJ2ZXI7XG5leHBvcnRzLkludGVyc2VjdGlvbk9ic2VydmVyID0gSW50ZXJzZWN0aW9uT2JzZXJ2ZXI7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-loadable-visibility/capacities.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-loadable-visibility/createLoadableVisibilityComponent.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/react-loadable-visibility/createLoadableVisibilityComponent.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\n\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\n\nvar _capacities = __webpack_require__(/*! ./capacities */ \"(ssr)/./node_modules/react-loadable-visibility/capacities.js\");\n\nfunction _getRequireWildcardCache() { if (typeof WeakMap !== \"function\") return null; var cache = new WeakMap(); _getRequireWildcardCache = function _getRequireWildcardCache() { return cache; }; return cache; }\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nvar intersectionObserver;\nvar trackedElements = new Map();\n\nif (_capacities.IntersectionObserver) {\n  intersectionObserver = new window.IntersectionObserver(function (entries, observer) {\n    entries.forEach(function (entry) {\n      var visibilityHandler = trackedElements.get(entry.target);\n\n      if (visibilityHandler && (entry.isIntersecting || entry.intersectionRatio > 0)) {\n        visibilityHandler();\n      }\n    });\n  });\n}\n\nfunction createLoadableVisibilityComponent(args, _ref) {\n  var Loadable = _ref.Loadable,\n      preloadFunc = _ref.preloadFunc,\n      LoadingComponent = _ref.LoadingComponent;\n  var preloaded = false;\n  var visibilityHandlers = [];\n  var LoadableComponent = Loadable.apply(void 0, args);\n\n  function LoadableVisibilityComponent(props) {\n    var visibilityElementRef = (0, _react.useRef)();\n\n    var _useState = (0, _react.useState)(preloaded),\n        isVisible = _useState[0],\n        setVisible = _useState[1];\n\n    function visibilityHandler() {\n      if (visibilityElementRef.current) {\n        intersectionObserver.unobserve(visibilityElementRef.current);\n        trackedElements[\"delete\"](visibilityElementRef.current);\n      }\n\n      setVisible(true);\n    }\n\n    (0, _react.useEffect)(function () {\n      var element = visibilityElementRef.current;\n\n      if (!isVisible && element) {\n        visibilityHandlers.push(visibilityHandler);\n        trackedElements.set(element, visibilityHandler);\n        intersectionObserver.observe(element);\n        return function () {\n          var handlerIndex = visibilityHandlers.indexOf(visibilityHandler);\n\n          if (handlerIndex >= 0) {\n            visibilityHandlers.splice(handlerIndex, 1);\n          }\n\n          intersectionObserver.unobserve(element);\n          trackedElements[\"delete\"](element);\n        };\n      }\n    }, [isVisible, visibilityElementRef.current]);\n\n    if (isVisible) {\n      return _react[\"default\"].createElement(LoadableComponent, props);\n    }\n\n    if (LoadingComponent || props.fallback) {\n      return _react[\"default\"].createElement(\"div\", _extends({\n        style: {\n          display: \"inline-block\",\n          minHeight: \"1px\",\n          minWidth: \"1px\"\n        }\n      }, props, {\n        ref: visibilityElementRef\n      }), LoadingComponent ? _react[\"default\"].createElement(LoadingComponent, _extends({\n        isLoading: true\n      }, props)) : props.fallback);\n    }\n\n    return _react[\"default\"].createElement(\"div\", _extends({\n      style: {\n        display: \"inline-block\",\n        minHeight: \"1px\",\n        minWidth: \"1px\"\n      }\n    }, props, {\n      ref: visibilityElementRef\n    }));\n  }\n\n  LoadableVisibilityComponent[preloadFunc] = function () {\n    if (!preloaded) {\n      preloaded = true;\n      visibilityHandlers.forEach(function (handler) {\n        return handler();\n      });\n    }\n\n    return LoadableComponent[preloadFunc]();\n  };\n\n  return LoadableVisibilityComponent;\n}\n\nvar _default = createLoadableVisibilityComponent;\nexports[\"default\"] = _default;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-loadable-visibility/createLoadableVisibilityComponent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-loadable-visibility/loadable-components.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-loadable-visibility/loadable-components.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\n\nvar _component = _interopRequireDefault(__webpack_require__(/*! @loadable/component */ \"(ssr)/./node_modules/@loadable/component/dist/cjs/loadable.cjs.js\"));\n\nvar _createLoadableVisibilityComponent = _interopRequireDefault(__webpack_require__(/*! ./createLoadableVisibilityComponent */ \"(ssr)/./node_modules/react-loadable-visibility/createLoadableVisibilityComponent.js\"));\n\nvar _capacities = __webpack_require__(/*! ./capacities */ \"(ssr)/./node_modules/react-loadable-visibility/capacities.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction _getRequireWildcardCache() { if (typeof WeakMap !== \"function\") return null; var cache = new WeakMap(); _getRequireWildcardCache = function _getRequireWildcardCache() { return cache; }; return cache; }\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { \"default\": obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj[\"default\"] = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n\nfunction loadableVisiblity(load, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n\n  if (_capacities.IntersectionObserver) {\n    return (0, _createLoadableVisibilityComponent[\"default\"])([load, opts], {\n      Loadable: _component[\"default\"],\n      preloadFunc: \"preload\",\n      LoadingComponent: opts.fallback ? function () {\n        return opts.fallback;\n      } : null\n    });\n  } else {\n    return (0, _component[\"default\"])(load, opts);\n  }\n}\n\nmodule.exports = loadableVisiblity;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-loadable-visibility/loadable-components.js\n");

/***/ })

};
;