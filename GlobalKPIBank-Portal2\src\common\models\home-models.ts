import { z } from 'zod'

export interface DescribableEntity {
    externalId?: string
    name?: string
    description?: string
}

export interface HomeFilterValue {
    siteList: DescribableEntity[]
    kpiList: DescribableEntity[]
    businessSegment: DescribableEntity[]
    currentTab: number
    period: string
}

export const homeFilterSchema = z.object({
    businessSegment: z.array(
        z.object({
            externalId: z.string(),
            description: z.string(),
        })
    ),
    siteList: z.array(
        z.object({
            externalId: z.string(),
            description: z.string(),
        })
    ),
    kpiList: z.array(
        z.object({
            externalId: z.string(),
            name: z.string(),
        })
    ),
    period: z.string(),
})

export type HomeFilterSchema = z.infer<typeof homeFilterSchema>

export enum HomeTabsEnum {
    General = 0,
    Engagement = 5,
}
