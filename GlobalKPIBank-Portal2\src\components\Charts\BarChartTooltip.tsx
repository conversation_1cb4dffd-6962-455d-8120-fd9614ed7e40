import { useDataChartContext } from '@/common/contexts/DataChartContext'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { useReportingSiteList } from '@/common/hooks/useReportingSiteList'
import { Box, Table, TableHead, TableRow, TableCell, TableBody, CircularProgress, Typography } from '@mui/material'
import { StripedCell } from '../DataTab/KpiDataTable/styles'
import { ReactNode, useMemo } from 'react'
import { usePathname } from 'next/navigation'

interface BarChartTooltipProps {
    localTooltipRows?: Array<{ unit: string; value: string }>
    localLoadingReportingUnits?: boolean
    localTotalSiteValue?: string
}

export const BarChartTooltip = ({
    localTooltipRows,
    localLoadingReportingUnits,
    localTotalSiteValue,
}: BarChartTooltipProps) => {
    const {
        hoveredBar,
        tooltipRows,
        kpiHasUnit,
        loadingReportingUnits,
        xLabel,
        columnValue,
        seriesColor,
        seriesName,
        totalSiteValue,
    } = useDataChartContext()

    const { requestData } = useGlobalKpiContext()
    const pathname = usePathname()
    const isGlobalView = pathname === '/global-view'

    const effectiveTooltipRows = isGlobalView ? localTooltipRows || [] : tooltipRows
    const effectiveLoadingReportingUnits = isGlobalView ? localLoadingReportingUnits || false : loadingReportingUnits
    const effectiveTotalSiteValue = isGlobalView ? localTotalSiteValue || '' : totalSiteValue

    const { dataSiteList: allSites } = useReportingSiteList()

    const siteIdToNameMap = useMemo(() => {
        return allSites.reduce((map, site) => {
            map[site.externalId] = site.name || site.description || site.externalId
            return map
        }, {} as Record<string, string>)
    }, [allSites])

    const getSiteName = () => {
        if (isGlobalView) {
            return seriesName || ''
        }

        if (!hoveredBar || !requestData?.kpiFilters?.refSite) return seriesName

        const sortedSiteList = [...requestData.kpiFilters.refSite].sort((a, b) => {
            const exception = ['STS-BIS', 'STS-BCH']
            if (exception.includes(a) && exception.includes(b)) {
                return a === 'STS-BIS' ? -1 : 1
            }
            return a.localeCompare(b, 'en-US', { sensitivity: 'base' })
        })

        const siteId = sortedSiteList[hoveredBar.seriesIndex]
        if (siteId && siteIdToNameMap[siteId]) {
            return siteIdToNameMap[siteId]
        }

        return siteId || seriesName
    }

    function wrapLongUnitNames(unit: string): ReactNode {
        if (!unit) return unit
        if (unit.length <= 25) return unit
        const index = unit.indexOf(' ', 25)

        if (index === -1) return unit

        return (
            <>
                {unit.slice(0, index)}
                <br />
                {unit.slice(index + 1)}
            </>
        )
    }

    return (
        <Box
            sx={{
                background: '#fff',
                boxShadow: '2px 2px 6px -4px #00000040',
                borderRadius: '5px',
                border: '1px solid #e3e3e3',
                overflow: 'hidden',
            }}
        >
            {hoveredBar && kpiHasUnit && (
                <Box>
                    {effectiveLoadingReportingUnits ? (
                        <CircularProgress size={20} sx={{ padding: '4px' }} />
                    ) : (
                        <>
                            <Box
                                sx={{
                                    backgroundColor: '#ECEFF1',
                                    borderBottom: '1px solid #ddd',
                                    padding: '6px',
                                    marginBottom: '4px',
                                    fontFamily: 'inherit',
                                }}
                            >
                                <Typography fontSize={'13px'}>{xLabel}</Typography>
                            </Box>
                            <Box sx={{ padding: '4px' }}>
                                <Table size="small">
                                    <TableHead>
                                        <TableRow>
                                            <TableCell>Site</TableCell>
                                            <TableCell>Value</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        <TableRow sx={{ backgroundColor: '#f9f9f9' }}>
                                            <TableCell
                                                sx={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    textAlign: 'left',
                                                    justifyContent: 'left',
                                                }}
                                            >
                                                <Box
                                                    sx={{
                                                        borderRadius: '50%',
                                                        width: '12px',
                                                        height: '12px',
                                                        position: 'relative',
                                                        marginRight: '10px',
                                                        backgroundColor: seriesColor,
                                                        top: '0px',
                                                    }}
                                                ></Box>
                                                {getSiteName()}
                                            </TableCell>
                                            <TableCell>{effectiveTotalSiteValue || columnValue}</TableCell>
                                        </TableRow>
                                    </TableBody>

                                    {effectiveTooltipRows.length > 0 && (
                                        <>
                                            <TableHead>
                                                <TableRow>
                                                    <TableCell>Unit</TableCell>
                                                    <TableCell>Value</TableCell>
                                                </TableRow>
                                            </TableHead>
                                            <TableBody>
                                                {effectiveTooltipRows.map((row, index) => (
                                                    <TableRow key={row.unit}>
                                                        <StripedCell index={index}>
                                                            {wrapLongUnitNames(row.unit)}
                                                        </StripedCell>
                                                        <StripedCell index={index}>{row.value}</StripedCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </>
                                    )}
                                </Table>
                            </Box>
                        </>
                    )}
                </Box>
            )}
            {hoveredBar && !kpiHasUnit && (
                <Box>
                    <Box
                        sx={{
                            backgroundColor: '#ECEFF1',
                            borderBottom: '1px solid #ddd',
                            padding: '6px',
                            marginBottom: '4px',
                            fontFamily: 'inherit',
                        }}
                    >
                        <Typography fontSize={'13px'}>{xLabel}</Typography>
                    </Box>
                    <Box
                        sx={{
                            padding: '4px 8px',
                            display: 'flex',
                            alignItems: 'center',
                            textAlign: 'left',
                            justifyContent: 'left',
                        }}
                    >
                        <Box
                            sx={{
                                borderRadius: '50%',
                                width: '12px',
                                height: '12px',
                                position: 'relative',
                                marginRight: '10px',
                                backgroundColor: seriesColor,
                                top: '0px',
                            }}
                        ></Box>
                        <Typography fontSize={'13px'} sx={{ marginRight: '10px' }}>
                            {getSiteName()}:
                        </Typography>
                        <Typography fontWeight={'bold'} fontSize={'13px'}>
                            {columnValue}
                        </Typography>
                    </Box>
                </Box>
            )}
        </Box>
    )
}
