import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { Checklist } from '../models/checklist'
import { ArrayEntity } from '../models'
import { ChecklistItem } from '../models/checklist-item'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface ChecklistQueryRequest {
    title?: string
    status?: string
    assignedTo?: string
    templateIds?: string[]
    start?: number
    end?: number
    nextPage?: string
    first?: number
    rangeByLastUpdatedTime?: boolean
    limitForItems: number
}

const buildChecklistItemsStatusQuery = (request: ChecklistQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.title && request.title != '') {
        filter.push(`{ title: { eq: "${request.title}" }}`)
    }

    if (request.templateIds?.length) {
        filter.push(`{ sourceId: { in: [${request.templateIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.status && request.status != '') {
        filter.push(`{ status: { eq: "${request.status}" }}`)
    }

    if (request.assignedTo && request.assignedTo != '') {
        filter.push(`{ assignedTo: { containsAny: ["${request.assignedTo}"] }}`)
    }

    if (request.end && request.start) {
        if (request.rangeByLastUpdatedTime) {
            filter.push(`{ lastUpdatedTime: { gte: ${request.start} }}`)
            filter.push(`{ lastUpdatedTime: { lte: ${request.end} }}`)
        } else {
            filter.push(`{
            or: [
              {
                and: [
                  { startTime: { gte: ${request.start} }},
                  { startTime: { lte: ${request.end} }}
                ]
              },
              {
                and: [
                  { endTime: { gte: ${request.start} }},
                  { endTime: { lte: ${request.end} }}
                ]
              }
            ]
          }`)
        }
    }

    filter.push(
        `{
          space: {
            in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
          }
      }`
    )

    filter.push(`{ not: { isArchived: { eq: true } } }`)

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    if (!request.first) {
        request.first = 100
    }

    const query = `
    query GetChecklistItemStatusData {
        listChecklist(
            filter: ${queryFilter}
        , first: ${request.first}, after: ${request.nextPage ? `"${request.nextPage}"` : 'null'}) {
          items {
            externalId
            status
            sourceId
            startTime
            checklistItems (first: ${request.limitForItems}) {
              items {
                externalId
                status
                endTime
              }
            }
          }
          pageInfo {
            hasPreviousPage
            hasNextPage
            startCursor
            endCursor
          }
        }
      }
    `

    return query
}

export const useChecklistsItemsStatus = (request: ChecklistQueryRequest) => {
    const query = buildChecklistItemsStatusQuery(request)

    const {
        data: fdmData,
        loading,
        pageInfo,
    } = useGraphqlQuery<Checklist>(gql(query), 'listChecklist', { fetchPolicy: 'no-cache' })

    const [resultData, setResultData] = useState<{
        data: Checklist[]
        loading: boolean
        pagging: boolean
        pageInfo: PageInfo
    }>({
        data: [],
        loading: true,
        pagging: false,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: loading, pageInfo, pagging: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayChecklistItemEntity = d.checklistItems as any as ArrayEntity<ChecklistItem>

                return {
                    ...d,
                    checklistItems: arrayChecklistItemEntity.items,
                }
            })

            setResultData({ data: fdmDataParsed, loading: loading, pageInfo, pagging: Boolean(request.nextPage) })
        }
    }, [fdmData, pageInfo, loading])

    return {
        loadingChecklistsItemsStatus: resultData.loading,
        checklistsItemsStatus: resultData.data,
        pageInfo: resultData.pageInfo,
        pagging: resultData.pagging,
    }
}
