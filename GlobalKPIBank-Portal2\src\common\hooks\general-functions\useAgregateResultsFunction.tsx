import { LazyQueryHookOptions } from '@apollo/client'
import { useCallback } from 'react'
import { AggregateFunctionPromise, useFdmAgregate } from '../cognite/useFdmAgregate'

export function useAgregateResultsFunction<T>(externalId: string) {
    const [agregateFunctionFdm] = useFdmAgregate()
    return {
        getAllResults: useCallback(
            async (filter?: T, options?: LazyQueryHookOptions): Promise<AggregateFunctionPromise[]> => {
                const res = await agregateFunctionFdm({ externalId: externalId, filter: filter })
                return res?.items ?? []
            },
            [externalId]
        ),
    }
}
