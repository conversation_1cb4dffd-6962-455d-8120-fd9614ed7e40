"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-virtuoso";
exports.ids = ["vendor-chunks/react-virtuoso"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-virtuoso/dist/index.cjs":
/*!****************************************************!*\
  !*** ./node_modules/react-virtuoso/dist/index.cjs ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("Object.defineProperty(exports,Symbol.toStringTag,{value:\"Module\"});const L=__webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\"),R=__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"),oo=__webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\"),we=0,zt=1,Kt=2,Rn=4;function rn(t){return()=>t}function ro(t){t()}function te(t,e){return n=>t(e(n))}function sn(t,e){return()=>t(e)}function En(t,e){return n=>t(e,n)}function Ve(t){return t!==void 0}function so(...t){return()=>{t.map(ro)}}function Gt(){}function Ce(t,e){return e(t),t}function io(t,e){return e(t)}function X(...t){return t}function U(t,e){return t(zt,e)}function A(t,e){t(we,e)}function Pe(t){t(Kt)}function lt(t){return t(Rn)}function F(t,e){return U(t,En(e,we))}function Rt(t,e){const n=t(zt,o=>{n(),e(o)});return n}function ln(t){let e,n;return o=>r=>{e=r,n&&clearTimeout(n),n=setTimeout(()=>{o(e)},t)}}function Hn(t,e){return t===e}function Y(t=Hn){let e;return n=>o=>{t(e,o)||(e=o,n(o))}}function P(t){return e=>n=>{t(n)&&e(n)}}function H(t){return e=>te(e,t)}function vt(t){return e=>()=>{e(t)}}function S(t,...e){const n=lo(...e);return(o,r)=>{switch(o){case Kt:Pe(t);return;case zt:return U(t,n(r))}}}function bt(t,e){return n=>o=>{n(e=t(e,o))}}function Ut(t){return e=>n=>{t>0?t--:e(n)}}function Lt(t){let e=null,n;return o=>r=>{e=r,!n&&(n=setTimeout(()=>{n=void 0,o(e)},t))}}function G(...t){const e=new Array(t.length);let n=0,o=null;const r=Math.pow(2,t.length)-1;return t.forEach((s,i)=>{const l=Math.pow(2,i);U(s,c=>{const d=n;n=n|l,e[i]=c,d!==r&&n===r&&o&&(o(),o=null)})}),s=>i=>{const l=()=>{s([i].concat(e))};n===r?l():o=l}}function lo(...t){return e=>t.reduceRight(io,e)}function co(t){let e,n;const o=()=>e==null?void 0:e();return function(r,s){switch(r){case zt:return s?n===s?void 0:(o(),n=s,e=U(t,s),e):(o(),Gt);case Kt:o(),n=null;return}}}function w(t){let e=t;const n=D();return(o,r)=>{switch(o){case we:e=r;break;case zt:{r(e);break}case Rn:return e}return n(o,r)}}function ct(t,e){return Ce(w(e),n=>F(t,n))}function D(){const t=[];return(e,n)=>{switch(e){case we:t.slice().forEach(o=>{o(n)});return;case Kt:t.splice(0,t.length);return;case zt:return t.push(n),()=>{const o=t.indexOf(n);o>-1&&t.splice(o,1)}}}}function ht(t){return Ce(D(),e=>F(t,e))}function $(t,e=[],{singleton:n}={singleton:!0}){return{constructor:t,dependencies:e,id:uo(),singleton:n}}const uo=()=>Symbol();function ao(t){const e=new Map,n=({constructor:o,dependencies:r,id:s,singleton:i})=>{if(i&&e.has(s))return e.get(s);const l=o(r.map(c=>n(c)));return i&&e.set(s,l),l};return n(t)}function rt(...t){const e=D(),n=new Array(t.length);let o=0;const r=Math.pow(2,t.length)-1;return t.forEach((s,i)=>{const l=Math.pow(2,i);U(s,c=>{n[i]=c,o=o|l,o===r&&A(e,n)})}),function(s,i){switch(s){case Kt:{Pe(e);return}case zt:return o===r&&i(n),U(e,i)}}}function M(t,e=Hn){return S(t,Y(e))}function cn(...t){return function(e,n){switch(e){case Kt:return;case zt:return so(...t.map(o=>U(o,n)))}}}var mt=(t=>(t[t.DEBUG=0]=\"DEBUG\",t[t.INFO=1]=\"INFO\",t[t.WARN=2]=\"WARN\",t[t.ERROR=3]=\"ERROR\",t))(mt||{});const fo={0:\"debug\",3:\"error\",1:\"log\",2:\"warn\"},mo=()=>typeof globalThis>\"u\"?window:globalThis,Vt=$(()=>{const t=w(3);return{log:w((n,o,r=1)=>{var i;const s=(i=mo().VIRTUOSO_LOG_LEVEL)!=null?i:lt(t);r>=s&&console[fo[r]](\"%creact-virtuoso: %c%s %o\",\"color: #0253b3; font-weight: bold\",\"color: initial\",n,o)}),logLevel:t}},[],{singleton:!0});function Et(t,e,n){return Me(t,e,n).callbackRef}function Me(t,e,n){const o=R.useRef(null);let r=s=>{};if(typeof ResizeObserver<\"u\"){const s=R.useMemo(()=>new ResizeObserver(i=>{const l=()=>{const c=i[0].target;c.offsetParent!==null&&t(c)};n?l():requestAnimationFrame(l)}),[t]);r=i=>{i&&e?(s.observe(i),o.current=i):(o.current&&s.unobserve(o.current),o.current=null)}}return{callbackRef:r,ref:o}}function kn(t,e,n,o,r,s,i,l,c){const d=R.useCallback(f=>{const I=po(f.children,e,l?\"offsetWidth\":\"offsetHeight\",r);let p=f.parentElement;for(;!p.dataset.virtuosoScroller;)p=p.parentElement;const m=p.lastElementChild.dataset.viewportType===\"window\",C=i?l?i.scrollLeft:i.scrollTop:m?l?window.pageXOffset||document.documentElement.scrollLeft:window.pageYOffset||document.documentElement.scrollTop:l?p.scrollLeft:p.scrollTop,x=i?l?i.scrollWidth:i.scrollHeight:m?l?document.documentElement.scrollWidth:document.documentElement.scrollHeight:l?p.scrollWidth:p.scrollHeight,y=i?l?i.offsetWidth:i.offsetHeight:m?l?window.innerWidth:window.innerHeight:l?p.offsetWidth:p.offsetHeight;o({scrollHeight:x,scrollTop:Math.max(C,0),viewportHeight:y}),s==null||s(l?un(\"column-gap\",getComputedStyle(f).columnGap,r):un(\"row-gap\",getComputedStyle(f).rowGap,r)),I!==null&&t(I)},[t,e,r,s,i,o]);return Me(d,n,c)}function po(t,e,n,o){const r=t.length;if(r===0)return null;const s=[];for(let i=0;i<r;i++){const l=t.item(i);if(l.dataset.index===void 0)continue;const c=parseInt(l.dataset.index),d=parseFloat(l.dataset.knownSize),f=e(l,n);if(f===0&&o(\"Zero-sized element, this should not happen\",{child:l},mt.ERROR),f===d)continue;const I=s[s.length-1];s.length===0||I.size!==f||I.endIndex!==c-1?s.push({endIndex:c,size:f,startIndex:c}):s[s.length-1].endIndex++}return s}function un(t,e,n){return e!==\"normal\"&&!(e!=null&&e.endsWith(\"px\"))&&n(`${t} was not resolved to pixel value correctly`,e,mt.WARN),e===\"normal\"?0:parseInt(e!=null?e:\"0\",10)}function je(t,e,n){const o=R.useRef(null),r=R.useCallback(c=>{if(!(c!=null&&c.offsetParent))return;const d=c.getBoundingClientRect(),f=d.width;let I,p;if(e){const m=e.getBoundingClientRect(),C=d.top-m.top;p=m.height-Math.max(0,C),I=C+e.scrollTop}else p=window.innerHeight-Math.max(0,d.top),I=d.top+window.pageYOffset;o.current={offsetTop:I,visibleHeight:p,visibleWidth:f},t(o.current)},[t,e]),{callbackRef:s,ref:i}=Me(r,!0,n),l=R.useCallback(()=>{r(i.current)},[r,i]);return R.useEffect(()=>{if(e){e.addEventListener(\"scroll\",l);const c=new ResizeObserver(()=>{requestAnimationFrame(l)});return c.observe(e),()=>{e.removeEventListener(\"scroll\",l),c.unobserve(e)}}else return window.addEventListener(\"scroll\",l),window.addEventListener(\"resize\",l),()=>{window.removeEventListener(\"scroll\",l),window.removeEventListener(\"resize\",l)}},[l,e]),s}const at=$(()=>{const t=D(),e=D(),n=w(0),o=D(),r=w(0),s=D(),i=D(),l=w(0),c=w(0),d=w(0),f=w(0),I=D(),p=D(),m=w(!1),C=w(!1),x=w(!1);return F(S(t,H(({scrollTop:y})=>y)),e),F(S(t,H(({scrollHeight:y})=>y)),i),F(e,r),{deviation:n,fixedFooterHeight:d,fixedHeaderHeight:c,footerHeight:f,headerHeight:l,horizontalDirection:C,scrollBy:p,scrollContainerState:t,scrollHeight:i,scrollingInProgress:m,scrollTo:I,scrollTop:e,skipAnimationFrameInResizeObserver:x,smoothScrollTargetReached:o,statefulScrollTop:r,viewportHeight:s}},[],{singleton:!0}),ee={lvl:0};function Bn(t,e){const n=t.length;if(n===0)return[];let{index:o,value:r}=e(t[0]);const s=[];for(let i=1;i<n;i++){const{index:l,value:c}=e(t[i]);s.push({end:l-1,start:o,value:r}),o=l,r=c}return s.push({end:1/0,start:o,value:r}),s}function K(t){return t===ee}function ne(t,e){if(!K(t))return e===t.k?t.v:e<t.k?ne(t.l,e):ne(t.r,e)}function wt(t,e,n=\"k\"){if(K(t))return[-1/0,void 0];if(Number(t[n])===e)return[t.k,t.v];if(Number(t[n])<e){const o=wt(t.r,e,n);return o[0]===-1/0?[t.k,t.v]:o}return wt(t.l,e,n)}function pt(t,e,n){return K(t)?Ln(e,n,1):e===t.k?ot(t,{k:e,v:n}):e<t.k?an(ot(t,{l:pt(t.l,e,n)})):an(ot(t,{r:pt(t.r,e,n)}))}function $t(){return ee}function ye(t,e,n){if(K(t))return[];const o=wt(t,e)[0];return ho(Oe(t,o,n))}function Be(t,e){if(K(t))return ee;const{k:n,l:o,r}=t;if(e===n){if(K(o))return r;if(K(r))return o;{const[s,i]=Fn(o);return he(ot(t,{k:s,l:On(o),v:i}))}}else return e<n?he(ot(t,{l:Be(o,e)})):he(ot(t,{r:Be(r,e)}))}function Wt(t){return K(t)?[]:[...Wt(t.l),{k:t.k,v:t.v},...Wt(t.r)]}function Oe(t,e,n){if(K(t))return[];const{k:o,l:r,r:s,v:i}=t;let l=[];return o>e&&(l=l.concat(Oe(r,e,n))),o>=e&&o<=n&&l.push({k:o,v:i}),o<=n&&(l=l.concat(Oe(s,e,n))),l}function he(t){const{l:e,lvl:n,r:o}=t;if(o.lvl>=n-1&&e.lvl>=n-1)return t;if(n>o.lvl+1){if(Re(e))return zn(ot(t,{lvl:n-1}));if(!K(e)&&!K(e.r))return ot(e.r,{l:ot(e,{r:e.r.l}),lvl:n,r:ot(t,{l:e.r.r,lvl:n-1})});throw new Error(\"Unexpected empty nodes\")}else{if(Re(t))return Fe(ot(t,{lvl:n-1}));if(!K(o)&&!K(o.l)){const r=o.l,s=Re(r)?o.lvl-1:o.lvl;return ot(r,{l:ot(t,{lvl:n-1,r:r.l}),lvl:r.lvl+1,r:Fe(ot(o,{l:r.r,lvl:s}))})}else throw new Error(\"Unexpected empty nodes\")}}function ot(t,e){return Ln(e.k!==void 0?e.k:t.k,e.v!==void 0?e.v:t.v,e.lvl!==void 0?e.lvl:t.lvl,e.l!==void 0?e.l:t.l,e.r!==void 0?e.r:t.r)}function On(t){return K(t.r)?t.l:he(ot(t,{r:On(t.r)}))}function Re(t){return K(t)||t.lvl>t.r.lvl}function Fn(t){return K(t.r)?[t.k,t.v]:Fn(t.r)}function Ln(t,e,n,o=ee,r=ee){return{k:t,l:o,lvl:n,r,v:e}}function an(t){return Fe(zn(t))}function zn(t){const{l:e}=t;return!K(e)&&e.lvl===t.lvl?ot(e,{r:ot(t,{l:e.r})}):t}function Fe(t){const{lvl:e,r:n}=t;return!K(n)&&!K(n.r)&&n.lvl===e&&n.r.lvl===e?ot(n,{l:ot(t,{r:n.l}),lvl:e+1}):t}function ho(t){return Bn(t,({k:e,v:n})=>({index:e,value:n}))}function Vn(t,e){return!!(t&&t.startIndex===e.startIndex&&t.endIndex===e.endIndex)}function oe(t,e){return!!(t&&t[0]===e[0]&&t[1]===e[1])}const Ae=$(()=>({recalcInProgress:w(!1)}),[],{singleton:!0});function Pn(t,e,n){return t[xe(t,e,n)]}function xe(t,e,n,o=0){let r=t.length-1;for(;o<=r;){const s=Math.floor((o+r)/2),i=t[s],l=n(i,e);if(l===0)return s;if(l===-1){if(r-o<2)return s-1;r=s-1}else{if(r===o)return s;o=s+1}}throw new Error(`Failed binary finding record in array - ${t.join(\",\")}, searched for ${e}`)}function go(t,e,n,o){const r=xe(t,e,o),s=xe(t,n,o,r);return t.slice(r,s+1)}function Ct(t,e){return Math.round(t.getBoundingClientRect()[e])}function ve(t){return!K(t.groupOffsetTree)}function We({index:t},e){return e===t?0:e<t?-1:1}function xo(){return{groupIndices:[],groupOffsetTree:$t(),lastIndex:0,lastOffset:0,lastSize:0,offsetTree:[],sizeTree:$t()}}function Io(t,e){let n=K(t)?0:1/0;for(const o of e){const{endIndex:r,size:s,startIndex:i}=o;if(n=Math.min(n,i),K(t)){t=pt(t,0,s);continue}const l=ye(t,i-1,r+1);if(l.some(bo(o)))continue;let c=!1,d=!1;for(const{end:f,start:I,value:p}of l)c?(r>=I||s===p)&&(t=Be(t,I)):(d=p!==s,c=!0),f>r&&r>=I&&p!==s&&(t=pt(t,r+1,p));d&&(t=pt(t,i,s))}return[t,n]}function So(t){return typeof t.groupIndex<\"u\"}function To({offset:t},e){return e===t?0:e<t?-1:1}function re(t,e,n){if(e.length===0)return 0;const{index:o,offset:r,size:s}=Pn(e,t,We),i=t-o,l=s*i+(i-1)*n+r;return l>0?l+n:l}function Mn(t,e){if(!ve(e))return t;let n=0;for(;e.groupIndices[n]<=t+n;)n++;return t+n}function jn(t,e,n){if(So(t))return e.groupIndices[t.groupIndex]+1;{const o=t.index===\"LAST\"?n:t.index;let r=Mn(o,e);return r=Math.max(0,r,Math.min(n,r)),r}}function wo(t,e,n,o=0){return o>0&&(e=Math.max(e,Pn(t,o,We).offset)),Bn(go(t,e,n,To),vo)}function Co(t,[e,n,o,r]){e.length>0&&o(\"received item sizes\",e,mt.DEBUG);const s=t.sizeTree;let i=s,l=0;if(n.length>0&&K(s)&&e.length===2){const p=e[0].size,m=e[1].size;i=n.reduce((C,x)=>pt(pt(C,x,p),x+1,m),i)}else[i,l]=Io(i,e);if(i===s)return t;const{lastIndex:c,lastOffset:d,lastSize:f,offsetTree:I}=Le(t.offsetTree,l,i,r);return{groupIndices:n,groupOffsetTree:n.reduce((p,m)=>pt(p,m,re(m,I,r)),$t()),lastIndex:c,lastOffset:d,lastSize:f,offsetTree:I,sizeTree:i}}function yo(t){return Wt(t).map(({k:e,v:n},o,r)=>{const s=r[o+1];return{endIndex:s?s.k-1:1/0,size:n,startIndex:e}})}function dn(t,e){let n=0,o=0;for(;n<t;)n+=e[o+1]-e[o]-1,o++;return o-(n===t?0:1)}function Le(t,e,n,o){let r=t,s=0,i=0,l=0,c=0;if(e!==0){c=xe(r,e-1,We),l=r[c].offset;const f=wt(n,e-1);s=f[0],i=f[1],r.length&&r[c].size===wt(n,e)[1]&&(c-=1),r=r.slice(0,c+1)}else r=[];for(const{start:d,value:f}of ye(n,e,1/0)){const I=d-s,p=I*i+l+I*o;r.push({index:d,offset:p,size:f}),s=d,l=p,i=f}return{lastIndex:s,lastOffset:l,lastSize:i,offsetTree:r}}function vo(t){return{index:t.index,value:t}}function bo(t){const{endIndex:e,size:n,startIndex:o}=t;return r=>r.start===o&&(r.end===e||r.end===1/0)&&r.value===n}const Ro={offsetHeight:\"height\",offsetWidth:\"width\"},Ht=$(([{log:t},{recalcInProgress:e}])=>{const n=D(),o=D(),r=ct(o,0),s=D(),i=D(),l=w(0),c=w([]),d=w(void 0),f=w(void 0),I=w((h,a)=>Ct(h,Ro[a])),p=w(void 0),m=w(0),C=xo(),x=ct(S(n,G(c,t,m),bt(Co,C),Y()),C),y=ct(S(c,Y(),bt((h,a)=>({current:a,prev:h.current}),{current:[],prev:[]}),H(({prev:h})=>h)),[]);F(S(c,P(h=>h.length>0),G(x,m),H(([h,a,E])=>{const B=h.reduce((O,z,V)=>pt(O,z,re(z,a.offsetTree,E)||V),$t());return{...a,groupIndices:h,groupOffsetTree:B}})),x),F(S(o,G(x),P(([h,{lastIndex:a}])=>h<a),H(([h,{lastIndex:a,lastSize:E}])=>[{endIndex:a,size:E,startIndex:h}])),n),F(d,f);const g=ct(S(d,H(h=>h===void 0)),!0);F(S(f,P(h=>h!==void 0&&K(lt(x).sizeTree)),H(h=>[{endIndex:0,size:h,startIndex:0}])),n);const u=ht(S(n,G(x),bt(({sizes:h},[a,E])=>({changed:E!==h,sizes:E}),{changed:!1,sizes:C}),H(h=>h.changed)));U(S(l,bt((h,a)=>({diff:h.prev-a,prev:a}),{diff:0,prev:0}),H(h=>h.diff)),h=>{const{groupIndices:a}=lt(x);if(h>0)A(e,!0),A(s,h+dn(h,a));else if(h<0){const E=lt(y);E.length>0&&(h-=dn(-h,E)),A(i,h)}}),U(S(l,G(t)),([h,a])=>{h<0&&a(\"`firstItemIndex` prop should not be set to less than zero. If you don't know the total count, just use a very high value\",{firstItemIndex:l},mt.ERROR)});const T=ht(s);F(S(s,G(x),H(([h,a])=>{const E=a.groupIndices.length>0,B=[],O=a.lastSize;if(E){const z=ne(a.sizeTree,0);let V=0,N=0;for(;V<h;){const k=a.groupIndices[N],q=a.groupIndices.length===N+1?1/0:a.groupIndices[N+1]-k-1;B.push({endIndex:k,size:z,startIndex:k}),B.push({endIndex:k+1+q-1,size:O,startIndex:k+1}),N++,V+=q+1}const J=Wt(a.sizeTree);return V!==h&&J.shift(),J.reduce((k,{k:q,v:st})=>{let dt=k.ranges;return k.prevSize!==0&&(dt=[...k.ranges,{endIndex:q+h-1,size:k.prevSize,startIndex:k.prevIndex}]),{prevIndex:q+h,prevSize:st,ranges:dt}},{prevIndex:h,prevSize:0,ranges:B}).ranges}return Wt(a.sizeTree).reduce((z,{k:V,v:N})=>({prevIndex:V+h,prevSize:N,ranges:[...z.ranges,{endIndex:V+h-1,size:z.prevSize,startIndex:z.prevIndex}]}),{prevIndex:0,prevSize:O,ranges:[]}).ranges})),n);const b=ht(S(i,G(x,m),H(([h,{offsetTree:a},E])=>{const B=-h;return re(B,a,E)})));return F(S(i,G(x,m),H(([h,a,E])=>{if(a.groupIndices.length>0){if(K(a.sizeTree))return a;let O=$t();const z=lt(y);let V=0,N=0,J=0;for(;V<-h;){J=z[N];const k=z[N+1]-J-1;N++,V+=k+1}if(O=Wt(a.sizeTree).reduce((k,{k:q,v:st})=>pt(k,Math.max(0,q+h),st),O),V!==-h){const k=ne(a.sizeTree,J);O=pt(O,0,k);const q=wt(a.sizeTree,-h+1)[1];O=pt(O,1,q)}return{...a,sizeTree:O,...Le(a.offsetTree,0,O,E)}}else{const O=Wt(a.sizeTree).reduce((z,{k:V,v:N})=>pt(z,Math.max(0,V+h),N),$t());return{...a,sizeTree:O,...Le(a.offsetTree,0,O,E)}}})),x),{beforeUnshiftWith:T,data:p,defaultItemSize:f,firstItemIndex:l,fixedItemSize:d,gap:m,groupIndices:c,itemSize:I,listRefresh:u,shiftWith:i,shiftWithOffset:b,sizeRanges:n,sizes:x,statefulTotalCount:r,totalCount:o,trackItemSizes:g,unshiftWith:s}},X(Vt,Ae),{singleton:!0});function Eo(t){return t.reduce((e,n)=>(e.groupIndices.push(e.totalCount),e.totalCount+=n+1,e),{groupIndices:[],totalCount:0})}const An=$(([{groupIndices:t,sizes:e,totalCount:n},{headerHeight:o,scrollTop:r}])=>{const s=D(),i=D(),l=ht(S(s,H(Eo)));return F(S(l,H(c=>c.totalCount)),n),F(S(l,H(c=>c.groupIndices)),t),F(S(rt(r,e,o),P(([c,d])=>ve(d)),H(([c,d,f])=>wt(d.groupOffsetTree,Math.max(c-f,0),\"v\")[0]),Y(),H(c=>[c])),i),{groupCounts:s,topItemsIndexes:i}},X(Ht,at)),Pt=$(([{log:t}])=>{const e=w(!1),n=ht(S(e,P(o=>o),Y()));return U(e,o=>{o&&lt(t)(\"props updated\",{},mt.DEBUG)}),{didMount:n,propsReady:e}},X(Vt),{singleton:!0}),Ho=typeof document<\"u\"&&\"scrollBehavior\"in document.documentElement.style;function Wn(t){const e=typeof t==\"number\"?{index:t}:t;return e.align||(e.align=\"start\"),(!e.behavior||!Ho)&&(e.behavior=\"auto\"),e.offset||(e.offset=0),e}const ie=$(([{gap:t,listRefresh:e,sizes:n,totalCount:o},{fixedFooterHeight:r,fixedHeaderHeight:s,footerHeight:i,headerHeight:l,scrollingInProgress:c,scrollTo:d,smoothScrollTargetReached:f,viewportHeight:I},{log:p}])=>{const m=D(),C=D(),x=w(0);let y=null,g=null,u=null;function T(){y&&(y(),y=null),u&&(u(),u=null),g&&(clearTimeout(g),g=null),A(c,!1)}return F(S(m,G(n,I,o,x,l,i,p),G(t,s,r),H(([[b,h,a,E,B,O,z,V],N,J,nt])=>{const k=Wn(b),{align:q,behavior:st,offset:dt}=k,It=E-1,ft=jn(k,h,It);let ut=re(ft,h.offsetTree,N)+O;q===\"end\"?(ut+=J+wt(h.sizeTree,ft)[1]-a+nt,ft===It&&(ut+=z)):q===\"center\"?ut+=(J+wt(h.sizeTree,ft)[1]-a+nt)/2:ut-=B,dt&&(ut+=dt);const Mt=St=>{T(),St?(V(\"retrying to scroll to\",{location:b},mt.DEBUG),A(m,b)):(A(C,!0),V(\"list did not change, scroll successful\",{},mt.DEBUG))};if(T(),st===\"smooth\"){let St=!1;u=U(e,Yt=>{St=St||Yt}),y=Rt(f,()=>{Mt(St)})}else y=Rt(S(e,ko(150)),Mt);return g=setTimeout(()=>{T()},1200),A(c,!0),V(\"scrolling from index to\",{behavior:st,index:ft,top:ut},mt.DEBUG),{behavior:st,top:ut}})),d),{scrollTargetReached:C,scrollToIndex:m,topListHeight:x}},X(Ht,at,Vt),{singleton:!0});function ko(t){return e=>{const n=setTimeout(()=>{e(!1)},t);return o=>{o&&(e(!0),clearTimeout(n))}}}function Ge(t,e){t==0?e():requestAnimationFrame(()=>{Ge(t-1,e)})}function _e(t,e){const n=e-1;return typeof t==\"number\"?t:t.index===\"LAST\"?n:t.index}const le=$(([{defaultItemSize:t,listRefresh:e,sizes:n},{scrollTop:o},{scrollTargetReached:r,scrollToIndex:s},{didMount:i}])=>{const l=w(!0),c=w(0),d=w(!0);return F(S(i,G(c),P(([f,I])=>!!I),vt(!1)),l),F(S(i,G(c),P(([f,I])=>!!I),vt(!1)),d),U(S(rt(e,i),G(l,n,t,d),P(([[,f],I,{sizeTree:p},m,C])=>f&&(!K(p)||Ve(m))&&!I&&!C),G(c)),([,f])=>{Rt(r,()=>{A(d,!0)}),Ge(4,()=>{Rt(o,()=>{A(l,!0)}),A(s,f)})}),{initialItemFinalLocationReached:d,initialTopMostItemIndex:c,scrolledToInitialItem:l}},X(Ht,at,ie,Pt),{singleton:!0});function Gn(t,e){return Math.abs(t-e)<1.01}const se=\"up\",Jt=\"down\",Bo=\"none\",Oo={atBottom:!1,notAtBottomBecause:\"NOT_SHOWING_LAST_ITEM\",state:{offsetBottom:0,scrollHeight:0,scrollTop:0,viewportHeight:0}},Fo=0,ce=$(([{footerHeight:t,headerHeight:e,scrollBy:n,scrollContainerState:o,scrollTop:r,viewportHeight:s}])=>{const i=w(!1),l=w(!0),c=D(),d=D(),f=w(4),I=w(Fo),p=ct(S(cn(S(M(r),Ut(1),vt(!0)),S(M(r),Ut(1),vt(!1),ln(100))),Y()),!1),m=ct(S(cn(S(n,vt(!0)),S(n,vt(!1),ln(200))),Y()),!1);F(S(rt(M(r),M(I)),H(([u,T])=>u<=T),Y()),l),F(S(l,Lt(50)),d);const C=ht(S(rt(o,M(s),M(e),M(t),M(f)),bt((u,[{scrollHeight:T,scrollTop:b},h,a,E,B])=>{const O=b+h-T>-B,z={scrollHeight:T,scrollTop:b,viewportHeight:h};if(O){let N,J;return b>u.state.scrollTop?(N=\"SCROLLED_DOWN\",J=u.state.scrollTop-b):(N=\"SIZE_DECREASED\",J=u.state.scrollTop-b||u.scrollTopDelta),{atBottom:!0,atBottomBecause:N,scrollTopDelta:J,state:z}}let V;return z.scrollHeight>u.state.scrollHeight?V=\"SIZE_INCREASED\":h<u.state.viewportHeight?V=\"VIEWPORT_HEIGHT_DECREASING\":b<u.state.scrollTop?V=\"SCROLLING_UPWARDS\":V=\"NOT_FULLY_SCROLLED_TO_LAST_ITEM_BOTTOM\",{atBottom:!1,notAtBottomBecause:V,state:z}},Oo),Y((u,T)=>u&&u.atBottom===T.atBottom))),x=ct(S(o,bt((u,{scrollHeight:T,scrollTop:b,viewportHeight:h})=>{if(Gn(u.scrollHeight,T))return{changed:!1,jump:0,scrollHeight:T,scrollTop:b};{const a=T-(b+h)<1;return u.scrollTop!==b&&a?{changed:!0,jump:u.scrollTop-b,scrollHeight:T,scrollTop:b}:{changed:!0,jump:0,scrollHeight:T,scrollTop:b}}},{changed:!1,jump:0,scrollHeight:0,scrollTop:0}),P(u=>u.changed),H(u=>u.jump)),0);F(S(C,H(u=>u.atBottom)),i),F(S(i,Lt(50)),c);const y=w(Jt);F(S(o,H(({scrollTop:u})=>u),Y(),bt((u,T)=>lt(m)?{direction:u.direction,prevScrollTop:T}:{direction:T<u.prevScrollTop?se:Jt,prevScrollTop:T},{direction:Jt,prevScrollTop:0}),H(u=>u.direction)),y),F(S(o,Lt(50),vt(Bo)),y);const g=w(0);return F(S(p,P(u=>!u),vt(0)),g),F(S(r,Lt(100),G(p),P(([u,T])=>!!T),bt(([u,T],[b])=>[T,b],[0,0]),H(([u,T])=>T-u)),g),{atBottomState:C,atBottomStateChange:c,atBottomThreshold:f,atTopStateChange:d,atTopThreshold:I,isAtBottom:i,isAtTop:l,isScrolling:p,lastJumpDueToItemResize:x,scrollDirection:y,scrollVelocity:g}},X(at)),Ie=\"top\",Se=\"bottom\",fn=\"none\";function mn(t,e,n){return typeof t==\"number\"?n===se&&e===Ie||n===Jt&&e===Se?t:0:n===se?e===Ie?t.main:t.reverse:e===Se?t.main:t.reverse}function pn(t,e){var n;return typeof t==\"number\"?t:(n=t[e])!=null?n:0}const Ne=$(([{deviation:t,fixedHeaderHeight:e,headerHeight:n,scrollTop:o,viewportHeight:r}])=>{const s=D(),i=w(0),l=w(0),c=w(0),d=ct(S(rt(M(o),M(r),M(n),M(s,oe),M(c),M(i),M(e),M(t),M(l)),H(([f,I,p,[m,C],x,y,g,u,T])=>{const b=f-u,h=y+g,a=Math.max(p-b,0);let E=fn;const B=pn(T,Ie),O=pn(T,Se);return m-=u,m+=p+g,C+=p+g,C-=u,m>f+h-B&&(E=se),C<f-a+I+O&&(E=Jt),E!==fn?[Math.max(b-p-mn(x,Ie,E)-B,0),b-a-g+I+mn(x,Se,E)+O]:null}),P(f=>f!=null),Y(oe)),[0,0]);return{increaseViewportBy:l,listBoundary:s,overscan:c,topListHeight:i,visibleRange:d}},X(at),{singleton:!0});function Lo(t,e,n){if(ve(e)){const o=Mn(t,e);return[{index:wt(e.groupOffsetTree,o)[0],offset:0,size:0},{data:n==null?void 0:n[0],index:o,offset:0,size:0}]}return[{data:n==null?void 0:n[0],index:t,offset:0,size:0}]}const Ee={bottom:0,firstItemIndex:0,items:[],offsetBottom:0,offsetTop:0,top:0,topItems:[],topListHeight:0,totalCount:0};function ge(t,e,n,o,r,s){const{lastIndex:i,lastOffset:l,lastSize:c}=r;let d=0,f=0;if(t.length>0){d=t[0].offset;const x=t[t.length-1];f=x.offset+x.size}const I=n-i,p=l+I*c+(I-1)*o,m=d,C=p-f;return{bottom:f,firstItemIndex:s,items:hn(t,r,s),offsetBottom:C,offsetTop:d,top:m,topItems:hn(e,r,s),topListHeight:e.reduce((x,y)=>y.size+x,0),totalCount:n}}function _n(t,e,n,o,r,s){let i=0;if(n.groupIndices.length>0)for(const f of n.groupIndices){if(f-i>=t)break;i++}const l=t+i,c=_e(e,l),d=Array.from({length:l}).map((f,I)=>({data:s[I+c],index:I+c,offset:0,size:0}));return ge(d,[],l,r,n,o)}function hn(t,e,n){if(t.length===0)return[];if(!ve(e))return t.map(d=>({...d,index:d.index+n,originalIndex:d.index}));const o=t[0].index,r=t[t.length-1].index,s=[],i=ye(e.groupOffsetTree,o,r);let l,c=0;for(const d of t){(!l||l.end<d.index)&&(l=i.shift(),c=e.groupIndices.indexOf(l.start));let f;d.index===l.start?f={index:c,type:\"group\"}:f={groupIndex:c,index:d.index-(c+1)+n},s.push({...f,data:d.data,offset:d.offset,originalIndex:d.index,size:d.size})}return s}const _t=$(([{data:t,firstItemIndex:e,gap:n,sizes:o,totalCount:r},s,{listBoundary:i,topListHeight:l,visibleRange:c},{initialTopMostItemIndex:d,scrolledToInitialItem:f},{topListHeight:I},p,{didMount:m},{recalcInProgress:C}])=>{const x=w([]),y=w(0),g=D();F(s.topItemsIndexes,x);const u=ct(S(rt(m,C,M(c,oe),M(r),M(o),M(d),f,M(x),M(e),M(n),t),P(([a,E,,B,,,,,,,O])=>{const z=O&&O.length!==B;return a&&!E&&!z}),H(([,,[a,E],B,O,z,V,N,J,nt,k])=>{const q=O,{offsetTree:st,sizeTree:dt}=q,It=lt(y);if(B===0)return{...Ee,totalCount:B};if(a===0&&E===0)return It===0?{...Ee,totalCount:B}:_n(It,z,O,J,nt,k||[]);if(K(dt))return It>0?null:ge(Lo(_e(z,B),q,k),[],B,nt,q,J);const ft=[];if(N.length>0){const jt=N[0],yt=N[N.length-1];let kt=0;for(const v of ye(dt,jt,yt)){const _=v.value,Q=Math.max(v.start,jt),it=Math.min(v.end,yt);for(let tt=Q;tt<=it;tt++)ft.push({data:k==null?void 0:k[tt],index:tt,offset:kt,size:_}),kt+=_}}if(!V)return ge([],ft,B,nt,q,J);const ut=N.length>0?N[N.length-1]+1:0,Mt=wo(st,a,E,ut);if(Mt.length===0)return null;const St=B-1,Yt=Ce([],jt=>{for(const yt of Mt){const kt=yt.value;let v=kt.offset,_=yt.start;const Q=kt.size;if(kt.offset<a){_+=Math.floor((a-kt.offset+nt)/(Q+nt));const tt=_-yt.start;v+=tt*Q+tt*nt}_<ut&&(v+=(ut-_)*Q,_=ut);const it=Math.min(yt.end,St);for(let tt=_;tt<=it&&!(v>=E);tt++)jt.push({data:k==null?void 0:k[tt],index:tt,offset:v,size:Q}),v+=Q+nt}});return ge(Yt,ft,B,nt,q,J)}),P(a=>a!==null),Y()),Ee);F(S(t,P(Ve),H(a=>a==null?void 0:a.length)),r),F(S(u,H(a=>a.topListHeight)),I),F(I,l),F(S(u,H(a=>[a.top,a.bottom])),i),F(S(u,H(a=>a.items)),g);const T=ht(S(u,P(({items:a})=>a.length>0),G(r,t),P(([{items:a},E])=>a[a.length-1].originalIndex===E-1),H(([,a,E])=>[a-1,E]),Y(oe),H(([a])=>a))),b=ht(S(u,Lt(200),P(({items:a,topItems:E})=>a.length>0&&a[0].originalIndex===E.length),H(({items:a})=>a[0].index),Y())),h=ht(S(u,P(({items:a})=>a.length>0),H(({items:a})=>{let E=0,B=a.length-1;for(;a[E].type===\"group\"&&E<B;)E++;for(;a[B].type===\"group\"&&B>E;)B--;return{endIndex:a[B].index,startIndex:a[E].index}}),Y(Vn)));return{endReached:T,initialItemCount:y,itemsRendered:g,listState:u,rangeChanged:h,startReached:b,topItemsIndexes:x,...p}},X(Ht,An,Ne,le,ie,ce,Pt,Ae),{singleton:!0}),Nn=$(([{fixedFooterHeight:t,fixedHeaderHeight:e,footerHeight:n,headerHeight:o},{listState:r}])=>{const s=D(),i=ct(S(rt(n,t,o,e,r),H(([l,c,d,f,I])=>l+c+d+f+I.offsetBottom+I.bottom)),0);return F(M(i),s),{totalListHeight:i,totalListHeightChanged:s}},X(at,_t),{singleton:!0}),zo=$(([{viewportHeight:t},{totalListHeight:e}])=>{const n=w(!1),o=ct(S(rt(n,t,e),P(([r])=>r),H(([,r,s])=>Math.max(0,r-s)),Lt(0),Y()),0);return{alignToBottom:n,paddingTopAddition:o}},X(at,Nn),{singleton:!0});function gn(t){return t?t===\"smooth\"?\"smooth\":\"auto\":!1}const Vo=(t,e)=>typeof t==\"function\"?gn(t(e)):e&&gn(t),Po=$(([{listRefresh:t,totalCount:e},{atBottomState:n,isAtBottom:o},{scrollToIndex:r},{scrolledToInitialItem:s},{didMount:i,propsReady:l},{log:c},{scrollingInProgress:d}])=>{const f=w(!1),I=D();let p=null;function m(x){A(r,{align:\"end\",behavior:x,index:\"LAST\"})}U(S(rt(S(M(e),Ut(1)),i),G(M(f),o,s,d),H(([[x,y],g,u,T,b])=>{let h=y&&T,a=\"auto\";return h&&(a=Vo(g,u||b),h=h&&!!a),{followOutputBehavior:a,shouldFollow:h,totalCount:x}}),P(({shouldFollow:x})=>x)),({followOutputBehavior:x,totalCount:y})=>{p&&(p(),p=null),p=Rt(t,()=>{lt(c)(\"following output to \",{totalCount:y},mt.DEBUG),m(x),p=null})});function C(x){const y=Rt(n,g=>{x&&!g.atBottom&&g.notAtBottomBecause===\"SIZE_INCREASED\"&&!p&&(lt(c)(\"scrolling to bottom due to increased size\",{},mt.DEBUG),m(\"auto\"))});setTimeout(y,100)}return U(S(rt(M(f),e,l),P(([x,,y])=>x&&y),bt(({value:x},[,y])=>({refreshed:x===y,value:y}),{refreshed:!1,value:0}),P(({refreshed:x})=>x),G(f,e)),([,x])=>{lt(s)&&C(x!==!1)}),U(I,()=>{C(lt(f)!==!1)}),U(rt(M(f),n),([x,y])=>{x&&!y.atBottom&&y.notAtBottomBecause===\"VIEWPORT_HEIGHT_DECREASING\"&&m(\"auto\")}),{autoscrollToBottom:I,followOutput:f}},X(Ht,ce,ie,le,Pt,Vt,at)),Mo=$(([{data:t,firstItemIndex:e,gap:n,sizes:o},{initialTopMostItemIndex:r},{initialItemCount:s,listState:i},{didMount:l}])=>(F(S(l,G(s),P(([,c])=>c!==0),G(r,o,e,n,t),H(([[,c],d,f,I,p,m=[]])=>_n(c,d,f,I,p,m))),i),{}),X(Ht,le,_t,Pt),{singleton:!0}),jo=$(([{didMount:t},{scrollTo:e},{listState:n}])=>{const o=w(0);return U(S(t,G(o),P(([,r])=>r!==0),H(([,r])=>({top:r}))),r=>{Rt(S(n,Ut(1),P(s=>s.items.length>1)),()=>{requestAnimationFrame(()=>{A(e,r)})})}),{initialScrollTop:o}},X(Pt,at,_t),{singleton:!0}),Ao=({itemBottom:t,itemTop:e,locationParams:{align:n,behavior:o,...r},viewportBottom:s,viewportTop:i})=>e<i?{...r,align:n!=null?n:\"start\",behavior:o}:t>s?{...r,align:n!=null?n:\"end\",behavior:o}:null,Wo=$(([{gap:t,sizes:e,totalCount:n},{fixedFooterHeight:o,fixedHeaderHeight:r,headerHeight:s,scrollingInProgress:i,scrollTop:l,viewportHeight:c},{scrollToIndex:d}])=>{const f=D();return F(S(f,G(e,c,n,s,r,o,l),G(t),H(([[I,p,m,C,x,y,g,u],T])=>{const{align:b,behavior:h,calculateViewLocation:a=Ao,done:E,...B}=I,O=jn(I,p,C-1),z=re(O,p.offsetTree,T)+x+y,V=z+wt(p.sizeTree,O)[1],N=u+y,J=u+m-g,nt=a({itemBottom:V,itemTop:z,locationParams:{align:b,behavior:h,...B},viewportBottom:J,viewportTop:N});return nt?E&&Rt(S(i,P(k=>!k),Ut(lt(i)?1:2)),E):E&&E(),nt}),P(I=>I!==null)),d),{scrollIntoView:f}},X(Ht,at,ie,_t,Vt),{singleton:!0}),Dn=$(([{scrollVelocity:t}])=>{const e=w(!1),n=D(),o=w(!1);return F(S(t,G(o,e,n),P(([r,s])=>!!s),H(([r,s,i,l])=>{const{enter:c,exit:d}=s;if(i){if(d(r,l))return!1}else if(c(r,l))return!0;return i}),Y()),e),U(S(rt(e,t,n),G(o)),([[r,s,i],l])=>{r&&l&&l.change&&l.change(s,i)}),{isSeeking:e,scrollSeekConfiguration:o,scrollSeekRangeChanged:n,scrollVelocity:t}},X(ce),{singleton:!0}),De=$(([{scrollContainerState:t,scrollTo:e}])=>{const n=D(),o=D(),r=D(),s=w(!1),i=w(void 0);return F(S(rt(n,o),H(([{scrollHeight:l,scrollTop:c,viewportHeight:d},{offsetTop:f}])=>({scrollHeight:l,scrollTop:Math.max(0,c-f),viewportHeight:d}))),t),F(S(e,G(o),H(([l,{offsetTop:c}])=>({...l,top:l.top+c}))),r),{customScrollParent:i,useWindowScroll:s,windowScrollContainerState:n,windowScrollTo:r,windowViewportRect:o}},X(at)),Go=$(([{sizeRanges:t,sizes:e},{headerHeight:n,scrollTop:o},{initialTopMostItemIndex:r},{didMount:s},{useWindowScroll:i,windowScrollContainerState:l,windowViewportRect:c}])=>{const d=D(),f=w(void 0),I=w(null),p=w(null);return F(l,I),F(c,p),U(S(d,G(e,o,i,I,p,n)),([m,C,x,y,g,u,T])=>{const b=yo(C.sizeTree);y&&g!==null&&u!==null&&(x=g.scrollTop-u.offsetTop),x-=T,m({ranges:b,scrollTop:x})}),F(S(f,P(Ve),H(_o)),r),F(S(s,G(f),P(([,m])=>m!==void 0),Y(),H(([,m])=>m.ranges)),t),{getState:d,restoreStateFrom:f}},X(Ht,at,le,Pt,De));function _o(t){return{align:\"start\",index:0,offset:t.scrollTop}}const No=$(([{topItemsIndexes:t}])=>{const e=w(0);return F(S(e,P(n=>n>=0),H(n=>Array.from({length:n}).map((o,r)=>r))),t),{topItemCount:e}},X(_t));function $n(t){let e=!1,n;return()=>(e||(e=!0,n=t()),n)}const Do=$n(()=>/iP(ad|od|hone)/i.test(navigator.userAgent)&&/WebKit/i.test(navigator.userAgent)),$o=$(([{deviation:t,scrollBy:e,scrollingInProgress:n,scrollTop:o},{isAtBottom:r,isScrolling:s,lastJumpDueToItemResize:i,scrollDirection:l},{listState:c},{beforeUnshiftWith:d,gap:f,shiftWithOffset:I,sizes:p},{log:m},{recalcInProgress:C}])=>{const x=ht(S(c,G(i),bt(([,g,u,T],[{bottom:b,items:h,offsetBottom:a,totalCount:E},B])=>{const O=b+a;let z=0;return u===E&&g.length>0&&h.length>0&&(h[0].originalIndex===0&&g[0].originalIndex===0||(z=O-T,z!==0&&(z+=B))),[z,h,E,O]},[0,[],0,0]),P(([g])=>g!==0),G(o,l,n,r,m,C),P(([,g,u,T,,,b])=>!b&&!T&&g!==0&&u===se),H(([[g],,,,,u])=>(u(\"Upward scrolling compensation\",{amount:g},mt.DEBUG),g))));function y(g){g>0?(A(e,{behavior:\"auto\",top:-g}),A(t,0)):(A(t,0),A(e,{behavior:\"auto\",top:-g}))}return U(S(x,G(t,s)),([g,u,T])=>{T&&Do()?A(t,u-g):y(-g)}),U(S(rt(ct(s,!1),t,C),P(([g,u,T])=>!g&&!T&&u!==0),H(([g,u])=>u),Lt(1)),y),F(S(I,H(g=>({top:-g}))),e),U(S(d,G(p,f),H(([g,{groupIndices:u,lastSize:T,sizeTree:b},h])=>{function a(E){return E*(T+h)}if(u.length===0)return a(g);{let E=0;const B=ne(b,0);let O=0,z=0;for(;O<g;){O++,E+=B;let V=u.length===z+1?1/0:u[z+1]-u[z]-1;O+V>g&&(E-=B,V=g-O+1),O+=V,E+=a(V),z++}return E}})),g=>{A(t,g),requestAnimationFrame(()=>{A(e,{top:g}),requestAnimationFrame(()=>{A(t,0),A(C,!1)})})}),{deviation:t}},X(at,ce,_t,Ht,Vt,Ae)),Uo=$(([t,e,n,o,r,s,i,l,c,d])=>({...t,...e,...n,...o,...r,...s,...i,...l,...c,...d}),X(Ne,Mo,Pt,Dn,Nn,jo,zo,De,Wo,Vt)),Un=$(([{data:t,defaultItemSize:e,firstItemIndex:n,fixedItemSize:o,gap:r,groupIndices:s,itemSize:i,sizeRanges:l,sizes:c,statefulTotalCount:d,totalCount:f,trackItemSizes:I},{initialItemFinalLocationReached:p,initialTopMostItemIndex:m,scrolledToInitialItem:C},x,y,g,{listState:u,topItemsIndexes:T,...b},{scrollToIndex:h},a,{topItemCount:E},{groupCounts:B},O])=>(F(b.rangeChanged,O.scrollSeekRangeChanged),F(S(O.windowViewportRect,H(z=>z.visibleHeight)),x.viewportHeight),{data:t,defaultItemHeight:e,firstItemIndex:n,fixedItemHeight:o,gap:r,groupCounts:B,initialItemFinalLocationReached:p,initialTopMostItemIndex:m,scrolledToInitialItem:C,sizeRanges:l,topItemCount:E,topItemsIndexes:T,totalCount:f,...g,groupIndices:s,itemSize:i,listState:u,scrollToIndex:h,statefulTotalCount:d,trackItemSizes:I,...b,...O,...x,sizes:c,...y}),X(Ht,le,at,Go,Po,_t,ie,$o,No,An,Uo));function Ko(t,e){const n={},o={};let r=0;const s=t.length;for(;r<s;)o[t[r]]=1,r+=1;for(const i in e)Object.hasOwn(o,i)||(n[i]=e[i]);return n}const me=typeof document<\"u\"?R.useLayoutEffect:R.useEffect;function $e(t,e,n){const o=Object.keys(e.required||{}),r=Object.keys(e.optional||{}),s=Object.keys(e.methods||{}),i=Object.keys(e.events||{}),l=R.createContext({});function c(g,u){g.propsReady&&A(g.propsReady,!1);for(const T of o){const b=g[e.required[T]];A(b,u[T])}for(const T of r)if(T in u){const b=g[e.optional[T]];A(b,u[T])}g.propsReady&&A(g.propsReady,!0)}function d(g){return s.reduce((u,T)=>(u[T]=b=>{const h=g[e.methods[T]];A(h,b)},u),{})}function f(g){return i.reduce((u,T)=>(u[T]=co(g[e.events[T]]),u),{})}const I=R.forwardRef((g,u)=>{const{children:T,...b}=g,[h]=R.useState(()=>Ce(ao(t),B=>{c(B,b)})),[a]=R.useState(sn(f,h));me(()=>{for(const B of i)B in b&&U(a[B],b[B]);return()=>{Object.values(a).map(Pe)}},[b,a,h]),me(()=>{c(h,b)}),R.useImperativeHandle(u,rn(d(h)));const E=n;return L.jsx(l.Provider,{value:h,children:n?L.jsx(E,{...Ko([...o,...r,...i],b),children:T}):T})}),p=g=>R.useCallback(En(A,R.useContext(l)[g]),[g]),m=g=>{const T=R.useContext(l)[g],b=R.useCallback(h=>U(T,h),[T]);return R.useSyncExternalStore(b,()=>lt(T),()=>lt(T))},C=g=>{const T=R.useContext(l)[g],[b,h]=R.useState(sn(lt,T));return me(()=>U(T,a=>{a!==b&&h(rn(a))}),[T,b]),b},x=R.version.startsWith(\"18\")?m:C;return{Component:I,useEmitter:(g,u)=>{const b=R.useContext(l)[g];me(()=>U(b,u),[u,b])},useEmitterValue:x,usePublisher:p}}const ue=R.createContext(void 0),Ue=R.createContext(void 0),Kn=typeof document<\"u\"?R.useLayoutEffect:R.useEffect;function qn(t,e,n,o=Gt,r,s){const i=R.useRef(null),l=R.useRef(null),c=R.useRef(null),d=R.useCallback(p=>{const m=p.target,C=m===window||m===document,x=s?C?window.pageXOffset||document.documentElement.scrollLeft:m.scrollLeft:C?window.pageYOffset||document.documentElement.scrollTop:m.scrollTop,y=s?C?document.documentElement.scrollWidth:m.scrollWidth:C?document.documentElement.scrollHeight:m.scrollHeight,g=s?C?window.innerWidth:m.offsetWidth:C?window.innerHeight:m.offsetHeight,u=()=>{t({scrollHeight:y,scrollTop:Math.max(x,0),viewportHeight:g})};p.suppressFlushSync?u():oo.flushSync(u),l.current!==null&&(x===l.current||x<=0||x===y-g)&&(l.current=null,e(!0),c.current&&(clearTimeout(c.current),c.current=null))},[t,e]);R.useEffect(()=>{const p=r||i.current;return o(r||i.current),d({suppressFlushSync:!0,target:p}),p.addEventListener(\"scroll\",d,{passive:!0}),()=>{o(null),p.removeEventListener(\"scroll\",d)}},[i,d,n,o,r]);function f(p){const m=i.current;if(!m||(s?\"offsetWidth\"in m&&m.offsetWidth===0:\"offsetHeight\"in m&&m.offsetHeight===0))return;const C=p.behavior===\"smooth\";let x,y,g;m===window?(y=Math.max(Ct(document.documentElement,s?\"width\":\"height\"),s?document.documentElement.scrollWidth:document.documentElement.scrollHeight),x=s?window.innerWidth:window.innerHeight,g=s?document.documentElement.scrollLeft:document.documentElement.scrollTop):(y=m[s?\"scrollWidth\":\"scrollHeight\"],x=Ct(m,s?\"width\":\"height\"),g=m[s?\"scrollLeft\":\"scrollTop\"]);const u=y-x;if(p.top=Math.ceil(Math.max(Math.min(u,p.top),0)),Gn(x,y)||p.top===g){t({scrollHeight:y,scrollTop:g,viewportHeight:x}),C&&e(!0);return}C?(l.current=p.top,c.current&&clearTimeout(c.current),c.current=setTimeout(()=>{c.current=null,l.current=null,e(!0)},1e3)):l.current=null,s&&(p={behavior:p.behavior,left:p.top}),m.scrollTo(p)}function I(p){s&&(p={behavior:p.behavior,left:p.top}),i.current.scrollBy(p)}return{scrollByCallback:I,scrollerRef:i,scrollToCallback:f}}const He=\"-webkit-sticky\",xn=\"sticky\",Yn=$n(()=>{if(typeof document>\"u\")return xn;const t=document.createElement(\"div\");return t.style.position=He,t.style.position===He?He:xn});function Ke(t){return t}const qo=$(()=>{const t=w(c=>`Item ${c}`),e=w(null),n=w(c=>`Group ${c}`),o=w({}),r=w(Ke),s=w(\"div\"),i=w(Gt),l=(c,d=null)=>ct(S(o,H(f=>f[c]),Y()),d);return{components:o,computeItemKey:r,context:e,EmptyPlaceholder:l(\"EmptyPlaceholder\"),FooterComponent:l(\"Footer\"),GroupComponent:l(\"Group\",\"div\"),groupContent:n,HeaderComponent:l(\"Header\"),HeaderFooterTag:s,ItemComponent:l(\"Item\",\"div\"),itemContent:t,ListComponent:l(\"List\",\"div\"),ScrollerComponent:l(\"Scroller\",\"div\"),scrollerRef:i,ScrollSeekPlaceholder:l(\"ScrollSeekPlaceholder\"),TopItemListComponent:l(\"TopItemList\")}}),Yo=$(([t,e])=>({...t,...e}),X(Un,qo)),Zo=({height:t})=>L.jsx(\"div\",{style:{height:t}}),Xo={overflowAnchor:\"none\",position:Yn(),zIndex:1},Zn={overflowAnchor:\"none\"},Jo={...Zn,display:\"inline-block\",height:\"100%\"},In=R.memo(function({showTopList:e=!1}){const n=j(\"listState\"),o=gt(\"sizeRanges\"),r=j(\"useWindowScroll\"),s=j(\"customScrollParent\"),i=gt(\"windowScrollContainerState\"),l=gt(\"scrollContainerState\"),c=s||r?i:l,d=j(\"itemContent\"),f=j(\"context\"),I=j(\"groupContent\"),p=j(\"trackItemSizes\"),m=j(\"itemSize\"),C=j(\"log\"),x=gt(\"gap\"),y=j(\"horizontalDirection\"),{callbackRef:g}=kn(o,m,p,e?Gt:c,C,x,s,y,j(\"skipAnimationFrameInResizeObserver\")),[u,T]=R.useState(0);Ze(\"deviation\",k=>{u!==k&&T(k)});const b=j(\"EmptyPlaceholder\"),h=j(\"ScrollSeekPlaceholder\")||Zo,a=j(\"ListComponent\"),E=j(\"ItemComponent\"),B=j(\"GroupComponent\"),O=j(\"computeItemKey\"),z=j(\"isSeeking\"),V=j(\"groupIndices\").length>0,N=j(\"alignToBottom\"),J=j(\"initialItemFinalLocationReached\"),nt=e?{}:{boxSizing:\"border-box\",...y?{display:\"inline-block\",height:\"100%\",marginLeft:u!==0?u:N?\"auto\":0,paddingLeft:n.offsetTop,paddingRight:n.offsetBottom,whiteSpace:\"nowrap\"}:{marginTop:u!==0?u:N?\"auto\":0,paddingBottom:n.offsetBottom,paddingTop:n.offsetTop},...J?{}:{visibility:\"hidden\"}};return!e&&n.totalCount===0&&b?L.jsx(b,{...Z(b,f)}):L.jsx(a,{...Z(a,f),\"data-testid\":e?\"virtuoso-top-item-list\":\"virtuoso-item-list\",ref:g,style:nt,children:(e?n.topItems:n.items).map(k=>{const q=k.originalIndex,st=O(q+n.firstItemIndex,k.data,f);return z?R.createElement(h,{...Z(h,f),height:k.size,index:k.index,key:st,type:k.type||\"item\",...k.type===\"group\"?{}:{groupIndex:k.groupIndex}}):k.type===\"group\"?R.createElement(B,{...Z(B,f),\"data-index\":q,\"data-item-index\":k.index,\"data-known-size\":k.size,key:st,style:Xo},I(k.index,f)):R.createElement(E,{...Z(E,f),...Xn(E,k.data),\"data-index\":q,\"data-item-group-index\":k.groupIndex,\"data-item-index\":k.index,\"data-known-size\":k.size,key:st,style:y?Jo:Zn},V?d(k.index,k.groupIndex,k.data,f):d(k.index,k.data,f))})})}),Qo={height:\"100%\",outline:\"none\",overflowY:\"auto\",position:\"relative\",WebkitOverflowScrolling:\"touch\"},tr={outline:\"none\",overflowX:\"auto\",position:\"relative\"},qt=t=>({height:\"100%\",position:\"absolute\",top:0,width:\"100%\",...t?{display:\"flex\",flexDirection:\"column\"}:{}}),er={position:Yn(),top:0,width:\"100%\",zIndex:1};function Z(t,e){if(typeof t!=\"string\")return{context:e}}function Xn(t,e){return{item:typeof t==\"string\"?void 0:e}}const nr=R.memo(function(){const e=j(\"HeaderComponent\"),n=gt(\"headerHeight\"),o=j(\"HeaderFooterTag\"),r=Et(R.useMemo(()=>i=>{n(Ct(i,\"height\"))},[n]),!0,j(\"skipAnimationFrameInResizeObserver\")),s=j(\"context\");return e?L.jsx(o,{ref:r,children:L.jsx(e,{...Z(e,s)})}):null}),or=R.memo(function(){const e=j(\"FooterComponent\"),n=gt(\"footerHeight\"),o=j(\"HeaderFooterTag\"),r=Et(R.useMemo(()=>i=>{n(Ct(i,\"height\"))},[n]),!0,j(\"skipAnimationFrameInResizeObserver\")),s=j(\"context\");return e?L.jsx(o,{ref:r,children:L.jsx(e,{...Z(e,s)})}):null});function qe({useEmitter:t,useEmitterValue:e,usePublisher:n}){return R.memo(function({children:s,style:i,...l}){const c=n(\"scrollContainerState\"),d=e(\"ScrollerComponent\"),f=n(\"smoothScrollTargetReached\"),I=e(\"scrollerRef\"),p=e(\"context\"),m=e(\"horizontalDirection\")||!1,{scrollByCallback:C,scrollerRef:x,scrollToCallback:y}=qn(c,f,d,I,void 0,m);t(\"scrollTo\",y),t(\"scrollBy\",C);const g=m?tr:Qo;return L.jsx(d,{\"data-testid\":\"virtuoso-scroller\",\"data-virtuoso-scroller\":!0,ref:x,style:{...g,...i},tabIndex:0,...l,...Z(d,p),children:s})})}function Ye({useEmitter:t,useEmitterValue:e,usePublisher:n}){return R.memo(function({children:s,style:i,...l}){const c=n(\"windowScrollContainerState\"),d=e(\"ScrollerComponent\"),f=n(\"smoothScrollTargetReached\"),I=e(\"totalListHeight\"),p=e(\"deviation\"),m=e(\"customScrollParent\"),C=e(\"context\"),{scrollByCallback:x,scrollerRef:y,scrollToCallback:g}=qn(c,f,d,Gt,m);return Kn(()=>(y.current=m||window,()=>{y.current=null}),[y,m]),t(\"windowScrollTo\",g),t(\"scrollBy\",x),L.jsx(d,{\"data-virtuoso-scroller\":!0,style:{position:\"relative\",...i,...I!==0?{height:I+p}:{}},...l,...Z(d,C),children:s})})}const rr=({children:t})=>{const e=R.useContext(ue),n=gt(\"viewportHeight\"),o=gt(\"fixedItemHeight\"),r=j(\"alignToBottom\"),s=j(\"horizontalDirection\"),i=R.useMemo(()=>te(n,c=>Ct(c,s?\"width\":\"height\")),[n,s]),l=Et(i,!0,j(\"skipAnimationFrameInResizeObserver\"));return R.useEffect(()=>{e&&(n(e.viewportHeight),o(e.itemHeight))},[e,n,o]),L.jsx(\"div\",{\"data-viewport-type\":\"element\",ref:l,style:qt(r),children:t})},sr=({children:t})=>{const e=R.useContext(ue),n=gt(\"windowViewportRect\"),o=gt(\"fixedItemHeight\"),r=j(\"customScrollParent\"),s=je(n,r,j(\"skipAnimationFrameInResizeObserver\")),i=j(\"alignToBottom\");return R.useEffect(()=>{e&&(o(e.itemHeight),n({offsetTop:0,visibleHeight:e.viewportHeight,visibleWidth:100}))},[e,n,o]),L.jsx(\"div\",{\"data-viewport-type\":\"window\",ref:s,style:qt(i),children:t})},ir=({children:t})=>{const e=j(\"TopItemListComponent\")||\"div\",n=j(\"headerHeight\"),o={...er,marginTop:`${n}px`},r=j(\"context\");return L.jsx(e,{style:o,...Z(e,r),children:t})},lr=R.memo(function(e){const n=j(\"useWindowScroll\"),o=j(\"topItemsIndexes\").length>0,r=j(\"customScrollParent\"),s=j(\"context\"),i=r||n?ur:cr,l=r||n?sr:rr;return L.jsxs(i,{...e,...Z(i,s),children:[o&&L.jsx(ir,{children:L.jsx(In,{showTopList:!0})}),L.jsxs(l,{children:[L.jsx(nr,{}),L.jsx(In,{}),L.jsx(or,{})]})]})}),{Component:Jn,useEmitter:Ze,useEmitterValue:j,usePublisher:gt}=$e(Yo,{events:{atBottomStateChange:\"atBottomStateChange\",atTopStateChange:\"atTopStateChange\",endReached:\"endReached\",groupIndices:\"groupIndices\",isScrolling:\"isScrolling\",itemsRendered:\"itemsRendered\",rangeChanged:\"rangeChanged\",startReached:\"startReached\",totalListHeightChanged:\"totalListHeightChanged\"},methods:{autoscrollToBottom:\"autoscrollToBottom\",getState:\"getState\",scrollBy:\"scrollBy\",scrollIntoView:\"scrollIntoView\",scrollTo:\"scrollTo\",scrollToIndex:\"scrollToIndex\"},optional:{alignToBottom:\"alignToBottom\",atBottomThreshold:\"atBottomThreshold\",atTopThreshold:\"atTopThreshold\",components:\"components\",computeItemKey:\"computeItemKey\",context:\"context\",customScrollParent:\"customScrollParent\",data:\"data\",defaultItemHeight:\"defaultItemHeight\",firstItemIndex:\"firstItemIndex\",fixedItemHeight:\"fixedItemHeight\",followOutput:\"followOutput\",groupContent:\"groupContent\",groupCounts:\"groupCounts\",headerFooterTag:\"HeaderFooterTag\",horizontalDirection:\"horizontalDirection\",increaseViewportBy:\"increaseViewportBy\",initialItemCount:\"initialItemCount\",initialScrollTop:\"initialScrollTop\",initialTopMostItemIndex:\"initialTopMostItemIndex\",itemContent:\"itemContent\",itemSize:\"itemSize\",logLevel:\"logLevel\",overscan:\"overscan\",restoreStateFrom:\"restoreStateFrom\",scrollerRef:\"scrollerRef\",scrollSeekConfiguration:\"scrollSeekConfiguration\",skipAnimationFrameInResizeObserver:\"skipAnimationFrameInResizeObserver\",topItemCount:\"topItemCount\",totalCount:\"totalCount\",useWindowScroll:\"useWindowScroll\"},required:{}},lr),cr=qe({useEmitter:Ze,useEmitterValue:j,usePublisher:gt}),ur=Ye({useEmitter:Ze,useEmitterValue:j,usePublisher:gt}),ar=Jn,dr=Jn,fr=$(()=>{const t=w(c=>L.jsxs(\"td\",{children:[\"Item $\",c]})),e=w(null),n=w(null),o=w(null),r=w({}),s=w(Ke),i=w(Gt),l=(c,d=null)=>ct(S(r,H(f=>f[c]),Y()),d);return{components:r,computeItemKey:s,context:e,EmptyPlaceholder:l(\"EmptyPlaceholder\"),FillerRow:l(\"FillerRow\"),fixedFooterContent:o,fixedHeaderContent:n,itemContent:t,ScrollerComponent:l(\"Scroller\",\"div\"),scrollerRef:i,ScrollSeekPlaceholder:l(\"ScrollSeekPlaceholder\"),TableBodyComponent:l(\"TableBody\",\"tbody\"),TableComponent:l(\"Table\",\"table\"),TableFooterComponent:l(\"TableFoot\",\"tfoot\"),TableHeadComponent:l(\"TableHead\",\"thead\"),TableRowComponent:l(\"TableRow\",\"tr\")}}),mr=$(([t,e])=>({...t,...e}),X(Un,fr)),pr=({height:t})=>L.jsx(\"tr\",{children:L.jsx(\"td\",{style:{height:t}})}),hr=({height:t})=>L.jsx(\"tr\",{children:L.jsx(\"td\",{style:{border:0,height:t,padding:0}})}),gr={overflowAnchor:\"none\"},Sn=R.memo(function({showTopList:e=!1}){const n=W(\"listState\"),o=W(\"computeItemKey\"),r=W(\"firstItemIndex\"),s=W(\"isSeeking\"),i=W(\"ScrollSeekPlaceholder\")||pr,l=W(\"context\"),c=W(\"TableRowComponent\"),d=W(\"fixedHeaderHeight\"),f=W(\"itemContent\"),I=(e?n.topItems:[]).reduce((m,C,x)=>(x===0?m.push(C.size):m.push(m[x-1]+C.size),m),[]),p=(e?n.topItems:n.items).map(m=>{const C=m.originalIndex,x=o(C+r,m.data,l),y=e?C===0?0:I[C-1]:0;return s?R.createElement(i,{...Z(i,l),height:m.size,index:m.index,key:x,type:m.type||\"item\"}):R.createElement(c,{...Z(c,l),...Xn(c,m.data),\"data-index\":C,\"data-item-index\":m.index,\"data-known-size\":m.size,key:x,style:e?{overflowAnchor:\"none\",position:\"sticky\",top:d+y,zIndex:2}:gr},f(m.index,m.data,l))});return L.jsx(L.Fragment,{children:p})}),xr=R.memo(function(){const e=W(\"listState\"),n=W(\"topItemsIndexes\").length>0,o=Tt(\"sizeRanges\"),r=W(\"useWindowScroll\"),s=W(\"customScrollParent\"),i=Tt(\"windowScrollContainerState\"),l=Tt(\"scrollContainerState\"),c=s||r?i:l,d=W(\"trackItemSizes\"),f=W(\"itemSize\"),I=W(\"log\"),{callbackRef:p,ref:m}=kn(o,f,d,c,I,void 0,s,!1,W(\"skipAnimationFrameInResizeObserver\")),[C,x]=R.useState(0);Xe(\"deviation\",V=>{C!==V&&(m.current.style.marginTop=`${V}px`,x(V))});const y=W(\"EmptyPlaceholder\"),g=W(\"FillerRow\")||hr,u=W(\"TableBodyComponent\"),T=W(\"paddingTopAddition\"),b=W(\"statefulTotalCount\"),h=W(\"context\");if(b===0&&y)return L.jsx(y,{...Z(y,h)});const a=(n?e.topItems:[]).reduce((V,N)=>V+N.size,0),E=e.offsetTop+T+C-a,B=e.offsetBottom,O=E>0?L.jsx(g,{context:h,height:E},\"padding-top\"):null,z=B>0?L.jsx(g,{context:h,height:B},\"padding-bottom\"):null;return L.jsxs(u,{\"data-testid\":\"virtuoso-item-list\",ref:p,...Z(u,h),children:[O,n&&L.jsx(Sn,{showTopList:!0}),L.jsx(Sn,{}),z]})}),Ir=({children:t})=>{const e=R.useContext(ue),n=Tt(\"viewportHeight\"),o=Tt(\"fixedItemHeight\"),r=Et(R.useMemo(()=>te(n,s=>Ct(s,\"height\")),[n]),!0,W(\"skipAnimationFrameInResizeObserver\"));return R.useEffect(()=>{e&&(n(e.viewportHeight),o(e.itemHeight))},[e,n,o]),L.jsx(\"div\",{\"data-viewport-type\":\"element\",ref:r,style:qt(!1),children:t})},Sr=({children:t})=>{const e=R.useContext(ue),n=Tt(\"windowViewportRect\"),o=Tt(\"fixedItemHeight\"),r=W(\"customScrollParent\"),s=je(n,r,W(\"skipAnimationFrameInResizeObserver\"));return R.useEffect(()=>{e&&(o(e.itemHeight),n({offsetTop:0,visibleHeight:e.viewportHeight,visibleWidth:100}))},[e,n,o]),L.jsx(\"div\",{\"data-viewport-type\":\"window\",ref:s,style:qt(!1),children:t})},Tr=R.memo(function(e){const n=W(\"useWindowScroll\"),o=W(\"customScrollParent\"),r=Tt(\"fixedHeaderHeight\"),s=Tt(\"fixedFooterHeight\"),i=W(\"fixedHeaderContent\"),l=W(\"fixedFooterContent\"),c=W(\"context\"),d=Et(R.useMemo(()=>te(r,u=>Ct(u,\"height\")),[r]),!0,W(\"skipAnimationFrameInResizeObserver\")),f=Et(R.useMemo(()=>te(s,u=>Ct(u,\"height\")),[s]),!0,W(\"skipAnimationFrameInResizeObserver\")),I=o||n?yr:Cr,p=o||n?Sr:Ir,m=W(\"TableComponent\"),C=W(\"TableHeadComponent\"),x=W(\"TableFooterComponent\"),y=i?L.jsx(C,{ref:d,style:{position:\"sticky\",top:0,zIndex:2},...Z(C,c),children:i()},\"TableHead\"):null,g=l?L.jsx(x,{ref:f,style:{bottom:0,position:\"sticky\",zIndex:1},...Z(x,c),children:l()},\"TableFoot\"):null;return L.jsx(I,{...e,...Z(I,c),children:L.jsx(p,{children:L.jsxs(m,{style:{borderSpacing:0,overflowAnchor:\"none\"},...Z(m,c),children:[y,L.jsx(xr,{},\"TableBody\"),g]})})})}),{Component:wr,useEmitter:Xe,useEmitterValue:W,usePublisher:Tt}=$e(mr,{events:{atBottomStateChange:\"atBottomStateChange\",atTopStateChange:\"atTopStateChange\",endReached:\"endReached\",groupIndices:\"groupIndices\",isScrolling:\"isScrolling\",itemsRendered:\"itemsRendered\",rangeChanged:\"rangeChanged\",startReached:\"startReached\",totalListHeightChanged:\"totalListHeightChanged\"},methods:{getState:\"getState\",scrollBy:\"scrollBy\",scrollIntoView:\"scrollIntoView\",scrollTo:\"scrollTo\",scrollToIndex:\"scrollToIndex\"},optional:{alignToBottom:\"alignToBottom\",atBottomThreshold:\"atBottomThreshold\",atTopThreshold:\"atTopThreshold\",components:\"components\",computeItemKey:\"computeItemKey\",context:\"context\",customScrollParent:\"customScrollParent\",data:\"data\",defaultItemHeight:\"defaultItemHeight\",firstItemIndex:\"firstItemIndex\",fixedFooterContent:\"fixedFooterContent\",fixedHeaderContent:\"fixedHeaderContent\",fixedItemHeight:\"fixedItemHeight\",followOutput:\"followOutput\",groupCounts:\"groupCounts\",increaseViewportBy:\"increaseViewportBy\",initialItemCount:\"initialItemCount\",initialScrollTop:\"initialScrollTop\",initialTopMostItemIndex:\"initialTopMostItemIndex\",itemContent:\"itemContent\",itemSize:\"itemSize\",logLevel:\"logLevel\",overscan:\"overscan\",restoreStateFrom:\"restoreStateFrom\",scrollerRef:\"scrollerRef\",scrollSeekConfiguration:\"scrollSeekConfiguration\",topItemCount:\"topItemCount\",totalCount:\"totalCount\",useWindowScroll:\"useWindowScroll\"},required:{}},Tr),Cr=qe({useEmitter:Xe,useEmitterValue:W,usePublisher:Tt}),yr=Ye({useEmitter:Xe,useEmitterValue:W,usePublisher:Tt}),vr=wr,Tn={bottom:0,itemHeight:0,items:[],itemWidth:0,offsetBottom:0,offsetTop:0,top:0},br={bottom:0,itemHeight:0,items:[{index:0}],itemWidth:0,offsetBottom:0,offsetTop:0,top:0},{ceil:wn,floor:Te,max:Qt,min:ke,round:Cn}=Math;function yn(t,e,n){return Array.from({length:e-t+1}).map((o,r)=>({data:n===null?null:n[r+t],index:r+t}))}function Rr(t){return{...br,items:t}}function pe(t,e){return t&&t.width===e.width&&t.height===e.height}function Er(t,e){return t&&t.column===e.column&&t.row===e.row}const Hr=$(([{increaseViewportBy:t,listBoundary:e,overscan:n,visibleRange:o},{footerHeight:r,headerHeight:s,scrollBy:i,scrollContainerState:l,scrollTo:c,scrollTop:d,smoothScrollTargetReached:f,viewportHeight:I},p,m,{didMount:C,propsReady:x},{customScrollParent:y,useWindowScroll:g,windowScrollContainerState:u,windowScrollTo:T,windowViewportRect:b},h])=>{const a=w(0),E=w(0),B=w(Tn),O=w({height:0,width:0}),z=w({height:0,width:0}),V=D(),N=D(),J=w(0),nt=w(null),k=w({column:0,row:0}),q=D(),st=D(),dt=w(!1),It=w(0),ft=w(!0),ut=w(!1),Mt=w(!1);U(S(C,G(It),P(([v,_])=>!!_)),()=>{A(ft,!1)}),U(S(rt(C,ft,z,O,It,ut),P(([v,_,Q,it,,tt])=>v&&!_&&Q.height!==0&&it.height!==0&&!tt)),([,,,,v])=>{A(ut,!0),Ge(1,()=>{A(V,v)}),Rt(S(d),()=>{A(e,[0,0]),A(ft,!0)})}),F(S(st,P(v=>v!=null&&v.scrollTop>0),vt(0)),E),U(S(C,G(st),P(([,v])=>v!=null)),([,v])=>{v&&(A(O,v.viewport),A(z,v.item),A(k,v.gap),v.scrollTop>0&&(A(dt,!0),Rt(S(d,Ut(1)),_=>{A(dt,!1)}),A(c,{top:v.scrollTop})))}),F(S(O,H(({height:v})=>v)),I),F(S(rt(M(O,pe),M(z,pe),M(k,(v,_)=>v&&v.column===_.column&&v.row===_.row),M(d)),H(([v,_,Q,it])=>({gap:Q,item:_,scrollTop:it,viewport:v}))),q),F(S(rt(M(a),o,M(k,Er),M(z,pe),M(O,pe),M(nt),M(E),M(dt),M(ft),M(It)),P(([,,,,,,,v])=>!v),H(([v,[_,Q],it,tt,Bt,Zt,Nt,,ae,Ot])=>{const{column:Ft,row:Xt}=it,{height:de,width:be}=tt,{width:Je}=Bt;if(Nt===0&&(v===0||Je===0))return Tn;if(be===0){const on=_e(Ot,v),no=on+Math.max(Nt-1,0);return Rr(yn(on,no,Zt))}const fe=Qn(Je,be,Ft);let Dt,At;ae?_===0&&Q===0&&Nt>0?(Dt=0,At=Nt-1):(Dt=fe*Te((_+Xt)/(de+Xt)),At=fe*wn((Q+Xt)/(de+Xt))-1,At=ke(v-1,Qt(At,fe-1)),Dt=ke(At,Qt(0,Dt))):(Dt=0,At=-1);const Qe=yn(Dt,At,Zt),{bottom:tn,top:en}=vn(Bt,it,tt,Qe),nn=wn(v/fe),eo=nn*de+(nn-1)*Xt-tn;return{bottom:tn,itemHeight:de,items:Qe,itemWidth:be,offsetBottom:eo,offsetTop:en,top:en}})),B),F(S(nt,P(v=>v!==null),H(v=>v.length)),a),F(S(rt(O,z,B,k),P(([v,_,{items:Q}])=>Q.length>0&&_.height!==0&&v.height!==0),H(([v,_,{items:Q},it])=>{const{bottom:tt,top:Bt}=vn(v,it,_,Q);return[Bt,tt]}),Y(oe)),e);const St=w(!1);F(S(d,G(St),H(([v,_])=>_||v!==0)),St);const Yt=ht(S(rt(B,a),P(([{items:v}])=>v.length>0),G(St),P(([[v,_],Q])=>{const tt=v.items[v.items.length-1].index===_-1;return(Q||v.bottom>0&&v.itemHeight>0&&v.offsetBottom===0&&v.items.length===_)&&tt}),H(([[,v]])=>v-1),Y())),jt=ht(S(M(B),P(({items:v})=>v.length>0&&v[0].index===0),vt(0),Y())),yt=ht(S(M(B),G(dt),P(([{items:v},_])=>v.length>0&&!_),H(([{items:v}])=>({endIndex:v[v.length-1].index,startIndex:v[0].index})),Y(Vn),Lt(0)));F(yt,m.scrollSeekRangeChanged),F(S(V,G(O,z,a,k),H(([v,_,Q,it,tt])=>{const Bt=Wn(v),{align:Zt,behavior:Nt,offset:ae}=Bt;let Ot=Bt.index;Ot===\"LAST\"&&(Ot=it-1),Ot=Qt(0,Ot,ke(it-1,Ot));let Ft=ze(_,tt,Q,Ot);return Zt===\"end\"?Ft=Cn(Ft-_.height+Q.height):Zt===\"center\"&&(Ft=Cn(Ft-_.height/2+Q.height/2)),ae&&(Ft+=ae),{behavior:Nt,top:Ft}})),c);const kt=ct(S(B,H(v=>v.offsetBottom+v.bottom)),0);return F(S(b,H(v=>({height:v.visibleHeight,width:v.visibleWidth}))),O),{customScrollParent:y,data:nt,deviation:J,footerHeight:r,gap:k,headerHeight:s,increaseViewportBy:t,initialItemCount:E,itemDimensions:z,overscan:n,restoreStateFrom:st,scrollBy:i,scrollContainerState:l,scrollHeight:N,scrollTo:c,scrollToIndex:V,scrollTop:d,smoothScrollTargetReached:f,totalCount:a,useWindowScroll:g,viewportDimensions:O,windowScrollContainerState:u,windowScrollTo:T,windowViewportRect:b,...m,gridState:B,horizontalDirection:Mt,initialTopMostItemIndex:It,totalListHeight:kt,...p,endReached:Yt,propsReady:x,rangeChanged:yt,startReached:jt,stateChanged:q,stateRestoreInProgress:dt,...h}},X(Ne,at,ce,Dn,Pt,De,Vt));function Qn(t,e,n){return Qt(1,Te((t+n)/(Te(e)+n)))}function vn(t,e,n,o){const{height:r}=n;if(r===void 0||o.length===0)return{bottom:0,top:0};const s=ze(t,e,n,o[0].index);return{bottom:ze(t,e,n,o[o.length-1].index)+r,top:s}}function ze(t,e,n,o){const r=Qn(t.width,n.width,e.column),s=Te(o/r),i=s*n.height+Qt(0,s-1)*e.row;return i>0?i+e.row:i}const kr=$(()=>{const t=w(I=>`Item ${I}`),e=w({}),n=w(null),o=w(\"virtuoso-grid-item\"),r=w(\"virtuoso-grid-list\"),s=w(Ke),i=w(\"div\"),l=w(Gt),c=(I,p=null)=>ct(S(e,H(m=>m[I]),Y()),p),d=w(!1),f=w(!1);return F(M(f),d),{components:e,computeItemKey:s,context:n,FooterComponent:c(\"Footer\"),HeaderComponent:c(\"Header\"),headerFooterTag:i,itemClassName:o,ItemComponent:c(\"Item\",\"div\"),itemContent:t,listClassName:r,ListComponent:c(\"List\",\"div\"),readyStateChanged:d,reportReadyState:f,ScrollerComponent:c(\"Scroller\",\"div\"),scrollerRef:l,ScrollSeekPlaceholder:c(\"ScrollSeekPlaceholder\",\"div\")}}),Br=$(([t,e])=>({...t,...e}),X(Hr,kr)),Or=R.memo(function(){const e=et(\"gridState\"),n=et(\"listClassName\"),o=et(\"itemClassName\"),r=et(\"itemContent\"),s=et(\"computeItemKey\"),i=et(\"isSeeking\"),l=xt(\"scrollHeight\"),c=et(\"ItemComponent\"),d=et(\"ListComponent\"),f=et(\"ScrollSeekPlaceholder\"),I=et(\"context\"),p=xt(\"itemDimensions\"),m=xt(\"gap\"),C=et(\"log\"),x=et(\"stateRestoreInProgress\"),y=xt(\"reportReadyState\"),g=Et(R.useMemo(()=>u=>{const T=u.parentElement.parentElement.scrollHeight;l(T);const b=u.firstChild;if(b){const{height:h,width:a}=b.getBoundingClientRect();p({height:h,width:a})}m({column:bn(\"column-gap\",getComputedStyle(u).columnGap,C),row:bn(\"row-gap\",getComputedStyle(u).rowGap,C)})},[l,p,m,C]),!0,!1);return Kn(()=>{e.itemHeight>0&&e.itemWidth>0&&y(!0)},[e]),x?null:L.jsx(d,{className:n,ref:g,...Z(d,I),\"data-testid\":\"virtuoso-item-list\",style:{paddingBottom:e.offsetBottom,paddingTop:e.offsetTop},children:e.items.map(u=>{const T=s(u.index,u.data,I);return i?L.jsx(f,{...Z(f,I),height:e.itemHeight,index:u.index,width:e.itemWidth},T):R.createElement(c,{...Z(c,I),className:o,\"data-index\":u.index,key:T},r(u.index,u.data,I))})})}),Fr=R.memo(function(){const e=et(\"HeaderComponent\"),n=xt(\"headerHeight\"),o=et(\"headerFooterTag\"),r=Et(R.useMemo(()=>i=>{n(Ct(i,\"height\"))},[n]),!0,!1),s=et(\"context\");return e?L.jsx(o,{ref:r,children:L.jsx(e,{...Z(e,s)})}):null}),Lr=R.memo(function(){const e=et(\"FooterComponent\"),n=xt(\"footerHeight\"),o=et(\"headerFooterTag\"),r=Et(R.useMemo(()=>i=>{n(Ct(i,\"height\"))},[n]),!0,!1),s=et(\"context\");return e?L.jsx(o,{ref:r,children:L.jsx(e,{...Z(e,s)})}):null}),zr=({children:t})=>{const e=R.useContext(Ue),n=xt(\"itemDimensions\"),o=xt(\"viewportDimensions\"),r=Et(R.useMemo(()=>s=>{o(s.getBoundingClientRect())},[o]),!0,!1);return R.useEffect(()=>{e&&(o({height:e.viewportHeight,width:e.viewportWidth}),n({height:e.itemHeight,width:e.itemWidth}))},[e,o,n]),L.jsx(\"div\",{ref:r,style:qt(!1),children:t})},Vr=({children:t})=>{const e=R.useContext(Ue),n=xt(\"windowViewportRect\"),o=xt(\"itemDimensions\"),r=et(\"customScrollParent\"),s=je(n,r,!1);return R.useEffect(()=>{e&&(o({height:e.itemHeight,width:e.itemWidth}),n({offsetTop:0,visibleHeight:e.viewportHeight,visibleWidth:e.viewportWidth}))},[e,n,o]),L.jsx(\"div\",{ref:s,style:qt(!1),children:t})},Pr=R.memo(function({...e}){const n=et(\"useWindowScroll\"),o=et(\"customScrollParent\"),r=o||n?Ar:jr,s=o||n?Vr:zr,i=et(\"context\");return L.jsx(r,{...e,...Z(r,i),children:L.jsxs(s,{children:[L.jsx(Fr,{}),L.jsx(Or,{}),L.jsx(Lr,{})]})})}),{Component:Mr,useEmitter:to,useEmitterValue:et,usePublisher:xt}=$e(Br,{events:{atBottomStateChange:\"atBottomStateChange\",atTopStateChange:\"atTopStateChange\",endReached:\"endReached\",isScrolling:\"isScrolling\",rangeChanged:\"rangeChanged\",readyStateChanged:\"readyStateChanged\",startReached:\"startReached\",stateChanged:\"stateChanged\"},methods:{scrollBy:\"scrollBy\",scrollTo:\"scrollTo\",scrollToIndex:\"scrollToIndex\"},optional:{components:\"components\",computeItemKey:\"computeItemKey\",context:\"context\",customScrollParent:\"customScrollParent\",data:\"data\",headerFooterTag:\"headerFooterTag\",increaseViewportBy:\"increaseViewportBy\",initialItemCount:\"initialItemCount\",initialTopMostItemIndex:\"initialTopMostItemIndex\",itemClassName:\"itemClassName\",itemContent:\"itemContent\",listClassName:\"listClassName\",logLevel:\"logLevel\",overscan:\"overscan\",restoreStateFrom:\"restoreStateFrom\",scrollerRef:\"scrollerRef\",scrollSeekConfiguration:\"scrollSeekConfiguration\",totalCount:\"totalCount\",useWindowScroll:\"useWindowScroll\"}},Pr),jr=qe({useEmitter:to,useEmitterValue:et,usePublisher:xt}),Ar=Ye({useEmitter:to,useEmitterValue:et,usePublisher:xt});function bn(t,e,n){return e!==\"normal\"&&!(e!=null&&e.endsWith(\"px\"))&&n(`${t} was not resolved to pixel value correctly`,e,mt.WARN),e===\"normal\"?0:parseInt(e!=null?e:\"0\",10)}const Wr=Mr;exports.GroupedVirtuoso=dr;exports.LogLevel=mt;exports.TableVirtuoso=vr;exports.Virtuoso=ar;exports.VirtuosoGrid=Wr;exports.VirtuosoGridMockContext=Ue;exports.VirtuosoMockContext=ue;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-virtuoso/dist/index.cjs\n");

/***/ })

};
;