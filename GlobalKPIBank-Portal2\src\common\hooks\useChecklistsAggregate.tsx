import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface ChecklistAggregateQueryRequest {
    title?: string
    externalIds?: string[]
    status?: string
    assignedTo?: string
    start?: number
    end?: number
    nextPage?: string
    includeArchived?: boolean
    templateIds?: string[]
}

const buildChecklistsAggregateQuery = (request: ChecklistAggregateQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.externalIds?.length) {
        filter.push(`{ externalId: { in: [${request.externalIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.templateIds?.length) {
        filter.push(`{ sourceId: { in: [${request.templateIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.title && request.title != '') {
        filter.push(`{ title: { prefix: "${request.title}" }}`)
    }

    if (request.status && request.status != '') {
        filter.push(`{ status: { eq: "${request.status}" }}`)
    }

    if (request.assignedTo && request.assignedTo != '') {
        filter.push(`{ assignedTo: { containsAny: ["${request.assignedTo}"] }}`)
    }

    if (request.end && request.start) {
        filter.push(`{
                        or: [
                          {
                            and: [
                              { startTime: { gte: ${request.start} }},
                              { startTime: { lte: ${request.end} }}
                            ]
                          },
                          {
                            and: [
                              { endTime: { gte: ${request.start} }},
                              { endTime: { lte: ${request.end} }}
                            ]
                          },
                        ]
                      }`)
    }

    filter.push(`{ not: { isArchived: { eq: true } } }`)

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetChecklistAggregate {
            aggregateChecklist(
                filter: ${queryFilter})
            {
                items {
                    count {
                        externalId
                    }
                }
            }
        }
    `

    return query
}

export const useChecklistsAggregate = (request: ChecklistAggregateQueryRequest) => {
    const query = buildChecklistsAggregateQuery(request)

    const result = useGraphqlQuery<any>(gql(query), 'aggregateChecklist', { fetchPolicy: 'no-cache' })

    const [resultData, setResultData] = useState<{ data: number; loading: boolean; pageInfo: PageInfo }>({
        data: 0,
        loading: true,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (result.data.length == 0) {
            setResultData({ data: 0, loading: result.loading, pageInfo: result.pageInfo })
        } else {
            setResultData({ data: result.data[0].count.externalId, loading: result.loading, pageInfo: result.pageInfo })
        }
    }, [result.data, result.loading, result.pageInfo])

    return {
        loadingCountChecklist: resultData.loading,
        countChecklist: resultData.data,
        refetchCountChecklist: result.refetch,
        pageInfo: resultData.pageInfo,
    }
}
