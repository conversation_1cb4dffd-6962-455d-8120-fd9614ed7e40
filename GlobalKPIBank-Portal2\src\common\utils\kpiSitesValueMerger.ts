import dayjs from 'dayjs'
import { GlobalVisionKpiTableModel } from '../models/globalVisionKpiTableModel'

export const mergeSiteValues = (
    allIds: Set<string>,
    globalViewDataMonth: GlobalVisionKpiTableModel[],
    globalViewDataQuarter: GlobalVisionKpiTableModel[],
    globalViewDataAnnual: GlobalVisionKpiTableModel[],
    arrayLength: number
): GlobalVisionKpiTableModel[] => {
    const monthMap = new Map(globalViewDataMonth.map((item) => [item.kpi.externalId, item]))
    const quarterMap = new Map(globalViewDataQuarter.map((item) => [item.kpi.externalId, item]))
    const yearMap = new Map(globalViewDataAnnual.map((item) => [item.kpi.externalId, item]))

    const result = []
    for (const id of allIds) {
        const monthItem = monthMap.get(id)
        const quarterItem = quarterMap.get(id)
        const yearItem = yearMap.get(id)

        const kpiInfo = monthItem ? monthItem.kpi : quarterItem ? quarterItem.kpi : yearItem ? yearItem.kpi : undefined
        const max =
            globalViewDataMonth[0]?.data?.length > 0
                ? globalViewDataMonth[0]?.data?.length
                : globalViewDataQuarter[0]?.data?.length > 0
                ? globalViewDataQuarter[0]?.data?.length
                : globalViewDataAnnual[0]?.data?.length > 0
                ? globalViewDataAnnual[0]?.data?.length
                : 0

        const merged = []
        Array.from({ length: Math.max(max, arrayLength) }, (_, i) => {
            const refSite = monthItem?.data[i]?.refReportingSite
                ? monthItem.data[i].refReportingSite
                : quarterItem?.data[i]?.refReportingSite
                ? quarterItem.data[i].refReportingSite
                : yearItem?.data[i]?.refReportingSite
                ? yearItem.data[i].refReportingSite
                : undefined

            const globalVisionKpi = monthItem?.data[i]?.refGlobalVisionKPI
                ? monthItem.data[i].refGlobalVisionKPI
                : quarterItem?.data[i]?.refGlobalVisionKPI
                ? quarterItem.data[i].refGlobalVisionKPI
                : yearItem?.data[i]?.refGlobalVisionKPI
                ? yearItem.data[i].refGlobalVisionKPI
                : undefined

            if (id === 'KPIG-PCM' || id === 'KPIG-MKG') {
                const aux = {
                    january: quarterItem?.data[i]?.january ?? 0,
                    february: quarterItem?.data[i]?.february ?? 0,
                    march: quarterItem?.data[i]?.march ?? 0,
                    april: quarterItem?.data[i]?.april ?? 0,
                    may: quarterItem?.data[i]?.may ?? 0,
                    june: quarterItem?.data[i]?.june ?? 0,
                    july: quarterItem?.data[i]?.july ?? 0,
                    august: quarterItem?.data[i]?.august ?? 0,
                    september: quarterItem?.data[i]?.september ?? 0,
                    october: quarterItem?.data[i]?.october ?? 0,
                    november: quarterItem?.data[i]?.november ?? 0,
                    december: quarterItem?.data[i]?.december ?? 0,

                    actualMonth: quarterItem?.data[i]?.[dayjs().format('MMMM').toLowerCase()] ?? 0,
                    lastMonth: quarterItem?.data[i]?.[dayjs().subtract(1, 'month').format('MMMM').toLowerCase()] ?? 0,
                    lastTwoMonth: quarterItem?.data[i]?.[dayjs().subtract(2, 'month').format('MMMM').toLowerCase()] ?? 0,
                    lastThreeMonth:
                        quarterItem?.data[i]?.[dayjs().subtract(3, 'month').format('MMMM').toLowerCase()] ?? 0,

                    ytd: yearItem?.data[i]?.ytd ?? 0,
                    lastYear: yearItem?.data[i]?.lastYear ?? 0,
                    lastTwoYear: yearItem?.data[i]?.lastTwoYear ?? 0,

                    refReportingSite: refSite,
                    refGlobalVisionKPI: globalVisionKpi,
                }
                merged.push(aux)
            } else {
                const aux = {
                    january: monthItem?.data[i]?.january ?? 0,
                    february: monthItem?.data[i]?.february ?? 0,
                    march: monthItem?.data[i]?.march ?? 0,
                    april: monthItem?.data[i]?.april ?? 0,
                    may: monthItem?.data[i]?.may ?? 0,
                    june: monthItem?.data[i]?.june ?? 0,
                    july: monthItem?.data[i]?.july ?? 0,
                    august: monthItem?.data[i]?.august ?? 0,
                    september: monthItem?.data[i]?.september ?? 0,
                    october: monthItem?.data[i]?.october ?? 0,
                    november: monthItem?.data[i]?.november ?? 0,
                    december: monthItem?.data[i]?.december ?? 0,

                    actualMonth: monthItem?.data[i]?.[dayjs().format('MMMM').toLowerCase()] ?? 0,
                    lastMonth: monthItem?.data[i]?.[dayjs().subtract(1, 'month').format('MMMM').toLowerCase()] ?? 0,
                    lastTwoMonth: monthItem?.data[i]?.[dayjs().subtract(2, 'month').format('MMMM').toLowerCase()] ?? 0,
                    lastThreeMonth: monthItem?.data[i]?.[dayjs().subtract(3, 'month').format('MMMM').toLowerCase()] ?? 0,

                    ytd: yearItem?.data[i]?.ytd ?? 0,
                    lastYear: yearItem?.data[i]?.lastYear ?? 0,
                    lastTwoYear: yearItem?.data[i]?.lastTwoYear ?? 0,

                    refReportingSite: refSite,
                    refGlobalVisionKPI: globalVisionKpi,
                }
                merged.push(aux)
            }
        })
        result.push({ kpi: kpiInfo, data: merged })
    }

    return result
}
