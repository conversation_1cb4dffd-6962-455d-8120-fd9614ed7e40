'use client'

import React, { useEffect, useState } from 'react'
import { ApexOptions } from 'apexcharts'
import { Box, Popper } from '@mui/material'
import { chartDefaultColors } from './styles'
import './styles.css'
import dynamic from 'next/dynamic'
import { numberFormat } from '@/common/utils/numberFormat'
import { useDataChartContext } from '@/common/contexts/DataChartContext'
import { BarChartTooltip } from './BarChartTooltip'
import { usePathname } from 'next/navigation'
import { useLocalTooltipData } from '@/common/hooks/useLocalTooltipData'
const ReactApexChart = dynamic(() => import('react-apexcharts'), { ssr: false })

const getMonthIndexFromLabel = (label: string): number => {
    if (label.startsWith('Q')) {
        const quarter = parseInt(label.substring(1))
        switch (quarter) {
            case 1:
                return 0 // January
            case 2:
                return 3 // April
            case 3:
                return 6 // July
            case 4:
                return 9 // October
            default:
                return -1
        }
    }

    const translationKeyMap: { [key: string]: number } = {
        'TABLE_COLS.MONTHS.JANUARY': 0,
        'TABLE_COLS.MONTHS.FEBRUARY': 1,
        'TABLE_COLS.MONTHS.MARCH': 2,
        'TABLE_COLS.MONTHS.APRIL': 3,
        'TABLE_COLS.MONTHS.MAY': 4,
        'TABLE_COLS.MONTHS.JUNE': 5,
        'TABLE_COLS.MONTHS.JULY': 6,
        'TABLE_COLS.MONTHS.AUGUST': 7,
        'TABLE_COLS.MONTHS.SEPTEMBER': 8,
        'TABLE_COLS.MONTHS.OCTOBER': 9,
        'TABLE_COLS.MONTHS.NOVEMBER': 10,
        'TABLE_COLS.MONTHS.DECEMBER': 11,
    }

    if (translationKeyMap[label] !== undefined) {
        return translationKeyMap[label]
    }

    const monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ]
    const normalizedLabel = label.toLowerCase()
    return monthNames.findIndex((month) => month.toLowerCase() === normalizedLabel)
}

export interface QuantityBarItemChart {
    label: string
    value: number | number[]
}

export interface ClnBarChartConfig {
    id: string
    data: QuantityBarItemChart[]
    labels?: string[]
    groups?: Array<string | number>
    title?: string
    isHorizontal?: boolean
    isStacked?: boolean
    width?: number | string
    heigth?: number | string
    labelSuffix?: string
    labelPrefix?: string
    colors?: string[]
    range?: number
    selectedKpi?: string
    setSelectedSitesOnLegend?: React.Dispatch<React.SetStateAction<QuantityBarItemChart[]>>
    onPointSelect?: (data: { seriesIndex: number; dataPointIndex: number }) => void
}

export function BarChart(config: ClnBarChartConfig) {
    const width = config.width || '100%'
    const heigth = config.heigth || '100%'
    const prefix = config.labelPrefix || ''
    const suffix = config.labelSuffix || ''
    const colors = config.colors ?? chartDefaultColors
    const [offsetXValue, setOffsetXValue] = useState<number>(0)
    const [seriesCollapsed, setSeriesCollapsed] = useState<number[]>([])

    const {
        disableSelection,
        setHoveredBar,
        setXLabel,
        setColumnValue,
        setSeriesColor,
        setSeriesName,
        setHoveredSiteIdOverride,
        setHoveredKpiOverride,
        setHoveredPeriodOverride,
    } = useDataChartContext()
    const pathname = usePathname()
    const isGlobalView = pathname === '/global-view'
    const [mousePosition, setMousePosition] = useState<{ top: number; left: number } | null>(null)
    const anchorRef = React.useRef<HTMLDivElement>(null)
    const chartRef = React.useRef<HTMLDivElement>(null)

    const [localTooltipParams, setLocalTooltipParams] = useState<{
        siteId?: string
        kpiCode?: string
        period?: string
        isActive: boolean
    }>({
        siteId: undefined,
        kpiCode: undefined,
        period: undefined,
        isActive: false,
    })

    const {
        localTooltipRows,
        loadingReportingUnits: localLoadingReportingUnits,
        totalSiteValue: localTotalSiteValue,
    } = useLocalTooltipData(localTooltipParams)

    const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
        if (!anchorRef.current) return
        setMousePosition({ top: event.clientY, left: event.clientX })
    }

    const handlePopoverClose = () => {
        setMousePosition(null)
    }

    useEffect(() => {
        if (config.id) {
            setHoveredSiteIdOverride(config.id)
        }
    }, [config.id, setHoveredSiteIdOverride])

    useEffect(() => {
        const chartDiv = chartRef.current
        if (!chartDiv) return

        const handleMouseLeave = () => {
            setHoveredBar(null)
            handlePopoverClose()
            setMousePosition(null)
        }

        chartDiv.addEventListener('mouseleave', handleMouseLeave)
        return () => {
            chartDiv.removeEventListener('mouseleave', handleMouseLeave)
        }
    }, [])

    const open = Boolean(mousePosition)

    const title = {
        text: config.title,
        style: {
            fontSize: '14px',
            color: '#9E9E9E',
            fontFamily: 'Roboto',
        },
    }

    const apexchartsSeries: ApexAxisChartSeries = []
    const apexchartsSeriesData = []

    for (const item of config.data) {
        if (config.labels && config.labels.length > 0) {
            apexchartsSeries.push({
                name: item.label,
                data: Array.isArray(item.value) ? (item.value as number[]) : [item.value],
            })
        } else {
            apexchartsSeriesData.push({
                x: item.label,
                y: item.value as number[],
            })
        }
    }

    const series: ApexAxisChartSeries =
        config.labels && config.labels.length > 0
            ? apexchartsSeries
            : [
                  {
                      name: config.title || 'Data',
                      data: apexchartsSeriesData,
                  },
              ]

    const xaxis = {
        categories:
            config.labels && config.labels.length > 0 ? config.labels : config.data.map((point: any) => point.label),
        labels: {
            formatter: function (val: any) {
                return config.isHorizontal ? `${prefix} ${val} ${suffix}` : val
            },
        },
    }

    const dataValues: number[] = []
    config.data.forEach((item) => {
        if (Array.isArray(item.value)) {
            dataValues.push(...item.value)
        } else if (typeof item.value === 'number') {
            dataValues.push(item.value)
        }
    })

    const hasData = dataValues.length > 0
    const hasTarget = config.range !== undefined
    const isNotOnlyPositiveKpi =
        config.selectedKpi === 'KPIG-MAF' ||
        config.selectedKpi === 'KPIG-QN1' ||
        config.selectedKpi === 'KPIG-HQS' ||
        config.selectedKpi === 'KPIG-TCT' ||
        config.selectedKpi === 'KPIG-TEP' ||
        config.selectedKpi === 'KPIG-TTL'

    const isOnlyPositiveKpi =
        config.selectedKpi === 'KPIG-RCI' ||
        config.selectedKpi === 'KPIG-NFA' ||
        config.selectedKpi === 'KPIG-PST' ||
        config.selectedKpi === 'KPIG-EVT' ||
        config.selectedKpi === 'KPIG-NFT' ||
        config.selectedKpi === 'KPIG-NNM' ||
        config.selectedKpi === 'KPIG-HPE' ||
        config.selectedKpi === 'KPIG-HQS' ||
        config.selectedKpi === 'KPIG-QN1' ||
        config.selectedKpi === 'KPIG-HQN' ||
        config.selectedKpi === 'KPIG-MAF'

    const allValues = [...dataValues, config.range!].filter((val) => val !== undefined)

    const minValue = Math.min(...allValues.map((val) => val ?? 0))
    const maxValue = Math.max(...allValues.map((val) => val ?? 0))

    const minValueWithOffset = parseInt((minValue * 1.1).toString())
    const maxValueWithOffset = parseInt(
        (isOnlyPositiveKpi ? (maxValue < 5 ? 5 : maxValue * 1.1) : maxValue * 1.1).toString()
    )

    const yaxis = {
        labels: {
            formatter: function (val: any) {
                return numberFormat(val) as any
            },
        },
        forceNiceScale: true,
        min:
            pathname === '/site-view' || pathname === '/home'
                ? (!hasData && !hasTarget ? 0 : undefined) ||
                  (!hasData && hasTarget ? (config.range >= 0 ? 0 : config.range * 1.1) : undefined) ||
                  (hasData && hasTarget && isNotOnlyPositiveKpi ? minValueWithOffset : undefined) ||
                  (hasData && hasTarget && isOnlyPositiveKpi ? minValueWithOffset : undefined) ||
                  (hasData && !hasTarget && isNotOnlyPositiveKpi ? minValueWithOffset : undefined) ||
                  (hasData && !hasTarget && isOnlyPositiveKpi ? minValueWithOffset : undefined)
                : undefined,
        max:
            pathname === '/site-view' || pathname === '/home'
                ? (!hasData && !hasTarget ? 5 : undefined) ||
                  (!hasData && hasTarget ? (config.range <= 0 ? 5 : config.range * 1.1) : undefined) ||
                  (hasData && hasTarget && isNotOnlyPositiveKpi ? maxValueWithOffset : undefined) ||
                  (hasData && hasTarget && isOnlyPositiveKpi ? maxValueWithOffset : undefined) ||
                  (hasData && !hasTarget && isNotOnlyPositiveKpi ? maxValueWithOffset : undefined) ||
                  (hasData && !hasTarget && isOnlyPositiveKpi ? maxValueWithOffset : undefined)
                : undefined,
    }

    const chart = {
        stacked: config.isStacked,
        toolbar: {
            show: false,
        },
        events: {
            legendClick: async function (chartContext, seriesIndex) {
                const seriesCollapsedAux: number[] = seriesCollapsed
                if (seriesCollapsedAux.filter((num) => num === seriesIndex).length === 0) {
                    seriesCollapsedAux.push(seriesIndex)
                    setSeriesCollapsed(seriesCollapsedAux)
                } else {
                    seriesCollapsedAux.splice(seriesCollapsed.indexOf(seriesIndex), 1)
                    setSeriesCollapsed(seriesCollapsedAux)
                }

                config.setSelectedSitesOnLegend(
                    config.data.filter(
                        (element) => !seriesCollapsed.map((index) => config.data[index]).includes(element)
                    )
                )
            },
            mounted: function (chartContext) {
                if (config.selectedKpi !== 'KPIG-PCM' && config.selectedKpi !== 'KPIG-MKG') {
                    const plotWidth = chartContext.w.globals.gridWidth * 0.25
                    setOffsetXValue(plotWidth)
                } else {
                    const plotWidth = chartContext.w.globals.gridWidth * 0.5
                    setOffsetXValue(plotWidth)
                }
            },
            updated: function (chartContext) {
                if (config.selectedKpi !== 'KPIG-PCM' && config.selectedKpi !== 'KPIG-MKG') {
                    const plotWidth = chartContext.w.globals.gridWidth * 0.25
                    setOffsetXValue(plotWidth)
                } else {
                    const plotWidth = chartContext.w.globals.gridWidth * 0.5
                    setOffsetXValue(plotWidth)
                }
            },
            dataPointSelection: function (event, chartContext, opts) {
                const { seriesIndex, dataPointIndex } = opts
                if (config.onPointSelect && disableSelection === false) {
                    config.onPointSelect({ seriesIndex, dataPointIndex })
                }
            },
            dataPointMouseEnter: function (_event, chartContext, { seriesIndex, dataPointIndex }) {
                setHoveredBar({ seriesIndex, dataPointIndex })
                handlePopoverOpen(_event)

                const xLabel = chartContext.w.globals.labels?.[dataPointIndex] ?? ''
                const columnValue = numberFormat(chartContext.w.globals.series?.[seriesIndex]?.[dataPointIndex])
                const seriesColor = chartContext.w.globals.colors?.[seriesIndex]
                const seriesName = chartContext.w.globals.seriesNames?.[seriesIndex]

                setXLabel(xLabel ?? '')
                setColumnValue(columnValue)
                setSeriesColor(seriesColor)
                setSeriesName(seriesName)

                if (isGlobalView && config.selectedKpi) {
                    setHoveredKpiOverride(config.selectedKpi)

                    const currentYear = new Date().getFullYear()
                    const monthIndex = getMonthIndexFromLabel(xLabel)
                    const formattedPeriod =
                        monthIndex !== -1 ? `${currentYear}-${String(monthIndex + 1).padStart(2, '0')}` : ''

                    setHoveredPeriodOverride(formattedPeriod)

                    setLocalTooltipParams({
                        siteId: config.id,
                        kpiCode: config.selectedKpi,
                        period: formattedPeriod,
                        isActive: true,
                    })
                }
            },
            dataPointMouseLeave: function () {
                setHoveredBar(null)
                handlePopoverClose()
                setMousePosition(null)

                if (isGlobalView) {
                    setHoveredKpiOverride('')
                    setHoveredPeriodOverride('')
                    setLocalTooltipParams({
                        siteId: undefined,
                        kpiCode: undefined,
                        period: undefined,
                        isActive: false,
                    })
                }
            },
        },
    }

    const plotOptions = {
        bar: {
            borderRadius: 0,
            horizontal: config.isHorizontal,
            barHeight: config.isHorizontal ? '20px' : 0,
        },
    }

    const dataLabels = {
        enabled: false,
    }

    const legend: ApexLegend = {
        show: config.labels && config.labels.length > 0,
        offsetY: 0,
        position: 'top',
        horizontalAlign: 'left',
        markers: {
            offsetY: 1.5,
            radius: 30,
        },
    }

    const tooltip = {
        enabled: false,
    }

    const options: ApexOptions =
        config.range !== undefined
            ? {
                  title,
                  plotOptions,
                  legend,
                  dataLabels,
                  colors,
                  chart,
                  xaxis,
                  yaxis,
                  tooltip,
                  annotations: {
                      yaxis: [
                          {
                              y: `${config.range}`,
                              borderColor: '#ED6C02',
                              strokeDashArray: 10,
                              width: '100%',
                              offsetX: 0,
                          },
                      ],
                  },
              }
            : {
                  title,
                  plotOptions,
                  legend,
                  dataLabels,
                  colors,
                  chart,
                  xaxis,
                  yaxis,
                  tooltip,
                  annotations: { yaxis: [] },
              }

    return (
        <Box
            ref={chartRef}
            sx={{
                height: 'inherit',
                minHeight: '300px',
                overflowX: 'hidden',
                overflowY:
                    config.isHorizontal && typeof heigth == 'number' ? (heigth < 300 ? 'hidden' : 'auto') : 'hidden',
            }}
        >
            <div
                ref={anchorRef}
                style={{
                    position: 'fixed',
                    top: mousePosition?.top || 0,
                    left: mousePosition?.left || 0,
                    width: 0,
                    height: 0,
                    pointerEvents: 'none',
                    overflow: 'hidden',
                }}
            />

            <ReactApexChart id={config.id} options={options} series={series} type="bar" width={width} height={heigth} />

            <Popper
                open={open}
                anchorEl={anchorRef.current}
                placement="top-start"
                modifiers={[
                    { name: 'offset', options: { offset: [0, 10] } },
                    { name: 'preventOverflow', options: { padding: 8 } },
                    { name: 'computeStyles', options: { adaptive: false } },
                ]}
                disablePortal={false}
                sx={{
                    zIndex: 1400,
                    transition: 'max-height 0.3s ease, width 0.3s ease',
                }}
            >
                <BarChartTooltip
                    localTooltipRows={isGlobalView ? localTooltipRows : undefined}
                    localLoadingReportingUnits={isGlobalView ? localLoadingReportingUnits : undefined}
                    localTotalSiteValue={isGlobalView ? localTotalSiteValue : undefined}
                />
            </Popper>
        </Box>
    )
}
