import { numberFormat } from '@/common/utils/numberFormat'
import {
    Box,
    Card,
    CardActionArea,
    CardContent,
    Collapse,
    IconButton,
    Table,
    TableBody,
    TableCell,
    TableRow,
    Typography,
} from '@mui/material'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import TrendingDownIcon from '@mui/icons-material/TrendingDown'
import { StripedRowOdd, StripedRowEven } from './styles'
import './styles.css'
import dayjs from 'dayjs'
import { agglomerateValues } from '@/common/utils/agglomerateKpi'
import { translate } from '@celanese/celanese-sdk'

interface WidgetMultiSitesProps {
    openRows: any
    handleToggle: (index: number) => void
    index: number
    filters: any
    currentTargetKpi?: any
    filteredData: any
    kpi: any
    onClick?: (filters: any) => void
}

function getTargetValue(diff: number, isRed: boolean) {
    const color = isRed ? 'red' : 'green'

    return (
        <>
            (
            {Math.sign(diff) === 1 ? (
                <TrendingUpIcon
                    sx={{
                        color: color,
                        verticalAlign: 'middle',
                    }}
                />
            ) : (
                <TrendingDownIcon
                    sx={{
                        color: color,
                        verticalAlign: 'middle',
                    }}
                />
            )}{' '}
            {numberFormat(Math.abs(diff)) ?? '--'})
        </>
    )
}

export const WidgetMultiSites: React.FC<WidgetMultiSitesProps> = ({
    openRows,
    handleToggle,
    index,
    filters,
    currentTargetKpi,
    filteredData,
    kpi,
    onClick,
}) => {
    const handleOpenModal = (site: any) => {
        onClick({ openModal: true, kpi: kpi, site: site })
    }
    const bannedKpis = ['TRIR Combined', 'TRIR Employee', 'TRIR Contractor']

    return (
        <>
            <StripedRowOdd index={index} sx={{ width: '100%', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                <TableCell sx={{ padding: 1 }}>
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            fontWeight: '500',
                        }}
                    >
                        {kpi.kpi.name}
                    </Box>
                </TableCell>
                <Typography
                    sx={{
                        fontSize: '15px',
                        lineHeight: '26px',
                        letterSpacing: '0.5px',
                        color: '#645F5F',
                        pt: '8px',
                        pb: '8px',
                        display: 'flex',
                        justifyContent: 'flex-end',
                    }}
                >
                    {bannedKpis.includes(kpi.kpi.name) ? (
                        <></>
                    ) : (
                        translate('TABLE_COLS.SELECTED_SITES_TOTAL') +
                        ': ' +
                        numberFormat(
                            agglomerateValues(
                                filteredData.map((row) => {
                                    const monthKey = dayjs(filters.period).format('MMMM').toLowerCase()
                                    const value = row[monthKey]
                                    if (typeof value === 'number' && !isNaN(value)) return value
                                    if (typeof value === 'string' && !isNaN(Number(value))) return Number(value)
                                    return undefined
                                }),
                                kpi.kpi.externalId
                            )
                        )
                    )}
                </Typography>
                <TableCell sx={{ width: '3%', padding: 1 }}>
                    <IconButton
                        aria-label="expand row"
                        size="small"
                        onClick={() => handleToggle(index)}
                        sx={{ padding: 0 }}
                    >
                        {openRows[index] ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                    </IconButton>
                </TableCell>
            </StripedRowOdd>
            <StripedRowEven index={index} sx={{ width: '100%', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                <TableCell sx={{ width: '100%', padding: '0px', borderBottom: 'unset' }} colSpan={12}>
                    <Collapse in={openRows[index]} timeout="auto" unmountOnExit>
                        <Box sx={{ margin: 0, borderBottom: 'unset' }}>
                            <Table>
                                <TableBody key={index}>
                                    <TableRow key={index}>
                                        <TableCell sx={{ borderBottom: 'unset', padding: 1 }} colSpan={12}>
                                            <Box
                                                sx={{
                                                    display: 'flex',
                                                    flexDirection: 'row',
                                                    flexWrap: 'wrap',
                                                    padding: '0px 0px',
                                                    gap: 1,
                                                }}
                                            >
                                                {kpi.data &&
                                                    filteredData.map((detailsRow) => {
                                                        const kpiValue = Number.parseFloat(
                                                            detailsRow[
                                                                dayjs(filters.period).format('MMMM').toLowerCase()
                                                            ]
                                                        )
                                                        const aux = currentTargetKpi.find(
                                                            (kpi) =>
                                                                kpi.refReportingSite.externalId ===
                                                                detailsRow.refReportingSite.externalId
                                                        )
                                                        const targetValue = aux?.value ?? 0
                                                        const isRed =
                                                            aux?.rangeDirection === 'Above'
                                                                ? kpiValue < targetValue
                                                                : kpiValue > targetValue
                                                        const diff = kpiValue - targetValue

                                                        return (
                                                            <Card
                                                                key={detailsRow.refReportingSite.externalId}
                                                                className="card"
                                                                sx={{
                                                                    borderColor: isRed ? 'red' : 'green',
                                                                    margin: 0,
                                                                }}
                                                            >
                                                                <CardActionArea
                                                                    onClick={() =>
                                                                        handleOpenModal(detailsRow.refReportingSite)
                                                                    }
                                                                >
                                                                    <CardContent>
                                                                        <>
                                                                            <Box
                                                                                sx={{
                                                                                    display: 'flex',
                                                                                    flexDirection: 'row',
                                                                                    color: isRed ? 'red' : 'green',
                                                                                    alignItems: 'baseline',
                                                                                }}
                                                                            >
                                                                                <Box>
                                                                                    <Typography
                                                                                        sx={{
                                                                                            fontSize: '36px',
                                                                                        }}
                                                                                    >
                                                                                        {numberFormat(kpiValue)}
                                                                                    </Typography>
                                                                                </Box>
                                                                                <Box
                                                                                    sx={{
                                                                                        marginLeft: '5px',
                                                                                    }}
                                                                                >
                                                                                    {isRed
                                                                                        ? getTargetValue(diff, isRed)
                                                                                        : getTargetValue(diff, isRed)}
                                                                                </Box>
                                                                            </Box>
                                                                            <Box>
                                                                                <Typography>
                                                                                    {detailsRow.refReportingSite.name}
                                                                                </Typography>
                                                                            </Box>
                                                                        </>
                                                                    </CardContent>
                                                                </CardActionArea>
                                                            </Card>
                                                        )
                                                    })}
                                            </Box>
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </Box>
                    </Collapse>
                </TableCell>
            </StripedRowEven>
        </>
    )
}
