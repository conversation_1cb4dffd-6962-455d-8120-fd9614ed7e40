import { gql } from 'graphql-tag'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import { KpiK<PERSON> } from '@/common/models/kpi'
import { KpiTableView } from '@/common/models/KpiTable'

const queryBuilder = (request: DataModelView) => {
    let codeQ = ''

    if (request && request.kpiFilters.info !== '') {
        codeQ = `{code: {eq: "${request.kpiFilters.info}"}}`
    }

    return `query getKpiDataView {
        listKpi (
            filter: {and: [${codeQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                externalId
                space
                code
                name
                description
                formula
                businessOwner {
                    externalId
                    email
                }
            }
        }
    }`
}

const mapKpiInfo = (data: KpiKpi[]): KpiTableView[] => {
    const mappedResult: KpiTableView[] = []
    if (data && data.length > 0) {
        data.map((item) => {
            const result: KpiTableView = {
                code: item.code ?? '',
                name: item.name ?? '',
                description: item.description ?? '',
                formula: item.formula ?? '',
                parameters: {},
                businessOwner: item.businessOwner?.email ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useKpiInfo = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<KpiKpi>(gql(query), 'listKpi', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'kpidomView',
        },
    })

    const [resultData, setResultData] = useState<{ data: any }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)
    const loadMoreExport = () => {
        if (!fdmPageInfo.hasNextPage) return
        setLoadMore(!loadMore)
    }
    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                const mappedResult = mapKpiInfo(fdmData)
                setResultData({ data: mappedResult })
                setPageInfoData(fdmPageInfo)
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
