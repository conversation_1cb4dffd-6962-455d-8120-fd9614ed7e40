import { useCallback, useState } from 'react'
import { Entity, MutationFunction, MutationFunctionResult, MutationHookResult } from '../models'
import { useCognite } from './useCognite'

export type EdgeMutation = Entity & {
    type: string
    startNode: string
    endNode: string
}

export function useFdmEdgeMutation<T extends EdgeMutation>(entityName: string): MutationHookResult<T> {
    const { fdmClient: client } = useCognite()
    const [data, setData] = useState<any | undefined>()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)
    const reset = useCallback(() => {
        setData(undefined)
        setError(undefined)
        setLoading(false)
        setCalled(false)
    }, [])

    const mutationFunction: MutationFunction<T> = useCallback(
        (data: T[]): Promise<MutationFunctionResult<T>> => {
            setLoading(true)
            setCalled(true)
            if (!data?.length) {
                return Promise.resolve({ ok: true })
            }

            return client
                .upsertEdges({ entityName, edges: data })
                .then((response) => {
                    setData(response)
                    return {
                        ok: true,
                        data: response,
                    }
                })
                .catch((error) => {
                    console.error(error)
                    setError(error)
                    throw error
                })
                .finally(() => setLoading(false))
        },
        [client, entityName]
    )

    return [mutationFunction, { data, error, loading, called, client, reset }]
}
