import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { useBusinessSegmentList } from '@/common/hooks/useBusinessSegment'
import { useGetKpiGroups } from '@/common/hooks/useGetKpiGroups'
import { useLocalStorage } from '@/common/hooks/useLocalStorage'
import { useReportingSiteList } from '@/common/hooks/useReportingSiteList'
import { HomeFilterValue } from '@/common/models/home-models'
import { FoundationalKpisKpidom } from '@/common/utils/foundational-kpis'
import HomeViewFilters from '@/components/Home/HomeViewFilters'
import { translate } from '@celanese/celanese-sdk'
import { ClnTabs, TabProps } from '@celanese/ui-lib'
import { Box, Grid, Typography } from '@mui/material'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { SiteViewTab } from '../SiteViewTab'
import dayjs from 'dayjs'

const HOME_FILTERS = 'homeFilters'

const styles = {
    wrapper: {
        borderRadius: '8px',
        width: 'auto',
        height: 'auto',
    },
}

const tabGroupsPermission = [
    'siteViewFoundationalKpi',
    'siteViewStewardshipKpi',
    'siteViewQualityKpi',
    'siteViewReliabilityKpi',
    'siteViewEngagementKpi',
]

export const SiteViewPage = () => {
    const {
        currentSitePageTab,
        setCurrentSitePageTab,
        doesKPIHasBusinessSegmentFilter,
        setDoesKPIHasBusinessSegmentFilter,
        setSelectedBusinessSegment,
        setIsBusinessSegmentFilterActive,
        setRequestData,
        selectedKpi,
        setIsProductivityKpi,
        setIsFutureProductivityKpi,
        setSelectedMultipleSiteIds,
    } = useGlobalKpiContext()

    const [parsedFilters, setParsedFilters] = useLocalStorage<HomeFilterValue | undefined>(HOME_FILTERS, undefined)
    const { kpiGroupsList, loadingKpiGroups } = useGetKpiGroups()
    const { checkPermissionsFromRoutes } = useAuthGuard()
    const { dataBusinessSegmentList: businessSegments, loadingBusinessSegmentList } = useBusinessSegmentList()
    const { dataSiteList: sites, loadingSiteList } = useReportingSiteList()
    const [filters, setFilters] = useState<any>()
    const isLoading = loadingKpiGroups || loadingBusinessSegmentList || loadingSiteList

    useEffect(() => {
        const isFlawlessDaysSelected = [
            FoundationalKpisKpidom.FLAWLESS_DAYS.EXTERNAL_ID,
            FoundationalKpisKpidom.KG_PER_HEADCOUNT.EXTERNAL_ID,
        ].includes(selectedKpi)

        setDoesKPIHasBusinessSegmentFilter(!isFlawlessDaysSelected)
        setIsProductivityKpi(
            [
                FoundationalKpisKpidom.PRODUCTIVITY_YEAR_FORECAST.EXTERNAL_ID,
                FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_NEXT_YEAR.EXTERNAL_ID,
                FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_PLUS_TWO_YEARS.EXTERNAL_ID,
            ].includes(selectedKpi)
        )
        setIsFutureProductivityKpi(
            [
                FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_NEXT_YEAR.EXTERNAL_ID,
                FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_PLUS_TWO_YEARS.EXTERNAL_ID,
            ].includes(selectedKpi)
        )

        const selectedBusinessSegment =
                doesKPIHasBusinessSegmentFilter && filters.businessSegment?.length == 1
                    ? filters.businessSegment[0].externalId
                    : ''
        setIsBusinessSegmentFilterActive(Boolean(selectedBusinessSegment))
    }, [currentSitePageTab, selectedKpi])

    const applyFilter = useCallback(
        (updatedFilters: HomeFilterValue) => {
            const mappedFilters = {
                ...updatedFilters,
                businessSegment: updatedFilters.businessSegment?.length == 1 ? updatedFilters.businessSegment : [],
                siteList: updatedFilters.siteList.map((site: any) => site.externalId),
            }
            const hasFilterChanged = JSON.stringify(filters) !== JSON.stringify(mappedFilters)
            if (hasFilterChanged) {
                setFilters(mappedFilters)
            } else {
                return
            }

            const selectedBusinessSegment =
                doesKPIHasBusinessSegmentFilter && updatedFilters.businessSegment?.length == 1
                    ? updatedFilters.businessSegment[0].externalId
                    : ''
            const selectedSites = updatedFilters.siteList.map((s) => s.externalId)
            setRequestData((prevData: any) => ({
                ...prevData,
                cursor: '',
                kpiFilters: {
                    ...prevData.kpiFilters,
                    businessSegment: selectedBusinessSegment,
                    refSite: selectedSites,
                    date: dayjs(updatedFilters.period).format('YYYY-MM-DD'),
                },
            }))
            setIsBusinessSegmentFilterActive(Boolean(selectedBusinessSegment))
            setSelectedBusinessSegment(selectedBusinessSegment)
            setSelectedMultipleSiteIds(selectedSites)

            setParsedFilters(updatedFilters)

            window.dispatchEvent(new CustomEvent('homeFiltersChanged', { detail: updatedFilters }))
        },
        [filters]
    )

    const tabsContent: React.ReactNode[] = useMemo(() => {
        return kpiGroupsList.map((groupName) => {
            return groupName?.name === 'Engagement' ? (
                <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" sx={{ color: '#083D5B', marginTop: '2rem' }}>
                        {translate('COMING_SOON')}
                    </Typography>
                </Box>
            ) : (
                <SiteViewTab tabGroup={groupName} filters={filters} />
            )
        })
    }, [kpiGroupsList, filters])

    const tabs: TabProps[] = kpiGroupsList.map((groupName, index) => ({
        label: translate('KPI_TAB_LIST.' + groupName?.name.toUpperCase()).toUpperCase(),
        content: tabsContent[index],
        disabled: !checkPermissionsFromRoutes(tabGroupsPermission[index]),
    }))

    if (isLoading) {
        return <></>
    }

    return (
        <div>
            <Grid container justifyContent="space-between" alignItems="flex-start" spacing={2}>
                <Grid item xs={12} sm={6}>
                    <Typography
                        variant="h3"
                        sx={{ color: '#083D5B', marginBottom: '1rem', fontWeight: 'bold', alignContent: 'flex-start' }}
                    >
                        {translate('MENU.SITE_VIEW')}
                    </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                    <Grid container spacing={2} justifyContent="flex-end">
                        <HomeViewFilters
                            currentTab={currentSitePageTab}
                            businessSegments={businessSegments}
                            sites={sites}
                            defaultValues={parsedFilters}
                            onSubmit={applyFilter}
                            disableSegmentSelector={!doesKPIHasBusinessSegmentFilter}
                            isPeriodYear
                        />
                    </Grid>
                </Grid>
            </Grid>

            <Box sx={styles.wrapper}>
                <ClnTabs
                    value={currentSitePageTab}
                    tabs={tabs}
                    onChange={(_e, value) => setCurrentSitePageTab(value)}
                />
            </Box>
        </div>
    )
}
