import { useGlobalVisionKPISite } from '@/common/hooks/useGlobalVisionKPISite'
import { useGlobalVisionKPISegment } from '@/common/hooks/useGlobalVisionKPISegment'
import { useGlobalVisionKPITarget } from '@/common/hooks/useGlobalVisionKPITarget'
import { KpiGroup } from '@/common/models/kpiGroup'
import { Dispatch, SetStateAction, useEffect, useMemo, useRef, useState, useCallback } from 'react'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { FinancialKpisDom } from '@/common/utils/financial-kpis'
import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import {
    Box,
    Button,
    ClickAwayListener,
    Paper,
    Popper,
    Table,
    TableBody,
    TableContainer,
    Typography,
} from '@mui/material'
import { LoaderCircular } from '../../Loader'
import { WidgetTable } from './WidgetTable'
import { PageNames } from '@/common/utils/page-names'
import { HomeViewModal } from '../HomeViewModal'
import { ClnButton } from '@celanese/ui-lib'
import { HomeViewTableKpi } from '../HomeViewTableKpi'
import ExpandSharpIcon from '@mui/icons-material/ExpandSharp'
import CloseFullscreenSharpIcon from '@mui/icons-material/CloseFullscreenSharp'
import { translate } from '@celanese/celanese-sdk'
import { WidgetGeneral } from '../HomeViewGeneral/WidgetGeneral'
import { GeneralFilter } from '@/common/models/homeGeneralFilter'
import { mergeSiteValues } from '@/common/utils/kpiSitesValueMerger'
import { getKpiFromCache, setKpiCache } from '@/common/clients/kpi-cache'

interface HomeViewWidgetsProps {
    tabGroup: KpiGroup | undefined
    filters?: any
    setGeneralFilterTag: Dispatch<SetStateAction<boolean>>
}

const useDebounce = (value: any, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value)

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value)
        }, delay)

        return () => {
            clearTimeout(handler)
        }
    }, [value, delay])

    return debouncedValue
}

export const HomeViewWidgets: React.FC<HomeViewWidgetsProps> = ({ tabGroup, filters, setGeneralFilterTag }) => {
    const referenceFoundationalKpidom = ['KPIG-APC', 'KPIG-APV', 'KPIG-FPC', 'KPIG-FPV']
    const { checkPermissionsFromRoutes } = useAuthGuard()
    const [permissionedHomeViewData, setPermissionedHomeViewData] = useState<GlobalVisionKpiTableModel[]>([])
    const [permissionedHomeViewDataSegment, setPermissionedHomeViewDataSegment] = useState<GlobalVisionKpiTableModel[]>(
        []
    )
    const [openModal, setOpenModal] = useState(false)
    const [isTableView, setIsTableView] = useState(false)
    const [data, setData] = useState<GlobalVisionKpiTableModel[]>([])
    const [allRows, setAllRows] = useState({})
    const [openRows, setOpenRows] = useState({})
    const [selectedKpi, setSelectedKpi] = useState<GlobalVisionKpiTableModel>()
    const [generalFilterOpen, setGeneralFilterOpen] = useState(false)
    const [generalViewMore, setGeneralViewMore] = useState(true)
    const [generalFilter, setGeneralFilter] = useState<GeneralFilter>({ externalId: 'MONTH', description: 'Month' })
    const anchorRef = useRef(null)

    const debouncedFilters = useDebounce(filters, 500)

    const cacheKeyBase = useMemo(
        () => ({
            sites: debouncedFilters?.siteList ?? [],
            kpiGroup: tabGroup !== undefined && tabGroup.name !== 'General' ? tabGroup.name : '',
            page: PageNames.HOME_VIEW.NAME,
            businessSegment: debouncedFilters?.businessSegment?.length
                ? debouncedFilters.businessSegment[0].externalId
                : undefined,
        }),
        [debouncedFilters, tabGroup]
    )

    const [cachedData, setCachedData] = useState<{
        month?: GlobalVisionKpiTableModel[]
        quarter?: GlobalVisionKpiTableModel[]
        annual?: GlobalVisionKpiTableModel[]
    }>({})

    const getGlobalVisionKpisSiteRequest = useMemo(
        () => ({
            skip: !debouncedFilters || debouncedFilters?.siteList?.length === 0,
            site: debouncedFilters?.siteList ?? [],
            kpiGroup: tabGroup !== undefined && tabGroup.name !== 'General' ? tabGroup.name : '',
            page: PageNames.HOME_VIEW.NAME,
            granularity: 'MON',
        }),
        [debouncedFilters, tabGroup]
    )

    useEffect(() => {
        if (!debouncedFilters?.siteList?.length) return

        const monthCache = getKpiFromCache({ ...cacheKeyBase, granularity: 'MON' })
        const quarterCache = getKpiFromCache({ ...cacheKeyBase, granularity: 'QRT' })
        const annualCache = getKpiFromCache({ ...cacheKeyBase, granularity: 'ANL' })

        setCachedData({
            month: monthCache,
            quarter: quarterCache,
            annual: annualCache,
        })
    }, [cacheKeyBase])

    const shouldFetchMonth = !cachedData.month && getGlobalVisionKpisSiteRequest.site.length > 0
    const shouldFetchQuarter = !cachedData.quarter && getGlobalVisionKpisSiteRequest.site.length > 0
    const shouldFetchAnnual = !cachedData.annual && getGlobalVisionKpisSiteRequest.site.length > 0

    const {
        dataGlobalVisionKPI: globalViewDataMonth,
        refetchGlobalVisionKPI,
        loadingGlobalVisionKPI,
    } = useGlobalVisionKPISite({
        ...getGlobalVisionKpisSiteRequest,
        granularity: 'MON',
        skip: !shouldFetchMonth,
    })

    const { dataGlobalVisionKPI: globalViewDataQuarter, loadingGlobalVisionKPI: loadingGlobalVisionKPISiteQuarter } =
        useGlobalVisionKPISite({
            ...getGlobalVisionKpisSiteRequest,
            granularity: 'QRT',
            skip: !shouldFetchQuarter,
        })

    const { dataGlobalVisionKPI: globalViewDataAnnual, loadingGlobalVisionKPI: loadingGlobalVisionKPISiteAnnual } =
        useGlobalVisionKPISite({
            ...getGlobalVisionKpisSiteRequest,
            granularity: 'ANL',
            skip: !shouldFetchAnnual,
        })

    const [globalViewData, setGlobalViewData] = useState([])

    useEffect(() => {
        if (globalViewDataMonth.length > 0) {
            setKpiCache({ ...cacheKeyBase, granularity: 'MON' }, globalViewDataMonth)
            setCachedData((prev) => ({ ...prev, month: globalViewDataMonth }))
        }
    }, [globalViewDataMonth, cacheKeyBase])

    useEffect(() => {
        if (globalViewDataQuarter.length > 0) {
            setKpiCache({ ...cacheKeyBase, granularity: 'QRT' }, globalViewDataQuarter)
            setCachedData((prev) => ({ ...prev, quarter: globalViewDataQuarter }))
        }
    }, [globalViewDataQuarter, cacheKeyBase])

    useEffect(() => {
        if (globalViewDataAnnual.length > 0) {
            setKpiCache({ ...cacheKeyBase, granularity: 'ANL' }, globalViewDataAnnual)
            setCachedData((prev) => ({ ...prev, annual: globalViewDataAnnual }))
        }
    }, [globalViewDataAnnual, cacheKeyBase])

    useEffect(() => {
        const monthData = cachedData.month || globalViewDataMonth
        const quarterData = cachedData.quarter || globalViewDataQuarter
        const annualData = cachedData.annual || globalViewDataAnnual

        if (monthData?.length > 0 && quarterData?.length > 0 && annualData?.length > 0) {
            const allIds = new Set<string>()
            monthData.forEach((item) => allIds.add(item.kpi.externalId))
            quarterData.forEach((item) => allIds.add(item.kpi.externalId))
            annualData.forEach((item) => allIds.add(item.kpi.externalId))

            const merged = mergeSiteValues(
                allIds,
                monthData,
                quarterData,
                annualData,
                debouncedFilters?.siteList?.length || 0
            )
            const result = merged.sort((a, b) => a.kpi.order - b.kpi.order)
            setGlobalViewData(result)
        }
    }, [cachedData, globalViewDataMonth, globalViewDataQuarter, globalViewDataAnnual, debouncedFilters])

    const getGlobalVisionKpiSegmentRequest = useMemo(
        () => ({
            skip: !debouncedFilters || debouncedFilters?.siteList?.length === 0,
            site: debouncedFilters?.siteList ?? [],
            businessSegment: debouncedFilters?.businessSegment?.length
                ? debouncedFilters.businessSegment[0].externalId
                : [],
            kpiGroup: tabGroup !== undefined && tabGroup.name !== 'General' ? tabGroup.name : '',
            page: PageNames.HOME_VIEW.NAME,
            granularity: 'MON',
        }),
        [debouncedFilters, tabGroup]
    )

    const segmentCacheKeyBase = useMemo(
        () => ({
            ...cacheKeyBase,
            businessSegment: debouncedFilters?.businessSegment?.length
                ? debouncedFilters.businessSegment[0].externalId
                : 'segment',
        }),
        [cacheKeyBase, debouncedFilters]
    )

    const [cachedSegmentData, setCachedSegmentData] = useState<{
        month?: GlobalVisionKpiTableModel[]
        quarter?: GlobalVisionKpiTableModel[]
        annual?: GlobalVisionKpiTableModel[]
    }>({})

    useEffect(() => {
        if (!debouncedFilters?.siteList?.length) return

        const monthCache = getKpiFromCache({ ...segmentCacheKeyBase, granularity: 'MON' })
        const quarterCache = getKpiFromCache({ ...segmentCacheKeyBase, granularity: 'QRT' })
        const annualCache = getKpiFromCache({ ...segmentCacheKeyBase, granularity: 'ANL' })

        setCachedSegmentData({
            month: monthCache,
            quarter: quarterCache,
            annual: annualCache,
        })
    }, [segmentCacheKeyBase])

    const shouldFetchSegmentMonth = !cachedSegmentData.month && getGlobalVisionKpiSegmentRequest.site.length > 0
    const shouldFetchSegmentQuarter = !cachedSegmentData.quarter && getGlobalVisionKpiSegmentRequest.site.length > 0
    const shouldFetchSegmentAnnual = !cachedSegmentData.annual && getGlobalVisionKpiSegmentRequest.site.length > 0

    const {
        dataGlobalVisionKPI: globalViewDataSegmentMonth,
        refetchGlobalVisionKPI: refetchGlobalVisionKPISegment,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISegment,
    } = useGlobalVisionKPISegment({
        ...getGlobalVisionKpiSegmentRequest,
        granularity: 'MON',
        skip: !shouldFetchSegmentMonth,
    })

    const {
        dataGlobalVisionKPI: globalViewDataSegmentQuarter,
        refetchGlobalVisionKPI: refetchGlobalVisionKPISegmentQuarter,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISegmentQuarter,
    } = useGlobalVisionKPISegment({
        ...getGlobalVisionKpiSegmentRequest,
        granularity: 'QRT',
        skip: !shouldFetchSegmentQuarter,
    })

    const {
        dataGlobalVisionKPI: globalViewDataSegmentAnnual,
        refetchGlobalVisionKPI: refetchGlobalVisionKPISegmentAnnual,
        loadingGlobalVisionKPI: loadingGlobalVisionKPISegmentAnnual,
    } = useGlobalVisionKPISegment({
        ...getGlobalVisionKpiSegmentRequest,
        granularity: 'ANL',
        skip: !shouldFetchSegmentAnnual,
    })

    const [globalViewDataSegment, setGlobalViewDataSegment] = useState<GlobalVisionKpiTableModel[]>([])

    useEffect(() => {
        if (globalViewDataSegmentMonth.length > 0) {
            setKpiCache({ ...segmentCacheKeyBase, granularity: 'MON' }, globalViewDataSegmentMonth)
            setCachedSegmentData((prev) => ({ ...prev, month: globalViewDataSegmentMonth }))
        }
    }, [globalViewDataSegmentMonth, segmentCacheKeyBase])

    useEffect(() => {
        if (globalViewDataSegmentQuarter.length > 0) {
            setKpiCache({ ...segmentCacheKeyBase, granularity: 'QRT' }, globalViewDataSegmentQuarter)
            setCachedSegmentData((prev) => ({ ...prev, quarter: globalViewDataSegmentQuarter }))
        }
    }, [globalViewDataSegmentQuarter, segmentCacheKeyBase])

    useEffect(() => {
        if (globalViewDataSegmentAnnual.length > 0) {
            setKpiCache({ ...segmentCacheKeyBase, granularity: 'ANL' }, globalViewDataSegmentAnnual)
            setCachedSegmentData((prev) => ({ ...prev, annual: globalViewDataSegmentAnnual }))
        }
    }, [globalViewDataSegmentAnnual, segmentCacheKeyBase])

    useEffect(() => {
        const monthData = cachedSegmentData.month || globalViewDataSegmentMonth
        const quarterData = cachedSegmentData.quarter || globalViewDataSegmentQuarter
        const annualData = cachedSegmentData.annual || globalViewDataSegmentAnnual

        if (monthData?.length > 0 && quarterData?.length > 0 && annualData?.length > 0) {
            const allIds = new Set<string>()
            monthData.forEach((item) => allIds.add(item.kpi.externalId))
            quarterData.forEach((item) => allIds.add(item.kpi.externalId))
            annualData.forEach((item) => allIds.add(item.kpi.externalId))

            const merged = mergeSiteValues(
                allIds,
                monthData,
                quarterData,
                annualData,
                debouncedFilters?.siteList?.length || 0
            )
            const result = merged.sort((a, b) => a.kpi.order - b.kpi.order)
            setGlobalViewDataSegment(result)
        }
    }, [
        cachedSegmentData,
        globalViewDataSegmentMonth,
        globalViewDataSegmentQuarter,
        globalViewDataSegmentAnnual,
        debouncedFilters,
    ])

    const getGlobalVisionKpiTargetRequest = useMemo(
        () => ({
            skip: !debouncedFilters,
            site: debouncedFilters?.siteList ?? [],
            kpiGroupExternalId: tabGroup !== undefined && tabGroup.name !== 'General' ? tabGroup.externalId : '',
            year: new Date(debouncedFilters?.period).getFullYear(),
        }),
        [debouncedFilters, tabGroup]
    )
    const {
        dataGlobalVisionKPITarget: globalViewTarget,
        refetchGlobalVisionKPITarget,
        loadingGlobalVisionKPITarget,
    } = useGlobalVisionKPITarget(getGlobalVisionKpiTargetRequest)

    const handleClosePopover = () => {
        setGeneralFilterOpen(false)
    }

    const handleToggle = (rowId) => {
        setOpenRows((prev) => ({
            ...prev,
            [rowId]: !prev[rowId],
        }))
    }
    const setAllRowsOpen = (kpis: any) => {
        setOpenRows(() => {
            const initialRow = {}
            kpis.forEach((_, index) => {
                initialRow[index] = true
            })
            return initialRow
        })
    }
    const setAllRowsClose = (kpis: any) => {
        setOpenRows(() => {
            const initialRow = {}
            kpis.forEach((_, index) => {
                initialRow[index] = false
            })
            return initialRow
        })
    }

    useEffect(() => {
        if (!globalViewData || globalViewData.length === 0) return
        let filteredKpisByPermission = globalViewData
        if (!checkPermissionsFromRoutes('globalViewFoundationalPlantCash')) {
            filteredKpisByPermission = filteredKpisByPermission.filter(
                (value) => value.kpi.externalId !== FinancialKpisDom.PLANT_CASH
            )
        }
        if (!checkPermissionsFromRoutes('globalViewFoundationalPlantCashKg')) {
            filteredKpisByPermission = filteredKpisByPermission.filter(
                (value) => value.kpi.externalId !== FinancialKpisDom.PLANT_CASH_KG
            )
        }
        filteredKpisByPermission = filteredKpisByPermission.filter(
            (kpi) => !referenceFoundationalKpidom.includes(kpi.kpi.externalId)
        )

        setPermissionedHomeViewData(filteredKpisByPermission)
    }, [globalViewData])

    useEffect(() => {
        if (globalViewDataSegment.length > 0) {
            const filteredKpisByPermission = globalViewDataSegment.filter(
                (kpi) => !referenceFoundationalKpidom.includes(kpi.kpi.externalId)
            )
            setPermissionedHomeViewDataSegment(filteredKpisByPermission)
        }
    }, [globalViewDataSegment])

    const debouncedRefetch = useCallback(() => {
        const timeoutId = setTimeout(() => {
            refetchGlobalVisionKPI()
            refetchGlobalVisionKPISegment()
            refetchGlobalVisionKPITarget()
        }, 300)

        return () => clearTimeout(timeoutId)
    }, [refetchGlobalVisionKPI, refetchGlobalVisionKPISegment, refetchGlobalVisionKPITarget])

    useEffect(() => {
        debouncedRefetch()
    }, [tabGroup, debouncedFilters])

    useEffect(() => {
        if (tabGroup.name === 'General') setGeneralFilterTag(false)
    }, [tabGroup])

    useEffect(() => {
        const canExpandAll = Object.values(openRows).some((value) => value === false)
        setAllRows(canExpandAll)
    }, [openRows])

    useEffect(() => {
        if (!debouncedFilters) {
            return
        }
        if (permissionedHomeViewData.length > 0 && permissionedHomeViewDataSegment.length > 0) {
            const result =
                debouncedFilters.businessSegment.length === 0
                    ? permissionedHomeViewData
                    : permissionedHomeViewDataSegment

            if (
                debouncedFilters.businessSegment.length > 0 &&
                debouncedFilters.siteList.length !== result[0]?.data.length
            ) {
                const filteredResult: GlobalVisionKpiTableModel[] = result.map((group) => {
                    return {
                        ...group,
                        data: group.data.filter(
                            (item) =>
                                item.refBusinessSegment.externalId === debouncedFilters.businessSegment[0].externalId
                        ),
                    }
                })
                setData(filteredResult)
            } else {
                setData(result)
            }

            if (debouncedFilters.siteList.length >= 2) {
                setAllRowsOpen(result)
            }
        }
    }, [debouncedFilters, permissionedHomeViewData, permissionedHomeViewDataSegment])

    const isLoading =
        (shouldFetchMonth && loadingGlobalVisionKPI) ||
        (shouldFetchQuarter && loadingGlobalVisionKPISiteQuarter) ||
        (shouldFetchAnnual && loadingGlobalVisionKPISiteAnnual) ||
        (shouldFetchSegmentMonth && loadingGlobalVisionKPISegment) ||
        (shouldFetchSegmentQuarter && loadingGlobalVisionKPISegmentQuarter) ||
        (shouldFetchSegmentAnnual && loadingGlobalVisionKPISegmentAnnual) ||
        loadingGlobalVisionKPITarget

    if (!debouncedFilters) {
        return <></>
    }

    return (
        <Box sx={{ maxWidth: '100%', height: 'calc(100% - 55px)', display: 'flex', flexDirection: 'column' }}>
            {tabGroup.name !== 'General' ? (
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                        mt: 1,
                        mb: 1,
                        columnGap: 1,
                    }}
                >
                    {debouncedFilters.siteList.length !== 1 && (
                        <ClnButton
                            variant="text"
                            size="small"
                            label={
                                allRows ? translate('COMMONBUTTON.EXPAND_ALL') : translate('COMMONBUTTON.COLLAPSE_ALL')
                            }
                            onClick={() => (allRows ? setAllRowsOpen(data) : setAllRowsClose(data))}
                            startIcon={allRows ? <ExpandSharpIcon /> : <CloseFullscreenSharpIcon />}
                        />
                    )}
                    <ClnButton
                        variant="outlined"
                        label={
                            isTableView === false
                                ? translate('COMMONBUTTON.TABLE_VIEW')
                                : translate('COMMONBUTTON.CARD_VIEW')
                        }
                        size="small"
                        onClick={() => (isTableView === false ? setIsTableView(true) : setIsTableView(false))}
                    />
                </Box>
            ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography sx={{ fontWeight: 400, size: '16px', lineHeight: '12px', letterSpacing: '0.5px' }}>
                            {translate('COMMONFILTER.VIEW_BY') + ' :'}
                        </Typography>
                        <Button ref={anchorRef} onClick={() => setGeneralFilterOpen(true)} variant="text">
                            {translate('COMMONFILTER.' + generalFilter.externalId)}
                            <span className="material-symbols-outlined">arrow_drop_down</span>
                        </Button>
                        <Popper open={generalFilterOpen} anchorEl={anchorRef.current} disablePortal sx={{ zIndex: 2 }}>
                            <ClickAwayListener onClickAway={handleClosePopover}>
                                <Paper sx={{ padding: '3px 0px', width: '100% !important' }}>
                                    <Box padding="2px 0px">
                                        <Button
                                            sx={{ width: '100%' }}
                                            onClick={() => {
                                                setGeneralFilter({ externalId: 'MONTH', description: 'Month' })
                                                setGeneralFilterOpen(false)
                                                setGeneralFilterTag(false)
                                            }}
                                        >
                                            {translate('COMMONFILTER.MONTH')}
                                        </Button>
                                    </Box>
                                    <Box padding="2px 0px">
                                        <Button
                                            sx={{ width: '100%' }}
                                            onClick={() => {
                                                setGeneralFilter({
                                                    externalId: 'YEAR_TO_DATE',
                                                    description: 'Year to Date',
                                                })
                                                setGeneralFilterOpen(false)
                                                setGeneralFilterTag(true)
                                            }}
                                        >
                                            {translate('COMMONFILTER.YEAR_TO_DATE')}
                                        </Button>
                                    </Box>
                                </Paper>
                            </ClickAwayListener>
                        </Popper>
                    </Box>
                    <Box>
                        <ClnButton
                            variant="outlined"
                            label={
                                generalViewMore
                                    ? translate('COMMONBUTTON.VIEW_LESS')
                                    : translate('COMMONBUTTON.VIEW_MORE')
                            }
                            size="small"
                            onClick={() => setGeneralViewMore((prevState) => !prevState)}
                        />
                    </Box>
                </Box>
            )}
            <TableContainer
                sx={{
                    overflow: 'auto',
                    height: '100%',
                }}
            >
                {isLoading ? (
                    LoaderCircular()
                ) : tabGroup.name === 'General' ? (
                    <>
                        <WidgetGeneral
                            filters={debouncedFilters}
                            generalFilter={generalFilter}
                            generalViewMore={generalViewMore}
                            data={data}
                            globalViewTarget={globalViewTarget}
                        />
                    </>
                ) : isTableView ? (
                    <HomeViewTableKpi data={data} filters={debouncedFilters} />
                ) : (
                    <Table>
                        <TableBody>
                            <WidgetTable
                                openRows={openRows}
                                handleToggle={handleToggle}
                                filters={debouncedFilters}
                                globalViewTarget={globalViewTarget}
                                data={data}
                                setOpenModal={setOpenModal}
                                setSelectedKpi={setSelectedKpi}
                            />
                        </TableBody>
                    </Table>
                )}
            </TableContainer>
            <HomeViewModal
                open={openModal}
                handleClose={() => setOpenModal(false)}
                data={selectedKpi}
                target={globalViewTarget}
            />
        </Box>
    )
}
