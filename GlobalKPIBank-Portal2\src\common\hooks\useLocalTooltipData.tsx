import { useState, useEffect } from 'react'
import { useReportingUnitTooltip } from './useReportingUnitTooltip'

interface TooltipRow {
    unit: string
    value: string
}

interface UseLocalTooltipDataProps {
    siteId?: string
    kpiCode?: string
    period?: string
    isActive: boolean
}

export const useLocalTooltipData = ({ siteId, kpiCode, period, isActive }: UseLocalTooltipDataProps) => {
    const [localTooltipRows, setLocalTooltipRows] = useState<TooltipRow[]>([])

    const {
        dataReportingUnits: reportingUnitsData,
        loadingReportingUnits,
        totalSiteValue,
    } = useReportingUnitTooltip(
        isActive ? siteId : undefined,
        isActive ? kpiCode : undefined,
        isActive ? period : undefined
    )

    useEffect(() => {
        if (isActive && reportingUnitsData && reportingUnitsData.length > 0) {
            const rows: TooltipRow[] = reportingUnitsData.map((unit) => ({
                unit: unit.unitDescription,
                value: unit.value,
            }))
            setLocalTooltipRows(rows)
        } else {
            setLocalTooltipRows([])
        }
    }, [reportingUnitsData, isActive])

    return {
        localTooltipRows,
        loadingReportingUnits,
        totalSiteValue,
    }
}
