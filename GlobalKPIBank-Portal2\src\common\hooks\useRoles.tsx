import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from './useGraphqlQuery'
import { Role } from '../models/role'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface RoleQueryRequest {
    name?: string
    siteId?: string
    category?: string
}

const buildRoleQuery = (request: RoleQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.name && request.name != '') {
        filter.push(`{ name: { eq: "${request.name}" }}`)
    }

    if (request?.siteId) {
        filter.push(`{ site: { externalId: { eq: "${request.siteId}" }}}`)
    }

    if (request?.category) {
        filter.push(`{ roleCategory: { externalId: { eq: "${request.category}" }}}`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.UMG, getLocalUserSite()?.siteCode)}", "${GetSpace(EntityType.UMG)}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetRole {
            listRole(
                filter: ${queryFilter}
            , first: 1000) {
                items {
                    externalId
                  	name
                  	description
                    createdTime
                    space
                  	roleCategory {
                        externalId
                        name
                        description
                        space
                    }
                    site {
                        externalId
                        name
                        description
                        aliases
                        createdTime
                        space
                        siteCode
                    }
                }
            }
        }
    `

    return query
}

export const useRoles = (request: RoleQueryRequest) => {
    const query = buildRoleQuery(request)
    const { data: fdmData } = useGraphqlQuery<Role>(gql(query), 'listRole', {})

    const [resultData, setResultData] = useState<{ data: Role[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        roles: resultData.data,
    }
}
