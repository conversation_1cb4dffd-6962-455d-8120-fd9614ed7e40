import { ObservationPriority, ObservationPriorityEnum } from './observation'

export const listPriorityDescription = [
    { id: '1', description: '1-Immediate' },
    { id: '2', description: '2-Within 2 wks' },
    { id: '3', description: '3-Within 2 to 4 wks' },
    { id: '4', description: '4-Within 4 to 8 wks' },
    { id: '5', description: '5-Greater than 8 wks' },
    { id: ObservationPriorityEnum.Low, description: 'LOW' },
    { id: ObservationPriorityEnum.Medium, description: 'MEDIUM' },
    { id: ObservationPriorityEnum.High, description: 'HIGH' },
    { id: ObservationPriorityEnum.Urgent, description: 'URGENT' },
    { id: '6', description: 'HIGH' },
    { id: '7', description: 'MEDIUM' },
    { id: '8', description: 'LOW' },
]

export const getpriorityDescription = (id: number | ObservationPriority | undefined | string) => {
    if (id === undefined) return ''
    return listPriorityDescription.find((x) => x.id === id)?.description
}
