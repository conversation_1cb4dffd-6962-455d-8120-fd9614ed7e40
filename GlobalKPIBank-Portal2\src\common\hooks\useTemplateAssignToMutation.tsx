import { Entity, ExternalEntity, MutationState } from '../models'
import { useCallback } from 'react'
import { useFdmMutation } from '.'
import { Template } from '../models/template'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export type TemplateAssignToMutationArgs<T extends Entity> = {
    newEntities: T[]
    fieldsToIgnore?: (keyof T)[]
}

export type UseTemplateAssignToMutationResult<T extends Entity> = [
    (args: TemplateAssignToMutationArgs<T>) => Promise<any>,
    MutationState
]

export interface TemplateWritableMutation extends ExternalEntity {
    assignedTo: string[]
}

export const useTemplateAssignToMutation = (): UseTemplateAssignToMutationResult<Template> => {
    const [templateWritableMutation, templateWritableMutationStatus] = useFdmMutation<TemplateWritableMutation>(
        'TemplateWritable',
        false,
        false
    )

    const newMutationFunction = useCallback(
        async ({ newEntities }: TemplateAssignToMutationArgs<Template>) => {
            const entitySpace = newEntities?.length
                ? newEntities[0].space
                : GetSpace(EntityType.APMATEMP, getLocalUserSite()?.siteCode)

            const newEntitiesWithoutEdges = newEntities.map(
                (e) =>
                    ({
                        externalId: e.externalId,
                        space: e.space,
                        assignedTo: e.assignedTo,
                    } as TemplateWritableMutation)
            )

            const templateWritableMutationResult = await templateWritableMutation(
                newEntitiesWithoutEdges,
                [],
                undefined,
                entitySpace
            )

            return {
                ok: templateWritableMutationResult.ok,
                data: templateWritableMutationResult.data,
                error: templateWritableMutationResult.error,
            }
        },
        [templateWritableMutation]
    )

    const newResetFunction = useCallback(() => {
        templateWritableMutationStatus.reset()
    }, [templateWritableMutationStatus])

    return [newMutationFunction, { ...templateWritableMutationStatus, reset: newResetFunction }]
}
