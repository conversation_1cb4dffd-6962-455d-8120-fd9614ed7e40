const kpisUsingAvg = ['KPIG-FLD', 'KPIG-ACF', 'KPIG-APF', 'KPIG-KGH', 'KPIG-OEE']

export function agglomerateValues(values: (number | undefined | null)[], selectedKpi: string): number {
    const isUsingAvg = kpisUsingAvg.includes(selectedKpi)
    const validValues = values.filter((v): v is number => typeof v === 'number' && !isNaN(v))
    if (validValues.length === 0) return 0
    return isUsingAvg
        ? validValues.reduce((acc, v) => acc + v, 0) / validValues.length
        : validValues.reduce((acc, v) => acc + v, 0)
}
