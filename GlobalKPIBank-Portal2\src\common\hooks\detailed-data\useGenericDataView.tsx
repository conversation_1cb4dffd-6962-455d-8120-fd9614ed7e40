import { useCostVariance } from './useCostVariance'
import { useVolumeVariance } from './useVolumeVariance'
import { useKpiManualInput } from './useKpiManualInput'
import { DataModelView } from '@/common/models/dataModelView'
import { useCashMargin } from './useCashMargin'
import { useProductivity } from './useProductivity'
import { useQualityNotification } from './useQualityNotification'
import { useBlockStock } from './useBlockStock'
import { useSwpIncidentImpact } from './useSwpIncidentImpact'
import { useWorkingHourReport } from './useWorkinHourReport'
import { useIncidentImpact } from './useIncidentImpact'
import { useIncident } from './useIncident'
import { useKpiInfo } from './useKpiInfo'
import { useDollarPerKg } from './useDollarPerKg'
import { useDefaultHook } from './useDefaultHook'

export const useGenericDataViewHook = (requestData: DataModelView) => {
    const hookMap = {
        '': useDefaultHook,
        'KPIManualInput': useKpiManualInput,
        'CostVariance': useCostVariance,
        'VolumeVariance': useVolumeVariance,
        'DollarPerKG': useDollarPerKg,
        'CashMargin': useCashMargin,
        'Productivity': useProductivity,
        'QualityNotification': useQualityNotification,
        'BlockStock': useBlockStock,
        'WorkingHourReport': useWorkingHourReport,
        'SwpIncidentImpact': useSwpIncidentImpact,
        'IncidentImpact': useIncidentImpact,
        'Incident': useIncident,
        'KpiInfo': useKpiInfo,
    }

    const tabIndex = requestData.kpiFilters.currentTab
    const kpiName = requestData.kpiFilters.views[tabIndex] ?? ''
    const selectedHook = hookMap[kpiName]

    return selectedHook(requestData)
}
