import { TimeSeries } from './timeseries'
import { ExternalEntity } from '.'
import { Dayjs } from 'dayjs'

export interface Measurement extends ExternalEntity {
    visibility: string
    title: string
    description: string
    labels: string[]
    type: string
    order: number
    timeseries: TimeSeries
    min: number
    max: number
    options: any[]
    measuredAt: Dayjs
    numericReading: number
    stringReading: string
}
