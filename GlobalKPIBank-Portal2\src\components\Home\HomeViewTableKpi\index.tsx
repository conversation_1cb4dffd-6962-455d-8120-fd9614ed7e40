import {
    Box,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tooltip,
    Typography,
} from '@mui/material'
import { useEffect, useState } from 'react'
import { LoaderCircular } from '../../../components/Loader'
import dayjs from 'dayjs'
import { translate } from '@celanese/celanese-sdk'
import { numberFormat } from '@/common/utils/numberFormat'
import { addsBreakLineOnText } from '@/common/utils/stringFormat'
import { MatIcon } from '@celanese/ui-lib'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { VisionKpiAggregation } from '@/common/models/visionKpiAggregation'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import { agglomerateValues } from '@/common/utils/agglomerateKpi'

interface HomeViewTableProps {
    data?: GlobalVisionKpiTableModel[]
    target?: GlobalVisionKpiTarget[]
    filters?: any
}

export const HomeViewTableKpi: React.FC<HomeViewTableProps> = ({ data, target, filters }) => {
    const [loader, setLoader] = useState(true)
    const { requestData } = useGlobalKpiContext()
    const [selectedKpis, setSelectedKpis] = useState<string[]>([])
    const formatLastMonths = (count: number): string[] => {
        const monthNames = [
            translate('TABLE_COLS.MONTHS.JANUARY'),
            translate('TABLE_COLS.MONTHS.FEBRUARY'),
            translate('TABLE_COLS.MONTHS.MARCH'),
            translate('TABLE_COLS.MONTHS.APRIL'),
            translate('TABLE_COLS.MONTHS.MAY'),
            translate('TABLE_COLS.MONTHS.JUNE'),
            translate('TABLE_COLS.MONTHS.JULY'),
            translate('TABLE_COLS.MONTHS.AUGUST'),
            translate('TABLE_COLS.MONTHS.SEPTEMBER'),
            translate('TABLE_COLS.MONTHS.OCTOBER'),
            translate('TABLE_COLS.MONTHS.NOVEMBER'),
            translate('TABLE_COLS.MONTHS.DECEMBER'),
        ]

        return Array.from({ length: count }, (_, i) => {
            const currentDate = dayjs().subtract(i, 'month')
            return `${monthNames[currentDate.month()]}/${currentDate.format('YY')}`
        }).reverse()
    }
    const last4Months = formatLastMonths(4)
    const selectedMonthKey = dayjs(requestData.kpiFilters.date)
        .format('MMMM')
        .toLowerCase() as keyof VisionKpiAggregation
    const uniqueSites = Array.from(new Set(data?.flatMap((kpi) => kpi.data.map((d) => d.refReportingSite.name)) || []))
    const filteredData = selectedKpis.includes('All')
        ? data
        : data?.filter((kpi) => selectedKpis.includes(kpi.kpi.name))

    useEffect(() => {
        setLoader(data?.length === 0)

        if (filters) {
            setSelectedKpis(filters.kpiList?.map((kpiName) => kpiName.name))
        }
    }, [data])

    return (
        <Box sx={{ maxWidth: '100%', height: '60vh', marginTop: '16px' }}>
            <TableContainer sx={{ overflow: 'auto', maxHeight: 'calc(70vh - 50px)' }}>
                {loader ? (
                    LoaderCircular()
                ) : uniqueSites.length === 1 ? (
                    <Table>
                        <TableHead
                            sx={{
                                position: 'sticky',
                                top: 0,
                                zIndex: 1,
                                backgroundColor: '#ffffff',
                                fontWeight: 'bold',
                                textAlign: 'center',
                                borderBottom: '2px solid #ddd',
                            }}
                        >
                            <TableRow>
                                <TableCell align="left" sx={{ fontWeight: 'bold' }}>
                                    {translate('TABLE_COLS.KPI')}
                                </TableCell>
                                <TableCell align="center" sx={{ width: '9%' }}>
                                    {dayjs().year() - 2}
                                </TableCell>
                                <TableCell align="center" sx={{ width: '9%' }}>
                                    {dayjs().year() - 1}
                                </TableCell>
                                <TableCell align="center" sx={{ width: '9%' }}>
                                    {translate('TABLE_COLS.TARGET')}
                                </TableCell>
                                <TableCell align="center">YTD</TableCell>
                                {last4Months.map((month, i) => (
                                    <TableCell key={i} align="center">
                                        {month}
                                    </TableCell>
                                ))}
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {filteredData?.flatMap((kpi, kpiIndex) =>
                                kpi.data.map((row, rowIndex) => {
                                    const absoluteIndex = kpiIndex * kpi.data.length + rowIndex
                                    return (
                                        <TableRow
                                            key={`${kpi.kpi.name}-${rowIndex}`}
                                            sx={{ backgroundColor: absoluteIndex % 2 === 0 ? '#f9f9f9' : '#ffffff' }}
                                        >
                                            <TableCell
                                                align="left"
                                                sx={{
                                                    fontWeight: 'bold',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: 1,
                                                }}
                                            >
                                                {kpi.kpi.name}
                                                {kpi.kpi.description && (
                                                    <Tooltip
                                                        title={
                                                            <Typography
                                                                sx={{ fontSize: '14px', whiteSpace: 'pre-line' }}
                                                            >
                                                                {addsBreakLineOnText(
                                                                    kpi.kpi.description,
                                                                    'Update frequency:'
                                                                )}
                                                            </Typography>
                                                        }
                                                    >
                                                        <div>
                                                            <MatIcon
                                                                icon="info"
                                                                sx={{
                                                                    color: '#083D5B',
                                                                    fontSize: 18,
                                                                    cursor: 'pointer',
                                                                }}
                                                            />
                                                        </div>
                                                    </Tooltip>
                                                )}
                                            </TableCell>
                                            <TableCell align="center">{numberFormat(row.lastTwoYear || 0)}</TableCell>
                                            <TableCell align="center">{numberFormat(row.lastYear || 0)}</TableCell>
                                            <TableCell align="center">
                                                {target?.find(
                                                    (item) =>
                                                        item.refGlobalVisionKPI.externalId === kpi.kpi.externalId &&
                                                        item.refReportingSite.externalId ===
                                                            row.refReportingSite.externalId
                                                )?.value || '-'}
                                            </TableCell>
                                            <TableCell align="center">{numberFormat(row.ytd || 0)}</TableCell>
                                            {last4Months.map((month, i) => {
                                                const monthKey = month.split('/')[0].toLowerCase()
                                                return (
                                                    <TableCell key={i} align="center">
                                                        {numberFormat(row[monthKey] || 0)}
                                                    </TableCell>
                                                )
                                            })}
                                        </TableRow>
                                    )
                                })
                            )}
                        </TableBody>
                    </Table>
                ) : (
                    <Table>
                        <TableHead
                            sx={{
                                position: 'sticky',
                                top: 0,
                                zIndex: 1,
                                backgroundColor: '#ffffff',
                                fontWeight: 'bold',
                                textAlign: 'center',
                                borderBottom: '2px solid #ddd',
                            }}
                        >
                            <TableRow>
                                <TableCell align="left" sx={{ fontWeight: 'bold' }}>
                                    {translate('TABLE_COLS.KPI')}
                                </TableCell>
                                <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                                    {translate('TABLE_COLS.SELECTED_SITES_TOTAL')}
                                </TableCell>
                                {uniqueSites.map((site, idx) => (
                                    <TableCell key={idx} align="center">
                                        {site}
                                    </TableCell>
                                ))}
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {filteredData?.map((kpi, index) => {
                                const siteValues = uniqueSites.map((site) => {
                                    const siteData = kpi.data.find((d) => d.refReportingSite.name === site)
                                    const value = siteData?.[selectedMonthKey]
                                    return typeof value === 'number'
                                        ? value
                                        : !isNaN(Number(value))
                                        ? Number(value)
                                        : undefined
                                }) as (number | undefined | null)[]
                                const selectedSitesValue = agglomerateValues(siteValues, kpi.kpi.externalId)
                                return (
                                    <TableRow
                                        key={index}
                                        sx={{ backgroundColor: index % 2 === 0 ? '#f9f9f9' : '#ffffff' }}
                                    >
                                        <TableCell
                                            align="left"
                                            sx={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 1 }}
                                        >
                                            {kpi.kpi.name}
                                            {kpi.kpi.description && (
                                                <Tooltip
                                                    title={
                                                        <Typography sx={{ fontSize: '14px', whiteSpace: 'pre-line' }}>
                                                            {addsBreakLineOnText(
                                                                kpi.kpi.description,
                                                                'Update frequency:'
                                                            )}
                                                        </Typography>
                                                    }
                                                >
                                                    <div>
                                                        <MatIcon
                                                            icon="info"
                                                            sx={{ color: '#083D5B', fontSize: 18, cursor: 'pointer' }}
                                                        />
                                                    </div>
                                                </Tooltip>
                                            )}
                                        </TableCell>
                                        <TableCell align="center">{numberFormat(selectedSitesValue)}</TableCell>
                                        {uniqueSites.map((site, idx) => {
                                            const siteData = kpi.data.find((d) => d.refReportingSite.name === site)
                                            const value = siteData?.[selectedMonthKey] as number | undefined
                                            return (
                                                <TableCell key={idx} align="center">
                                                    {value !== undefined ? numberFormat(value) : '-'}
                                                </TableCell>
                                            )
                                        })}
                                    </TableRow>
                                )
                            })}
                        </TableBody>
                    </Table>
                )}
            </TableContainer>
        </Box>
    )
}
