import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from './useGraphqlQuery'
import { Site } from '../models/site'
import { ArrayEntity } from '../models'
import { Unit } from '../models/unit'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface SiteQueryRequest {
    unitName?: string
}

const buildSiteQuery = (request: SiteQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.unitName && request.unitName != '') {
        filter.push(`{ reportingUnits: { name: { eq: "${request.unitName}" }}}`)
    }

    filter.push(`{ externalId: { eq: "${getLocalUserSite()?.siteId}"}}`)

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.REF, getLocalUserSite()?.siteCode)}", "${GetSpace(EntityType.REF)}"]
            }
        }`
    )

    filter.push(`{ isActive: { eq: ${true} } }`)

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetReportingSite {
            listReportingSite(
                filter: ${queryFilter}
            , first: 1000) {
                items {
                    externalId
                    name
                    description
                    aliases
                    createdTime
                    space
                    reportingUnits {
                        items {
                            externalId
                            name
                            description
                            space
                        }
                    }
                    functionalLocations{
                        items{
                          externalId
                          name
                        }
                    }
                }
            }
        }
    `

    return query
}

export const useSites = (request: SiteQueryRequest) => {
    const query = buildSiteQuery(request)
    const { data: fdmData } = useGraphqlQuery<Site>(gql(query), 'listReportingSite', {})

    const [resultData, setResultData] = useState<{ data: Site[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayEntity = d.reportingUnits as any as ArrayEntity<Unit>

                if (arrayEntity.items.length) {
                    return {
                        ...d,
                        reportingUnits: arrayEntity.items,
                    }
                }

                return {
                    ...d,
                    reportingUnits: [],
                }
            })
            setResultData({ data: fdmDataParsed, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        sites: resultData.data,
    }
}
