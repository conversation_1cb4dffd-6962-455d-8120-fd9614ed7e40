'use client'

import { Box } from '@mui/material'
import React from 'react'
import AuthGuardWrapper from '@/common/wrapper/AuthGuardWrapper'
import { ManualInputPage } from '../../components/ManualInput/ManualInputPage'
import { usePageTitle } from '@/common/hooks/general-functions/usePageTitle'
import { translate } from '@celanese/celanese-sdk'

export default function ManualInput() {
    usePageTitle(translate('MENU.MANUAL_INPUT'))
    return (
        <AuthGuardWrapper componentName={ManualInput.name}>
            <Box
                sx={{
                    width: '100%',
                    padding: '2rem',
                    height: 'calc(100vh - 48px)',
                    '@media (max-width:600px)': {
                        padding: '60px 10px 30px 10px',
                    },
                    background: '#F8F8F8',
                }}
            >
                <ManualInputPage />
            </Box>
        </AuthGuardWrapper>
    )
}
