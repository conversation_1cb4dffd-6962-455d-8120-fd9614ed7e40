import {
    Box,
    Table,
    TableHead,
    TableBody,
    TableCell,
    TableRow,
    IconButton,
    TextField,
    styled,
    Autocomplete,
    Checkbox,
} from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import ModeOutlinedIcon from '@mui/icons-material/ModeOutlined'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import AddIcon from '@mui/icons-material/Add'
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined'
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined'
import { Dispatch, SetStateAction, useContext, useEffect, useState } from 'react'
import {
    FavoriteFiltersMutation,
    useGlobalVisionFavoriteFilterMutation,
} from '@/common/hooks/useGlobalVisionFavoriteFiltersMutation'
import { ModalWrapper } from '../../Modal/ModalWrapper'
import { Site } from '@/common/models/site'
import { GlobalVisionFavoritesFilters } from '@/common/models/globalVisionFavoritesFilters'
import { translate } from '@celanese/celanese-sdk'
import { EntityType, GetSpace } from '@/common/utils/space-util'
import { ApolloQueryResult } from '@apollo/client'
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank'
import CheckBoxIcon from '@mui/icons-material/CheckBox'
import { AuthGuardContext } from '@/common/contexts/AuthGuardContext'

interface GlobalViewModalProps {
    data: string
    open: boolean
    customFiltersList: GlobalVisionFavoritesFilters[]
    setCustomFiltersList: Dispatch<SetStateAction<GlobalVisionFavoritesFilters[]>>
    sites: Site[]
    refetchGlobalVisionKPI: () => Promise<ApolloQueryResult<any>>
    handleClose: () => void
}

const configFilterSchema = z.object({
    customFiltersList: z.array(
        z.object({
            externalId: z.string(),
            name: z.string(),
            space: z.string(),
            refUser: z.object({
                externalId: z.string(),
                space: z.string(),
            }),
            refReportingSites: z.array(
                z.object({
                    externalId: z.string(),
                    name: z.string(),
                    description: z.string(),
                    space: z.string(),
                })
            ),
            order: z.number().optional(),
        })
    ),
})

type configFilterSchema = z.infer<typeof configFilterSchema>

const Form = styled('form')({})

export const EditFiltersModal: React.FC<GlobalViewModalProps> = ({
    open,
    handleClose,
    sites,
    customFiltersList,
    setCustomFiltersList,
    refetchGlobalVisionKPI,
}) => {
    const [editToggle, setEditToggle] = useState<boolean[]>([false])
    const { userInfo } = useContext(AuthGuardContext)

    useEffect(() => {
        setEditToggle(new Array(customFiltersList.length).fill(false))
    }, [!open])

    const handleEditToggle = (index: number, prevIndex?: number, action?: string) => {
        resetRowValues(index, prevIndex, action)
        setEditToggle((prevState) => {
            const currentState = [...prevState]
            if (prevIndex !== undefined && prevIndex !== -1) {
                currentState[prevIndex] = !prevState[prevIndex]
            }
            currentState[index] = !prevState[index]
            return currentState
        })
    }

    const resetRowValues = (index: number, prevIndex?: number, action?: string) => {
        const requestedIndex = prevIndex !== undefined && prevIndex !== -1 ? prevIndex : index
        if (action !== 'submit' && customFiltersList[requestedIndex]) {
            setValue(`customFiltersList.${requestedIndex}.name`, customFiltersList[requestedIndex].name)
            setValue(
                `customFiltersList.${requestedIndex}.refReportingSites`,
                customFiltersList[requestedIndex].refReportingSites
            )
        }
    }

    const {
        updateGlobalVisionFavoriteFiltersMutationFn,
        deleteNodesGlobalVisionFavoriteFiltersMutationFn,
        deleteEdgesGlobalVisionFavoriteFiltersMutationFn,
    } = useGlobalVisionFavoriteFilterMutation()

    const saveFilter = async (
        newFilters: FavoriteFiltersMutation[],
        refReportingSitesDiff?: FavoriteFiltersMutation[]
    ) => {
        if (refReportingSitesDiff)
            deleteEdgesGlobalVisionFavoriteFiltersMutationFn(refReportingSitesDiff).catch((error) => console.log(error))
        updateGlobalVisionFavoriteFiltersMutationFn(newFilters)
            .then(() => setCustomFiltersList(newFilters))
            .catch((error) => console.log(error))
    }

    const { reset, setValue, handleSubmit, control } = useForm<configFilterSchema>({
        defaultValues: {
            customFiltersList,
        },
        resolver: zodResolver(configFilterSchema),
    })

    useEffect(() => {
        reset({
            customFiltersList: customFiltersList,
        })
    }, [customFiltersList])

    const handleFilterSubmit = (data: configFilterSchema) => {
        let highestOrder = 1
        const isNameSet = !data.customFiltersList.some((item) => item.name === '')
        const newFilters = data.customFiltersList.map((e) => {
            highestOrder = e.order && e.order >= highestOrder ? (highestOrder = e.order + 1) : highestOrder
            const user = e.refUser.externalId
                ? e.refUser
                : { externalId: userInfo.email, space: GetSpace(EntityType.UMG) }
            const order = e.order ? e.order : highestOrder
            const externalId = e.externalId ? e.externalId : `GKPI-FILTER-${user.externalId}-${order}`
            const space = e.space ? e.space : GetSpace(EntityType.Instance)

            return {
                name: e.name,
                externalId: externalId,
                space: space,
                refUser: user,
                refReportingSites: e.refReportingSites,
                order: order,
            } as unknown as FavoriteFiltersMutation
        })
        const refReportingSitesDiff = customFiltersList.map((customFilter) => {
            const filterData = data.customFiltersList.find((e) => e.externalId === customFilter.externalId)

            const diff = customFilter.refReportingSites.filter((site) => {
                if (filterData)
                    return !filterData.refReportingSites.some((dataSite) => dataSite.externalId === site.externalId)
                return true
            })
            return {
                name: customFilter.name,
                externalId: customFilter.externalId,
                refReportingSites: diff,
            } as FavoriteFiltersMutation
        })
        const isDiff = refReportingSitesDiff.some((sitesDiff) => sitesDiff.refReportingSites.length > 0)
        isNameSet
            ? saveFilter(newFilters, isDiff ? refReportingSitesDiff : undefined)
            : setCustomFiltersList((prevState) => prevState.slice(0, -1))
    }

    const deleteFilter = async (filter: FavoriteFiltersMutation) => {
        deleteNodesGlobalVisionFavoriteFiltersMutationFn(filter)
            .then(() => refetchGlobalVisionKPI())
            .catch((error) => {
                setCustomFiltersList((prevState) => prevState.slice(0, -1))
            })
    }

    const handleDelete = (filter: FavoriteFiltersMutation) => {
        deleteFilter(filter)
    }

    return (
        <ModalWrapper
            openModal={open}
            closeModal={handleClose}
            title={translate('MODAL.PRE_SELECT_FILTERS_SETTINGS')}
            content={
                <Form onSubmit={handleSubmit(handleFilterSubmit)}>
                    <Box
                        sx={{
                            width: '1000px',
                            height: '100%',
                            maxHeight: '500px',
                            overflowY: 'auto',
                            padding: '20px',
                            fontFamily: 'Roboto',
                            margin: '10px',
                        }}
                    >
                        <Table>
                            <TableHead>
                                <TableCell align="left" sx={{ width: '17%' }}>
                                    {translate('TABLE_COLS.NAME')}
                                </TableCell>
                                <TableCell align="left" sx={{ width: '40%' }}>
                                    {translate('TABLE_COLS.SITE')}
                                </TableCell>
                                <TableCell sx={{ width: '4%' }} />
                                <TableCell sx={{ width: '4%' }} />
                            </TableHead>
                            <TableBody>
                                {customFiltersList.map((data, index) => (
                                    <TableRow
                                        key={index}
                                        sx={{ bgcolor: index % 2 === 0 ? 'background.default' : 'background.main' }}
                                    >
                                        <TableCell align="left" sx={{ width: '17%' }}>
                                            <Controller
                                                name={`customFiltersList.${index}.name`}
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        disabled={!editToggle[index]}
                                                        variant="outlined"
                                                        sx={{
                                                            '& .MuiOutlinedInput-root': {
                                                                '& fieldset': {
                                                                    border: editToggle[index] ? '1px solid' : 'none',
                                                                },
                                                            },
                                                            '& .Mui-disabled': {
                                                                color: 'black !important',
                                                                '-webkit-text-fill-color': 'black !important',
                                                            },
                                                        }}
                                                        onChange={(event) => {
                                                            setValue(
                                                                `customFiltersList.${index}.name`,
                                                                event.target.value
                                                            )
                                                        }}
                                                        onBlur={(event) => {
                                                            event.target.value || resetRowValues(index)
                                                        }}
                                                        placeholder={translate('COMMONFILTER.FILTER_NAME')}
                                                        size="small"
                                                    />
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell align="left" sx={{ width: '40%' }}>
                                            <Controller
                                                name={`customFiltersList.${index}.refReportingSites`}
                                                control={control}
                                                render={({ field }) => (
                                                    <Autocomplete
                                                        {...field}
                                                        loading
                                                        limitTags={2}
                                                        multiple
                                                        disabled={!editToggle[index]}
                                                        disableCloseOnSelect
                                                        size="small"
                                                        id="refReportingSites"
                                                        options={sites}
                                                        isOptionEqualToValue={(selectedValues, value) =>
                                                            selectedValues.name === value.name
                                                        }
                                                        getOptionLabel={(option) => option.description || ''}
                                                        value={Array.isArray(field.value) ? field.value : []}
                                                        noOptionsText={translate('')}
                                                        sx={{
                                                            maxWidth: '600px',
                                                            width: '100%',
                                                        }}
                                                        onChange={(event, value) =>
                                                            setValue(
                                                                `customFiltersList.${index}.refReportingSites`,
                                                                value
                                                            )
                                                        }
                                                        renderInput={(params) => (
                                                            <TextField
                                                                {...params}
                                                                variant="outlined"
                                                                sx={{
                                                                    '& .MuiOutlinedInput-root': {
                                                                        '& fieldset': {
                                                                            border: editToggle[index]
                                                                                ? '1px solid'
                                                                                : 'none',
                                                                            width: '100%',
                                                                        },
                                                                    },
                                                                    '& .Mui-disabled': {
                                                                        opacity: '1 !important',
                                                                    },
                                                                }}
                                                                placeholder={
                                                                    field.value?.length === 0
                                                                        ? translate(
                                                                              'COMMONFILTER.PLACEHOLDER.SELECT_A_SITE'
                                                                          )
                                                                        : ''
                                                                }
                                                            />
                                                        )}
                                                        renderOption={(props, option, { selected }) => {
                                                            const { key, ...optionProps } = props
                                                            return (
                                                                <li key={key} {...optionProps} data-value={option}>
                                                                    <Checkbox
                                                                        icon={
                                                                            <CheckBoxOutlineBlankIcon fontSize="small" />
                                                                        }
                                                                        checkedIcon={<CheckBoxIcon fontSize="small" />}
                                                                        checked={selected}
                                                                    />
                                                                    <span> {option.description} </span>
                                                                </li>
                                                            )
                                                        }}
                                                    />
                                                )}
                                            />
                                        </TableCell>
                                        <TableCell align="left" sx={{ width: '4%' }}>
                                            <IconButton
                                                color="primary"
                                                type={editToggle[index] ? 'button' : 'submit'}
                                                onClick={() => {
                                                    const previousIndex = editToggle.indexOf(true)
                                                    editToggle[index]
                                                        ? handleEditToggle(index, previousIndex, 'submit')
                                                        : handleEditToggle(index, previousIndex)
                                                }}
                                            >
                                                {editToggle[index] ? <CheckOutlinedIcon /> : <ModeOutlinedIcon />}
                                            </IconButton>
                                        </TableCell>
                                        <TableCell align="left" sx={{ width: '4%' }}>
                                            <IconButton
                                                color="error"
                                                onClick={
                                                    editToggle[index]
                                                        ? () => {
                                                              handleEditToggle(index)
                                                              resetRowValues(index)
                                                          }
                                                        : () => handleDelete(customFiltersList[index])
                                                }
                                            >
                                                {editToggle[index] ? (
                                                    <CloseOutlinedIcon />
                                                ) : (
                                                    <DeleteOutlineOutlinedIcon />
                                                )}
                                            </IconButton>
                                        </TableCell>
                                    </TableRow>
                                ))}
                                <TableRow>
                                    <IconButton
                                        color="primary"
                                        disabled={customFiltersList.some((item) => item.name === '') ? true : false}
                                        onClick={() => {
                                            setCustomFiltersList((prevData) => [
                                                ...prevData,
                                                {
                                                    name: '',
                                                    refUser: { externalId: '', space: '', email: '' },
                                                    refReportingSites: [],
                                                    externalId: '',
                                                    space: '',
                                                },
                                            ])
                                            const previousIndex = editToggle.indexOf(true)
                                            const index = customFiltersList.length
                                            handleEditToggle(index, previousIndex)
                                        }}
                                    >
                                        <AddIcon />
                                    </IconButton>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </Box>
                </Form>
            }
        />
    )
}

export default EditFiltersModal
