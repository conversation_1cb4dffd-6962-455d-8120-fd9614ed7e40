import { cognite } from '../configurations/cognite'
import { CogniteViewsResponseItem, Entity, FdmInstanceRequest } from '../models'
import { EdgeInstance } from '../models/edge-instance'
import { CogniteSdkEntity, removeTypename, safeStringify } from '../utils'

interface FdmClientConstructor {
    cogniteProject: string
    cogniteBaseUrl: string
    cogniteFdmModelSpace: string
    cogniteFdmInstancesSpace: string
    cogniteApiVersion?: string
    getToken: () => Promise<string>
    isRelationshipProperty?: (entityName: string, entity: Entity, prop: string) => boolean
}

interface UpsertOptionsBase<T extends Entity> {
    entityName: string
    abortSignal?: AbortSignal
    autoCreateDirectRelations?: boolean
    replace?: boolean
    nodeSpace?: string
    sourceSpace?: string
    version?: string
}

interface UpsertEdgeOptions<T extends Entity> extends UpsertOptionsBase<T> {
    edges: T[]
}

interface UpsertNodesOptions<T extends Entity> extends UpsertOptionsBase<T> {
    nodes: T[]
}

interface UpsertNodesAndEdgesOptions<T extends Entity> extends UpsertOptionsBase<T> {
    nodes: T[]
    edges: EdgeInstance[]
}

export interface DeleteNodeOptions {
    externalIds: string[]
    abortSignal?: AbortSignal
    instanceType: InstanceTypes
    space: string
}

interface ConditionalActionOptions {
    externalId: string[]
    query: any
}

interface ViewOptions {
    type: string
    space?: string
    externalId: string
    version: string
}

interface AggregateNodeOptions {
    externalId: string
    propetyCount: string
    filter?: any
    groupBy?: string[]
    space?: string
}

interface BodyAggregateNodeOptions {
    aggregates: any[]
    filter?: any
    groupBy?: string[]
    view: ViewOptions
}

interface BodyQueryNodeOptions {
    with: any
    select: any
    cursors?: any
}

interface QueryNodeOptions {
    withQuery: any
    select: any
    cursors?: any
}

export type InstanceTypes = 'node' | 'edge'

const ENTITY_NAME_ENTITY_HISTORY = 'EntityHistory'
const ENTITY_HISTORY_NESTED_KEYS = ['value', 'oldValue']
const MAX_RETRY_ATTEMPTS = 5
const RETRY_COOLDOWN_MS = 500

export class FdmClient {
    private baseUrl: string
    private fdmModelSpace: string
    private fdmInstancesSpace: string
    private views?: CogniteViewsResponseItem[]
    private getToken: () => Promise<string>
    private isRelationshipProperty: <T extends CogniteSdkEntity>(entityName: string, entity: T, prop: string) => boolean

    constructor({
        cogniteProject,
        cogniteBaseUrl,
        cogniteFdmModelSpace,
        cogniteFdmInstancesSpace,
        cogniteApiVersion = 'v1',
        getToken,
        isRelationshipProperty,
    }: FdmClientConstructor) {
        this.baseUrl = `${cogniteBaseUrl}/api/${cogniteApiVersion}/projects/${cogniteProject}/models`
        this.getToken = getToken
        this.fdmModelSpace = cogniteFdmModelSpace
        this.fdmInstancesSpace = cogniteFdmInstancesSpace
        this.isRelationshipProperty = isRelationshipProperty || this.defaultIsRelationshipProperty
    }

    public async upsertEdges<T extends CogniteSdkEntity>({
        entityName,
        edges,
        abortSignal,
        autoCreateDirectRelations,
    }: UpsertEdgeOptions<T>) {
        let version = '0'

        try {
            version = await this.getEntityVersion(entityName)
        } catch (e) {
            console.error(e)
        }

        const requestItems = this.mapToRequestItems('edge', entityName, version, edges)
        return await this.sendNodeOrEdgeRequest(requestItems, abortSignal, autoCreateDirectRelations)
    }

    public async upsertNodes<T extends CogniteSdkEntity>({
        entityName,
        nodes,
        abortSignal,
        autoCreateDirectRelations,
        replace,
        nodeSpace,
        sourceSpace,
        version,
    }: UpsertNodesOptions<T>) {
        const entityVersion = version ?? (await this.getEntityVersion(entityName))
        const requestItems = this.mapToRequestItems('node', entityName, entityVersion, nodes, nodeSpace, sourceSpace)
        return await this.sendNodeOrEdgeRequest(requestItems, abortSignal, autoCreateDirectRelations, replace)
    }

    public async queryNodesOrEdges({ withQuery, select, cursors }: QueryNodeOptions) {
        const url = `${this.baseUrl}/instances/query`
        const body: BodyQueryNodeOptions = {
            with: withQuery,
            select: select,
        }
        if (cursors) body.cursors = cursors
        const response = await fetch(url, {
            method: 'POST',
            headers: await this.buildHeaders(),
            body: safeStringify(body),
            signal: undefined,
        })
        if (!response.ok) {
            throw new Error('Failed to send node request')
        }
        return await response.json()
    }

    public async upsertNodesAndEdges<T extends CogniteSdkEntity>({
        entityName,
        nodes,
        edges,
        abortSignal,
        autoCreateDirectRelations,
    }: UpsertNodesAndEdgesOptions<T>) {
        let version = '0'

        try {
            version = await this.getEntityVersion(entityName)
        } catch (e) {
            console.error(e)
        }

        const requestItems = this.mapToRequestItems('node', entityName, version, nodes)
        requestItems.push(...this.mapToEdgeItems(edges))

        return await this.sendNodeOrEdgeRequest(requestItems, abortSignal, autoCreateDirectRelations)
    }

    public async getEntityVersion(entityName: string) {
        await this.loadDatamodelVersion()

        if (!this.views) {
            throw new Error('Failed to load model version')
        }

        const version = this.views?.find((v) => v.externalId === entityName)?.version
        if (!version) {
            throw new Error('Failed to load model version')
        }
        return version
    }

    public async deleteNodesOrEdges({ externalIds, abortSignal, instanceType, space }: DeleteNodeOptions) {
        const url = `${this.baseUrl}/instances/delete`
        const body = {
            items: externalIds.map((externalId) => ({
                externalId,
                space: space ? space : this.fdmInstancesSpace,
                instanceType: instanceType,
            })),
        }
        const response = await fetch(url, {
            method: 'POST',
            headers: await this.buildHeaders(),
            body: safeStringify(body),
            signal: abortSignal,
        })
        if (!response.ok) {
            throw new Error('Failed to send node request')
        }
        return await response.json()
    }

    public async getConditionalAction({ externalId, query }: ConditionalActionOptions) {
        const url = `${this.baseUrl}/instances/query`
        const queryHook = query

        const response = await fetch(url, {
            method: 'POST',
            headers: await this.buildHeaders(),
            body: safeStringify(queryHook),
        })
        if (!response.ok) {
            throw new Error('Failed to load conditional action')
        }
        return await response.json()
    }

    public async aggregateNodesOrEdges({ externalId, propetyCount, filter, groupBy, space }: AggregateNodeOptions) {
        const url = `${this.baseUrl}/instances/aggregate`
        let version = '0'
        try {
            version = await this.getEntityVersion(externalId)
        } catch (e) {
            console.error(e)
        }
        const body: BodyAggregateNodeOptions = {
            aggregates: [
                {
                    count: {
                        property: propetyCount,
                    },
                },
            ],
            view: {
                type: 'view',
                space: space,
                externalId: externalId,
                version: version,
            },
        }
        if (filter) body.filter = filter
        if (groupBy) body.groupBy = groupBy
        const response = await fetch(url, {
            method: 'POST',
            headers: await this.buildHeaders(),
            body: safeStringify(body),
            signal: undefined,
        })
        if (!response.ok) {
            throw new Error('Failed to send node request')
        }
        return await response.json()
    }

    private async loadDatamodelVersion() {
        if (this.views) {
            return
        }
        const url = `${this.baseUrl}/datamodels?space=${this.fdmModelSpace}`
        const response = await fetch(url, {
            method: 'GET',
            headers: await this.buildHeaders(),
        })
        if (!response.ok) {
            throw new Error('Failed to load model version')
        }
        const jsonResponse = await response.json()
        const filteredItem = jsonResponse.items.filter((v: any) => v.externalId === 'GKPISOL')[0]
        if (filteredItem) {
            const views = filteredItem.views
            this.views = views
        } else {
            console.log('No item found with externalId === "GKPISOL"')
        }
    }

    private async buildHeaders(): Promise<HeadersInit> {
        const token = await this.getToken()
        return {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            'X-Cdp-App': cognite.cogniteXCdpApp,
        }
    }

    private async buildNodeOrEdgeRequestCall(
        nodes: FdmInstanceRequest[],
        abortSignal?: AbortSignal,
        autoCreateDirectRelations?: boolean,
        replace?: boolean
    ) {
        const url = `${this.baseUrl}/instances`
        const body = {
            items: nodes,
            skipOnVersionConflict: false,
            autoCreateDirectRelations: autoCreateDirectRelations ?? true,
            replace: replace ?? true,
        }

        return async () => {
            return await fetch(url, {
                method: 'POST',
                headers: await this.buildHeaders(),
                body: JSON.stringify(body),
                signal: abortSignal,
            })
        }
    }

    private async sendNodeOrEdgeRequest(
        nodes: FdmInstanceRequest[],
        abortSignal?: AbortSignal,
        autoCreateDirectRelations?: boolean,
        replace?: boolean
    ) {
        const postFunction = await this.buildNodeOrEdgeRequestCall(
            nodes,
            abortSignal,
            autoCreateDirectRelations,
            replace
        )
        let retryCount = 0
        while (retryCount < MAX_RETRY_ATTEMPTS) {
            const response = await postFunction()

            if (response.ok) {
                return await response.json()
            }

            retryCount++
            if (retryCount >= MAX_RETRY_ATTEMPTS) {
                console.error('Failed to send node request.', response.statusText + ' ' + response.status)
                throw new Error('Request failed: ' + response.status, {
                    cause: response.status,
                })
            }

            await new Promise((resolve) => setTimeout(resolve, Math.pow(2, retryCount) * RETRY_COOLDOWN_MS))
        }
    }

    private mapToRequestItems<T extends CogniteSdkEntity>(
        instanceType: InstanceTypes,
        entity: string,
        version: string,
        items: T[],
        nodeSpace?: string,
        sourceSpace?: string
    ): FdmInstanceRequest[] {
        const isEdge = instanceType === 'edge'
        const result: FdmInstanceRequest[] = items.map((item) => {
            const { externalId, type, startNode, endNode, ...properties } = removeTypename(item)

            let instanceSpace = this.fdmInstancesSpace

            if (item.space) {
                instanceSpace = item.space
            }

            if (nodeSpace) {
                instanceSpace = nodeSpace
            }

            return {
                instanceType: instanceType,
                space: instanceSpace,
                externalId: externalId,
                type: isEdge && type ? { externalId: type, space: this.fdmModelSpace } : undefined,
                startNode: isEdge && startNode ? { externalId: startNode, space: this.fdmInstancesSpace } : undefined,
                endNode: isEdge && endNode ? { externalId: endNode, space: this.fdmInstancesSpace } : undefined,
                sources: [
                    {
                        source: {
                            type: 'view',
                            space: sourceSpace ? sourceSpace : this.fdmModelSpace,
                            externalId: entity,
                            version: version,
                        },
                        properties: this.toRequestProperties(properties, this.fdmInstancesSpace, entity, !isEdge),
                    },
                ],
            }
        })
        return result
    }

    private mapToEdgeItems(items: EdgeInstance[]): FdmInstanceRequest[] {
        const result: FdmInstanceRequest[] = items.map((item) => {
            const { externalId, type, startNode, endNode, ...properties } = removeTypename(item)

            return {
                instanceType: 'edge',
                space: item.space,
                externalId: item.externalId,
                type: item.type,
                startNode: item.startNode,
                endNode: item.endNode,
            }
        })
        return result
    }

    private toRequestProperties<T extends CogniteSdkEntity>(
        codeList: T,
        space: string,
        entityName: string,
        ignoreSpace: boolean = false
    ) {
        const result: any = {}
        for (const key in codeList) {
            const isRelationshipProperty = this.isRelationshipProperty(entityName, codeList, key)
            if (isRelationshipProperty) {
                const isTimeSeries = key.toLowerCase().endsWith('timeseries')
                const externalId = codeList[key].externalId
                result[key] = isTimeSeries
                    ? externalId
                    : { externalId, space: codeList[key].space ? codeList[key].space : space }
                continue
            } else if (!ignoreSpace || key !== 'space') {
                result[key] = codeList[key]
            }
        }
        return result
    }

    private defaultIsRelationshipProperty<T extends CogniteSdkEntity>(entityName: string, entity: T, prop: keyof T) {
        if (entityName === ENTITY_NAME_ENTITY_HISTORY && ENTITY_HISTORY_NESTED_KEYS.includes(prop.toString())) {
            return false
        }
        return Boolean(entity[prop]?.externalId)
    }
}
