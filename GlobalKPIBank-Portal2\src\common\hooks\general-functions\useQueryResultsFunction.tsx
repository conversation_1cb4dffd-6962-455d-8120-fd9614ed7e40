import { useCallback } from 'react'
import { ItensFunctionPromise, useFdmQuery } from '../cognite/useFdmQuery'

export function useQueryResultsFunction<T>(select: T, cursors: T) {
    const [queryFunctionFdm] = useFdmQuery()
    return {
        getAllResults: useCallback(
            async (withQuery: T): Promise<ItensFunctionPromise> => {
                const res = await queryFunctionFdm({ withQuery: withQuery, select: select, cursors: cursors })
                return res
            },
            [select]
        ),
    }
}
