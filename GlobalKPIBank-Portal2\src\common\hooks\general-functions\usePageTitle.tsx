import { TranslationContextState, TranslationContext } from '@celanese/celanese-sdk'
import { useContext, useEffect, useState } from 'react'

export const usePageTitle = (pageTitle: string) => {
    const [isTranslated, setIsTranslated] = useState<boolean>(false)
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    useEffect(() => {
        const isTitleTranslated = !pageTitle.includes('MENU.')
        setIsTranslated(isTitleTranslated)
    }, [locale])

    useEffect(() => {
        document.title = `${pageTitle} | Global KPI`
    }, [pageTitle, isTranslated])
}
