import { gql } from 'graphql-tag'
import { useState, useEffect } from 'react'
import { useGraphqlQuery } from '../useGraphqlQuery'

const queryBuilder = () => {
    return `query getKpiDataView {
        listKpiParameter (
            first: 100
        ) {
            items {
                externalId
                space
                code
                name
            }
        }
    }`
}

export const useKpiParameter = () => {
    const query = queryBuilder()
    const {
        data: fdmData,
        refetch,
        loading,
    } = useGraphqlQuery<any>(gql(query), 'listKpiParameter', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'kpidomView',
        },
    })

    const [resultData, setResultData] = useState<{ data: any }>({
        data: [],
    })

    useEffect(() => {
        if (fdmData === undefined) {
            setResultData((prevState) => ({ ...prevState, loading: true }))
        } else {
            setResultData({ data: fdmData })
        }
    }, [fdmData])

    return {
        loadingDataModelView: loading,
        refetchDataModelView: refetch,
        resultKpiParameter: resultData.data,
    }
}
