import { gql } from '@apollo/client'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useState, useEffect } from 'react'
import { Unit } from '../models/unit'

const queryBuilder = (businessSegment: string[]) => {
    let segment: string = ''
    if (businessSegment.length > 0) {
        segment = `and:[{ businessSegment:{externalId:{ in:${JSON.stringify(businessSegment)} }}}]`
    }
    return `query getReportingUnits {
        listReportingUnit(filter:{${segment}}, first: 1000){
            items{
                externalId
            }
        }
    }`
}

export const useReportingUnitList = (businessSegment: string[]) => {
    const query = queryBuilder(businessSegment)

    const { data: fdmData, refetch } = useGraphqlQuery<Unit>(gql(query), 'listReportingUnit', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'assetHierarchy',
        },
    })

    const [resultData, setResultData] = useState<{ data: string[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData === undefined) {
            setResultData((prevState) => ({ ...prevState, loading: true }))
        } else {
            const sites = fdmData.map((ele) => {
                const row = ele.externalId
                return 'STS' + row.slice(3, -3)
            })
            setResultData({ data: sites, loading: false })
        }
    }, [fdmData])

    return {
        loadingReportingUnitsList: resultData.loading,
        refetchReportingUnitsList: refetch,
        dataReportingUnitsList: resultData.data,
    }
}
