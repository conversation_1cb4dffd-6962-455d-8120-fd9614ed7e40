import { useEffect, useState } from 'react'
import { getLocalUserSite } from '../../utils'
import { EntityType, GetSpace } from '../../utils/space-util'
import { useAgregateResultsFunction } from '../general-functions/useAgregateResultsFunction'
import dayjs from 'dayjs'

export interface KpisQueryRequest {
    currentStatusValues?: string[]
    currentTaskStatusValues?: string
    assignedToValue?: string
    relatedToValue?: string
    units?: string[]
    startDate?: string
    endDate?: string
    dataModelTable?: string
    overdue?: boolean
    title?: string
    discipline?: string[]
    location?: string[]
    configs?: string[]
    templateConfig: string[]
    shift: string[]
}

const buildActionItemCategoryQuery = (request: KpisQueryRequest): any => {
    const currentStatusEqualsArray = request.currentStatusValues?.map((status) => ({
        in: {
            property: ['status'],
            values: status,
        },
    }))
    const query = {
        and: [
            {
                or: [
                    {
                        equals: {
                            property: ['node', 'space'],
                            value: GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode),
                        },
                    },
                    {
                        equals: {
                            property: ['node', 'space'],
                            value: GetSpace(EntityType.APMATEMP, getLocalUserSite()?.siteCode),
                        },
                    },
                ],
            },
            {
                not: {
                    equals: {
                        property: ['isArchived'],
                        value: true,
                    },
                },
            },
            request.title && {
                and: [
                    {
                        prefix: {
                            property: ['title'],
                            value: request.title,
                        },
                    },
                ],
            },
            request.currentStatusValues && {
                in: {
                    property: ['status'],
                    values: request.currentStatusValues,
                },
            },
            request.startDate && {
                or: [
                    {
                        range: {
                            property: ['startTime'],
                            gte: request.startDate,
                            lte: request.endDate,
                        },
                    },
                    {
                        range: {
                            property: ['endTime'],
                            gte: request.startDate,
                            lte: request.endDate,
                        },
                    },
                ],
            },
            request.overdue && {
                or: [
                    {
                        range: {
                            property: ['startTime'],
                            lt: dayjs().add(-7, 'days').valueOf(),
                        },
                    },
                ],
            },
            request.templateConfig.length > 0 && {
                in: {
                    property: ['sourceId'],
                    values: request.templateConfig.map((item) => item.replace('CTC-', '')),
                },
            },
        ].filter(Boolean),
    }
    return query
}

export const useKpis = (request: KpisQueryRequest) => {
    const unitsByFilter = request.units && request.units?.length > 0 ? true : false
    const locationByFilter = request.location && request.location?.length > 0 ? true : false
    const disciplineByFilter = request.discipline && request.discipline?.length > 0 ? true : false
    const shiftByFilter = request.shift.length > 0 ? true : false

    const filter = buildActionItemCategoryQuery(request)

    const [resultData, setResultData] = useState<{ data: any; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllResults: getAllData } = useAgregateResultsFunction<any>(request.dataModelTable ?? 'Checklist')

    useEffect(() => {
        getAllData(filter).then((res) => {
            if (
                res?.length === 0 ||
                (request.templateConfig.length === 0 &&
                    (unitsByFilter || locationByFilter || disciplineByFilter || shiftByFilter))
            ) {
                setResultData({ data: [], loading: false })
            } else {
                setResultData({ data: res, loading: true })
            }
        })
    }, [request])

    return {
        loading: resultData.loading,
        countResult: resultData.data,
    }
}
