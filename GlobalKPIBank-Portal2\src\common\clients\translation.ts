import { enviroment } from '../configurations/enviroment'
import { IPublicClientApplication, PublicClientApplication } from '@azure/msal-browser'
import { availableLanguages, getIdTokenFromMsal, safeStringify } from '../utils'

const translationEndPointName = enviroment.appsTranslationUrl ?? ''
const dynamicTranslationEndPointName = translationEndPointName.replace('/static', '/dynamic')

export async function getTranslation(
    msal: PublicClientApplication,
    app: string = 'ChecklistManagement',
    lang: string = 'LAN-EN'
) {
    await msal.initialize()
    const token = await getIdTokenFromMsal(msal)
    const translation = await fetch(translationEndPointName, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${token}` },
        body: safeStringify({
            application: app,
            language: lang,
        }),
    })

    if (translation.status === 200) {
        const result = await translation.json()

        return Object.keys(result).length !== 0 && result.constructor === Object ? result : undefined
    } else {
        return undefined
    }
}

export async function getAvailableLanguages(msal: PublicClientApplication, app: string = 'ChecklistManagement') {
    try {
        return availableLanguages
    } catch {}
}

export async function getDynamicTranslation(
    msal: IPublicClientApplication,
    app: string = 'APP-NTF',
    lang: string = 'LAN-EN',
    text: string[]
) {
    await msal.initialize()
    const token = await getIdTokenFromMsal(msal)
    const translation = await fetch(dynamicTranslationEndPointName, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${token}` },
        body: safeStringify({
            application: app,
            language: lang,
            texts: text,
        }),
    })

    if (translation.status === 200) {
        const result = await translation.json()
        return result && result.length > 0 ? result : ''
    } else {
        return ''
    }
}
