import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'

const queryBuilder = () => {
    return `query getKpiDataView {
        listKPIManualInput (
            first: 1
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                refSite {
                    externalId
                    name
                }
            }
        }
    }`
}

export const useDefaultHook = (request: DataModelView) => {
    const query = queryBuilder()
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<any>(gql(query), 'listKPIManualInput', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'gkpisol',
        },
    })

    const [resultData, setResultData] = useState<{ data: any }>({
        data: [],
    })
    const [loadMore, setLoadMore] = useState<boolean>(false)
    const loadMoreExport = () => {
        if (!fdmPageInfo.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                setResultData({ data: fdmData })
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: refetch,
        loadMoreExport: loadMoreExport,
    }
}
