import { Entity } from '@/common/models'
import { BusinessSegment } from '@/common/models/business-segment'
import { homeFilterSchema, HomeFilterSchema, HomeFilterValue } from '@/common/models/home-models'
import { KpiGroup } from '@/common/models/kpiGroup'
import { KpiList } from '@/common/models/kpiList'
import { Site } from '@/common/models/site'
import MultiAutoCompleteWithSelectAll from '@/components/MultiAutoCompleteWithSelectAll'
import { AlertData, Toastr } from '@/components/Toastr'
import { translate } from '@celanese/celanese-sdk'
import { ClnDatePicker } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import { Grid } from '@mui/material'
import dayjs from 'dayjs'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { getDefaultFilterState, getKpisByGroup, getSelectedKpis, getSelectedSites, getSitesBySegment } from './helpers'

const minDateCalendar = dayjs('2023-01-01').startOf('year')
const maxDateCalendar = dayjs().endOf('year')
const limitTags = 1

interface HomeViewFiltersProps {
    kpiGroups?: KpiGroup[]
    kpis?: KpiList[]
    businessSegments: BusinessSegment[]
    sites: Site[]
    currentTab?: number
    defaultValues?: HomeFilterValue
    isPeriodYear?: boolean
    onSubmit?: (filters: HomeFilterValue) => void
    generalFilterTag?: boolean
    disableSegmentSelector?: boolean
    hidePeriodSelector?: boolean
}

const HomeViewFilters: React.FC<HomeViewFiltersProps> = ({
    kpiGroups,
    kpis,
    businessSegments,
    sites,
    currentTab = -1,
    isPeriodYear = false,
    defaultValues,
    onSubmit,
    generalFilterTag = false,
    disableSegmentSelector,

    hidePeriodSelector = false,
}) => {
    const prevTab = useRef(-1)
    const [open, setOpen] = useState<boolean>(false)
    const [alertData, setAlertData] = useState<AlertData>({
        isOpen: false,
        message: '',
        severity: 'info',
        autoHideDuration: 6000,
    })
    const defaultState = getDefaultFilterState(
        currentTab,
        sites,
        businessSegments,
        kpis,
        kpiGroups,
        defaultValues,
        disableSegmentSelector
    )
    const {
        setValue,
        setError,
        clearErrors,
        getValues,
        handleSubmit,
        watch,
        control,
        formState: { errors },
    } = useForm<HomeFilterSchema>({
        defaultValues: defaultState,
        resolver: zodResolver(homeFilterSchema),
    })
    const segmentsSelected = watch('businessSegment')
    const siteOptions = useMemo(() => getSitesBySegment(sites, segmentsSelected), [sites, segmentsSelected])
    const kpiOptions = useMemo(() => getKpisByGroup(currentTab, kpis, kpiGroups), [currentTab, kpis, kpiGroups])

    useEffect(
        function updateKpiListOnTabChange() {
            if (prevTab.current === currentTab) {
                return
            }
            if (kpis) {
                const kpisFinal = getSelectedKpis(currentTab, kpis, kpiGroups, defaultValues?.kpiList)
                setValue('kpiList', kpisFinal)
            }
            prevTab.current = currentTab
            handleSubmit(handleFiltersSubmit)()
        },
        [currentTab, kpis, kpiGroups, defaultValues]
    )

    const handleFiltersSubmit = useCallback(
        (data: HomeFilterSchema) => {
            clearErrors()
            let isValid = true

            if (!disableSegmentSelector && !data.businessSegment?.length) {
                isValid = false
                setError('businessSegment', { message: 'Required' })
            }
            if (!data.siteList?.length) {
                isValid = false
                setError('siteList', { message: 'Required' })
            }
            if (kpis?.length && !data.kpiList?.length) {
                isValid = false
                setError('kpiList', { message: 'Required' })
            }
            if (isValid) {
                onSubmit?.(data as HomeFilterValue)
            }
        },
        [setValue, onSubmit, kpis]
    )

    const handleOptionRemoved = useCallback(
        <T extends Entity>(
            key: keyof Pick<HomeFilterSchema, 'siteList' | 'businessSegment' | 'kpiList'>,
            option: T,
            prev: T[]
        ) => {
            const result = getValues(key).filter((item) => item.externalId !== option.externalId)
            setValue(key, result)
            if (result.length && key === 'businessSegment') {
                const currentSites = getValues('siteList')
                const selectedSites = getSelectedSites(result, sites, currentSites)
                setValue('siteList', selectedSites)
            }
            if (!result.length) {
                setValue(key, prev)
            }
            handleSubmit(handleFiltersSubmit)()
        },
        [getValues, setValue, handleSubmit, handleFiltersSubmit]
    )

    const handleSiteRemoved = useCallback(
        (option: Site) => handleOptionRemoved('siteList', option, siteOptions),
        [handleOptionRemoved, siteOptions]
    )

    const handleKpiRemoved = useCallback(
        (option: KpiList) => handleOptionRemoved('kpiList', option, kpiOptions),
        [handleOptionRemoved, kpiOptions]
    )

    const isOptionEqualToValue = useCallback(
        (option: Entity, value: Entity) => option.externalId === value.externalId,
        []
    )

    const handleSegmentChanged = useCallback(
        (_: React.SyntheticEvent, value: BusinessSegment[]) => {
            setValue('businessSegment', value)
            const currentSites = getValues('siteList')
            const selectedSites = getSelectedSites(value, sites, currentSites)
            setValue('siteList', selectedSites)

            if (selectedSites?.length != currentSites?.length) {
                setAlertData({
                    isOpen: true,
                    message: 'Sites without the selected Business Segment have been removed.',
                    severity: 'info',
                    autoHideDuration: 6000,
                })
            }
        },
        [setValue, setAlertData]
    )

    const handleSegmentRemoved = useCallback(
        (option: BusinessSegment) => handleOptionRemoved('businessSegment', option, businessSegments),
        [handleOptionRemoved, businessSegments]
    )

    return (
        <>
            <Toastr
                alertData={alertData}
                onClose={() => {
                    setAlertData({ ...alertData, isOpen: false })
                }}
            />
            <Grid container spacing={2} justifyContent="flex-end">
                <Grid item sx={{ width: '245px' }}>
                    <Controller
                        control={control}
                        name="businessSegment"
                        render={({ field }) => (
                            <MultiAutoCompleteWithSelectAll
                                {...field}
                                id="business_segment"
                                limitTags={limitTags}
                                label={translate('COMMONFILTER.BUSINESS_SEGMENT')}
                                labelField="description"
                                value={field.value}
                                defaultValue={field.value}
                                options={businessSegments}
                                isOptionEqualToValue={isOptionEqualToValue}
                                onClose={handleSubmit(handleFiltersSubmit)}
                                onChange={handleSegmentChanged}
                                disabled={disableSegmentSelector}
                                onSelectAll={(value) => {
                                    const newValue =
                                        value.length === businessSegments.length ? [] : [...businessSegments]
                                    setValue('businessSegment', newValue)
                                }}
                                onItemRemoved={handleSegmentRemoved}
                                slotProps={{
                                    textField: {
                                        error: Boolean(errors.businessSegment),
                                        helperText: errors.businessSegment?.message,
                                    },
                                }}
                            />
                        )}
                    />
                </Grid>
                <Grid item sx={{ width: '245px' }}>
                    <Controller
                        control={control}
                        name="siteList"
                        render={({ field }) => (
                            <MultiAutoCompleteWithSelectAll
                                {...field}
                                id="siteList"
                                limitTags={limitTags}
                                label={translate('COMMONFILTER.SITES')}
                                labelField="description"
                                defaultValue={field.value}
                                value={field.value}
                                options={siteOptions}
                                isOptionEqualToValue={isOptionEqualToValue}
                                onClose={handleSubmit(handleFiltersSubmit)}
                                onChange={(_, newValue) => {
                                    setValue('siteList', newValue)
                                }}
                                onSelectAll={(value) => {
                                    const newValue = value.length === siteOptions.length ? [] : [...siteOptions]
                                    setValue('siteList', newValue)
                                }}
                                onItemRemoved={handleSiteRemoved}
                                slotProps={{
                                    textField: {
                                        error: Boolean(errors.siteList),
                                        helperText: errors.siteList?.message,
                                    },
                                }}
                            />
                        )}
                    />
                </Grid>
                {Boolean(kpis?.length) && (
                    <Grid item sx={{ width: '245px' }}>
                        <Controller
                            control={control}
                            name="kpiList"
                            render={({ field }) => (
                                <MultiAutoCompleteWithSelectAll
                                    {...field}
                                    disabled={[0, 5].includes(currentTab)}
                                    id="kpiList"
                                    limitTags={limitTags}
                                    label={'KPI'}
                                    labelField="name"
                                    value={field.value}
                                    defaultValue={field.value}
                                    options={kpiOptions}
                                    isOptionEqualToValue={isOptionEqualToValue}
                                    onClose={handleSubmit(handleFiltersSubmit)}
                                    onChange={(_, newValue) => {
                                        setValue('kpiList', newValue)
                                    }}
                                    onSelectAll={(value) => {
                                        const newValue = value.length === kpiOptions.length ? [] : [...kpiOptions]
                                        setValue('kpiList', newValue)
                                    }}
                                    onItemRemoved={handleKpiRemoved}
                                    slotProps={{
                                        textField: {
                                            error: Boolean(errors.kpiList),
                                            helperText: errors.kpiList?.message,
                                        },
                                    }}
                                />
                            )}
                        />
                    </Grid>
                )}
                {!hidePeriodSelector && (
                    <Grid item sx={{ width: '245px' }}>
                        <Controller
                            control={control}
                            name="period"
                            render={({ field }) => (
                                <ClnDatePicker
                                    {...field}
                                    sx={{
                                        '.MuiOutlinedInput-root': {
                                            height: '40px',
                                        },
                                        backgroundColor: 'background.paper',
                                        width: '100%',
                                    }}
                                    open={open}
                                    disabled={generalFilterTag && currentTab === 0}
                                    onOpen={() => setOpen(true)}
                                    onClose={() => setOpen(false)}
                                    views={!isPeriodYear ? ['month', 'year'] : ['year']}
                                    label={translate('COMMONFILTER.PERIOD')}
                                    value={dayjs(field.value)}
                                    minDate={minDateCalendar}
                                    maxDate={maxDateCalendar}
                                    onAccept={(date) => {
                                        setValue('period', date.format('YYYY-MM-DD'))
                                        handleSubmit(handleFiltersSubmit)()
                                    }}
                                    slotProps={{
                                        textField: {
                                            onClick: () => setOpen(true),
                                        },
                                        field: {
                                            readOnly: true,
                                        },
                                    }}
                                    size="small"
                                />
                            )}
                        />
                    </Grid>
                )}
            </Grid>
        </>
    )
}
export default HomeViewFilters
