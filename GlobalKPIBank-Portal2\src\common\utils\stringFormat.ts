export const formatVariableName = (name: string): string => {
    const separatedName = name.replace(/([A-Z])/g, ' $1')
    const formatedName = separatedName.replace(/^./, (str) => str.toUpperCase()).trim()
    return formatedName
}

export const addsBreakLineOnText = (text: string, positionBefore: string): string => {
    const regex = new RegExp(`(${positionBefore})`, 'g')
    return text.replace(regex, `\n$1`)
}
