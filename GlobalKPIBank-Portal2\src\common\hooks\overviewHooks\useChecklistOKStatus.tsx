import { Checklist } from '@/common/models/checklist'
import { getLocalUserSite } from '@/common/utils'
import { GetSpace, EntityType } from '@/common/utils/space-util'
import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { useGraphqlQuery, PageInfo } from '..'
import { ArrayEntity } from '@/common/models'
import { ChecklistItem } from '@/common/models/checklist-item'

export interface ChecklistHookQueryRequest {
    title?: string
    status?: string
    templateIds?: string[]
    start?: number
    end?: number
    nextPage?: string
}

const buildChecklistQuery = (request: ChecklistHookQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.title && request.title != '') {
        filter.push(`{ title: { eq: "${request.title}" }}`)
    }

    if (request.templateIds?.length) {
        filter.push(`{ sourceId: { in: [${request.templateIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    filter.push(`{ status: { eq: "Done" }}`)

    if (request.end && request.start) {
        filter.push(`{
                        or: [
                          {
                            and: [
                              { startTime: { gte: ${request.start} }},
                              { startTime: { lte: ${request.end} }}
                            ]
                          },
                          {
                            and: [
                              { endTime: { gte: ${request.start} }},
                              { endTime: { lte: ${request.end} }}
                            ]
                          }
                        ]
                      }`)
    }

    filter.push(
        `{
          space: {
            in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
          }
      }`
    )

    filter.push(`{ not: { isArchived: { eq: true } } }`)

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
    query GetChecklistData {
        listChecklist( filter: ${queryFilter}, first: 1000, after: ${
        request.nextPage ? `"${request.nextPage}"` : 'null'
    } ) {
          items {
            externalId
            status
            sourceId
            checklistItems(first: 1, filter: { ${
                request.status == 'Ok'
                    ? `not: {status: { in: ["Blocked", "Not Applicable", "Not ok", ""] } }`
                    : `status: { in: ["Blocked", "Not Applicable", "Not ok", ""] }`
            } }) {              
              items {
                externalId
              }
            }
          }
          pageInfo {
            hasPreviousPage
            hasNextPage
            startCursor
            endCursor
          }
        }
      }
    `

    return query
}

export const useChecklistOKStatus = (request: ChecklistHookQueryRequest) => {
    const query = buildChecklistQuery(request)

    const { data: fdmData, pageInfo } = useGraphqlQuery<Checklist>(gql(query), 'listChecklist', {})

    const [resultData, setResultData] = useState<{
        data: Checklist[]
        loading: boolean
        pagging: boolean
        pageInfo: PageInfo
    }>({
        data: [],
        loading: true,
        pagging: false,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false, pageInfo, pagging: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayChecklistItemEntity = d.checklistItems as any as ArrayEntity<ChecklistItem>
                const checklistItems = arrayChecklistItemEntity.items

                return {
                    ...d,
                    checklistItems: checklistItems,
                }
            })

            setResultData({ data: fdmDataParsed, loading: false, pageInfo, pagging: Boolean(request.nextPage) })
        }
    }, [fdmData, pageInfo])

    return {
        loadingChecklistHook: resultData.loading,
        checklistsHook: resultData.data,
        pageInfoChecklistHook: resultData.pageInfo,
        pagging: resultData.pagging,
    }
}
