# See http://help.github.com/ignore-files/ for more about ignoring files.

# dependencies
node_modules
.next

# profiling files
chrome-profiler-events*.json
speed-measure-plugin*.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
debug.log

# System Files
.DS_Store
Thumbs.db

# Next cache
package-lock.json

.env*.local
tsconfig.tsbuildinfo