import { Entity, ExternalEntity, MutationState } from '../models'
import { useCallback } from 'react'
import { useFdmMutation } from '.'
import { Checklist } from '../models/checklist'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export type ChecklistAssignToMutationArgs<T extends Entity> = {
    newEntities: T[]
    fieldsToIgnore?: (keyof T)[]
}

export type UseChecklistAssignToMutationResult<T extends Entity> = [
    (args: ChecklistAssignToMutationArgs<T>) => Promise<any>,
    MutationState
]

export interface ChecklistWritableMutation extends ExternalEntity {
    assignedTo: string[]
}

export const useChecklistAssignToMutation = (): UseChecklistAssignToMutationResult<Checklist> => {
    const [checklistWritableMutation, checklistWritableMutationStatus] = useFdmMutation<ChecklistWritableMutation>(
        'ChecklistWritable',
        false,
        false
    )

    const newMutationFunction = useCallback(
        async ({ newEntities }: ChecklistAssignToMutationArgs<Checklist>) => {
            const entitySpace = newEntities?.length
                ? newEntities[0].space
                : GetSpace(EntityType.APMATEMP, getLocalUserSite()?.siteCode)

            const newEntitiesWithoutEdges = newEntities.map(
                (e) =>
                    ({
                        externalId: e.externalId,
                        space: e.space,
                        assignedTo: e.assignedTo,
                    } as ChecklistWritableMutation)
            )

            const checklistWritableMutationResult = await checklistWritableMutation(
                newEntitiesWithoutEdges,
                [],
                undefined,
                entitySpace
            )

            return {
                ok: checklistWritableMutationResult.ok,
                data: checklistWritableMutationResult.data,
                error: checklistWritableMutationResult.error,
            }
        },
        [checklistWritableMutation]
    )

    const newResetFunction = useCallback(() => {
        checklistWritableMutationStatus.reset()
    }, [checklistWritableMutationStatus])

    return [newMutationFunction, { ...checklistWritableMutationStatus, reset: newResetFunction }]
}
