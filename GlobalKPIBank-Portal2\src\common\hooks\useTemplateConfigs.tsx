import { ApolloQueryResult, gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { ArrayEntity, TemplateConfig } from '../models'
import { Role } from '../models/role'
import { NotificationConfig } from '../models/notification-config'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'
import { ShiftConfiguration } from '../models/shiftConfiguration'

export interface TemplateConfigQueryRequest {
    templateIds?: string[]
    unitIds?: string[]
    shiftIds?: string[]
    locationIds?: string[]
    disciplines?: string[]
    limit?: number
    skip?: boolean
    end?: number
    first?: number
    overdue?: boolean
}

export interface TemplateConfigQueryResponse {
    loading: boolean
    templateConfigs: TemplateConfig[]
    refetchTemplateConfig: () => Promise<ApolloQueryResult<any>>
    pageInfo: PageInfo
}

const buildTemplateConfigsQuery = (request: TemplateConfigQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.templateIds?.length) {
        filter.push(`{ template: { externalId: { in: [${request.templateIds.map((e) => `"${e}"`).join(',')}] }}}`)
    }

    if (request.unitIds?.length) {
        filter.push(`{ reportingUnit: { externalId: { in: [${request.unitIds.map((e) => `"${e}"`).join(',')}] }}}`)
    }

    if (request.locationIds?.length) {
        filter.push(
            `{ reportingLocation: { externalId: { in: [${request.locationIds.map((e) => `"${e}"`).join(',')}] }}}`
        )
    }

    if (request.disciplines?.length) {
        filter.push(`{ disciplines: { containsAny: [${request.disciplines.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.end) {
        filter.push(`{ createdTime: { lte: ${request.end} }}`)
    }

    if (request.overdue) {
        filter.push(`{ daysUntilDue: { isNull: false } }`)
        filter.push(`{ not: { daysUntilDue: { eq: "" } } }`)
    }

    filter.push(`{ template: { externalId: { isNull: false } } }`)

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.Instance
        )}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    if (!request.first) {
        request.first = 1000
    }

    const query = `
        query GetChecklistTemplateConfig {
            listChecklistTemplateConfig(
                filter: ${queryFilter},
                first: ${request.first})
            {
                items {
                    externalId
                    space
                    checklistNotificationConfigs {
                        items {
                            externalId
                            space
                            name
                            description
                            notificationType {
                                externalId
                                space
                                name
                                description
                            }
                        }
                    }
                    reportingUnit {
                      externalId
                      name
                      space
                      description
                    }
                  	reportingLocation {
                      externalId
                      name
                      space
                      description
                    }
                    assignedRoles {
                        items {
                            externalId
                            name
                            space
                        }
                    }
                    rolesToBeNotified {
                        items {
                            externalId
                            name
                            space
                        }
                    }
                  	template {
                      externalId
                      title
                      space
                    }
                    disciplines
                    daysUntilDue
                    shiftConfigurations {
                        items {
                            externalId
                            space
                            shiftName
                            refSite {
                                externalId
                                space
                                name
                                description
                            }
                            refUnit {
                                externalId
                                space
                                name
                                description
                            }
                        }
                    }
                }
                pageInfo {
                    hasPreviousPage
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    return query
}

export const useTemplateConfigs = (request: TemplateConfigQueryRequest): TemplateConfigQueryResponse => {
    const query = buildTemplateConfigsQuery(request)
    const {
        data: fdmData,
        refetch,
        pageInfo,
    } = useGraphqlQuery<TemplateConfig>(gql(query), 'listChecklistTemplateConfig', { fetchPolicy: 'no-cache' })

    const [resultData, setResultData] = useState<{ data: TemplateConfig[]; loading: boolean; pageInfo: PageInfo }>({
        data: [],
        loading: true,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false, pageInfo })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayChecklistNotificationConfigsEntity =
                    d.checklistNotificationConfigs as any as ArrayEntity<NotificationConfig>
                const arrayAssignedRolesEntity = d.assignedRoles as any as ArrayEntity<Role>
                const arrayRolesToBeNotifiedEntity = d.rolesToBeNotified as any as ArrayEntity<Role>
                const arrayShiftConfigurationsEntity = d.shiftConfigurations as any as ArrayEntity<ShiftConfiguration>

                return {
                    ...d,
                    checklistNotificationConfigs: arrayChecklistNotificationConfigsEntity.items,
                    assignedRoles: arrayAssignedRolesEntity.items,
                    rolesToBeNotified: arrayRolesToBeNotifiedEntity.items,
                    shiftConfigurations: arrayShiftConfigurationsEntity.items,
                }
            })

            setResultData({ data: fdmDataParsed, loading: false, pageInfo })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        templateConfigs: resultData.data,
        refetchTemplateConfig: refetch,
        pageInfo: resultData.pageInfo,
    }
}
