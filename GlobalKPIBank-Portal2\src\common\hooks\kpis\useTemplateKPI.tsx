import { useEffect, useState } from 'react'
import { useQueryResultsFunction } from '../general-functions/useQueryResultsFunction'
import { EntityType, GetSpace } from '@/common/utils/space-util'
import { getLocalUserSite } from '@/common/utils'

export interface TemplateQueryRequest {
    templateStatus?: string
    title?: string
    configs: string[]
    units?: string[]
    discipline?: string[]
    location?: string[]
}

const buildTemplateQuery = (request: TemplateQueryRequest): any => {
    const configsFilter = request.configs.length > 0 ? request.configs : undefined

    const query = {
        template: {
            nodes: {
                filter: {
                    and: [
                        {
                            or: [
                                {
                                    equals: {
                                        property: ['node', 'space'],
                                        value: GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode),
                                    },
                                },
                            ],
                        },
                        ...(configsFilter
                            ? [
                                  {
                                      in: {
                                          property: ['node', 'externalId'],
                                          values: configsFilter.map((item) => item.replace('CTC-', '')),
                                      },
                                  },
                              ]
                            : []),
                        {
                            equals: {
                                property: ['cdf_apm', 'Template/v5', 'status'],
                                value: request.templateStatus,
                            },
                        },
                        {
                            not: {
                                equals: {
                                    property: ['cdf_apm', 'Template/v5', 'isArchived'],
                                    value: true,
                                },
                            },
                        },
                        request.title != undefined && {
                            prefix: {
                                property: ['cdf_apm', 'Template/v5', 'title'],
                                value: request.title,
                            },
                        },
                    ].filter(Boolean),
                },
                chainTo: 'destination',
                direction: 'outwards',
            },
            limit: 10000,
        },
    }

    return query
}

export const useTemplateKpis = (request: TemplateQueryRequest) => {
    const unitsByFilter = request.units && request.units?.length > 0 ? true : false
    const locationByFilter = request.location && request.location?.length > 0 ? true : false
    const disciplineByFilter = request.discipline && request.discipline?.length > 0 ? true : false

    const [filter, setFilter] = useState<any>()
    const [resultData, setResultData] = useState<{ data: number; loading: boolean }>({
        data: 0,
        loading: true,
    })

    const select = {
        template: {
            sources: [
                {
                    source: {
                        space: 'cdf_apm',
                        externalId: 'Template',
                        version: 'v5',
                        type: 'view',
                    },
                    properties: ['*'],
                },
            ],
            limit: 10000,
        },
    }

    const cursors = {
        template: null,
    }

    const { getAllResults: getAllData } = useQueryResultsFunction<any>(select, cursors)

    useEffect(() => {
        setFilter(buildTemplateQuery(request))
    }, [request])

    useEffect(() => {
        filter &&
            getAllData(filter)
                .then((res) => {
                    if (
                        res.items.template.length === 0 ||
                        (request.configs.length === 0 && (unitsByFilter || locationByFilter || disciplineByFilter))
                    ) {
                        setResultData({ data: 0, loading: false })
                    } else {
                        setResultData({ data: res.items.template.length, loading: false })
                    }
                })
                .catch((e) => console.log('error', e, 'filter', filter))
    }, [filter])

    return {
        loading: resultData.loading,
        countResult: resultData.data,
    }
}
