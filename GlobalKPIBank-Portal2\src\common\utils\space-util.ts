import { cognite } from '../configurations/cognite'

export enum EntityType {
    Model = 'Model',
    Protected = 'Protected',
    Static = 'Static',
    REF = 'REF',
    Instance = 'Instance',
    SAP = 'SAP',
    APMA = 'APMA',
    APMATEMP = 'APMATEMP',
    UMG = 'UMG',
    APW = 'APW',
    APWREF = 'APWST',
    CKM = 'CKM',
    INO = 'INO',
}

export function GetSpace(type: EntityType, siteCode?: string, unitCode?: string) {
    let prefix = cognite.cogniteFdmProjectCode
    let suffix = cognite.cogniteFdmSuffixInstancesSpace

    const siteFragment = siteCode ?? cognite.cogniteFdmGlobalSiteSpace
    const unitFragment = unitCode ?? cognite.cogniteFdmGlobalUnitSpace

    if (type == EntityType.Static) {
        suffix = cognite.cogniteFdmSuffixStaticSpace
    } else if (type == EntityType.Model) {
        suffix = cognite.cogniteFdmSuffixModelSpace
    } else if (type == EntityType.Protected) {
        suffix = cognite.cogniteFdmSuffixProtectedSpace
    } else if (type == EntityType.APMATEMP) {
        prefix = 'APMA'
        suffix = 'DAT-TEMP'
    } else if (type == EntityType.INO) {
        prefix = type
        suffix = cognite.cogniteFdmSuffixModelSpace
    } else if (type != EntityType.Instance) {
        prefix = type
    }

    return [prefix, siteFragment, unitFragment, suffix].join('-')
}
