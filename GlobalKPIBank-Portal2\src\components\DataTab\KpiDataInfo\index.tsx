import { Box, Typography } from '@mui/material'
import { translate } from '@celanese/celanese-sdk'
import React, { useEffect } from 'react'
import { KpiTableView } from '@/common/models/KpiTable'
import { useGenericDataViewHook } from '@/common/hooks/detailed-data/useGenericDataView'
import { ClnCircularProgress } from '@celanese/ui-lib'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { usePathname } from 'next/navigation'

interface KpiTableViewProps {
    infoParameters: any
}

export const KpiDataInfo: React.FC<KpiTableViewProps> = ({ infoParameters }) => {
    const { requestData } = useGlobalKpiContext()
    const { kpiDataModelView: result, refetchDataModelView, loadingDataModelView } = useGenericDataViewHook(requestData)
    const pathName = usePathname().split('/')[1]

    useEffect(() => {
        refetchDataModelView()
    }, [requestData.kpiFilters.currentTab])

    const GetFormulaParameters = (kpi: KpiTableView) => {
        if (!kpi?.formula || !infoParameters) return []
        const resultRecords = []
        const matches = kpi.formula.match(/{(.*?)}/g)
        if (matches) {
            const values = matches ? matches.map((match) => match.slice(1, -1)) : []
            const kpiParameters = infoParameters.filter((item) => values.includes(item.code))
            values.forEach((formula) => {
                const newParameters = {
                    code: formula ?? '',
                    name: kpiParameters.find((item) => item.code === formula).name ?? '',
                }
                resultRecords.push(newParameters)
            })
        }
        return resultRecords
    }

    return (
        <Box
            sx={{
                ...(pathName === 'site-view' && {
                    height: '100%',
                }),
                overflowX: 'hidden',
                overflowY: 'auto',
            }}
        >
            {loadingDataModelView ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <ClnCircularProgress />
                </Box>
            ) : result.length >= 0 ? (
                <Box
                    sx={{
                        textAlign: 'left',
                        backgroundColor: '#f8f8f8',
                        borderRadius: '8px',
                        marginTop: '10px',
                    }}
                >
                    {result.map((item: any) => (
                        <>
                            <Box display="flex" flexDirection="column" gap={2} sx={{ margin: '1rem', padding: '10px' }}>
                                <Box>
                                    <Typography sx={{ color: 'text.secondary' }}>
                                        {translate('DATA_TAB.DEFINITION.DESCRIPTION') + ':'}
                                    </Typography>
                                    <Typography sx={{ flex: 1 }} fontWeight="bold">
                                        {item.description.split('Update frequency:')[0].trim()}
                                    </Typography>
                                </Box>
                                <Box>
                                    <Box>
                                        <Typography sx={{ color: 'text.secondary' }}>
                                            {translate('DATA_TAB.DEFINITION.FREQUENCY') + ':'}
                                        </Typography>

                                        <Typography sx={{ flex: 1 }} fontWeight="bold">
                                            {item.description.includes('Update frequency:')
                                                ? item.description.split('Update frequency:')[1].trim()
                                                : ''}
                                        </Typography>
                                    </Box>
                                </Box>
                                <Box>
                                    <Typography sx={{ color: 'text.secondary' }}>
                                        {translate('DATA_TAB.DEFINITION.FORMULA') + ':'}
                                    </Typography>
                                    <Typography sx={{ flex: 1 }} fontWeight="bold">
                                        {item.formula}
                                    </Typography>
                                </Box>
                                <Box>
                                    <Typography sx={{ color: 'text.secondary' }}>
                                        {translate('DATA_TAB.DEFINITION.PARAMETERS') + ':'}
                                    </Typography>
                                    {GetFormulaParameters(item).map((parameters) => (
                                        <>
                                            <Typography sx={{ flex: 1 }} fontWeight="bold">
                                                {parameters.code + ': ' + parameters.name}
                                            </Typography>
                                        </>
                                    ))}
                                </Box>
                                <Box>
                                    <Typography sx={{ color: 'text.secondary' }}>
                                        {translate('DATA_TAB.DEFINITION.BUSINESS_OWNER') + ':'}
                                    </Typography>
                                    <Typography sx={{ flex: 1 }} fontWeight="bold">
                                        {item.businessOwner}
                                    </Typography>
                                </Box>
                            </Box>
                        </>
                    ))}
                </Box>
            ) : (
                <Typography
                    sx={{
                        color: 'text.secondary',
                        fontWeight: 'bold',
                        fontSize: '1.5rem',
                        textAlign: 'center',
                        margin: '2rem',
                    }}
                >
                    {translate('DATA_TAB.NO_DATA')}
                </Typography>
            )}
        </Box>
    )
}
