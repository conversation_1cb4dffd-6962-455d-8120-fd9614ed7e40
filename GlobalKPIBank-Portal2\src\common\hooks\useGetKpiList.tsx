import { gql } from '@apollo/client'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useState, useEffect, useMemo } from 'react'
import { KpiList } from '../models/kpiList'

const queryBuilder = () => {
    return `query getKpiList {
    listGlobalVisionKPI (first: 1000) {
      items {
        externalId
        space
        name
        group {
            externalId
            name
        }
      }
    }
  }`
}

export const useGetKpiList = () => {
    const query = queryBuilder()

    const { data: fdmData, refetch } = useGraphqlQuery<KpiList>(gql(query), 'listGlobalVisionKPI', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'globalView',
        },
    })

    const [resultData, setResultData] = useState<{ data: KpiList[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData === undefined) {
            setResultData((prevState) => ({ ...prevState, loading: true }))
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return useMemo(
        () => ({
            loadingKpiList: resultData.loading,
            refetchKpiGroups: refetch,
            kpiList: resultData.data,
        }),
        [resultData, refetch]
    )
}
