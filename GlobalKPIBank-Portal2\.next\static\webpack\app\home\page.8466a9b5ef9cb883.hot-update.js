"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/common/hooks/detailed-data/useQualityNotification.tsx":
/*!*******************************************************************!*\
  !*** ./src/common/hooks/detailed-data/useQualityNotification.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQualityNotification: function() { return /* binding */ useQualityNotification; }\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/graphql-tag/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useGraphqlQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useGraphqlQuery */ \"(app-pages-browser)/./src/common/hooks/useGraphqlQuery.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _common_utils_kpis_data_table_export__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/common/utils/kpis-data-table-export */ \"(app-pages-browser)/./src/common/utils/kpis-data-table-export.ts\");\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst queryBuilder = (request)=>{\n    let siteQ = \"\";\n    let businessSegmentQ = \"\";\n    let startDateQ = \"\";\n    let endDateQ = \"\";\n    let fixedFilters = \"\";\n    let limitQ = \"\";\n    let cursorQ = \"\";\n    if (request && request.kpiFilters.refSite.length > 0) {\n        const sites = request.kpiFilters.refSite.map((site)=>'\"'.concat(site, '\"')).join(\",\");\n        siteQ = \"{aboutReportingSite: {externalId: {in: [\".concat(sites, \"]}}},\");\n    }\n    if (request && request.kpiFilters.businessSegment !== \"\") {\n        businessSegmentQ = '{businessSegment: {externalId: {eq: \"'.concat(request.kpiFilters.businessSegment, '\"}}},');\n    }\n    if (request && request.kpiFilters.date !== \"\") {\n        let sanitizedInitialDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(request.kpiFilters.date).startOf(\"month\").format(\"YYYY-MM-DD\");\n        let sanitizedEndDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(request.kpiFilters.date).endOf(\"month\").format(\"YYYY-MM-DD\");\n        const time = \"T00:00:00+00:00\";\n        if (request.exportFilters && request.exportFilters.isExport) {\n            [sanitizedInitialDate, sanitizedEndDate] = (0,_common_utils_kpis_data_table_export__WEBPACK_IMPORTED_MODULE_3__.setExportDateRange)(request.exportFilters.range, request.kpiFilters.date);\n        }\n        startDateQ = '{notificationDate: {gte: \"'.concat(sanitizedInitialDate).concat(time, '\"}},');\n        endDateQ = '{notificationDate: {lte: \"'.concat(sanitizedEndDate).concat(time, '\"}},');\n    }\n    if (request && request.kpiFilters.kpiName !== \"\") {\n        if (request.kpiFilters.kpiName === \"HS QN1s\") {\n            fixedFilters = '\\n                {notificationType: {eq: \"Q1\"}},\\n                {or: [{systemStatusMapping: {eq: \"Accepted\"}}, {systemStatusMapping: {eq: \"Open\"}}]},\\n            ';\n        }\n        if (request.kpiFilters.kpiName === \"QN1s\") {\n            fixedFilters = '\\n                {notificationType: {eq: \"Q1\"}},\\n                {systemStatusMapping: {eq: \"Accepted\"}},\\n                {causeCodeGroup: {eq: \"QMMAN500\"}},\\n                {not: {priority: {eq: \"E\"}}},\\n            ';\n        }\n        if (request.kpiFilters.kpiName === \"Major Audit Findings - External\") {\n            fixedFilters = '\\n                {notificationType: {eq: \"Q3\"}},\\n                {priority: {eq: \"H\"}},\\n                {or: [{systemStatusMapping: {eq: \"Accepted\"}}, {systemStatusMapping: {eq: \"Open\"}}]},\\n                {qualityManagementCodeGroup: {eq: \"QMI00013\"}},\\n                {qualityManagementCode: {eq: \"0002\"}},\\n            ';\n        }\n    }\n    if (request && request.limit && request.limit !== \"\") {\n        limitQ = \"first: \".concat(request.limit);\n    }\n    if (request.cursor && request.cursor !== \"\") {\n        cursorQ = 'after: \"'.concat(request.cursor, '\"');\n    }\n    return \"query getKpiDataView {\\n        listQualityNotification (\\n            \".concat(limitQ, \"\\n            \").concat(cursorQ, \"\\n            filter: {and: [\").concat(siteQ, \" \").concat(businessSegmentQ, \" \").concat(startDateQ, \" \").concat(endDateQ, \" \").concat(fixedFilters, \"]}\\n        ) {\\n            pageInfo {\\n                hasNextPage\\n                endCursor\\n            }\\n            items {\\n                aboutReportingSite {\\n                    externalId\\n                    name\\n                }\\n                notificationDescription\\n                notificationNumber\\n                notificationDate\\n                notificationType\\n                systemStatusMapping\\n                systemStatus\\n                priority\\n                causeCodeGroup\\n                qualityManagementCodeGroup\\n                qualityManagementCode\\n                severity\\n                subjectCode\\n                businessSegment {\\n                    externalId\\n                    description\\n                }\\n            }\\n        }\\n    }\");\n};\nconst checkSystemStatus = (kpiName, systemStatus)=>{\n    let result = false;\n    const systemStatusArray = systemStatus.split(\" \");\n    const rank = \"RANK\";\n    if (kpiName === \"HS QN1s\") {\n        const words = [\n            \"NOCO\",\n            \"NOPR\",\n            \"OSNO\"\n        ];\n        const containsAtLeastOne = words.some((word)=>systemStatusArray.includes(word));\n        const containsRank = systemStatusArray.includes(rank);\n        if (containsAtLeastOne && containsRank) result = true;\n    }\n    if (kpiName === \"QN1s\") {\n        const words = [\n            \"NOCO\"\n        ];\n        const containsAtLeastOne = words.some((word)=>systemStatusArray.includes(word));\n        const containsRank = systemStatusArray.includes(rank);\n        if (containsAtLeastOne && containsRank) result = true;\n    }\n    if (kpiName === \"Major Audit Findings - External\") result = true;\n    return result;\n};\nconst mapQualityNotification = (data, kpiName)=>{\n    const mappedResult = [];\n    if (data && data.length > 0) {\n        data.map((item)=>{\n            var _item_notificationDate;\n            const isValidSystemStatus = checkSystemStatus(kpiName, item.systemStatus);\n            var _item_aboutReportingSite_name, _item_notificationDescription, _item_notificationNumber, _item_notificationDate_toString_split_, _item_notificationType, _item_systemStatusMapping, _item_priority, _item_causeCodeGroup, _item_qualityManagementCodeGroup, _item_qualityManagementCode, _item_severity, _item_subjectCode, _item_businessSegment_description;\n            const result = {\n                siteName: (_item_aboutReportingSite_name = item.aboutReportingSite.name) !== null && _item_aboutReportingSite_name !== void 0 ? _item_aboutReportingSite_name : \"\",\n                notificationDescription: (_item_notificationDescription = item.notificationDescription) !== null && _item_notificationDescription !== void 0 ? _item_notificationDescription : \"\",\n                notificationNumber: (_item_notificationNumber = item.notificationNumber) !== null && _item_notificationNumber !== void 0 ? _item_notificationNumber : \"\",\n                notificationDate: (_item_notificationDate_toString_split_ = (_item_notificationDate = item.notificationDate) === null || _item_notificationDate === void 0 ? void 0 : _item_notificationDate.toString().split(\"T\")[0]) !== null && _item_notificationDate_toString_split_ !== void 0 ? _item_notificationDate_toString_split_ : \"\",\n                notificationType: (_item_notificationType = item.notificationType) !== null && _item_notificationType !== void 0 ? _item_notificationType : \"\",\n                systemStatusMapping: (_item_systemStatusMapping = item.systemStatusMapping) !== null && _item_systemStatusMapping !== void 0 ? _item_systemStatusMapping : \"\",\n                priority: (_item_priority = item.priority) !== null && _item_priority !== void 0 ? _item_priority : \"\",\n                causeCodeGroup: (_item_causeCodeGroup = item.causeCodeGroup) !== null && _item_causeCodeGroup !== void 0 ? _item_causeCodeGroup : \"\",\n                qualityManagementCodeGroup: (_item_qualityManagementCodeGroup = item.qualityManagementCodeGroup) !== null && _item_qualityManagementCodeGroup !== void 0 ? _item_qualityManagementCodeGroup : \"\",\n                qualityManagementCode: (_item_qualityManagementCode = item.qualityManagementCode) !== null && _item_qualityManagementCode !== void 0 ? _item_qualityManagementCode : \"\",\n                severity: (_item_severity = item.severity) !== null && _item_severity !== void 0 ? _item_severity : \"\",\n                subjectCode: (_item_subjectCode = item.subjectCode) !== null && _item_subjectCode !== void 0 ? _item_subjectCode : \"\",\n                businessSegment: (_item_businessSegment_description = item.businessSegment.description) !== null && _item_businessSegment_description !== void 0 ? _item_businessSegment_description : \"\"\n            };\n            if (isValidSystemStatus) mappedResult.push(result);\n        });\n    }\n    return mappedResult;\n};\nconst useQualityNotification = (request)=>{\n    _s();\n    const query = queryBuilder(request);\n    const { data: fdmData, pageInfo: fdmPageInfo, loading, refetch } = (0,_useGraphqlQuery__WEBPACK_IMPORTED_MODULE_1__.useGraphqlQuery)((0,_apollo_client__WEBPACK_IMPORTED_MODULE_4__.gql)(query), \"listQualityNotification\", {\n        fetchPolicy: \"network-only\",\n        context: {\n            clientName: \"qualityView\"\n        }\n    });\n    const [resultData, setResultData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        data: []\n    });\n    const [pageInfoData, setPageInfoData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        hasPreviousPage: false,\n        hasNextPage: false,\n        startCursor: \"\",\n        endCursor: \"\"\n    });\n    const [loadMore, setLoadMore] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const loadMoreExport = ()=>{\n        if (!pageInfoData.hasNextPage) return;\n        setLoadMore(!loadMore);\n    };\n    const triggerFilter = ()=>{\n        setPageInfoData({\n            hasPreviousPage: false,\n            hasNextPage: false,\n            startCursor: \"\",\n            endCursor: \"\"\n        });\n        setResultData({\n            data: []\n        });\n        setLoadMore(!refetch);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (request.kpiFilters.kpiName !== \"\") {\n            if (fdmData === undefined) {\n                setResultData((prevState)=>({\n                        ...prevState,\n                        loading: true\n                    }));\n            } else {\n                if (request.exportFilters.isExport) {\n                    const mappedResult = mapQualityNotification(fdmData, request.kpiFilters.kpiName);\n                    setResultData({\n                        data: mappedResult\n                    });\n                    setPageInfoData(fdmPageInfo);\n                }\n                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {\n                    const mappedResult = mapQualityNotification(fdmData, request.kpiFilters.kpiName);\n                    setResultData((prevData)=>{\n                        var _prevData_data;\n                        return {\n                            data: [\n                                ...(_prevData_data = prevData.data) !== null && _prevData_data !== void 0 ? _prevData_data : [],\n                                ...mappedResult\n                            ]\n                        };\n                    });\n                    setPageInfoData(fdmPageInfo);\n                }\n            }\n        }\n    }, [\n        fdmData,\n        request.kpiFilters.kpiName,\n        loadMore\n    ]);\n    return {\n        kpiDataModelView: resultData.data,\n        pageInfoDataModelView: pageInfoData,\n        loadingDataModelView: loading,\n        refetchDataModelView: triggerFilter,\n        loadMoreExport: loadMoreExport\n    };\n};\n_s(useQualityNotification, \"IQIJgWDJDBuq6crxPYS5OnqFtj0=\", false, function() {\n    return [\n        _useGraphqlQuery__WEBPACK_IMPORTED_MODULE_1__.useGraphqlQuery\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/common/hooks/detailed-data/useQualityNotification.tsx\n"));

/***/ })

});