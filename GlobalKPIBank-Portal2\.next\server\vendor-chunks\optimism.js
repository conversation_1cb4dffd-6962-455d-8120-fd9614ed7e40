"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/optimism";
exports.ids = ["vendor-chunks/optimism"];
exports.modules = {

/***/ "(ssr)/./node_modules/optimism/lib/bundle.cjs":
/*!**********************************************!*\
  !*** ./node_modules/optimism/lib/bundle.cjs ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar trie = __webpack_require__(/*! @wry/trie */ \"(ssr)/./node_modules/@wry/trie/lib/bundle.cjs\");\nvar caches$1 = __webpack_require__(/*! @wry/caches */ \"(ssr)/./node_modules/@wry/caches/lib/bundle.cjs\");\nvar context = __webpack_require__(/*! @wry/context */ \"(ssr)/./node_modules/@wry/context/lib/bundle.cjs\");\n\nvar parentEntrySlot = new context.Slot();\nfunction nonReactive(fn) {\n    return parentEntrySlot.withValue(void 0, fn);\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar arrayFromSet = Array.from ||\n    function (set) {\n        var array = [];\n        set.forEach(function (item) { return array.push(item); });\n        return array;\n    };\nfunction maybeUnsubscribe(entryOrDep) {\n    var unsubscribe = entryOrDep.unsubscribe;\n    if (typeof unsubscribe === \"function\") {\n        entryOrDep.unsubscribe = void 0;\n        unsubscribe();\n    }\n}\n\nvar emptySetPool = [];\nvar POOL_TARGET_SIZE = 100;\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition, optionalMessage) {\n    if (!condition) {\n        throw new Error(optionalMessage || \"assertion failure\");\n    }\n}\nfunction valueIs(a, b) {\n    var len = a.length;\n    return (\n    // Unknown values are not equal to each other.\n    len > 0 &&\n        // Both values must be ordinary (or both exceptional) to be equal.\n        len === b.length &&\n        // The underlying value or exception must be the same.\n        a[len - 1] === b[len - 1]);\n}\nfunction valueGet(value) {\n    switch (value.length) {\n        case 0: throw new Error(\"unknown value\");\n        case 1: return value[0];\n        case 2: throw value[1];\n    }\n}\nfunction valueCopy(value) {\n    return value.slice(0);\n}\nvar Entry = /** @class */ (function () {\n    function Entry(fn) {\n        this.fn = fn;\n        this.parents = new Set();\n        this.childValues = new Map();\n        // When this Entry has children that are dirty, this property becomes\n        // a Set containing other Entry objects, borrowed from emptySetPool.\n        // When the set becomes empty, it gets recycled back to emptySetPool.\n        this.dirtyChildren = null;\n        this.dirty = true;\n        this.recomputing = false;\n        this.value = [];\n        this.deps = null;\n        ++Entry.count;\n    }\n    Entry.prototype.peek = function () {\n        if (this.value.length === 1 && !mightBeDirty(this)) {\n            rememberParent(this);\n            return this.value[0];\n        }\n    };\n    // This is the most important method of the Entry API, because it\n    // determines whether the cached this.value can be returned immediately,\n    // or must be recomputed. The overall performance of the caching system\n    // depends on the truth of the following observations: (1) this.dirty is\n    // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n    // (3) valueGet(this.value) is usually returned without recomputation.\n    Entry.prototype.recompute = function (args) {\n        assert(!this.recomputing, \"already recomputing\");\n        rememberParent(this);\n        return mightBeDirty(this)\n            ? reallyRecompute(this, args)\n            : valueGet(this.value);\n    };\n    Entry.prototype.setDirty = function () {\n        if (this.dirty)\n            return;\n        this.dirty = true;\n        reportDirty(this);\n        // We can go ahead and unsubscribe here, since any further dirty\n        // notifications we receive will be redundant, and unsubscribing may\n        // free up some resources, e.g. file watchers.\n        maybeUnsubscribe(this);\n    };\n    Entry.prototype.dispose = function () {\n        var _this = this;\n        this.setDirty();\n        // Sever any dependency relationships with our own children, so those\n        // children don't retain this parent Entry in their child.parents sets,\n        // thereby preventing it from being fully garbage collected.\n        forgetChildren(this);\n        // Because this entry has been kicked out of the cache (in index.js),\n        // we've lost the ability to find out if/when this entry becomes dirty,\n        // whether that happens through a subscription, because of a direct call\n        // to entry.setDirty(), or because one of its children becomes dirty.\n        // Because of this loss of future information, we have to assume the\n        // worst (that this entry might have become dirty very soon), so we must\n        // immediately mark this entry's parents as dirty. Normally we could\n        // just call entry.setDirty() rather than calling parent.setDirty() for\n        // each parent, but that would leave this entry in parent.childValues\n        // and parent.dirtyChildren, which would prevent the child from being\n        // truly forgotten.\n        eachParent(this, function (parent, child) {\n            parent.setDirty();\n            forgetChild(parent, _this);\n        });\n    };\n    Entry.prototype.forget = function () {\n        // The code that creates Entry objects in index.ts will replace this method\n        // with one that actually removes the Entry from the cache, which will also\n        // trigger the entry.dispose method.\n        this.dispose();\n    };\n    Entry.prototype.dependOn = function (dep) {\n        dep.add(this);\n        if (!this.deps) {\n            this.deps = emptySetPool.pop() || new Set();\n        }\n        this.deps.add(dep);\n    };\n    Entry.prototype.forgetDeps = function () {\n        var _this = this;\n        if (this.deps) {\n            arrayFromSet(this.deps).forEach(function (dep) { return dep.delete(_this); });\n            this.deps.clear();\n            emptySetPool.push(this.deps);\n            this.deps = null;\n        }\n    };\n    Entry.count = 0;\n    return Entry;\n}());\nfunction rememberParent(child) {\n    var parent = parentEntrySlot.getValue();\n    if (parent) {\n        child.parents.add(parent);\n        if (!parent.childValues.has(child)) {\n            parent.childValues.set(child, []);\n        }\n        if (mightBeDirty(child)) {\n            reportDirtyChild(parent, child);\n        }\n        else {\n            reportCleanChild(parent, child);\n        }\n        return parent;\n    }\n}\nfunction reallyRecompute(entry, args) {\n    forgetChildren(entry);\n    // Set entry as the parent entry while calling recomputeNewValue(entry).\n    parentEntrySlot.withValue(entry, recomputeNewValue, [entry, args]);\n    if (maybeSubscribe(entry, args)) {\n        // If we successfully recomputed entry.value and did not fail to\n        // (re)subscribe, then this Entry is no longer explicitly dirty.\n        setClean(entry);\n    }\n    return valueGet(entry.value);\n}\nfunction recomputeNewValue(entry, args) {\n    entry.recomputing = true;\n    var normalizeResult = entry.normalizeResult;\n    var oldValueCopy;\n    if (normalizeResult && entry.value.length === 1) {\n        oldValueCopy = valueCopy(entry.value);\n    }\n    // Make entry.value an empty array, representing an unknown value.\n    entry.value.length = 0;\n    try {\n        // If entry.fn succeeds, entry.value will become a normal Value.\n        entry.value[0] = entry.fn.apply(null, args);\n        // If we have a viable oldValueCopy to compare with the (successfully\n        // recomputed) new entry.value, and they are not already === identical, give\n        // normalizeResult a chance to pick/choose/reuse parts of oldValueCopy[0]\n        // and/or entry.value[0] to determine the final cached entry.value.\n        if (normalizeResult && oldValueCopy && !valueIs(oldValueCopy, entry.value)) {\n            try {\n                entry.value[0] = normalizeResult(entry.value[0], oldValueCopy[0]);\n            }\n            catch (_a) {\n                // If normalizeResult throws, just use the newer value, rather than\n                // saving the exception as entry.value[1].\n            }\n        }\n    }\n    catch (e) {\n        // If entry.fn throws, entry.value will hold that exception.\n        entry.value[1] = e;\n    }\n    // Either way, this line is always reached.\n    entry.recomputing = false;\n}\nfunction mightBeDirty(entry) {\n    return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\nfunction setClean(entry) {\n    entry.dirty = false;\n    if (mightBeDirty(entry)) {\n        // This Entry may still have dirty children, in which case we can't\n        // let our parents know we're clean just yet.\n        return;\n    }\n    reportClean(entry);\n}\nfunction reportDirty(child) {\n    eachParent(child, reportDirtyChild);\n}\nfunction reportClean(child) {\n    eachParent(child, reportCleanChild);\n}\nfunction eachParent(child, callback) {\n    var parentCount = child.parents.size;\n    if (parentCount) {\n        var parents = arrayFromSet(child.parents);\n        for (var i = 0; i < parentCount; ++i) {\n            callback(parents[i], child);\n        }\n    }\n}\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent, child) {\n    // Must have called rememberParent(child) before calling\n    // reportDirtyChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(mightBeDirty(child));\n    var parentWasClean = !mightBeDirty(parent);\n    if (!parent.dirtyChildren) {\n        parent.dirtyChildren = emptySetPool.pop() || new Set;\n    }\n    else if (parent.dirtyChildren.has(child)) {\n        // If we already know this child is dirty, then we must have already\n        // informed our own parents that we are dirty, so we can terminate\n        // the recursion early.\n        return;\n    }\n    parent.dirtyChildren.add(child);\n    // If parent was clean before, it just became (possibly) dirty (according to\n    // mightBeDirty), since we just added child to parent.dirtyChildren.\n    if (parentWasClean) {\n        reportDirty(parent);\n    }\n}\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent, child) {\n    // Must have called rememberChild(child) before calling\n    // reportCleanChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(!mightBeDirty(child));\n    var childValue = parent.childValues.get(child);\n    if (childValue.length === 0) {\n        parent.childValues.set(child, valueCopy(child.value));\n    }\n    else if (!valueIs(childValue, child.value)) {\n        parent.setDirty();\n    }\n    removeDirtyChild(parent, child);\n    if (mightBeDirty(parent)) {\n        return;\n    }\n    reportClean(parent);\n}\nfunction removeDirtyChild(parent, child) {\n    var dc = parent.dirtyChildren;\n    if (dc) {\n        dc.delete(child);\n        if (dc.size === 0) {\n            if (emptySetPool.length < POOL_TARGET_SIZE) {\n                emptySetPool.push(dc);\n            }\n            parent.dirtyChildren = null;\n        }\n    }\n}\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent) {\n    if (parent.childValues.size > 0) {\n        parent.childValues.forEach(function (_value, child) {\n            forgetChild(parent, child);\n        });\n    }\n    // Remove this parent Entry from any sets to which it was added by the\n    // addToSet method.\n    parent.forgetDeps();\n    // After we forget all our children, this.dirtyChildren must be empty\n    // and therefore must have been reset to null.\n    assert(parent.dirtyChildren === null);\n}\nfunction forgetChild(parent, child) {\n    child.parents.delete(parent);\n    parent.childValues.delete(child);\n    removeDirtyChild(parent, child);\n}\nfunction maybeSubscribe(entry, args) {\n    if (typeof entry.subscribe === \"function\") {\n        try {\n            maybeUnsubscribe(entry); // Prevent double subscriptions.\n            entry.unsubscribe = entry.subscribe.apply(null, args);\n        }\n        catch (e) {\n            // If this Entry has a subscribe function and it threw an exception\n            // (or an unsubscribe function it previously returned now throws),\n            // return false to indicate that we were not able to subscribe (or\n            // unsubscribe), and this Entry should remain dirty.\n            entry.setDirty();\n            return false;\n        }\n    }\n    // Returning true indicates either that there was no entry.subscribe\n    // function or that it succeeded.\n    return true;\n}\n\nvar EntryMethods = {\n    setDirty: true,\n    dispose: true,\n    forget: true, // Fully remove parent Entry from LRU cache and computation graph\n};\nfunction dep(options) {\n    var depsByKey = new Map();\n    var subscribe = options && options.subscribe;\n    function depend(key) {\n        var parent = parentEntrySlot.getValue();\n        if (parent) {\n            var dep_1 = depsByKey.get(key);\n            if (!dep_1) {\n                depsByKey.set(key, dep_1 = new Set);\n            }\n            parent.dependOn(dep_1);\n            if (typeof subscribe === \"function\") {\n                maybeUnsubscribe(dep_1);\n                dep_1.unsubscribe = subscribe(key);\n            }\n        }\n    }\n    depend.dirty = function dirty(key, entryMethodName) {\n        var dep = depsByKey.get(key);\n        if (dep) {\n            var m_1 = (entryMethodName &&\n                hasOwnProperty.call(EntryMethods, entryMethodName)) ? entryMethodName : \"setDirty\";\n            // We have to use arrayFromSet(dep).forEach instead of dep.forEach,\n            // because modifying a Set while iterating over it can cause elements in\n            // the Set to be removed from the Set before they've been iterated over.\n            arrayFromSet(dep).forEach(function (entry) { return entry[m_1](); });\n            depsByKey.delete(key);\n            maybeUnsubscribe(dep);\n        }\n    };\n    return depend;\n}\n\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCacheKey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nvar defaultKeyTrie;\nfunction defaultMakeCacheKey() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var trie$1 = defaultKeyTrie || (defaultKeyTrie = new trie.Trie(typeof WeakMap === \"function\"));\n    return trie$1.lookupArray(args);\n}\nvar caches = new Set();\nfunction wrap(originalFunction, _a) {\n    var _b = _a === void 0 ? Object.create(null) : _a, _c = _b.max, max = _c === void 0 ? Math.pow(2, 16) : _c, keyArgs = _b.keyArgs, _d = _b.makeCacheKey, makeCacheKey = _d === void 0 ? defaultMakeCacheKey : _d, normalizeResult = _b.normalizeResult, subscribe = _b.subscribe, _e = _b.cache, cacheOption = _e === void 0 ? caches$1.StrongCache : _e;\n    var cache = typeof cacheOption === \"function\"\n        ? new cacheOption(max, function (entry) { return entry.dispose(); })\n        : cacheOption;\n    var optimistic = function () {\n        var key = makeCacheKey.apply(null, keyArgs ? keyArgs.apply(null, arguments) : arguments);\n        if (key === void 0) {\n            return originalFunction.apply(null, arguments);\n        }\n        var entry = cache.get(key);\n        if (!entry) {\n            cache.set(key, entry = new Entry(originalFunction));\n            entry.normalizeResult = normalizeResult;\n            entry.subscribe = subscribe;\n            // Give the Entry the ability to trigger cache.delete(key), even though\n            // the Entry itself does not know about key or cache.\n            entry.forget = function () { return cache.delete(key); };\n        }\n        var value = entry.recompute(Array.prototype.slice.call(arguments));\n        // Move this entry to the front of the least-recently used queue,\n        // since we just finished computing its value.\n        cache.set(key, entry);\n        caches.add(cache);\n        // Clean up any excess entries in the cache, but only if there is no\n        // active parent entry, meaning we're not in the middle of a larger\n        // computation that might be flummoxed by the cleaning.\n        if (!parentEntrySlot.hasValue()) {\n            caches.forEach(function (cache) { return cache.clean(); });\n            caches.clear();\n        }\n        return value;\n    };\n    Object.defineProperty(optimistic, \"size\", {\n        get: function () { return cache.size; },\n        configurable: false,\n        enumerable: false,\n    });\n    Object.freeze(optimistic.options = {\n        max: max,\n        keyArgs: keyArgs,\n        makeCacheKey: makeCacheKey,\n        normalizeResult: normalizeResult,\n        subscribe: subscribe,\n        cache: cache,\n    });\n    function dirtyKey(key) {\n        var entry = key && cache.get(key);\n        if (entry) {\n            entry.setDirty();\n        }\n    }\n    optimistic.dirtyKey = dirtyKey;\n    optimistic.dirty = function dirty() {\n        dirtyKey(makeCacheKey.apply(null, arguments));\n    };\n    function peekKey(key) {\n        var entry = key && cache.get(key);\n        if (entry) {\n            return entry.peek();\n        }\n    }\n    optimistic.peekKey = peekKey;\n    optimistic.peek = function peek() {\n        return peekKey(makeCacheKey.apply(null, arguments));\n    };\n    function forgetKey(key) {\n        return key ? cache.delete(key) : false;\n    }\n    optimistic.forgetKey = forgetKey;\n    optimistic.forget = function forget() {\n        return forgetKey(makeCacheKey.apply(null, arguments));\n    };\n    optimistic.makeCacheKey = makeCacheKey;\n    optimistic.getKey = keyArgs ? function getKey() {\n        return makeCacheKey.apply(null, keyArgs.apply(null, arguments));\n    } : makeCacheKey;\n    return Object.freeze(optimistic);\n}\n\nObject.defineProperty(exports, \"KeyTrie\", ({\n    enumerable: true,\n    get: function () { return trie.Trie; }\n}));\nObject.defineProperty(exports, \"Slot\", ({\n    enumerable: true,\n    get: function () { return context.Slot; }\n}));\nObject.defineProperty(exports, \"asyncFromGen\", ({\n    enumerable: true,\n    get: function () { return context.asyncFromGen; }\n}));\nObject.defineProperty(exports, \"bindContext\", ({\n    enumerable: true,\n    get: function () { return context.bind; }\n}));\nObject.defineProperty(exports, \"noContext\", ({\n    enumerable: true,\n    get: function () { return context.noContext; }\n}));\nObject.defineProperty(exports, \"setTimeout\", ({\n    enumerable: true,\n    get: function () { return context.setTimeout; }\n}));\nexports.defaultMakeCacheKey = defaultMakeCacheKey;\nexports.dep = dep;\nexports.nonReactive = nonReactive;\nexports.wrap = wrap;\n//# sourceMappingURL=bundle.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/optimism/lib/bundle.cjs\n");

/***/ })

};
;