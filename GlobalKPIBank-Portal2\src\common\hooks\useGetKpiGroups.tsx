import { gql } from '@apollo/client'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useState, useEffect, useMemo } from 'react'
import { KpiGroup } from '../models/kpiGroup'

const queryBuilder = () => {
    return `query getKpiGroups {
    listGlobalVisionKPIGroup (first: 1000) {
      items {
        externalId
        space
        name
        order
      }
    }
  }`
}

export const useGetKpiGroups = () => {
    const query = queryBuilder()

    const { data: fdmData, refetch } = useGraphqlQuery<KpiGroup>(gql(query), 'listGlobalVisionKPIGroup', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'globalView',
        },
    })

    const [resultData, setResultData] = useState<{ data: KpiGroup[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData === undefined) {
            setResultData((prevState) => ({ ...prevState, loading: true }))
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return useMemo(
        () => ({
            loadingKpiGroups: resultData.loading,
            refetchKpiGroups: refetch,
            kpiGroupsList: resultData.data,
        }),
        [resultData, refetch]
    )
}
