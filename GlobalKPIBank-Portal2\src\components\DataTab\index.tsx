import { Box, Tooltip, Typography } from '@mui/material'
import { styles } from './styles'
import { ClnButton, ClnCircularProgress, ClnTabs, MatIcon } from '@celanese/ui-lib'
import { useEffect, useMemo, useRef, useState } from 'react'
import { translate } from '@celanese/celanese-sdk'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import HelpOutlineOutlinedIcon from '@mui/icons-material/HelpOutlineOutlined'
import { KpiDataInfo } from './KpiDataInfo'
import { useKpiParameter } from '@/common/hooks/detailed-data/useKpiParameter'
import { KpisDataViews } from '@/common/utils/kpis-data-table'
import { useForm } from 'react-hook-form'
import { DataModelView } from '@/common/models/dataModelView'
import { KpiDataTable } from './KpiDataTable'
import { KpiDataChart } from './KpiDataChart'
import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import { usePathname } from 'next/navigation'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { HomeViewTable } from '../Home/HomeViewTable'
import { addsBreakLineOnText } from '@/common/utils/stringFormat'
import { agglomerateValues } from '@/common/utils/agglomerateKpi'
import KpiDataDetails from './KpiDataDetails'
import { useDataChartContext } from '@/common/contexts/DataChartContext'

interface DataTabsProps {
    data?: GlobalVisionKpiTableModel
    globalViewData?: GlobalVisionKpiTableModel[]
    target?: GlobalVisionKpiTarget[]
}

export const DataTab: React.FC<DataTabsProps> = ({ globalViewData, target, data }) => {
    const {
        selectedKpiData,
        isProductivityKpi,
        selectedKpi,
        setRequestData,
        requestData,
        currentTab,
        setCurrentTab,
        currentSitePageTab,
        selectedSiteId,
        selectedMultipleSiteIds,
    } = useGlobalKpiContext()
    const selectedKpiRef = useRef(selectedKpi)
    const { resultKpiParameter: resultParameter } = useKpiParameter()
    const pathName = usePathname().split('/')[1]
    const [tableView, setTableView] = useState<boolean>(false)
    const kpisModels = Object.values(KpisDataViews)
    const kpiName = selectedKpiData?.name
    const { setValue } = useForm<DataModelView>({})
    const [openModal, setOpenModal] = useState(false)
    const { disableSelection, setDisableSelection } = useDataChartContext()

    useEffect(() => {
        const kpiViews = kpisModels.filter((kpi) => kpi.NAME === kpiName)[0]?.VIEWS
        const kpiInfo = kpisModels.filter((kpi) => kpi.NAME === kpiName)[0]?.INFO

        if (kpiViews?.length > 0) {
            setValue('kpiFilters.views', kpiViews)
        }

        const newTabIndex = kpiViews?.indexOf('KpiInfo')

        setRequestData((prevState) => ({
            ...prevState,
            kpiFilters: {
                ...prevState.kpiFilters,
                site: selectedSiteId,
                ...(currentTab === 2 ? { currentTab: newTabIndex } : {}),
                kpiName: kpiName,
                info: kpiInfo,
                views: kpiViews,
            },
        }))
    }, [currentTab, selectedKpiData])

    useEffect(() => {
        if (selectedKpiRef.current !== selectedKpi) {
            selectedKpiRef.current = selectedKpi
            setCurrentTab(0)
        }
    }, [currentSitePageTab, selectedKpi])

    useEffect(() => {
        setDisableSelection(openModal)
    }, [openModal])

    const tabsContent: React.ReactNode[] = useMemo(() => {
        const contents: React.ReactNode[] = []

        const valueTab =
            pathName === 'site-view' ? (
                tableView ? (
                    <HomeViewTable
                        data={
                            globalViewData.filter((item) => item.kpi.externalId === selectedKpiData.externalId)[0].data
                        }
                        target={target}
                        openModal={openModal}
                        setOpenModal={setOpenModal}
                    />
                ) : (
                    <>
                        <KpiDataChart
                            data={
                                globalViewData.filter((item) => item.kpi.externalId === selectedKpiData.externalId)[0]
                                    ?.data
                            }
                            selectedKpi={selectedKpi}
                            range={
                                target &&
                                (target.length == 0
                                    ? 0
                                    : target.find(
                                          (item) => item.refGlobalVisionKPI.externalId === selectedKpiData.externalId
                                      )?.value)
                            }
                            target={target}
                            openModal={openModal}
                            setOpenModal={setOpenModal}
                        />
                        <KpiDataDetails />
                    </>
                )
            ) : data ? (
                tableView ? (
                    <HomeViewTable
                        data={
                            selectedSiteId !== ''
                                ? data.data.filter((item) => item.refReportingSite.externalId === selectedSiteId)
                                : data.data
                        }
                        target={target}
                        openModal={openModal}
                        setOpenModal={setOpenModal}
                    />
                ) : (
                    <KpiDataChart
                        data={
                            selectedSiteId !== ''
                                ? data.data.filter((item) => item.refReportingSite.externalId === selectedSiteId)
                                : data.data
                        }
                        selectedKpi={selectedKpi}
                        range={
                            target &&
                            (selectedSiteId !== ''
                                ? target.length == 0
                                    ? 0
                                    : target.find(
                                          (item) =>
                                              item.refGlobalVisionKPI.externalId === selectedKpiData.externalId &&
                                              item.refReportingSite.externalId === selectedSiteId
                                      )?.value
                                : agglomerateValues(
                                      target
                                          .filter(
                                              (item) =>
                                                  item &&
                                                  item.refGlobalVisionKPI &&
                                                  item.refReportingSite &&
                                                  item.refGlobalVisionKPI.externalId === selectedKpiData?.externalId &&
                                                  selectedMultipleSiteIds.includes(item.refReportingSite.externalId)
                                          )
                                          .map((item) => item.value),
                                      selectedKpiData.externalId
                                  ))
                        }
                        target={target}
                        openModal={openModal}
                        setOpenModal={setOpenModal}
                    />
                )
            ) : (
                <Box></Box>
            )
        contents.push(valueTab)

        const detailedDataTab = requestData ? <KpiDataTable /> : <ClnCircularProgress />
        contents.push(detailedDataTab)

        const definitionTab = resultParameter ? (
            <KpiDataInfo infoParameters={resultParameter} />
        ) : (
            <ClnCircularProgress />
        )
        contents.push(definitionTab)

        return contents
    }, [requestData, resultParameter, currentTab, tableView, openModal])

    const tabs = [
        {
            label: translate('DATA_TAB.VALUE.TITLE').toUpperCase(),
            content: tabsContent[0],
            disabled: false,
        },
        {
            label: translate('DATA_TAB.DETAILED_DATA.TITLE').toUpperCase(),
            content: tabsContent[1],
            disabled: requestData.kpiFilters.info === 'OEE' ? true : false,
        },
        {
            label: translate('DATA_TAB.DEFINITION.TITLE').toUpperCase(),
            content: tabsContent[2],
            disabled: requestData.kpiFilters.info === 'OEE' ? true : false,
        },
    ]

    return (
        <Box
            sx={{
                ...styles.wrapper,
                ...(pathName === 'site-view' && {
                    height: '100%',
                    overflowY: 'auto',
                    '& .MuiStack-root': {
                        position: 'sticky',
                        top: 25,
                        zIndex: 100,
                        backgroundColor: '#fff',
                    },
                }),
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    position: 'sticky',
                    top: 0,
                    zIndex: 100,
                    backgroundColor: '#fff',
                }}
            >
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <Typography variant="h6" sx={styles.title}>
                        {selectedKpiData?.symbol
                            ? String(selectedKpiData?.name).concat(' ', `(${selectedKpiData?.symbol})`)
                            : selectedKpiData?.name}
                        {isProductivityKpi && (
                            <Typography sx={{ color: '#083D5B', fontSize: 9 }}>
                                <HelpOutlineOutlinedIcon sx={{ color: '#083D5B', fontSize: 9 }} />
                                {translate('FOUNDATION_KPI_DETAILS.PRODUCTIVITY_KPIS_NOTE')}
                            </Typography>
                        )}
                    </Typography>
                    {pathName === 'site-view' ? (
                        <Tooltip
                            title={
                                globalViewData.length > 0 || data?.data?.length > 0 ? (
                                    <Typography style={{ fontSize: '14px', whiteSpace: 'pre-line' }}>
                                        {addsBreakLineOnText(
                                            globalViewData.find((kpi) => kpi.kpi.externalId === selectedKpi)?.kpi
                                                .description ?? 'No Data',
                                            'Update frequency:'
                                        )}
                                    </Typography>
                                ) : (
                                    <ClnCircularProgress />
                                )
                            }
                        >
                            <div>
                                <MatIcon
                                    icon="info"
                                    sx={{ color: '#083D5B', marginTop: '0.1rem', marginLeft: '5px' }}
                                    fontSize="18px"
                                />
                            </div>
                        </Tooltip>
                    ) : (
                        <Box></Box>
                    )}
                </Box>
                {currentTab === 0 ? (
                    <Box display="flex" alignItems="center" columnGap={1}>
                        <ClnButton
                            variant="text"
                            label={
                                !tableView ? translate('COMMONBUTTON.TABLE_VIEW') : translate('COMMONBUTTON.CHART_VIEW')
                            }
                            onClick={() => setTableView(!tableView)}
                        />
                        <MatIcon
                            icon="open_in_new"
                            sx={{
                                color: '#083D5B',
                                fontWeight: '500',
                                display: 'flex',
                                justifyContent: 'flex-end',
                                cursor: 'pointer',
                            }}
                            fontSize="24px"
                            onClick={() => setOpenModal(true)}
                        />
                    </Box>
                ) : (
                    <></>
                )}
            </Box>
            <ClnTabs value={currentTab} tabs={tabs} onChange={(_e, value) => setCurrentTab(value)} />
        </Box>
    )
}
