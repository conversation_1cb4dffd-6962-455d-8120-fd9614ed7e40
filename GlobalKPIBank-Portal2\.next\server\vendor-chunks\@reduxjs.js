"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@reduxjs";
exports.ids = ["vendor-chunks/@reduxjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/cjs/index.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./redux-toolkit.development.cjs */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/redux-toolkit.development.cjs\")\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlZHV4anMvdG9vbGtpdC9kaXN0L2Nqcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTtBQUNaLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSwySkFBMkQ7QUFDN0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nbG9iYWwta3BpLWFwcC8uL25vZGVfbW9kdWxlcy9AcmVkdXhqcy90b29sa2l0L2Rpc3QvY2pzL2luZGV4LmpzPzE2M2EiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcmVkdXgtdG9vbGtpdC5wcm9kdWN0aW9uLm1pbi5janMnKVxufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL3JlZHV4LXRvb2xraXQuZGV2ZWxvcG1lbnQuY2pzJylcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@reduxjs/toolkit/dist/query/cjs/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/query/cjs/index.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./rtk-query.development.cjs */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/cjs/rtk-query.development.cjs\")\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlZHV4anMvdG9vbGtpdC9kaXN0L3F1ZXJ5L2Nqcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTtBQUNaLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx5SkFBdUQ7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nbG9iYWwta3BpLWFwcC8uL25vZGVfbW9kdWxlcy9AcmVkdXhqcy90b29sa2l0L2Rpc3QvcXVlcnkvY2pzL2luZGV4LmpzPzlhZjYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcnRrLXF1ZXJ5LnByb2R1Y3Rpb24ubWluLmNqcycpXG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcnRrLXF1ZXJ5LmRldmVsb3BtZW50LmNqcycpXG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@reduxjs/toolkit/dist/query/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/cjs/index.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/query/react/cjs/index.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./rtk-query-react.development.cjs */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/cjs/rtk-query-react.development.cjs\")\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlZHV4anMvdG9vbGtpdC9kaXN0L3F1ZXJ5L3JlYWN0L2Nqcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTtBQUNaLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSwyS0FBNkQ7QUFDL0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nbG9iYWwta3BpLWFwcC8uL25vZGVfbW9kdWxlcy9AcmVkdXhqcy90b29sa2l0L2Rpc3QvcXVlcnkvcmVhY3QvY2pzL2luZGV4LmpzP2U3NjkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcnRrLXF1ZXJ5LXJlYWN0LnByb2R1Y3Rpb24ubWluLmNqcycpXG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcnRrLXF1ZXJ5LXJlYWN0LmRldmVsb3BtZW50LmNqcycpXG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/redux-toolkit.development.cjs":
/*!******************************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/cjs/redux-toolkit.development.cjs ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  ReducerType: () => ReducerType,\n  SHOULD_AUTOBATCH: () => SHOULD_AUTOBATCH,\n  TaskAbortError: () => TaskAbortError,\n  Tuple: () => Tuple,\n  addListener: () => addListener,\n  asyncThunkCreator: () => asyncThunkCreator,\n  autoBatchEnhancer: () => autoBatchEnhancer,\n  buildCreateSlice: () => buildCreateSlice,\n  clearAllListeners: () => clearAllListeners,\n  combineSlices: () => combineSlices,\n  configureStore: () => configureStore,\n  createAction: () => createAction,\n  createActionCreatorInvariantMiddleware: () => createActionCreatorInvariantMiddleware,\n  createAsyncThunk: () => createAsyncThunk,\n  createDraftSafeSelector: () => createDraftSafeSelector,\n  createDraftSafeSelectorCreator: () => createDraftSafeSelectorCreator,\n  createDynamicMiddleware: () => createDynamicMiddleware,\n  createEntityAdapter: () => createEntityAdapter,\n  createImmutableStateInvariantMiddleware: () => createImmutableStateInvariantMiddleware,\n  createListenerMiddleware: () => createListenerMiddleware,\n  createNextState: () => import_immer6.produce,\n  createReducer: () => createReducer,\n  createSelector: () => import_reselect2.createSelector,\n  createSelectorCreator: () => import_reselect2.createSelectorCreator,\n  createSerializableStateInvariantMiddleware: () => createSerializableStateInvariantMiddleware,\n  createSlice: () => createSlice,\n  current: () => import_immer6.current,\n  findNonSerializableValue: () => findNonSerializableValue,\n  formatProdErrorMessage: () => formatProdErrorMessage,\n  freeze: () => import_immer6.freeze,\n  isActionCreator: () => isActionCreator,\n  isAllOf: () => isAllOf,\n  isAnyOf: () => isAnyOf,\n  isAsyncThunkAction: () => isAsyncThunkAction,\n  isDraft: () => import_immer6.isDraft,\n  isFluxStandardAction: () => isFSA,\n  isFulfilled: () => isFulfilled,\n  isImmutableDefault: () => isImmutableDefault,\n  isPending: () => isPending,\n  isPlain: () => isPlain,\n  isRejected: () => isRejected,\n  isRejectedWithValue: () => isRejectedWithValue,\n  lruMemoize: () => import_reselect2.lruMemoize,\n  miniSerializeError: () => miniSerializeError,\n  nanoid: () => nanoid,\n  original: () => import_immer6.original,\n  prepareAutoBatched: () => prepareAutoBatched,\n  removeListener: () => removeListener,\n  unwrapResult: () => unwrapResult,\n  weakMapMemoize: () => import_reselect2.weakMapMemoize\n});\nmodule.exports = __toCommonJS(src_exports);\n__reExport(src_exports, __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/cjs/redux.cjs\"), module.exports);\nvar import_immer6 = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\nvar import_reselect2 = __webpack_require__(/*! reselect */ \"(ssr)/./node_modules/reselect/dist/cjs/reselect.cjs\");\n\n// src/createDraftSafeSelector.ts\nvar import_immer = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\nvar import_reselect = __webpack_require__(/*! reselect */ \"(ssr)/./node_modules/reselect/dist/cjs/reselect.cjs\");\nvar createDraftSafeSelectorCreator = (...args) => {\n  const createSelector2 = (0, import_reselect.createSelectorCreator)(...args);\n  const createDraftSafeSelector2 = Object.assign((...args2) => {\n    const selector = createSelector2(...args2);\n    const wrappedSelector = (value, ...rest) => selector((0, import_immer.isDraft)(value) ? (0, import_immer.current)(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector;\n  }, {\n    withTypes: () => createDraftSafeSelector2\n  });\n  return createDraftSafeSelector2;\n};\nvar createDraftSafeSelector = /* @__PURE__ */ createDraftSafeSelectorCreator(import_reselect.weakMapMemoize);\n\n// src/configureStore.ts\nvar import_redux4 = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/cjs/redux.cjs\");\n\n// src/devtoolsExtension.ts\nvar import_redux = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/cjs/redux.cjs\");\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function() {\n  if (arguments.length === 0) return void 0;\n  if (typeof arguments[0] === \"object\") return import_redux.compose;\n  return import_redux.compose.apply(null, arguments);\n};\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function() {\n  return function(noop3) {\n    return noop3;\n  };\n};\n\n// src/getDefaultMiddleware.ts\nvar import_redux_thunk = __webpack_require__(/*! redux-thunk */ \"(ssr)/./node_modules/redux-thunk/dist/cjs/redux-thunk.cjs\");\n\n// src/createAction.ts\nvar import_redux2 = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/cjs/redux.cjs\");\n\n// src/tsHelpers.ts\nvar hasMatchFunction = (v) => {\n  return v && typeof v.match === \"function\";\n};\n\n// src/createAction.ts\nfunction createAction(type, prepareAction) {\n  function actionCreator(...args) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error( false ? 0 : \"prepareAction did not return an object\");\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...\"meta\" in prepared && {\n          meta: prepared.meta\n        },\n        ...\"error\" in prepared && {\n          error: prepared.error\n        }\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = (action) => (0, import_redux2.isAction)(action) && action.type === type;\n  return actionCreator;\n}\nfunction isActionCreator(action) {\n  return typeof action === \"function\" && \"type\" in action && // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action);\n}\nfunction isFSA(action) {\n  return (0, import_redux2.isAction)(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key) {\n  return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\n}\n\n// src/actionCreatorInvariantMiddleware.ts\nfunction getMessage(type) {\n  const splitType = type ? `${type}`.split(\"/\") : [];\n  const actionName = splitType[splitType.length - 1] || \"actionCreator\";\n  return `Detected an action creator with type \"${type || \"unknown\"}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nfunction createActionCreatorInvariantMiddleware(options = {}) {\n  if (false) {}\n  const {\n    isActionCreator: isActionCreator2 = isActionCreator\n  } = options;\n  return () => (next) => (action) => {\n    if (isActionCreator2(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}\n\n// src/utils.ts\nvar import_immer2 = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\nfunction getTimeMeasureUtils(maxDelay, fnName) {\n  let elapsed = 0;\n  return {\n    measureTime(fn) {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nvar Tuple = class _Tuple extends Array {\n  constructor(...items) {\n    super(...items);\n    Object.setPrototypeOf(this, _Tuple.prototype);\n  }\n  static get [Symbol.species]() {\n    return _Tuple;\n  }\n  concat(...arr) {\n    return super.concat.apply(this, arr);\n  }\n  prepend(...arr) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new _Tuple(...arr[0].concat(this));\n    }\n    return new _Tuple(...arr.concat(this));\n  }\n};\nfunction freezeDraftable(val) {\n  return (0, import_immer2.isDraftable)(val) ? (0, import_immer2.produce)(val, () => {\n  }) : val;\n}\nfunction getOrInsertComputed(map, key, compute) {\n  if (map.has(key)) return map.get(key);\n  return map.set(key, compute(key)).get(key);\n}\n\n// src/immutableStateInvariantMiddleware.ts\nfunction isImmutableDefault(value) {\n  return typeof value !== \"object\" || value == null || Object.isFrozen(value);\n}\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\nfunction trackProperties(isImmutable, ignorePaths = [], obj, path = \"\", checkedObjects = /* @__PURE__ */ new Set()) {\n  const tracked = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + \".\" + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked;\n}\nfunction detectMutations(isImmutable, ignoredPaths = [], trackedProperty, obj, sameParentRef = false, path = \"\") {\n  const prevObj = trackedProperty ? trackedProperty.value : void 0;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n  const keysToDetect = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\nfunction createImmutableStateInvariantMiddleware(options = {}) {\n  if (false) {} else {\n    let stringify2 = function(obj, serializer, indent, decycler) {\n      return JSON.stringify(obj, getSerialize2(serializer, decycler), indent);\n    }, getSerialize2 = function(serializer, decycler) {\n      let stack = [], keys = [];\n      if (!decycler) decycler = function(_, value) {\n        if (stack[0] === value) return \"[Circular ~]\";\n        return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\n      };\n      return function(key, value) {\n        if (stack.length > 0) {\n          var thisPos = stack.indexOf(this);\n          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n          if (~stack.indexOf(value)) value = decycler.call(this, key, value);\n        } else stack.push(value);\n        return serializer == null ? value : serializer.call(this, key, value);\n      };\n    };\n    var stringify = stringify2, getSerialize = getSerialize2;\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return (next) => (action) => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error( false ? 0 : `A state mutation was detected between dispatches, in the path '${result.path || \"\"}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error( false ? 0 : `A state mutation was detected inside a dispatch, in the path: ${result.path || \"\"}. Take a look at the reducer(s) handling the action ${stringify2(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}\n\n// src/serializableStateInvariantMiddleware.ts\nvar import_redux3 = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/cjs/redux.cjs\");\nfunction isPlain(val) {\n  const type = typeof val;\n  return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || (0, import_redux3.isPlainObject)(val);\n}\nfunction findNonSerializableValue(value, path = \"\", isSerializable = isPlain, getEntries, ignoredPaths = [], cache) {\n  let foundNestedSerializable;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || \"<root>\",\n      value\n    };\n  }\n  if (typeof value !== \"object\" || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === \"object\") {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nfunction isNestedFrozen(value) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== \"object\" || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\nfunction createSerializableStateInvariantMiddleware(options = {}) {\n  if (false) {} else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = [\"meta.arg\", \"meta.baseQueryMeta\"],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache = !disableCache && WeakSet ? /* @__PURE__ */ new WeakSet() : void 0;\n    return (storeAPI) => (next) => (action) => {\n      if (!(0, import_redux3.isAction)(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}\n\n// src/getDefaultMiddleware.ts\nfunction isBoolean(x) {\n  return typeof x === \"boolean\";\n}\nvar buildGetDefaultMiddleware = () => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(import_redux_thunk.thunk);\n    } else {\n      middlewareArray.push((0, import_redux_thunk.withExtraArgument)(thunk.extraArgument));\n    }\n  }\n  if (true) {\n    if (immutableCheck) {\n      let immutableOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n    }\n    if (serializableCheck) {\n      let serializableOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray;\n};\n\n// src/autoBatchEnhancer.ts\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\nvar prepareAutoBatched = () => (payload) => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nvar createQueueWithTimer = (timeout) => {\n  return (notify) => {\n    setTimeout(notify, timeout);\n  };\n};\nvar autoBatchEnhancer = (options = {\n  type: \"raf\"\n}) => (next) => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = /* @__PURE__ */ new Set();\n  const queueCallback = options.type === \"tick\" ? queueMicrotask : options.type === \"raf\" ? (\n    // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n    typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10)\n  ) : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach((l) => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener2) {\n      const wrappedListener = () => notifying && listener2();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener2);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener2);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action) {\n      try {\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        return store.dispatch(action);\n      } finally {\n        notifying = true;\n      }\n    }\n  });\n};\n\n// src/getDefaultEnhancers.ts\nvar buildGetDefaultEnhancers = (middlewareEnhancer) => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === \"object\" ? autoBatch : void 0));\n  }\n  return enhancerArray;\n};\n\n// src/configureStore.ts\nfunction configureStore(options) {\n  const getDefaultMiddleware = buildGetDefaultMiddleware();\n  const {\n    reducer = void 0,\n    middleware,\n    devTools = true,\n    preloadedState = void 0,\n    enhancers = void 0\n  } = options || {};\n  let rootReducer;\n  if (typeof reducer === \"function\") {\n    rootReducer = reducer;\n  } else if ((0, import_redux4.isPlainObject)(reducer)) {\n    rootReducer = (0, import_redux4.combineReducers)(reducer);\n  } else {\n    throw new Error( false ? 0 : \"`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers\");\n  }\n  if (middleware && typeof middleware !== \"function\") {\n    throw new Error( false ? 0 : \"`middleware` field must be a callback\");\n  }\n  let finalMiddleware;\n  if (typeof middleware === \"function\") {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if (!Array.isArray(finalMiddleware)) {\n      throw new Error( false ? 0 : \"when using a middleware builder function, an array of middleware must be returned\");\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if (finalMiddleware.some((item) => typeof item !== \"function\")) {\n    throw new Error( false ? 0 : \"each middleware provided to configureStore must be a function\");\n  }\n  let finalCompose = import_redux4.compose;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: true,\n      ...typeof devTools === \"object\" && devTools\n    });\n  }\n  const middlewareEnhancer = (0, import_redux4.applyMiddleware)(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers(middlewareEnhancer);\n  if (enhancers && typeof enhancers !== \"function\") {\n    throw new Error( false ? 0 : \"`enhancers` field must be a callback\");\n  }\n  let storeEnhancers = typeof enhancers === \"function\" ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if (!Array.isArray(storeEnhancers)) {\n    throw new Error( false ? 0 : \"`enhancers` callback must return an array\");\n  }\n  if (storeEnhancers.some((item) => typeof item !== \"function\")) {\n    throw new Error( false ? 0 : \"each enhancer provided to configureStore must be a function\");\n  }\n  if (finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error(\"middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`\");\n  }\n  const composedEnhancer = finalCompose(...storeEnhancers);\n  return (0, import_redux4.createStore)(rootReducer, preloadedState, composedEnhancer);\n}\n\n// src/createReducer.ts\nvar import_immer3 = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\n\n// src/mapBuilders.ts\nfunction executeReducerBuilderCallback(builderCallback) {\n  const actionsMap = {};\n  const actionMatchers = [];\n  let defaultCaseReducer;\n  const builder = {\n    addCase(typeOrActionCreator, reducer) {\n      if (true) {\n        if (actionMatchers.length > 0) {\n          throw new Error( false ? 0 : \"`builder.addCase` should only be called before calling `builder.addMatcher`\");\n        }\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error( false ? 0 : \"`builder.addCase` cannot be called with an empty action type\");\n      }\n      if (type in actionsMap) {\n        throw new Error( false ? 0 : `\\`builder.addCase\\` cannot be called with two reducers for the same action type '${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher(matcher, reducer) {\n      if (true) {\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer) {\n      if (true) {\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addDefaultCase` can only be called once\");\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}\n\n// src/createReducer.ts\nfunction isStateFunction(x) {\n  return typeof x === \"function\";\n}\nfunction createReducer(initialState, mapOrBuilderCallback) {\n  if (true) {\n    if (typeof mapOrBuilderCallback === \"object\") {\n      throw new Error( false ? 0 : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n  let getInitialState;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action) {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer: reducer2\n    }) => reducer2)];\n    if (caseReducers.filter((cr) => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer) => {\n      if (caseReducer) {\n        if ((0, import_immer3.isDraft)(previousState)) {\n          const draft = previousState;\n          const result = caseReducer(draft, action);\n          if (result === void 0) {\n            return previousState;\n          }\n          return result;\n        } else if (!(0, import_immer3.isDraftable)(previousState)) {\n          const result = caseReducer(previousState, action);\n          if (result === void 0) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error(\"A case reducer on a non-draftable value must not return undefined\");\n          }\n          return result;\n        } else {\n          return (0, import_immer3.produce)(previousState, (draft) => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer;\n}\n\n// src/matchers.ts\nvar matches = (matcher, action) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\nfunction isAnyOf(...matchers) {\n  return (action) => {\n    return matchers.some((matcher) => matches(matcher, action));\n  };\n}\nfunction isAllOf(...matchers) {\n  return (action) => {\n    return matchers.every((matcher) => matches(matcher, action));\n  };\n}\nfunction hasExpectedRequestMetadata(action, validStatus) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === \"string\";\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a) {\n  return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\n}\nfunction isPending(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.pending));\n}\nfunction isRejected(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.rejected));\n}\nfunction isRejectedWithValue(...asyncThunks) {\n  const hasFlag = (action) => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nfunction isFulfilled(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"fulfilled\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.fulfilled));\n}\nfunction isAsyncThunkAction(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap((asyncThunk) => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}\n\n// src/nanoid.ts\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\nvar nanoid = (size = 21) => {\n  let id = \"\";\n  let i = size;\n  while (i--) {\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};\n\n// src/createAsyncThunk.ts\nvar commonProperties = [\"name\", \"message\", \"stack\", \"code\"];\nvar RejectWithValue = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar FulfillWithMeta = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar miniSerializeError = (value) => {\n  if (typeof value === \"object\" && value !== null) {\n    const simpleError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === \"string\") {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nvar createAsyncThunk = /* @__PURE__ */ (() => {\n  function createAsyncThunk2(typePrefix, payloadCreator, options) {\n    const fulfilled = createAction(typePrefix + \"/fulfilled\", (payload, requestId, arg, meta) => ({\n      payload,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"fulfilled\"\n      }\n    }));\n    const pending = createAction(typePrefix + \"/pending\", (requestId, arg, meta) => ({\n      payload: void 0,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"pending\"\n      }\n    }));\n    const rejected = createAction(typePrefix + \"/rejected\", (error, requestId, arg, payload, meta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: \"rejected\",\n        aborted: error?.name === \"AbortError\",\n        condition: error?.name === \"ConditionError\"\n      }\n    }));\n    function actionCreator(arg) {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler;\n        let abortReason;\n        function abort(reason) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        const promise = async function() {\n          let finalAction;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              throw {\n                name: \"ConditionError\",\n                message: \"Aborted due to condition callback returning false.\"\n              };\n            }\n            const abortedPromise = new Promise((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: \"AbortError\",\n                  message: abortReason || \"Aborted\"\n                });\n              };\n              abortController.signal.addEventListener(\"abort\", abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })));\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: (value, meta) => {\n                return new RejectWithValue(value, meta);\n              },\n              fulfillWithValue: (value, meta) => {\n                return new FulfillWithMeta(value, meta);\n              }\n            })).then((result) => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener(\"abort\", abortHandler);\n            }\n          }\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk2.withTypes = () => createAsyncThunk2;\n  return createAsyncThunk2;\n})();\nfunction unwrapResult(action) {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\nfunction isThenable(value) {\n  return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\n}\n\n// src/createSlice.ts\nvar asyncThunkSymbol = /* @__PURE__ */ Symbol.for(\"rtk-slice-createasyncthunk\");\nvar asyncThunkCreator = {\n  [asyncThunkSymbol]: createAsyncThunk\n};\nvar ReducerType = /* @__PURE__ */ ((ReducerType2) => {\n  ReducerType2[\"reducer\"] = \"reducer\";\n  ReducerType2[\"reducerWithPrepare\"] = \"reducerWithPrepare\";\n  ReducerType2[\"asyncThunk\"] = \"asyncThunk\";\n  return ReducerType2;\n})(ReducerType || {});\nfunction getType(slice, actionKey) {\n  return `${slice}/${actionKey}`;\n}\nfunction buildCreateSlice({\n  creators\n} = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice2(options) {\n    const {\n      name,\n      reducerPath = name\n    } = options;\n    if (!name) {\n      throw new Error( false ? 0 : \"`name` is a required option for createSlice\");\n    }\n    if (typeof process !== \"undefined\" && true) {\n      if (options.initialState === void 0) {\n        console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\n      }\n    }\n    const reducers = (typeof options.reducers === \"function\" ? options.reducers(buildReducerCreators()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods = {\n      addCase(typeOrActionCreator, reducer2) {\n        const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error( false ? 0 : \"`context.addCase` cannot be called with an empty action type\");\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error( false ? 0 : \"`context.addCase` cannot be called with two reducers for the same action type: \" + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer2;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer2) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer: reducer2\n        });\n        return contextMethods;\n      },\n      exposeAction(name2, actionCreator) {\n        context.actionCreators[name2] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name2, reducer2) {\n        context.sliceCaseReducersByName[name2] = reducer2;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach((reducerName) => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === \"function\"\n      };\n      if (isAsyncThunkSliceReducerDefinition(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition(reducerDetails, reducerDefinition, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (true) {\n        if (typeof options.extraReducers === \"object\") {\n          throw new Error( false ? 0 : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = void 0] = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, (builder) => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key]);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = (state) => state;\n    const injectedSelectorCache = /* @__PURE__ */ new Map();\n    let _reducer;\n    function reducer(state, action) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps(reducerPath2, injected = false) {\n      function selectSlice(state) {\n        let sliceState = state[reducerPath2];\n        if (typeof sliceState === \"undefined\") {\n          if (injected) {\n            sliceState = getInitialState();\n          } else if (true) {\n            throw new Error( false ? 0 : \"selectSlice returned undefined for an uninjected slice reducer\");\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => /* @__PURE__ */ new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map = {};\n          for (const [name2, selector] of Object.entries(options.selectors ?? {})) {\n            map[name2] = wrapSelector(selector, selectState, getInitialState, injected);\n          }\n          return map;\n        });\n      }\n      return {\n        reducerPath: reducerPath2,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice = {\n      name,\n      reducer,\n      actions: context.actionCreators,\n      caseReducers: context.sliceCaseReducersByName,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        };\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector(selector, selectState, getInitialState, injected) {\n  function wrapper(rootState, ...args) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === \"undefined\") {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (true) {\n        throw new Error( false ? 0 : \"selectState returned undefined for an uninjected slice reducer\");\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper;\n}\nvar createSlice = /* @__PURE__ */ buildCreateSlice();\nfunction buildReducerCreators() {\n  function asyncThunk(payloadCreator, config) {\n    return {\n      _reducerDefinitionType: \"asyncThunk\" /* asyncThunk */,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: \"reducer\" /* reducer */\n      });\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: \"reducerWithPrepare\" /* reducerWithPrepare */,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk\n  };\n}\nfunction handleNormalReducerDefinition({\n  type,\n  reducerName,\n  createNotation\n}, maybeReducerWithPrepare, context) {\n  let caseReducer;\n  let prepareCallback;\n  if (\"reducer\" in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error( false ? 0 : \"Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.\");\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"asyncThunk\" /* asyncThunk */;\n}\nfunction isCaseReducerWithPrepareDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"reducerWithPrepare\" /* reducerWithPrepare */;\n}\nfunction handleThunkCaseReducerDefinition({\n  type,\n  reducerName\n}, reducerDefinition, context, cAT) {\n  if (!cAT) {\n    throw new Error( false ? 0 : \"Cannot use `create.asyncThunk` in the built-in `createSlice`. Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.\");\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {\n}\n\n// src/entities/entity_state.ts\nfunction getInitialEntityState() {\n  return {\n    ids: [],\n    entities: {}\n  };\n}\nfunction createInitialStateFactory(stateAdapter) {\n  function getInitialState(additionalState = {}, entities) {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}\n\n// src/entities/state_selectors.ts\nfunction createSelectorsFactory() {\n  function getSelectors(selectState, options = {}) {\n    const {\n      createSelector: createSelector2 = createDraftSafeSelector\n    } = options;\n    const selectIds = (state) => state.ids;\n    const selectEntities = (state) => state.entities;\n    const selectAll = createSelector2(selectIds, selectEntities, (ids, entities) => ids.map((id) => entities[id]));\n    const selectId = (_, id) => id;\n    const selectById = (entities, id) => entities[id];\n    const selectTotal = createSelector2(selectIds, (ids) => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector2(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector2(selectState, selectEntities);\n    return {\n      selectIds: createSelector2(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector2(selectState, selectAll),\n      selectTotal: createSelector2(selectState, selectTotal),\n      selectById: createSelector2(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}\n\n// src/entities/state_adapter.ts\nvar import_immer4 = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\nvar isDraftTyped = import_immer4.isDraft;\nfunction createSingleArgumentStateOperator(mutator) {\n  const operator = createStateOperator((_, state) => mutator(state));\n  return function operation(state) {\n    return operator(state, void 0);\n  };\n}\nfunction createStateOperator(mutator) {\n  return function operation(state, arg) {\n    function isPayloadActionArgument(arg2) {\n      return isFSA(arg2);\n    }\n    const runMutator = (draft) => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped(state)) {\n      runMutator(state);\n      return state;\n    }\n    return (0, import_immer4.produce)(state, runMutator);\n  };\n}\n\n// src/entities/utils.ts\nvar import_immer5 = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\nfunction selectIdValue(entity, selectId) {\n  const key = selectId(entity);\n  if (key === void 0) {\n    console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\n  }\n  return key;\n}\nfunction ensureEntitiesArray(entities) {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nfunction getCurrent(value) {\n  return (0, import_immer5.isDraft)(value) ? (0, import_immer5.current)(value) : value;\n}\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set(existingIdsArray);\n  const added = [];\n  const updated = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}\n\n// src/entities/unsorted_state_adapter.ts\nfunction createUnsortedStateAdapter(selectId) {\n  function addOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key);\n    state.entities[key] = entity;\n  }\n  function addManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key);\n    }\n    ;\n    state.entities[key] = entity;\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {};\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key, state) {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys, state) {\n    let didMutate = false;\n    keys.forEach((key) => {\n      if (key in state.entities) {\n        delete state.entities[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = state.ids.filter((id) => id in state.entities);\n    }\n  }\n  function removeAllMutably(state) {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys, update, state) {\n    const original3 = state.entities[update.id];\n    if (original3 === void 0) {\n      return false;\n    }\n    const updated = Object.assign({}, original3, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete state.entities[update.id];\n    }\n    ;\n    state.entities[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    const newKeys = {};\n    const updatesPerEntity = {};\n    updates.forEach((update) => {\n      if (update.id in state.entities) {\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter((update) => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map((e) => selectIdValue(e, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    updateManyMutably(updated, state);\n    addManyMutably(added, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}\n\n// src/entities/sorted_state_adapter.ts\nfunction findInsertIndex(sortedItems, item, comparisonFunction) {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nfunction insert(sortedItems, item, comparisonFunction) {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nfunction createSortedStateAdapter(selectId, comparer) {\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity, state) {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities, state, existingIds) {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter((model) => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity, state) {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete state.entities[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {};\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity = state.entities[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        replacedIds = true;\n        delete state.entities[update.id];\n        const oldIndex = state.ids.indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        state.entities[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n  }\n  function areArraysEqual(a, b) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  const mergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities;\n    let ids = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}\n\n// src/entities/create_adapter.ts\nfunction createEntityAdapter(options = {}) {\n  const {\n    selectId,\n    sortComparer\n  } = {\n    sortComparer: false,\n    selectId: (instance) => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}\n\n// src/listenerMiddleware/index.ts\nvar import_redux5 = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/cjs/redux.cjs\");\n\n// src/listenerMiddleware/exceptions.ts\nvar task = \"task\";\nvar listener = \"listener\";\nvar completed = \"completed\";\nvar cancelled = \"cancelled\";\nvar taskCancelled = `task-${cancelled}`;\nvar taskCompleted = `task-${completed}`;\nvar listenerCancelled = `${listener}-${cancelled}`;\nvar listenerCompleted = `${listener}-${completed}`;\nvar TaskAbortError = class {\n  constructor(code) {\n    this.code = code;\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n  name = \"TaskAbortError\";\n  message;\n};\n\n// src/listenerMiddleware/utils.ts\nvar assertFunction = (func, expected) => {\n  if (typeof func !== \"function\") {\n    throw new TypeError( false ? 0 : `${expected} is not a function`);\n  }\n};\nvar noop2 = () => {\n};\nvar catchRejection = (promise, onError = noop2) => {\n  promise.catch(onError);\n  return promise;\n};\nvar addAbortSignalListener = (abortSignal, callback) => {\n  abortSignal.addEventListener(\"abort\", callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener(\"abort\", callback);\n};\nvar abortControllerWithReason = (abortController, reason) => {\n  const signal = abortController.signal;\n  if (signal.aborted) {\n    return;\n  }\n  if (!(\"reason\" in signal)) {\n    Object.defineProperty(signal, \"reason\", {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  abortController.abort(reason);\n};\n\n// src/listenerMiddleware/task.ts\nvar validateActive = (signal) => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal;\n    throw new TaskAbortError(reason);\n  }\n};\nfunction raceWithSignal(signal, promise) {\n  let cleanup = noop2;\n  return new Promise((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    cleanup = noop2;\n  });\n}\nvar runTask = async (task2, cleanUp) => {\n  try {\n    await Promise.resolve();\n    const value = await task2();\n    return {\n      status: \"ok\",\n      value\n    };\n  } catch (error) {\n    return {\n      status: error instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\nvar createPause = (signal) => {\n  return (promise) => {\n    return catchRejection(raceWithSignal(signal, promise).then((output) => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\nvar createDelay = (signal) => {\n  const pause = createPause(signal);\n  return (timeoutMs) => {\n    return pause(new Promise((resolve) => setTimeout(resolve, timeoutMs)));\n  };\n};\n\n// src/listenerMiddleware/index.ts\nvar {\n  assign\n} = Object;\nvar INTERNAL_NIL_TOKEN = {};\nvar alm = \"listenerMiddleware\";\nvar createFork = (parentAbortSignal, parentBlockingPromises) => {\n  const linkControllers = (controller) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return (taskExecutor, opts) => {\n    assertFunction(taskExecutor, \"taskExecutor\");\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask(async () => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result2 = await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      });\n      validateActive(childAbortController.signal);\n      return result2;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop2));\n    }\n    return {\n      result: createPause(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nvar createTakePattern = (startListening, signal) => {\n  const take = async (predicate, timeout) => {\n    validateActive(signal);\n    let unsubscribe = () => {\n    };\n    const tuplePromise = new Promise((resolve, reject) => {\n      let stopListening = startListening({\n        predicate,\n        effect: (action, listenerApi) => {\n          listenerApi.unsubscribe();\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise((resolve) => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      unsubscribe();\n    }\n  };\n  return (predicate, timeout) => catchRejection(take(predicate, timeout));\n};\nvar getListenerEntryPropsFrom = (options) => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {\n  } else {\n    throw new Error( false ? 0 : \"Creating or removing a listener requires one of the known fields for matching an action\");\n  }\n  assertFunction(effect, \"options.listener\");\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\nvar createListenerEntry = /* @__PURE__ */ assign((options) => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: /* @__PURE__ */ new Set(),\n    unsubscribe: () => {\n      throw new Error( false ? 0 : \"Unsubscribe not initialized\");\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n});\nvar findListenerEntry = (listenerMap, options) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find((entry) => {\n    const matchPredicateOrType = typeof type === \"string\" ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nvar cancelActiveListeners = (entry) => {\n  entry.pending.forEach((controller) => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nvar createClearListenerMiddleware = (listenerMap) => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\nvar safelyNotifyError = (errorHandler, errorToNotify, errorInfo) => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\nvar addListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/add`), {\n  withTypes: () => addListener\n});\nvar clearAllListeners = /* @__PURE__ */ createAction(`${alm}/removeAll`);\nvar removeListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n});\nvar defaultErrorHandler = (...args) => {\n  console.error(`${alm}/error`, ...args);\n};\nvar createListenerMiddleware = (middlewareOptions = {}) => {\n  const listenerMap = /* @__PURE__ */ new Map();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, \"onError\");\n  const insertEntry = (entry) => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return (cancelOptions) => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options);\n    return insertEntry(entry);\n  };\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry, action, api, getOriginalState) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening, internalTaskController.signal);\n    const autoJoinPromises = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(\n        action,\n        // Use assign() rather than ... to avoid extra helper functions added to bundle\n        assign({}, api, {\n          getOriginalState,\n          condition: (predicate, timeout) => take(predicate, timeout).then(Boolean),\n          take,\n          delay: createDelay(internalTaskController.signal),\n          pause: createPause(internalTaskController.signal),\n          extra,\n          signal: internalTaskController.signal,\n          fork: createFork(internalTaskController.signal, autoJoinPromises),\n          unsubscribe: entry.unsubscribe,\n          subscribe: () => {\n            listenerMap.set(entry.id, entry);\n          },\n          cancelActiveListeners: () => {\n            entry.pending.forEach((controller, _, set) => {\n              if (controller !== internalTaskController) {\n                abortControllerWithReason(controller, listenerCancelled);\n                set.delete(controller);\n              }\n            });\n          },\n          cancel: () => {\n            abortControllerWithReason(internalTaskController, listenerCancelled);\n            entry.pending.delete(internalTaskController);\n          },\n          throwIfCancelled: () => {\n            validateActive(internalTaskController.signal);\n          }\n        })\n      ));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: \"effect\"\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted);\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware = (api) => (next) => (action) => {\n    if (!(0, import_redux5.isAction)(action)) {\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n    let originalState = api.getState();\n    const getOriginalState = () => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error( false ? 0 : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState;\n    };\n    let result;\n    try {\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: \"predicate\"\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  };\n};\n\n// src/dynamicMiddleware/index.ts\nvar import_redux6 = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/cjs/redux.cjs\");\nvar createMiddlewareEntry = (middleware) => ({\n  middleware,\n  applied: /* @__PURE__ */ new Map()\n});\nvar matchInstance = (instanceId) => (action) => action?.meta?.instanceId === instanceId;\nvar createDynamicMiddleware = () => {\n  const instanceId = nanoid();\n  const middlewareMap = /* @__PURE__ */ new Map();\n  const withMiddleware = Object.assign(createAction(\"dynamicMiddleware/add\", (...middlewares) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  });\n  const addMiddleware = Object.assign(function addMiddleware2(...middlewares) {\n    middlewares.forEach((middleware2) => {\n      getOrInsertComputed(middlewareMap, middleware2, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  });\n  const getFinalMiddleware = (api) => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map((entry) => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return (0, import_redux6.compose)(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware = (api) => (next) => (action) => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};\n\n// src/combineSlices.ts\nvar import_redux7 = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/cjs/redux.cjs\");\nvar isSliceLike = (maybeSliceLike) => \"reducerPath\" in maybeSliceLike && typeof maybeSliceLike.reducerPath === \"string\";\nvar getReducers = (slices) => slices.flatMap((sliceOrMap) => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer]] : Object.entries(sliceOrMap));\nvar ORIGINAL_STATE = Symbol.for(\"rtk-state-proxy-original\");\nvar isStateProxy = (value) => !!value && !!value[ORIGINAL_STATE];\nvar stateProxyMap = /* @__PURE__ */ new WeakMap();\nvar createStateProxy = (state, reducerMap) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === \"undefined\") {\n      const reducer = reducerMap[prop.toString()];\n      if (reducer) {\n        const reducerResult = reducer(void 0, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === \"undefined\") {\n          throw new Error( false ? 0 : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n        }\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n}));\nvar original = (state) => {\n  if (!isStateProxy(state)) {\n    throw new Error( false ? 0 : \"original must be used on state Proxy\");\n  }\n  return state[ORIGINAL_STATE];\n};\nvar noopReducer = (state = {}) => state;\nfunction combineSlices(...slices) {\n  const reducerMap = Object.fromEntries(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? (0, import_redux7.combineReducers)(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state, action) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const inject = (slice, config = {}) => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== \"undefined\" && true) {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector(selectorFn, selectState) {\n    return function selector2(state, ...args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state, ...args) : state, reducerMap), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  });\n}\n\n// src/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=redux-toolkit.development.cjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/redux-toolkit.development.cjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@reduxjs/toolkit/dist/query/cjs/rtk-query.development.cjs":
/*!********************************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/query/cjs/rtk-query.development.cjs ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/query/index.ts\nvar query_exports = {};\n__export(query_exports, {\n  QueryStatus: () => QueryStatus,\n  _NEVER: () => _NEVER,\n  buildCreateApi: () => buildCreateApi,\n  copyWithStructuralSharing: () => copyWithStructuralSharing,\n  coreModule: () => coreModule,\n  coreModuleName: () => coreModuleName,\n  createApi: () => createApi,\n  defaultSerializeQueryArgs: () => defaultSerializeQueryArgs,\n  fakeBaseQuery: () => fakeBaseQuery,\n  fetchBaseQuery: () => fetchBaseQuery,\n  retry: () => retry,\n  setupListeners: () => setupListeners,\n  skipToken: () => skipToken\n});\nmodule.exports = __toCommonJS(query_exports);\n\n// src/query/core/apiState.ts\nvar QueryStatus = /* @__PURE__ */ ((QueryStatus2) => {\n  QueryStatus2[\"uninitialized\"] = \"uninitialized\";\n  QueryStatus2[\"pending\"] = \"pending\";\n  QueryStatus2[\"fulfilled\"] = \"fulfilled\";\n  QueryStatus2[\"rejected\"] = \"rejected\";\n  return QueryStatus2;\n})(QueryStatus || {});\nfunction getRequestStatusFlags(status) {\n  return {\n    status,\n    isUninitialized: status === \"uninitialized\" /* uninitialized */,\n    isLoading: status === \"pending\" /* pending */,\n    isSuccess: status === \"fulfilled\" /* fulfilled */,\n    isError: status === \"rejected\" /* rejected */\n  };\n}\n\n// src/query/core/rtkImports.ts\nvar import_toolkit = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js\");\n\n// src/query/utils/copyWithStructuralSharing.ts\nvar isPlainObject2 = import_toolkit.isPlainObject;\nfunction copyWithStructuralSharing(oldObj, newObj) {\n  if (oldObj === newObj || !(isPlainObject2(oldObj) && isPlainObject2(newObj) || Array.isArray(oldObj) && Array.isArray(newObj))) {\n    return newObj;\n  }\n  const newKeys = Object.keys(newObj);\n  const oldKeys = Object.keys(oldObj);\n  let isSameObject = newKeys.length === oldKeys.length;\n  const mergeObj = Array.isArray(newObj) ? [] : {};\n  for (const key of newKeys) {\n    mergeObj[key] = copyWithStructuralSharing(oldObj[key], newObj[key]);\n    if (isSameObject) isSameObject = oldObj[key] === mergeObj[key];\n  }\n  return isSameObject ? oldObj : mergeObj;\n}\n\n// src/query/utils/countObjectKeys.ts\nfunction countObjectKeys(obj) {\n  let count = 0;\n  for (const _key in obj) {\n    count++;\n  }\n  return count;\n}\n\n// src/query/utils/flatten.ts\nvar flatten = (arr) => [].concat(...arr);\n\n// src/query/utils/isAbsoluteUrl.ts\nfunction isAbsoluteUrl(url) {\n  return new RegExp(`(^|:)//`).test(url);\n}\n\n// src/query/utils/isDocumentVisible.ts\nfunction isDocumentVisible() {\n  if (typeof document === \"undefined\") {\n    return true;\n  }\n  return document.visibilityState !== \"hidden\";\n}\n\n// src/query/utils/isNotNullish.ts\nfunction isNotNullish(v) {\n  return v != null;\n}\n\n// src/query/utils/isOnline.ts\nfunction isOnline() {\n  return typeof navigator === \"undefined\" ? true : navigator.onLine === void 0 ? true : navigator.onLine;\n}\n\n// src/query/utils/joinUrls.ts\nvar withoutTrailingSlash = (url) => url.replace(/\\/$/, \"\");\nvar withoutLeadingSlash = (url) => url.replace(/^\\//, \"\");\nfunction joinUrls(base, url) {\n  if (!base) {\n    return url;\n  }\n  if (!url) {\n    return base;\n  }\n  if (isAbsoluteUrl(url)) {\n    return url;\n  }\n  const delimiter = base.endsWith(\"/\") || !url.startsWith(\"?\") ? \"/\" : \"\";\n  base = withoutTrailingSlash(base);\n  url = withoutLeadingSlash(url);\n  return `${base}${delimiter}${url}`;\n}\n\n// src/query/utils/getOrInsert.ts\nfunction getOrInsert(map, key, value) {\n  if (map.has(key)) return map.get(key);\n  return map.set(key, value).get(key);\n}\n\n// src/query/fetchBaseQuery.ts\nvar defaultFetchFn = (...args) => fetch(...args);\nvar defaultValidateStatus = (response) => response.status >= 200 && response.status <= 299;\nvar defaultIsJsonContentType = (headers) => (\n  /*applicat*/\n  /ion\\/(vnd\\.api\\+)?json/.test(headers.get(\"content-type\") || \"\")\n);\nfunction stripUndefined(obj) {\n  if (!(0, import_toolkit.isPlainObject)(obj)) {\n    return obj;\n  }\n  const copy = {\n    ...obj\n  };\n  for (const [k, v] of Object.entries(copy)) {\n    if (v === void 0) delete copy[k];\n  }\n  return copy;\n}\nfunction fetchBaseQuery({\n  baseUrl,\n  prepareHeaders = (x) => x,\n  fetchFn = defaultFetchFn,\n  paramsSerializer,\n  isJsonContentType = defaultIsJsonContentType,\n  jsonContentType = \"application/json\",\n  jsonReplacer,\n  timeout: defaultTimeout,\n  responseHandler: globalResponseHandler,\n  validateStatus: globalValidateStatus,\n  ...baseFetchOptions\n} = {}) {\n  if (typeof fetch === \"undefined\" && fetchFn === defaultFetchFn) {\n    console.warn(\"Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments.\");\n  }\n  return async (arg, api, extraOptions) => {\n    const {\n      getState,\n      extra,\n      endpoint,\n      forced,\n      type\n    } = api;\n    let meta;\n    let {\n      url,\n      headers = new Headers(baseFetchOptions.headers),\n      params = void 0,\n      responseHandler = globalResponseHandler ?? \"json\",\n      validateStatus = globalValidateStatus ?? defaultValidateStatus,\n      timeout = defaultTimeout,\n      ...rest\n    } = typeof arg == \"string\" ? {\n      url: arg\n    } : arg;\n    let abortController, signal = api.signal;\n    if (timeout) {\n      abortController = new AbortController();\n      api.signal.addEventListener(\"abort\", abortController.abort);\n      signal = abortController.signal;\n    }\n    let config = {\n      ...baseFetchOptions,\n      signal,\n      ...rest\n    };\n    headers = new Headers(stripUndefined(headers));\n    config.headers = await prepareHeaders(headers, {\n      getState,\n      arg,\n      extra,\n      endpoint,\n      forced,\n      type,\n      extraOptions\n    }) || headers;\n    const isJsonifiable = (body) => typeof body === \"object\" && ((0, import_toolkit.isPlainObject)(body) || Array.isArray(body) || typeof body.toJSON === \"function\");\n    if (!config.headers.has(\"content-type\") && isJsonifiable(config.body)) {\n      config.headers.set(\"content-type\", jsonContentType);\n    }\n    if (isJsonifiable(config.body) && isJsonContentType(config.headers)) {\n      config.body = JSON.stringify(config.body, jsonReplacer);\n    }\n    if (params) {\n      const divider = ~url.indexOf(\"?\") ? \"&\" : \"?\";\n      const query = paramsSerializer ? paramsSerializer(params) : new URLSearchParams(stripUndefined(params));\n      url += divider + query;\n    }\n    url = joinUrls(baseUrl, url);\n    const request = new Request(url, config);\n    const requestClone = new Request(url, config);\n    meta = {\n      request: requestClone\n    };\n    let response, timedOut = false, timeoutId = abortController && setTimeout(() => {\n      timedOut = true;\n      abortController.abort();\n    }, timeout);\n    try {\n      response = await fetchFn(request);\n    } catch (e) {\n      return {\n        error: {\n          status: timedOut ? \"TIMEOUT_ERROR\" : \"FETCH_ERROR\",\n          error: String(e)\n        },\n        meta\n      };\n    } finally {\n      if (timeoutId) clearTimeout(timeoutId);\n      abortController?.signal.removeEventListener(\"abort\", abortController.abort);\n    }\n    const responseClone = response.clone();\n    meta.response = responseClone;\n    let resultData;\n    let responseText = \"\";\n    try {\n      let handleResponseError;\n      await Promise.all([\n        handleResponse(response, responseHandler).then((r) => resultData = r, (e) => handleResponseError = e),\n        // see https://github.com/node-fetch/node-fetch/issues/665#issuecomment-538995182\n        // we *have* to \"use up\" both streams at the same time or they will stop running in node-fetch scenarios\n        responseClone.text().then((r) => responseText = r, () => {\n        })\n      ]);\n      if (handleResponseError) throw handleResponseError;\n    } catch (e) {\n      return {\n        error: {\n          status: \"PARSING_ERROR\",\n          originalStatus: response.status,\n          data: responseText,\n          error: String(e)\n        },\n        meta\n      };\n    }\n    return validateStatus(response, resultData) ? {\n      data: resultData,\n      meta\n    } : {\n      error: {\n        status: response.status,\n        data: resultData\n      },\n      meta\n    };\n  };\n  async function handleResponse(response, responseHandler) {\n    if (typeof responseHandler === \"function\") {\n      return responseHandler(response);\n    }\n    if (responseHandler === \"content-type\") {\n      responseHandler = isJsonContentType(response.headers) ? \"json\" : \"text\";\n    }\n    if (responseHandler === \"json\") {\n      const text = await response.text();\n      return text.length ? JSON.parse(text) : null;\n    }\n    return response.text();\n  }\n}\n\n// src/query/HandledError.ts\nvar HandledError = class {\n  constructor(value, meta = void 0) {\n    this.value = value;\n    this.meta = meta;\n  }\n};\n\n// src/query/retry.ts\nasync function defaultBackoff(attempt = 0, maxRetries = 5) {\n  const attempts = Math.min(attempt, maxRetries);\n  const timeout = ~~((Math.random() + 0.4) * (300 << attempts));\n  await new Promise((resolve) => setTimeout((res) => resolve(res), timeout));\n}\nfunction fail(error, meta) {\n  throw Object.assign(new HandledError({\n    error,\n    meta\n  }), {\n    throwImmediately: true\n  });\n}\nvar EMPTY_OPTIONS = {};\nvar retryWithBackoff = (baseQuery, defaultOptions) => async (args, api, extraOptions) => {\n  const possibleMaxRetries = [5, (defaultOptions || EMPTY_OPTIONS).maxRetries, (extraOptions || EMPTY_OPTIONS).maxRetries].filter((x) => x !== void 0);\n  const [maxRetries] = possibleMaxRetries.slice(-1);\n  const defaultRetryCondition = (_, __, {\n    attempt\n  }) => attempt <= maxRetries;\n  const options = {\n    maxRetries,\n    backoff: defaultBackoff,\n    retryCondition: defaultRetryCondition,\n    ...defaultOptions,\n    ...extraOptions\n  };\n  let retry2 = 0;\n  while (true) {\n    try {\n      const result = await baseQuery(args, api, extraOptions);\n      if (result.error) {\n        throw new HandledError(result);\n      }\n      return result;\n    } catch (e) {\n      retry2++;\n      if (e.throwImmediately) {\n        if (e instanceof HandledError) {\n          return e.value;\n        }\n        throw e;\n      }\n      if (e instanceof HandledError && !options.retryCondition(e.value.error, args, {\n        attempt: retry2,\n        baseQueryApi: api,\n        extraOptions\n      })) {\n        return e.value;\n      }\n      await options.backoff(retry2, options.maxRetries);\n    }\n  }\n};\nvar retry = /* @__PURE__ */ Object.assign(retryWithBackoff, {\n  fail\n});\n\n// src/query/core/setupListeners.ts\nvar onFocus = /* @__PURE__ */ (0, import_toolkit.createAction)(\"__rtkq/focused\");\nvar onFocusLost = /* @__PURE__ */ (0, import_toolkit.createAction)(\"__rtkq/unfocused\");\nvar onOnline = /* @__PURE__ */ (0, import_toolkit.createAction)(\"__rtkq/online\");\nvar onOffline = /* @__PURE__ */ (0, import_toolkit.createAction)(\"__rtkq/offline\");\nvar initialized = false;\nfunction setupListeners(dispatch, customHandler) {\n  function defaultHandler() {\n    const handleFocus = () => dispatch(onFocus());\n    const handleFocusLost = () => dispatch(onFocusLost());\n    const handleOnline = () => dispatch(onOnline());\n    const handleOffline = () => dispatch(onOffline());\n    const handleVisibilityChange = () => {\n      if (window.document.visibilityState === \"visible\") {\n        handleFocus();\n      } else {\n        handleFocusLost();\n      }\n    };\n    if (!initialized) {\n      if (typeof window !== \"undefined\" && window.addEventListener) {\n        window.addEventListener(\"visibilitychange\", handleVisibilityChange, false);\n        window.addEventListener(\"focus\", handleFocus, false);\n        window.addEventListener(\"online\", handleOnline, false);\n        window.addEventListener(\"offline\", handleOffline, false);\n        initialized = true;\n      }\n    }\n    const unsubscribe = () => {\n      window.removeEventListener(\"focus\", handleFocus);\n      window.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      window.removeEventListener(\"online\", handleOnline);\n      window.removeEventListener(\"offline\", handleOffline);\n      initialized = false;\n    };\n    return unsubscribe;\n  }\n  return customHandler ? customHandler(dispatch, {\n    onFocus,\n    onFocusLost,\n    onOffline,\n    onOnline\n  }) : defaultHandler();\n}\n\n// src/query/endpointDefinitions.ts\nfunction isQueryDefinition(e) {\n  return e.type === \"query\" /* query */;\n}\nfunction isMutationDefinition(e) {\n  return e.type === \"mutation\" /* mutation */;\n}\nfunction calculateProvidedBy(description, result, error, queryArg, meta, assertTagTypes) {\n  if (isFunction(description)) {\n    return description(result, error, queryArg, meta).filter(isNotNullish).map(expandTagDescription).map(assertTagTypes);\n  }\n  if (Array.isArray(description)) {\n    return description.map(expandTagDescription).map(assertTagTypes);\n  }\n  return [];\n}\nfunction isFunction(t) {\n  return typeof t === \"function\";\n}\nfunction expandTagDescription(description) {\n  return typeof description === \"string\" ? {\n    type: description\n  } : description;\n}\n\n// src/query/core/buildThunks.ts\nvar import_immer = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\n\n// src/query/core/buildInitiate.ts\nvar import_toolkit2 = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js\");\n\n// src/tsHelpers.ts\nfunction asSafePromise(promise, fallback) {\n  return promise.catch(fallback);\n}\n\n// src/query/core/buildInitiate.ts\nvar forceQueryFnSymbol = Symbol(\"forceQueryFn\");\nvar isUpsertQuery = (arg) => typeof arg[forceQueryFnSymbol] === \"function\";\nfunction buildInitiate({\n  serializeQueryArgs,\n  queryThunk,\n  mutationThunk,\n  api,\n  context\n}) {\n  const runningQueries = /* @__PURE__ */ new Map();\n  const runningMutations = /* @__PURE__ */ new Map();\n  const {\n    unsubscribeQueryResult,\n    removeMutationResult,\n    updateSubscriptionOptions\n  } = api.internalActions;\n  return {\n    buildInitiateQuery,\n    buildInitiateMutation,\n    getRunningQueryThunk,\n    getRunningMutationThunk,\n    getRunningQueriesThunk,\n    getRunningMutationsThunk\n  };\n  function getRunningQueryThunk(endpointName, queryArgs) {\n    return (dispatch) => {\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      const queryCacheKey = serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      });\n      return runningQueries.get(dispatch)?.[queryCacheKey];\n    };\n  }\n  function getRunningMutationThunk(_endpointName, fixedCacheKeyOrRequestId) {\n    return (dispatch) => {\n      return runningMutations.get(dispatch)?.[fixedCacheKeyOrRequestId];\n    };\n  }\n  function getRunningQueriesThunk() {\n    return (dispatch) => Object.values(runningQueries.get(dispatch) || {}).filter(isNotNullish);\n  }\n  function getRunningMutationsThunk() {\n    return (dispatch) => Object.values(runningMutations.get(dispatch) || {}).filter(isNotNullish);\n  }\n  function middlewareWarning(dispatch) {\n    if (true) {\n      if (middlewareWarning.triggered) return;\n      const returnedValue = dispatch(api.internalActions.internal_getRTKQSubscriptions());\n      middlewareWarning.triggered = true;\n      if (typeof returnedValue !== \"object\" || typeof returnedValue?.type === \"string\") {\n        throw new Error( false ? 0 : `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\nYou must add the middleware for RTK-Query to function correctly!`);\n      }\n    }\n  }\n  function buildInitiateQuery(endpointName, endpointDefinition) {\n    const queryAction = (arg, {\n      subscribe = true,\n      forceRefetch,\n      subscriptionOptions,\n      [forceQueryFnSymbol]: forceQueryFn,\n      ...rest\n    } = {}) => (dispatch, getState) => {\n      const queryCacheKey = serializeQueryArgs({\n        queryArgs: arg,\n        endpointDefinition,\n        endpointName\n      });\n      const thunk = queryThunk({\n        ...rest,\n        type: \"query\",\n        subscribe,\n        forceRefetch,\n        subscriptionOptions,\n        endpointName,\n        originalArgs: arg,\n        queryCacheKey,\n        [forceQueryFnSymbol]: forceQueryFn\n      });\n      const selector = api.endpoints[endpointName].select(arg);\n      const thunkResult = dispatch(thunk);\n      const stateAfter = selector(getState());\n      middlewareWarning(dispatch);\n      const {\n        requestId,\n        abort\n      } = thunkResult;\n      const skippedSynchronously = stateAfter.requestId !== requestId;\n      const runningQuery = runningQueries.get(dispatch)?.[queryCacheKey];\n      const selectFromState = () => selector(getState());\n      const statePromise = Object.assign(forceQueryFn ? (\n        // a query has been forced (upsertQueryData)\n        // -> we want to resolve it once data has been written with the data that will be written\n        thunkResult.then(selectFromState)\n      ) : skippedSynchronously && !runningQuery ? (\n        // a query has been skipped due to a condition and we do not have any currently running query\n        // -> we want to resolve it immediately with the current data\n        Promise.resolve(stateAfter)\n      ) : (\n        // query just started or one is already in flight\n        // -> wait for the running query, then resolve with data from after that\n        Promise.all([runningQuery, thunkResult]).then(selectFromState)\n      ), {\n        arg,\n        requestId,\n        subscriptionOptions,\n        queryCacheKey,\n        abort,\n        async unwrap() {\n          const result = await statePromise;\n          if (result.isError) {\n            throw result.error;\n          }\n          return result.data;\n        },\n        refetch: () => dispatch(queryAction(arg, {\n          subscribe: false,\n          forceRefetch: true\n        })),\n        unsubscribe() {\n          if (subscribe) dispatch(unsubscribeQueryResult({\n            queryCacheKey,\n            requestId\n          }));\n        },\n        updateSubscriptionOptions(options) {\n          statePromise.subscriptionOptions = options;\n          dispatch(updateSubscriptionOptions({\n            endpointName,\n            requestId,\n            queryCacheKey,\n            options\n          }));\n        }\n      });\n      if (!runningQuery && !skippedSynchronously && !forceQueryFn) {\n        const running = getOrInsert(runningQueries, dispatch, {});\n        running[queryCacheKey] = statePromise;\n        statePromise.then(() => {\n          delete running[queryCacheKey];\n          if (!countObjectKeys(running)) {\n            runningQueries.delete(dispatch);\n          }\n        });\n      }\n      return statePromise;\n    };\n    return queryAction;\n  }\n  function buildInitiateMutation(endpointName) {\n    return (arg, {\n      track = true,\n      fixedCacheKey\n    } = {}) => (dispatch, getState) => {\n      const thunk = mutationThunk({\n        type: \"mutation\",\n        endpointName,\n        originalArgs: arg,\n        track,\n        fixedCacheKey\n      });\n      const thunkResult = dispatch(thunk);\n      middlewareWarning(dispatch);\n      const {\n        requestId,\n        abort,\n        unwrap\n      } = thunkResult;\n      const returnValuePromise = asSafePromise(thunkResult.unwrap().then((data) => ({\n        data\n      })), (error) => ({\n        error\n      }));\n      const reset = () => {\n        dispatch(removeMutationResult({\n          requestId,\n          fixedCacheKey\n        }));\n      };\n      const ret = Object.assign(returnValuePromise, {\n        arg: thunkResult.arg,\n        requestId,\n        abort,\n        unwrap,\n        reset\n      });\n      const running = runningMutations.get(dispatch) || {};\n      runningMutations.set(dispatch, running);\n      running[requestId] = ret;\n      ret.then(() => {\n        delete running[requestId];\n        if (!countObjectKeys(running)) {\n          runningMutations.delete(dispatch);\n        }\n      });\n      if (fixedCacheKey) {\n        running[fixedCacheKey] = ret;\n        ret.then(() => {\n          if (running[fixedCacheKey] === ret) {\n            delete running[fixedCacheKey];\n            if (!countObjectKeys(running)) {\n              runningMutations.delete(dispatch);\n            }\n          }\n        });\n      }\n      return ret;\n    };\n  }\n}\n\n// src/query/core/buildThunks.ts\nfunction defaultTransformResponse(baseQueryReturnValue) {\n  return baseQueryReturnValue;\n}\nfunction buildThunks({\n  reducerPath,\n  baseQuery,\n  context: {\n    endpointDefinitions\n  },\n  serializeQueryArgs,\n  api,\n  assertTagType\n}) {\n  const patchQueryData = (endpointName, arg, patches, updateProvided) => (dispatch, getState) => {\n    const endpointDefinition = endpointDefinitions[endpointName];\n    const queryCacheKey = serializeQueryArgs({\n      queryArgs: arg,\n      endpointDefinition,\n      endpointName\n    });\n    dispatch(api.internalActions.queryResultPatched({\n      queryCacheKey,\n      patches\n    }));\n    if (!updateProvided) {\n      return;\n    }\n    const newValue = api.endpoints[endpointName].select(arg)(\n      // Work around TS 4.1 mismatch\n      getState()\n    );\n    const providedTags = calculateProvidedBy(endpointDefinition.providesTags, newValue.data, void 0, arg, {}, assertTagType);\n    dispatch(api.internalActions.updateProvidedBy({\n      queryCacheKey,\n      providedTags\n    }));\n  };\n  const updateQueryData = (endpointName, arg, updateRecipe, updateProvided = true) => (dispatch, getState) => {\n    const endpointDefinition = api.endpoints[endpointName];\n    const currentState = endpointDefinition.select(arg)(\n      // Work around TS 4.1 mismatch\n      getState()\n    );\n    const ret = {\n      patches: [],\n      inversePatches: [],\n      undo: () => dispatch(api.util.patchQueryData(endpointName, arg, ret.inversePatches, updateProvided))\n    };\n    if (currentState.status === \"uninitialized\" /* uninitialized */) {\n      return ret;\n    }\n    let newValue;\n    if (\"data\" in currentState) {\n      if ((0, import_immer.isDraftable)(currentState.data)) {\n        const [value, patches, inversePatches] = (0, import_immer.produceWithPatches)(currentState.data, updateRecipe);\n        ret.patches.push(...patches);\n        ret.inversePatches.push(...inversePatches);\n        newValue = value;\n      } else {\n        newValue = updateRecipe(currentState.data);\n        ret.patches.push({\n          op: \"replace\",\n          path: [],\n          value: newValue\n        });\n        ret.inversePatches.push({\n          op: \"replace\",\n          path: [],\n          value: currentState.data\n        });\n      }\n    }\n    if (ret.patches.length === 0) {\n      return ret;\n    }\n    dispatch(api.util.patchQueryData(endpointName, arg, ret.patches, updateProvided));\n    return ret;\n  };\n  const upsertQueryData = (endpointName, arg, value) => (dispatch) => {\n    return dispatch(api.endpoints[endpointName].initiate(arg, {\n      subscribe: false,\n      forceRefetch: true,\n      [forceQueryFnSymbol]: () => ({\n        data: value\n      })\n    }));\n  };\n  const executeEndpoint = async (arg, {\n    signal,\n    abort,\n    rejectWithValue,\n    fulfillWithValue,\n    dispatch,\n    getState,\n    extra\n  }) => {\n    const endpointDefinition = endpointDefinitions[arg.endpointName];\n    try {\n      let transformResponse = defaultTransformResponse;\n      let result;\n      const baseQueryApi = {\n        signal,\n        abort,\n        dispatch,\n        getState,\n        extra,\n        endpoint: arg.endpointName,\n        type: arg.type,\n        forced: arg.type === \"query\" ? isForcedQuery(arg, getState()) : void 0,\n        queryCacheKey: arg.type === \"query\" ? arg.queryCacheKey : void 0\n      };\n      const forceQueryFn = arg.type === \"query\" ? arg[forceQueryFnSymbol] : void 0;\n      if (forceQueryFn) {\n        result = forceQueryFn();\n      } else if (endpointDefinition.query) {\n        result = await baseQuery(endpointDefinition.query(arg.originalArgs), baseQueryApi, endpointDefinition.extraOptions);\n        if (endpointDefinition.transformResponse) {\n          transformResponse = endpointDefinition.transformResponse;\n        }\n      } else {\n        result = await endpointDefinition.queryFn(arg.originalArgs, baseQueryApi, endpointDefinition.extraOptions, (arg2) => baseQuery(arg2, baseQueryApi, endpointDefinition.extraOptions));\n      }\n      if (typeof process !== \"undefined\" && true) {\n        const what = endpointDefinition.query ? \"`baseQuery`\" : \"`queryFn`\";\n        let err;\n        if (!result) {\n          err = `${what} did not return anything.`;\n        } else if (typeof result !== \"object\") {\n          err = `${what} did not return an object.`;\n        } else if (result.error && result.data) {\n          err = `${what} returned an object containing both \\`error\\` and \\`result\\`.`;\n        } else if (result.error === void 0 && result.data === void 0) {\n          err = `${what} returned an object containing neither a valid \\`error\\` and \\`result\\`. At least one of them should not be \\`undefined\\``;\n        } else {\n          for (const key of Object.keys(result)) {\n            if (key !== \"error\" && key !== \"data\" && key !== \"meta\") {\n              err = `The object returned by ${what} has the unknown property ${key}.`;\n              break;\n            }\n          }\n        }\n        if (err) {\n          console.error(`Error encountered handling the endpoint ${arg.endpointName}.\n              ${err}\n              It needs to return an object with either the shape \\`{ data: <value> }\\` or \\`{ error: <value> }\\` that may contain an optional \\`meta\\` property.\n              Object returned was:`, result);\n        }\n      }\n      if (result.error) throw new HandledError(result.error, result.meta);\n      return fulfillWithValue(await transformResponse(result.data, result.meta, arg.originalArgs), {\n        fulfilledTimeStamp: Date.now(),\n        baseQueryMeta: result.meta,\n        [import_toolkit.SHOULD_AUTOBATCH]: true\n      });\n    } catch (error) {\n      let catchedError = error;\n      if (catchedError instanceof HandledError) {\n        let transformErrorResponse = defaultTransformResponse;\n        if (endpointDefinition.query && endpointDefinition.transformErrorResponse) {\n          transformErrorResponse = endpointDefinition.transformErrorResponse;\n        }\n        try {\n          return rejectWithValue(await transformErrorResponse(catchedError.value, catchedError.meta, arg.originalArgs), {\n            baseQueryMeta: catchedError.meta,\n            [import_toolkit.SHOULD_AUTOBATCH]: true\n          });\n        } catch (e) {\n          catchedError = e;\n        }\n      }\n      if (typeof process !== \"undefined\" && true) {\n        console.error(`An unhandled error occurred processing a request for the endpoint \"${arg.endpointName}\".\nIn the case of an unhandled error, no tags will be \"provided\" or \"invalidated\".`, catchedError);\n      } else {\n        console.error(catchedError);\n      }\n      throw catchedError;\n    }\n  };\n  function isForcedQuery(arg, state) {\n    const requestState = state[reducerPath]?.queries?.[arg.queryCacheKey];\n    const baseFetchOnMountOrArgChange = state[reducerPath]?.config.refetchOnMountOrArgChange;\n    const fulfilledVal = requestState?.fulfilledTimeStamp;\n    const refetchVal = arg.forceRefetch ?? (arg.subscribe && baseFetchOnMountOrArgChange);\n    if (refetchVal) {\n      return refetchVal === true || (Number(/* @__PURE__ */ new Date()) - Number(fulfilledVal)) / 1e3 >= refetchVal;\n    }\n    return false;\n  }\n  const queryThunk = (0, import_toolkit.createAsyncThunk)(`${reducerPath}/executeQuery`, executeEndpoint, {\n    getPendingMeta() {\n      return {\n        startedTimeStamp: Date.now(),\n        [import_toolkit.SHOULD_AUTOBATCH]: true\n      };\n    },\n    condition(queryThunkArgs, {\n      getState\n    }) {\n      const state = getState();\n      const requestState = state[reducerPath]?.queries?.[queryThunkArgs.queryCacheKey];\n      const fulfilledVal = requestState?.fulfilledTimeStamp;\n      const currentArg = queryThunkArgs.originalArgs;\n      const previousArg = requestState?.originalArgs;\n      const endpointDefinition = endpointDefinitions[queryThunkArgs.endpointName];\n      if (isUpsertQuery(queryThunkArgs)) {\n        return true;\n      }\n      if (requestState?.status === \"pending\") {\n        return false;\n      }\n      if (isForcedQuery(queryThunkArgs, state)) {\n        return true;\n      }\n      if (isQueryDefinition(endpointDefinition) && endpointDefinition?.forceRefetch?.({\n        currentArg,\n        previousArg,\n        endpointState: requestState,\n        state\n      })) {\n        return true;\n      }\n      if (fulfilledVal) {\n        return false;\n      }\n      return true;\n    },\n    dispatchConditionRejection: true\n  });\n  const mutationThunk = (0, import_toolkit.createAsyncThunk)(`${reducerPath}/executeMutation`, executeEndpoint, {\n    getPendingMeta() {\n      return {\n        startedTimeStamp: Date.now(),\n        [import_toolkit.SHOULD_AUTOBATCH]: true\n      };\n    }\n  });\n  const hasTheForce = (options) => \"force\" in options;\n  const hasMaxAge = (options) => \"ifOlderThan\" in options;\n  const prefetch = (endpointName, arg, options) => (dispatch, getState) => {\n    const force = hasTheForce(options) && options.force;\n    const maxAge = hasMaxAge(options) && options.ifOlderThan;\n    const queryAction = (force2 = true) => {\n      const options2 = {\n        forceRefetch: force2,\n        isPrefetch: true\n      };\n      return api.endpoints[endpointName].initiate(arg, options2);\n    };\n    const latestStateValue = api.endpoints[endpointName].select(arg)(getState());\n    if (force) {\n      dispatch(queryAction());\n    } else if (maxAge) {\n      const lastFulfilledTs = latestStateValue?.fulfilledTimeStamp;\n      if (!lastFulfilledTs) {\n        dispatch(queryAction());\n        return;\n      }\n      const shouldRetrigger = (Number(/* @__PURE__ */ new Date()) - Number(new Date(lastFulfilledTs))) / 1e3 >= maxAge;\n      if (shouldRetrigger) {\n        dispatch(queryAction());\n      }\n    } else {\n      dispatch(queryAction(false));\n    }\n  };\n  function matchesEndpoint(endpointName) {\n    return (action) => action?.meta?.arg?.endpointName === endpointName;\n  }\n  function buildMatchThunkActions(thunk, endpointName) {\n    return {\n      matchPending: (0, import_toolkit.isAllOf)((0, import_toolkit.isPending)(thunk), matchesEndpoint(endpointName)),\n      matchFulfilled: (0, import_toolkit.isAllOf)((0, import_toolkit.isFulfilled)(thunk), matchesEndpoint(endpointName)),\n      matchRejected: (0, import_toolkit.isAllOf)((0, import_toolkit.isRejected)(thunk), matchesEndpoint(endpointName))\n    };\n  }\n  return {\n    queryThunk,\n    mutationThunk,\n    prefetch,\n    updateQueryData,\n    upsertQueryData,\n    patchQueryData,\n    buildMatchThunkActions\n  };\n}\nfunction calculateProvidedByThunk(action, type, endpointDefinitions, assertTagType) {\n  return calculateProvidedBy(endpointDefinitions[action.meta.arg.endpointName][type], (0, import_toolkit.isFulfilled)(action) ? action.payload : void 0, (0, import_toolkit.isRejectedWithValue)(action) ? action.payload : void 0, action.meta.arg.originalArgs, \"baseQueryMeta\" in action.meta ? action.meta.baseQueryMeta : void 0, assertTagType);\n}\n\n// src/query/core/buildSlice.ts\nvar import_immer2 = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\nvar import_immer3 = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\nfunction updateQuerySubstateIfExists(state, queryCacheKey, update) {\n  const substate = state[queryCacheKey];\n  if (substate) {\n    update(substate);\n  }\n}\nfunction getMutationCacheKey(id) {\n  return (\"arg\" in id ? id.arg.fixedCacheKey : id.fixedCacheKey) ?? id.requestId;\n}\nfunction updateMutationSubstateIfExists(state, id, update) {\n  const substate = state[getMutationCacheKey(id)];\n  if (substate) {\n    update(substate);\n  }\n}\nvar initialState = {};\nfunction buildSlice({\n  reducerPath,\n  queryThunk,\n  mutationThunk,\n  serializeQueryArgs,\n  context: {\n    endpointDefinitions: definitions,\n    apiUid,\n    extractRehydrationInfo,\n    hasRehydrationInfo\n  },\n  assertTagType,\n  config\n}) {\n  const resetApiState = (0, import_toolkit.createAction)(`${reducerPath}/resetApiState`);\n  function writePendingCacheEntry(draft, arg, upserting, meta) {\n    draft[arg.queryCacheKey] ??= {\n      status: \"uninitialized\" /* uninitialized */,\n      endpointName: arg.endpointName\n    };\n    updateQuerySubstateIfExists(draft, arg.queryCacheKey, (substate) => {\n      substate.status = \"pending\" /* pending */;\n      substate.requestId = upserting && substate.requestId ? (\n        // for `upsertQuery` **updates**, keep the current `requestId`\n        substate.requestId\n      ) : (\n        // for normal queries or `upsertQuery` **inserts** always update the `requestId`\n        meta.requestId\n      );\n      if (arg.originalArgs !== void 0) {\n        substate.originalArgs = arg.originalArgs;\n      }\n      substate.startedTimeStamp = meta.startedTimeStamp;\n    });\n  }\n  function writeFulfilledCacheEntry(draft, meta, payload, upserting) {\n    updateQuerySubstateIfExists(draft, meta.arg.queryCacheKey, (substate) => {\n      if (substate.requestId !== meta.requestId && !upserting) return;\n      const {\n        merge\n      } = definitions[meta.arg.endpointName];\n      substate.status = \"fulfilled\" /* fulfilled */;\n      if (merge) {\n        if (substate.data !== void 0) {\n          const {\n            fulfilledTimeStamp,\n            arg,\n            baseQueryMeta,\n            requestId\n          } = meta;\n          let newData = (0, import_toolkit.createNextState)(substate.data, (draftSubstateData) => {\n            return merge(draftSubstateData, payload, {\n              arg: arg.originalArgs,\n              baseQueryMeta,\n              fulfilledTimeStamp,\n              requestId\n            });\n          });\n          substate.data = newData;\n        } else {\n          substate.data = payload;\n        }\n      } else {\n        substate.data = definitions[meta.arg.endpointName].structuralSharing ?? true ? copyWithStructuralSharing((0, import_immer2.isDraft)(substate.data) ? (0, import_immer3.original)(substate.data) : substate.data, payload) : payload;\n      }\n      delete substate.error;\n      substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\n    });\n  }\n  const querySlice = (0, import_toolkit.createSlice)({\n    name: `${reducerPath}/queries`,\n    initialState,\n    reducers: {\n      removeQueryResult: {\n        reducer(draft, {\n          payload: {\n            queryCacheKey\n          }\n        }) {\n          delete draft[queryCacheKey];\n        },\n        prepare: (0, import_toolkit.prepareAutoBatched)()\n      },\n      cacheEntriesUpserted: {\n        reducer(draft, action) {\n          for (const entry of action.payload) {\n            const {\n              queryDescription: arg,\n              value\n            } = entry;\n            writePendingCacheEntry(draft, arg, true, {\n              arg,\n              requestId: action.meta.requestId,\n              startedTimeStamp: action.meta.timestamp\n            });\n            writeFulfilledCacheEntry(\n              draft,\n              {\n                arg,\n                requestId: action.meta.requestId,\n                fulfilledTimeStamp: action.meta.timestamp,\n                baseQueryMeta: {}\n              },\n              value,\n              // We know we're upserting here\n              true\n            );\n          }\n        },\n        prepare: (payload) => {\n          const queryDescriptions = payload.map((entry) => {\n            const {\n              endpointName,\n              arg,\n              value\n            } = entry;\n            const endpointDefinition = definitions[endpointName];\n            const queryDescription = {\n              type: \"query\",\n              endpointName,\n              originalArgs: entry.arg,\n              queryCacheKey: serializeQueryArgs({\n                queryArgs: arg,\n                endpointDefinition,\n                endpointName\n              })\n            };\n            return {\n              queryDescription,\n              value\n            };\n          });\n          const result = {\n            payload: queryDescriptions,\n            meta: {\n              [import_toolkit.SHOULD_AUTOBATCH]: true,\n              requestId: (0, import_toolkit.nanoid)(),\n              timestamp: Date.now()\n            }\n          };\n          return result;\n        }\n      },\n      queryResultPatched: {\n        reducer(draft, {\n          payload: {\n            queryCacheKey,\n            patches\n          }\n        }) {\n          updateQuerySubstateIfExists(draft, queryCacheKey, (substate) => {\n            substate.data = (0, import_immer3.applyPatches)(substate.data, patches.concat());\n          });\n        },\n        prepare: (0, import_toolkit.prepareAutoBatched)()\n      }\n    },\n    extraReducers(builder) {\n      builder.addCase(queryThunk.pending, (draft, {\n        meta,\n        meta: {\n          arg\n        }\n      }) => {\n        const upserting = isUpsertQuery(arg);\n        writePendingCacheEntry(draft, arg, upserting, meta);\n      }).addCase(queryThunk.fulfilled, (draft, {\n        meta,\n        payload\n      }) => {\n        const upserting = isUpsertQuery(meta.arg);\n        writeFulfilledCacheEntry(draft, meta, payload, upserting);\n      }).addCase(queryThunk.rejected, (draft, {\n        meta: {\n          condition,\n          arg,\n          requestId\n        },\n        error,\n        payload\n      }) => {\n        updateQuerySubstateIfExists(draft, arg.queryCacheKey, (substate) => {\n          if (condition) {\n          } else {\n            if (substate.requestId !== requestId) return;\n            substate.status = \"rejected\" /* rejected */;\n            substate.error = payload ?? error;\n          }\n        });\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          queries\n        } = extractRehydrationInfo(action);\n        for (const [key, entry] of Object.entries(queries)) {\n          if (\n            // do not rehydrate entries that were currently in flight.\n            entry?.status === \"fulfilled\" /* fulfilled */ || entry?.status === \"rejected\" /* rejected */\n          ) {\n            draft[key] = entry;\n          }\n        }\n      });\n    }\n  });\n  const mutationSlice = (0, import_toolkit.createSlice)({\n    name: `${reducerPath}/mutations`,\n    initialState,\n    reducers: {\n      removeMutationResult: {\n        reducer(draft, {\n          payload\n        }) {\n          const cacheKey = getMutationCacheKey(payload);\n          if (cacheKey in draft) {\n            delete draft[cacheKey];\n          }\n        },\n        prepare: (0, import_toolkit.prepareAutoBatched)()\n      }\n    },\n    extraReducers(builder) {\n      builder.addCase(mutationThunk.pending, (draft, {\n        meta,\n        meta: {\n          requestId,\n          arg,\n          startedTimeStamp\n        }\n      }) => {\n        if (!arg.track) return;\n        draft[getMutationCacheKey(meta)] = {\n          requestId,\n          status: \"pending\" /* pending */,\n          endpointName: arg.endpointName,\n          startedTimeStamp\n        };\n      }).addCase(mutationThunk.fulfilled, (draft, {\n        payload,\n        meta\n      }) => {\n        if (!meta.arg.track) return;\n        updateMutationSubstateIfExists(draft, meta, (substate) => {\n          if (substate.requestId !== meta.requestId) return;\n          substate.status = \"fulfilled\" /* fulfilled */;\n          substate.data = payload;\n          substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\n        });\n      }).addCase(mutationThunk.rejected, (draft, {\n        payload,\n        error,\n        meta\n      }) => {\n        if (!meta.arg.track) return;\n        updateMutationSubstateIfExists(draft, meta, (substate) => {\n          if (substate.requestId !== meta.requestId) return;\n          substate.status = \"rejected\" /* rejected */;\n          substate.error = payload ?? error;\n        });\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          mutations\n        } = extractRehydrationInfo(action);\n        for (const [key, entry] of Object.entries(mutations)) {\n          if (\n            // do not rehydrate entries that were currently in flight.\n            (entry?.status === \"fulfilled\" /* fulfilled */ || entry?.status === \"rejected\" /* rejected */) && // only rehydrate endpoints that were persisted using a `fixedCacheKey`\n            key !== entry?.requestId\n          ) {\n            draft[key] = entry;\n          }\n        }\n      });\n    }\n  });\n  const invalidationSlice = (0, import_toolkit.createSlice)({\n    name: `${reducerPath}/invalidation`,\n    initialState,\n    reducers: {\n      updateProvidedBy: {\n        reducer(draft, action) {\n          const {\n            queryCacheKey,\n            providedTags\n          } = action.payload;\n          for (const tagTypeSubscriptions of Object.values(draft)) {\n            for (const idSubscriptions of Object.values(tagTypeSubscriptions)) {\n              const foundAt = idSubscriptions.indexOf(queryCacheKey);\n              if (foundAt !== -1) {\n                idSubscriptions.splice(foundAt, 1);\n              }\n            }\n          }\n          for (const {\n            type,\n            id\n          } of providedTags) {\n            const subscribedQueries = (draft[type] ??= {})[id || \"__internal_without_id\"] ??= [];\n            const alreadySubscribed = subscribedQueries.includes(queryCacheKey);\n            if (!alreadySubscribed) {\n              subscribedQueries.push(queryCacheKey);\n            }\n          }\n        },\n        prepare: (0, import_toolkit.prepareAutoBatched)()\n      }\n    },\n    extraReducers(builder) {\n      builder.addCase(querySlice.actions.removeQueryResult, (draft, {\n        payload: {\n          queryCacheKey\n        }\n      }) => {\n        for (const tagTypeSubscriptions of Object.values(draft)) {\n          for (const idSubscriptions of Object.values(tagTypeSubscriptions)) {\n            const foundAt = idSubscriptions.indexOf(queryCacheKey);\n            if (foundAt !== -1) {\n              idSubscriptions.splice(foundAt, 1);\n            }\n          }\n        }\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          provided\n        } = extractRehydrationInfo(action);\n        for (const [type, incomingTags] of Object.entries(provided)) {\n          for (const [id, cacheKeys] of Object.entries(incomingTags)) {\n            const subscribedQueries = (draft[type] ??= {})[id || \"__internal_without_id\"] ??= [];\n            for (const queryCacheKey of cacheKeys) {\n              const alreadySubscribed = subscribedQueries.includes(queryCacheKey);\n              if (!alreadySubscribed) {\n                subscribedQueries.push(queryCacheKey);\n              }\n            }\n          }\n        }\n      }).addMatcher((0, import_toolkit.isAnyOf)((0, import_toolkit.isFulfilled)(queryThunk), (0, import_toolkit.isRejectedWithValue)(queryThunk)), (draft, action) => {\n        const providedTags = calculateProvidedByThunk(action, \"providesTags\", definitions, assertTagType);\n        const {\n          queryCacheKey\n        } = action.meta.arg;\n        invalidationSlice.caseReducers.updateProvidedBy(draft, invalidationSlice.actions.updateProvidedBy({\n          queryCacheKey,\n          providedTags\n        }));\n      });\n    }\n  });\n  const subscriptionSlice = (0, import_toolkit.createSlice)({\n    name: `${reducerPath}/subscriptions`,\n    initialState,\n    reducers: {\n      updateSubscriptionOptions(d, a) {\n      },\n      unsubscribeQueryResult(d, a) {\n      },\n      internal_getRTKQSubscriptions() {\n      }\n    }\n  });\n  const internalSubscriptionsSlice = (0, import_toolkit.createSlice)({\n    name: `${reducerPath}/internalSubscriptions`,\n    initialState,\n    reducers: {\n      subscriptionsUpdated: {\n        reducer(state, action) {\n          return (0, import_immer3.applyPatches)(state, action.payload);\n        },\n        prepare: (0, import_toolkit.prepareAutoBatched)()\n      }\n    }\n  });\n  const configSlice = (0, import_toolkit.createSlice)({\n    name: `${reducerPath}/config`,\n    initialState: {\n      online: isOnline(),\n      focused: isDocumentVisible(),\n      middlewareRegistered: false,\n      ...config\n    },\n    reducers: {\n      middlewareRegistered(state, {\n        payload\n      }) {\n        state.middlewareRegistered = state.middlewareRegistered === \"conflict\" || apiUid !== payload ? \"conflict\" : true;\n      }\n    },\n    extraReducers: (builder) => {\n      builder.addCase(onOnline, (state) => {\n        state.online = true;\n      }).addCase(onOffline, (state) => {\n        state.online = false;\n      }).addCase(onFocus, (state) => {\n        state.focused = true;\n      }).addCase(onFocusLost, (state) => {\n        state.focused = false;\n      }).addMatcher(hasRehydrationInfo, (draft) => ({\n        ...draft\n      }));\n    }\n  });\n  const combinedReducer = (0, import_toolkit.combineReducers)({\n    queries: querySlice.reducer,\n    mutations: mutationSlice.reducer,\n    provided: invalidationSlice.reducer,\n    subscriptions: internalSubscriptionsSlice.reducer,\n    config: configSlice.reducer\n  });\n  const reducer = (state, action) => combinedReducer(resetApiState.match(action) ? void 0 : state, action);\n  const actions = {\n    ...configSlice.actions,\n    ...querySlice.actions,\n    ...subscriptionSlice.actions,\n    ...internalSubscriptionsSlice.actions,\n    ...mutationSlice.actions,\n    ...invalidationSlice.actions,\n    resetApiState\n  };\n  return {\n    reducer,\n    actions\n  };\n}\n\n// src/query/core/buildSelectors.ts\nvar skipToken = /* @__PURE__ */ Symbol.for(\"RTKQ/skipToken\");\nvar initialSubState = {\n  status: \"uninitialized\" /* uninitialized */\n};\nvar defaultQuerySubState = /* @__PURE__ */ (0, import_toolkit.createNextState)(initialSubState, () => {\n});\nvar defaultMutationSubState = /* @__PURE__ */ (0, import_toolkit.createNextState)(initialSubState, () => {\n});\nfunction buildSelectors({\n  serializeQueryArgs,\n  reducerPath,\n  createSelector: createSelector2\n}) {\n  const selectSkippedQuery = (state) => defaultQuerySubState;\n  const selectSkippedMutation = (state) => defaultMutationSubState;\n  return {\n    buildQuerySelector,\n    buildMutationSelector,\n    selectInvalidatedBy,\n    selectCachedArgsForQuery\n  };\n  function withRequestFlags(substate) {\n    return {\n      ...substate,\n      ...getRequestStatusFlags(substate.status)\n    };\n  }\n  function selectInternalState(rootState) {\n    const state = rootState[reducerPath];\n    if (true) {\n      if (!state) {\n        if (selectInternalState.triggered) return state;\n        selectInternalState.triggered = true;\n        console.error(`Error: No data found at \\`state.${reducerPath}\\`. Did you forget to add the reducer to the store?`);\n      }\n    }\n    return state;\n  }\n  function buildQuerySelector(endpointName, endpointDefinition) {\n    return (queryArgs) => {\n      if (queryArgs === skipToken) {\n        return createSelector2(selectSkippedQuery, withRequestFlags);\n      }\n      const serializedArgs = serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      });\n      const selectQuerySubstate = (state) => selectInternalState(state)?.queries?.[serializedArgs] ?? defaultQuerySubState;\n      return createSelector2(selectQuerySubstate, withRequestFlags);\n    };\n  }\n  function buildMutationSelector() {\n    return (id) => {\n      let mutationId;\n      if (typeof id === \"object\") {\n        mutationId = getMutationCacheKey(id) ?? skipToken;\n      } else {\n        mutationId = id;\n      }\n      const selectMutationSubstate = (state) => selectInternalState(state)?.mutations?.[mutationId] ?? defaultMutationSubState;\n      const finalSelectMutationSubstate = mutationId === skipToken ? selectSkippedMutation : selectMutationSubstate;\n      return createSelector2(finalSelectMutationSubstate, withRequestFlags);\n    };\n  }\n  function selectInvalidatedBy(state, tags) {\n    const apiState = state[reducerPath];\n    const toInvalidate = /* @__PURE__ */ new Set();\n    for (const tag of tags.filter(isNotNullish).map(expandTagDescription)) {\n      const provided = apiState.provided[tag.type];\n      if (!provided) {\n        continue;\n      }\n      let invalidateSubscriptions = (tag.id !== void 0 ? (\n        // id given: invalidate all queries that provide this type & id\n        provided[tag.id]\n      ) : (\n        // no id: invalidate all queries that provide this type\n        flatten(Object.values(provided))\n      )) ?? [];\n      for (const invalidate of invalidateSubscriptions) {\n        toInvalidate.add(invalidate);\n      }\n    }\n    return flatten(Array.from(toInvalidate.values()).map((queryCacheKey) => {\n      const querySubState = apiState.queries[queryCacheKey];\n      return querySubState ? [{\n        queryCacheKey,\n        endpointName: querySubState.endpointName,\n        originalArgs: querySubState.originalArgs\n      }] : [];\n    }));\n  }\n  function selectCachedArgsForQuery(state, queryName) {\n    return Object.values(state[reducerPath].queries).filter((entry) => entry?.endpointName === queryName && entry.status !== \"uninitialized\" /* uninitialized */).map((entry) => entry.originalArgs);\n  }\n}\n\n// src/query/createApi.ts\nvar import_toolkit3 = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js\");\n\n// src/query/defaultSerializeQueryArgs.ts\nvar cache = WeakMap ? /* @__PURE__ */ new WeakMap() : void 0;\nvar defaultSerializeQueryArgs = ({\n  endpointName,\n  queryArgs\n}) => {\n  let serialized = \"\";\n  const cached = cache?.get(queryArgs);\n  if (typeof cached === \"string\") {\n    serialized = cached;\n  } else {\n    const stringified = JSON.stringify(queryArgs, (key, value) => {\n      value = typeof value === \"bigint\" ? {\n        $bigint: value.toString()\n      } : value;\n      value = (0, import_toolkit.isPlainObject)(value) ? Object.keys(value).sort().reduce((acc, key2) => {\n        acc[key2] = value[key2];\n        return acc;\n      }, {}) : value;\n      return value;\n    });\n    if ((0, import_toolkit.isPlainObject)(queryArgs)) {\n      cache?.set(queryArgs, stringified);\n    }\n    serialized = stringified;\n  }\n  return `${endpointName}(${serialized})`;\n};\n\n// src/query/createApi.ts\nvar import_reselect = __webpack_require__(/*! reselect */ \"(ssr)/./node_modules/reselect/dist/cjs/reselect.cjs\");\nfunction buildCreateApi(...modules) {\n  return function baseCreateApi(options) {\n    const extractRehydrationInfo = (0, import_reselect.weakMapMemoize)((action) => options.extractRehydrationInfo?.(action, {\n      reducerPath: options.reducerPath ?? \"api\"\n    }));\n    const optionsWithDefaults = {\n      reducerPath: \"api\",\n      keepUnusedDataFor: 60,\n      refetchOnMountOrArgChange: false,\n      refetchOnFocus: false,\n      refetchOnReconnect: false,\n      invalidationBehavior: \"delayed\",\n      ...options,\n      extractRehydrationInfo,\n      serializeQueryArgs(queryArgsApi) {\n        let finalSerializeQueryArgs = defaultSerializeQueryArgs;\n        if (\"serializeQueryArgs\" in queryArgsApi.endpointDefinition) {\n          const endpointSQA = queryArgsApi.endpointDefinition.serializeQueryArgs;\n          finalSerializeQueryArgs = (queryArgsApi2) => {\n            const initialResult = endpointSQA(queryArgsApi2);\n            if (typeof initialResult === \"string\") {\n              return initialResult;\n            } else {\n              return defaultSerializeQueryArgs({\n                ...queryArgsApi2,\n                queryArgs: initialResult\n              });\n            }\n          };\n        } else if (options.serializeQueryArgs) {\n          finalSerializeQueryArgs = options.serializeQueryArgs;\n        }\n        return finalSerializeQueryArgs(queryArgsApi);\n      },\n      tagTypes: [...options.tagTypes || []]\n    };\n    const context = {\n      endpointDefinitions: {},\n      batch(fn) {\n        fn();\n      },\n      apiUid: (0, import_toolkit.nanoid)(),\n      extractRehydrationInfo,\n      hasRehydrationInfo: (0, import_reselect.weakMapMemoize)((action) => extractRehydrationInfo(action) != null)\n    };\n    const api = {\n      injectEndpoints,\n      enhanceEndpoints({\n        addTagTypes,\n        endpoints\n      }) {\n        if (addTagTypes) {\n          for (const eT of addTagTypes) {\n            if (!optionsWithDefaults.tagTypes.includes(eT)) {\n              ;\n              optionsWithDefaults.tagTypes.push(eT);\n            }\n          }\n        }\n        if (endpoints) {\n          for (const [endpointName, partialDefinition] of Object.entries(endpoints)) {\n            if (typeof partialDefinition === \"function\") {\n              partialDefinition(context.endpointDefinitions[endpointName]);\n            } else {\n              Object.assign(context.endpointDefinitions[endpointName] || {}, partialDefinition);\n            }\n          }\n        }\n        return api;\n      }\n    };\n    const initializedModules = modules.map((m) => m.init(api, optionsWithDefaults, context));\n    function injectEndpoints(inject) {\n      const evaluatedEndpoints = inject.endpoints({\n        query: (x) => ({\n          ...x,\n          type: \"query\" /* query */\n        }),\n        mutation: (x) => ({\n          ...x,\n          type: \"mutation\" /* mutation */\n        })\n      });\n      for (const [endpointName, definition] of Object.entries(evaluatedEndpoints)) {\n        if (inject.overrideExisting !== true && endpointName in context.endpointDefinitions) {\n          if (inject.overrideExisting === \"throw\") {\n            throw new Error( false ? 0 : `called \\`injectEndpoints\\` to override already-existing endpointName ${endpointName} without specifying \\`overrideExisting: true\\``);\n          } else if (typeof process !== \"undefined\" && true) {\n            console.error(`called \\`injectEndpoints\\` to override already-existing endpointName ${endpointName} without specifying \\`overrideExisting: true\\``);\n          }\n          continue;\n        }\n        context.endpointDefinitions[endpointName] = definition;\n        for (const m of initializedModules) {\n          m.injectEndpoint(endpointName, definition);\n        }\n      }\n      return api;\n    }\n    return api.injectEndpoints({\n      endpoints: options.endpoints\n    });\n  };\n}\n\n// src/query/fakeBaseQuery.ts\nvar import_toolkit4 = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js\");\nvar _NEVER = /* @__PURE__ */ Symbol();\nfunction fakeBaseQuery() {\n  return function() {\n    throw new Error( false ? 0 : \"When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.\");\n  };\n}\n\n// src/query/core/module.ts\nvar import_immer5 = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\n\n// src/query/tsHelpers.ts\nfunction assertCast(v) {\n}\nfunction safeAssign(target, ...args) {\n  return Object.assign(target, ...args);\n}\n\n// src/query/core/buildMiddleware/batchActions.ts\nvar import_immer4 = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/cjs/index.js\");\nvar buildBatchedActionsHandler = ({\n  api,\n  queryThunk,\n  internalState\n}) => {\n  const subscriptionsPrefix = `${api.reducerPath}/subscriptions`;\n  let previousSubscriptions = null;\n  let updateSyncTimer = null;\n  const {\n    updateSubscriptionOptions,\n    unsubscribeQueryResult\n  } = api.internalActions;\n  const actuallyMutateSubscriptions = (mutableState, action) => {\n    if (updateSubscriptionOptions.match(action)) {\n      const {\n        queryCacheKey,\n        requestId,\n        options\n      } = action.payload;\n      if (mutableState?.[queryCacheKey]?.[requestId]) {\n        mutableState[queryCacheKey][requestId] = options;\n      }\n      return true;\n    }\n    if (unsubscribeQueryResult.match(action)) {\n      const {\n        queryCacheKey,\n        requestId\n      } = action.payload;\n      if (mutableState[queryCacheKey]) {\n        delete mutableState[queryCacheKey][requestId];\n      }\n      return true;\n    }\n    if (api.internalActions.removeQueryResult.match(action)) {\n      delete mutableState[action.payload.queryCacheKey];\n      return true;\n    }\n    if (queryThunk.pending.match(action)) {\n      const {\n        meta: {\n          arg,\n          requestId\n        }\n      } = action;\n      const substate = mutableState[arg.queryCacheKey] ??= {};\n      substate[`${requestId}_running`] = {};\n      if (arg.subscribe) {\n        substate[requestId] = arg.subscriptionOptions ?? substate[requestId] ?? {};\n      }\n      return true;\n    }\n    let mutated = false;\n    if (queryThunk.fulfilled.match(action) || queryThunk.rejected.match(action)) {\n      const state = mutableState[action.meta.arg.queryCacheKey] || {};\n      const key = `${action.meta.requestId}_running`;\n      mutated ||= !!state[key];\n      delete state[key];\n    }\n    if (queryThunk.rejected.match(action)) {\n      const {\n        meta: {\n          condition,\n          arg,\n          requestId\n        }\n      } = action;\n      if (condition && arg.subscribe) {\n        const substate = mutableState[arg.queryCacheKey] ??= {};\n        substate[requestId] = arg.subscriptionOptions ?? substate[requestId] ?? {};\n        mutated = true;\n      }\n    }\n    return mutated;\n  };\n  const getSubscriptions = () => internalState.currentSubscriptions;\n  const getSubscriptionCount = (queryCacheKey) => {\n    const subscriptions = getSubscriptions();\n    const subscriptionsForQueryArg = subscriptions[queryCacheKey] ?? {};\n    return countObjectKeys(subscriptionsForQueryArg);\n  };\n  const isRequestSubscribed = (queryCacheKey, requestId) => {\n    const subscriptions = getSubscriptions();\n    return !!subscriptions?.[queryCacheKey]?.[requestId];\n  };\n  const subscriptionSelectors = {\n    getSubscriptions,\n    getSubscriptionCount,\n    isRequestSubscribed\n  };\n  return (action, mwApi) => {\n    if (!previousSubscriptions) {\n      previousSubscriptions = JSON.parse(JSON.stringify(internalState.currentSubscriptions));\n    }\n    if (api.util.resetApiState.match(action)) {\n      previousSubscriptions = internalState.currentSubscriptions = {};\n      updateSyncTimer = null;\n      return [true, false];\n    }\n    if (api.internalActions.internal_getRTKQSubscriptions.match(action)) {\n      return [false, subscriptionSelectors];\n    }\n    const didMutate = actuallyMutateSubscriptions(internalState.currentSubscriptions, action);\n    let actionShouldContinue = true;\n    if (didMutate) {\n      if (!updateSyncTimer) {\n        updateSyncTimer = setTimeout(() => {\n          const newSubscriptions = JSON.parse(JSON.stringify(internalState.currentSubscriptions));\n          const [, patches] = (0, import_immer4.produceWithPatches)(previousSubscriptions, () => newSubscriptions);\n          mwApi.next(api.internalActions.subscriptionsUpdated(patches));\n          previousSubscriptions = newSubscriptions;\n          updateSyncTimer = null;\n        }, 500);\n      }\n      const isSubscriptionSliceAction = typeof action.type == \"string\" && !!action.type.startsWith(subscriptionsPrefix);\n      const isAdditionalSubscriptionAction = queryThunk.rejected.match(action) && action.meta.condition && !!action.meta.arg.subscribe;\n      actionShouldContinue = !isSubscriptionSliceAction && !isAdditionalSubscriptionAction;\n    }\n    return [actionShouldContinue, false];\n  };\n};\n\n// src/query/core/buildMiddleware/cacheCollection.ts\nfunction isObjectEmpty(obj) {\n  for (const k in obj) {\n    return false;\n  }\n  return true;\n}\nvar THIRTY_TWO_BIT_MAX_TIMER_SECONDS = 2147483647 / 1e3 - 1;\nvar buildCacheCollectionHandler = ({\n  reducerPath,\n  api,\n  queryThunk,\n  context,\n  internalState\n}) => {\n  const {\n    removeQueryResult,\n    unsubscribeQueryResult,\n    cacheEntriesUpserted\n  } = api.internalActions;\n  const canTriggerUnsubscribe = (0, import_toolkit.isAnyOf)(unsubscribeQueryResult.match, queryThunk.fulfilled, queryThunk.rejected, cacheEntriesUpserted.match);\n  function anySubscriptionsRemainingForKey(queryCacheKey) {\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    return !!subscriptions && !isObjectEmpty(subscriptions);\n  }\n  const currentRemovalTimeouts = {};\n  const handler = (action, mwApi, internalState2) => {\n    if (canTriggerUnsubscribe(action)) {\n      const state = mwApi.getState()[reducerPath];\n      let queryCacheKeys;\n      if (cacheEntriesUpserted.match(action)) {\n        queryCacheKeys = action.payload.map((entry) => entry.queryDescription.queryCacheKey);\n      } else {\n        const {\n          queryCacheKey\n        } = unsubscribeQueryResult.match(action) ? action.payload : action.meta.arg;\n        queryCacheKeys = [queryCacheKey];\n      }\n      for (const queryCacheKey of queryCacheKeys) {\n        handleUnsubscribe(queryCacheKey, state.queries[queryCacheKey]?.endpointName, mwApi, state.config);\n      }\n    }\n    if (api.util.resetApiState.match(action)) {\n      for (const [key, timeout] of Object.entries(currentRemovalTimeouts)) {\n        if (timeout) clearTimeout(timeout);\n        delete currentRemovalTimeouts[key];\n      }\n    }\n    if (context.hasRehydrationInfo(action)) {\n      const state = mwApi.getState()[reducerPath];\n      const {\n        queries\n      } = context.extractRehydrationInfo(action);\n      for (const [queryCacheKey, queryState] of Object.entries(queries)) {\n        handleUnsubscribe(queryCacheKey, queryState?.endpointName, mwApi, state.config);\n      }\n    }\n  };\n  function handleUnsubscribe(queryCacheKey, endpointName, api2, config) {\n    const endpointDefinition = context.endpointDefinitions[endpointName];\n    const keepUnusedDataFor = endpointDefinition?.keepUnusedDataFor ?? config.keepUnusedDataFor;\n    if (keepUnusedDataFor === Infinity) {\n      return;\n    }\n    const finalKeepUnusedDataFor = Math.max(0, Math.min(keepUnusedDataFor, THIRTY_TWO_BIT_MAX_TIMER_SECONDS));\n    if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\n      const currentTimeout = currentRemovalTimeouts[queryCacheKey];\n      if (currentTimeout) {\n        clearTimeout(currentTimeout);\n      }\n      currentRemovalTimeouts[queryCacheKey] = setTimeout(() => {\n        if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\n          api2.dispatch(removeQueryResult({\n            queryCacheKey\n          }));\n        }\n        delete currentRemovalTimeouts[queryCacheKey];\n      }, finalKeepUnusedDataFor * 1e3);\n    }\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/cacheLifecycle.ts\nvar neverResolvedError = new Error(\"Promise never resolved before cacheEntryRemoved.\");\nvar buildCacheLifecycleHandler = ({\n  api,\n  reducerPath,\n  context,\n  queryThunk,\n  mutationThunk,\n  internalState\n}) => {\n  const isQueryThunk = (0, import_toolkit.isAsyncThunkAction)(queryThunk);\n  const isMutationThunk = (0, import_toolkit.isAsyncThunkAction)(mutationThunk);\n  const isFulfilledThunk = (0, import_toolkit.isFulfilled)(queryThunk, mutationThunk);\n  const lifecycleMap = {};\n  function resolveLifecycleEntry(cacheKey, data, meta) {\n    const lifecycle = lifecycleMap[cacheKey];\n    if (lifecycle?.valueResolved) {\n      lifecycle.valueResolved({\n        data,\n        meta\n      });\n      delete lifecycle.valueResolved;\n    }\n  }\n  function removeLifecycleEntry(cacheKey) {\n    const lifecycle = lifecycleMap[cacheKey];\n    if (lifecycle) {\n      delete lifecycleMap[cacheKey];\n      lifecycle.cacheEntryRemoved();\n    }\n  }\n  const handler = (action, mwApi, stateBefore) => {\n    const cacheKey = getCacheKey(action);\n    function checkForNewCacheKey(endpointName, cacheKey2, requestId, originalArgs) {\n      const oldState = stateBefore[reducerPath].queries[cacheKey2];\n      const state = mwApi.getState()[reducerPath].queries[cacheKey2];\n      if (!oldState && state) {\n        handleNewKey(endpointName, originalArgs, cacheKey2, mwApi, requestId);\n      }\n    }\n    if (queryThunk.pending.match(action)) {\n      checkForNewCacheKey(action.meta.arg.endpointName, cacheKey, action.meta.requestId, action.meta.arg.originalArgs);\n    } else if (api.internalActions.cacheEntriesUpserted.match(action)) {\n      for (const {\n        queryDescription,\n        value\n      } of action.payload) {\n        const {\n          endpointName,\n          originalArgs,\n          queryCacheKey\n        } = queryDescription;\n        checkForNewCacheKey(endpointName, queryCacheKey, action.meta.requestId, originalArgs);\n        resolveLifecycleEntry(queryCacheKey, value, {});\n      }\n    } else if (mutationThunk.pending.match(action)) {\n      const state = mwApi.getState()[reducerPath].mutations[cacheKey];\n      if (state) {\n        handleNewKey(action.meta.arg.endpointName, action.meta.arg.originalArgs, cacheKey, mwApi, action.meta.requestId);\n      }\n    } else if (isFulfilledThunk(action)) {\n      resolveLifecycleEntry(cacheKey, action.payload, action.meta.baseQueryMeta);\n    } else if (api.internalActions.removeQueryResult.match(action) || api.internalActions.removeMutationResult.match(action)) {\n      removeLifecycleEntry(cacheKey);\n    } else if (api.util.resetApiState.match(action)) {\n      for (const cacheKey2 of Object.keys(lifecycleMap)) {\n        removeLifecycleEntry(cacheKey2);\n      }\n    }\n  };\n  function getCacheKey(action) {\n    if (isQueryThunk(action)) return action.meta.arg.queryCacheKey;\n    if (isMutationThunk(action)) {\n      return action.meta.arg.fixedCacheKey ?? action.meta.requestId;\n    }\n    if (api.internalActions.removeQueryResult.match(action)) return action.payload.queryCacheKey;\n    if (api.internalActions.removeMutationResult.match(action)) return getMutationCacheKey(action.payload);\n    return \"\";\n  }\n  function handleNewKey(endpointName, originalArgs, queryCacheKey, mwApi, requestId) {\n    const endpointDefinition = context.endpointDefinitions[endpointName];\n    const onCacheEntryAdded = endpointDefinition?.onCacheEntryAdded;\n    if (!onCacheEntryAdded) return;\n    const lifecycle = {};\n    const cacheEntryRemoved = new Promise((resolve) => {\n      lifecycle.cacheEntryRemoved = resolve;\n    });\n    const cacheDataLoaded = Promise.race([new Promise((resolve) => {\n      lifecycle.valueResolved = resolve;\n    }), cacheEntryRemoved.then(() => {\n      throw neverResolvedError;\n    })]);\n    cacheDataLoaded.catch(() => {\n    });\n    lifecycleMap[queryCacheKey] = lifecycle;\n    const selector = api.endpoints[endpointName].select(endpointDefinition.type === \"query\" /* query */ ? originalArgs : queryCacheKey);\n    const extra = mwApi.dispatch((_, __, extra2) => extra2);\n    const lifecycleApi = {\n      ...mwApi,\n      getCacheEntry: () => selector(mwApi.getState()),\n      requestId,\n      extra,\n      updateCachedData: endpointDefinition.type === \"query\" /* query */ ? (updateRecipe) => mwApi.dispatch(api.util.updateQueryData(endpointName, originalArgs, updateRecipe)) : void 0,\n      cacheDataLoaded,\n      cacheEntryRemoved\n    };\n    const runningHandler = onCacheEntryAdded(originalArgs, lifecycleApi);\n    Promise.resolve(runningHandler).catch((e) => {\n      if (e === neverResolvedError) return;\n      throw e;\n    });\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/devMiddleware.ts\nvar buildDevCheckHandler = ({\n  api,\n  context: {\n    apiUid\n  },\n  reducerPath\n}) => {\n  return (action, mwApi) => {\n    if (api.util.resetApiState.match(action)) {\n      mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\n    }\n    if (typeof process !== \"undefined\" && true) {\n      if (api.internalActions.middlewareRegistered.match(action) && action.payload === apiUid && mwApi.getState()[reducerPath]?.config?.middlewareRegistered === \"conflict\") {\n        console.warn(`There is a mismatch between slice and middleware for the reducerPath \"${reducerPath}\".\nYou can only have one api per reducer path, this will lead to crashes in various situations!${reducerPath === \"api\" ? `\nIf you have multiple apis, you *have* to specify the reducerPath option when using createApi!` : \"\"}`);\n      }\n    }\n  };\n};\n\n// src/query/core/buildMiddleware/invalidationByTags.ts\nvar buildInvalidationByTagsHandler = ({\n  reducerPath,\n  context,\n  context: {\n    endpointDefinitions\n  },\n  mutationThunk,\n  queryThunk,\n  api,\n  assertTagType,\n  refetchQuery,\n  internalState\n}) => {\n  const {\n    removeQueryResult\n  } = api.internalActions;\n  const isThunkActionWithTags = (0, import_toolkit.isAnyOf)((0, import_toolkit.isFulfilled)(mutationThunk), (0, import_toolkit.isRejectedWithValue)(mutationThunk));\n  const isQueryEnd = (0, import_toolkit.isAnyOf)((0, import_toolkit.isFulfilled)(mutationThunk, queryThunk), (0, import_toolkit.isRejected)(mutationThunk, queryThunk));\n  let pendingTagInvalidations = [];\n  const handler = (action, mwApi) => {\n    if (isThunkActionWithTags(action)) {\n      invalidateTags(calculateProvidedByThunk(action, \"invalidatesTags\", endpointDefinitions, assertTagType), mwApi);\n    } else if (isQueryEnd(action)) {\n      invalidateTags([], mwApi);\n    } else if (api.util.invalidateTags.match(action)) {\n      invalidateTags(calculateProvidedBy(action.payload, void 0, void 0, void 0, void 0, assertTagType), mwApi);\n    }\n  };\n  function hasPendingRequests(state) {\n    for (const key in state.queries) {\n      if (state.queries[key]?.status === \"pending\" /* pending */) return true;\n    }\n    for (const key in state.mutations) {\n      if (state.mutations[key]?.status === \"pending\" /* pending */) return true;\n    }\n    return false;\n  }\n  function invalidateTags(newTags, mwApi) {\n    const rootState = mwApi.getState();\n    const state = rootState[reducerPath];\n    pendingTagInvalidations.push(...newTags);\n    if (state.config.invalidationBehavior === \"delayed\" && hasPendingRequests(state)) {\n      return;\n    }\n    const tags = pendingTagInvalidations;\n    pendingTagInvalidations = [];\n    if (tags.length === 0) return;\n    const toInvalidate = api.util.selectInvalidatedBy(rootState, tags);\n    context.batch(() => {\n      const valuesArray = Array.from(toInvalidate.values());\n      for (const {\n        queryCacheKey\n      } of valuesArray) {\n        const querySubState = state.queries[queryCacheKey];\n        const subscriptionSubState = internalState.currentSubscriptions[queryCacheKey] ?? {};\n        if (querySubState) {\n          if (countObjectKeys(subscriptionSubState) === 0) {\n            mwApi.dispatch(removeQueryResult({\n              queryCacheKey\n            }));\n          } else if (querySubState.status !== \"uninitialized\" /* uninitialized */) {\n            mwApi.dispatch(refetchQuery(querySubState));\n          }\n        }\n      }\n    });\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/polling.ts\nvar buildPollingHandler = ({\n  reducerPath,\n  queryThunk,\n  api,\n  refetchQuery,\n  internalState\n}) => {\n  const currentPolls = {};\n  const handler = (action, mwApi) => {\n    if (api.internalActions.updateSubscriptionOptions.match(action) || api.internalActions.unsubscribeQueryResult.match(action)) {\n      updatePollingInterval(action.payload, mwApi);\n    }\n    if (queryThunk.pending.match(action) || queryThunk.rejected.match(action) && action.meta.condition) {\n      updatePollingInterval(action.meta.arg, mwApi);\n    }\n    if (queryThunk.fulfilled.match(action) || queryThunk.rejected.match(action) && !action.meta.condition) {\n      startNextPoll(action.meta.arg, mwApi);\n    }\n    if (api.util.resetApiState.match(action)) {\n      clearPolls();\n    }\n  };\n  function startNextPoll({\n    queryCacheKey\n  }, api2) {\n    const state = api2.getState()[reducerPath];\n    const querySubState = state.queries[queryCacheKey];\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    if (!querySubState || querySubState.status === \"uninitialized\" /* uninitialized */) return;\n    const {\n      lowestPollingInterval,\n      skipPollingIfUnfocused\n    } = findLowestPollingInterval(subscriptions);\n    if (!Number.isFinite(lowestPollingInterval)) return;\n    const currentPoll = currentPolls[queryCacheKey];\n    if (currentPoll?.timeout) {\n      clearTimeout(currentPoll.timeout);\n      currentPoll.timeout = void 0;\n    }\n    const nextPollTimestamp = Date.now() + lowestPollingInterval;\n    currentPolls[queryCacheKey] = {\n      nextPollTimestamp,\n      pollingInterval: lowestPollingInterval,\n      timeout: setTimeout(() => {\n        if (state.config.focused || !skipPollingIfUnfocused) {\n          api2.dispatch(refetchQuery(querySubState));\n        }\n        startNextPoll({\n          queryCacheKey\n        }, api2);\n      }, lowestPollingInterval)\n    };\n  }\n  function updatePollingInterval({\n    queryCacheKey\n  }, api2) {\n    const state = api2.getState()[reducerPath];\n    const querySubState = state.queries[queryCacheKey];\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    if (!querySubState || querySubState.status === \"uninitialized\" /* uninitialized */) {\n      return;\n    }\n    const {\n      lowestPollingInterval\n    } = findLowestPollingInterval(subscriptions);\n    if (!Number.isFinite(lowestPollingInterval)) {\n      cleanupPollForKey(queryCacheKey);\n      return;\n    }\n    const currentPoll = currentPolls[queryCacheKey];\n    const nextPollTimestamp = Date.now() + lowestPollingInterval;\n    if (!currentPoll || nextPollTimestamp < currentPoll.nextPollTimestamp) {\n      startNextPoll({\n        queryCacheKey\n      }, api2);\n    }\n  }\n  function cleanupPollForKey(key) {\n    const existingPoll = currentPolls[key];\n    if (existingPoll?.timeout) {\n      clearTimeout(existingPoll.timeout);\n    }\n    delete currentPolls[key];\n  }\n  function clearPolls() {\n    for (const key of Object.keys(currentPolls)) {\n      cleanupPollForKey(key);\n    }\n  }\n  function findLowestPollingInterval(subscribers = {}) {\n    let skipPollingIfUnfocused = false;\n    let lowestPollingInterval = Number.POSITIVE_INFINITY;\n    for (let key in subscribers) {\n      if (!!subscribers[key].pollingInterval) {\n        lowestPollingInterval = Math.min(subscribers[key].pollingInterval, lowestPollingInterval);\n        skipPollingIfUnfocused = subscribers[key].skipPollingIfUnfocused || skipPollingIfUnfocused;\n      }\n    }\n    return {\n      lowestPollingInterval,\n      skipPollingIfUnfocused\n    };\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/queryLifecycle.ts\nvar buildQueryLifecycleHandler = ({\n  api,\n  context,\n  queryThunk,\n  mutationThunk\n}) => {\n  const isPendingThunk = (0, import_toolkit.isPending)(queryThunk, mutationThunk);\n  const isRejectedThunk = (0, import_toolkit.isRejected)(queryThunk, mutationThunk);\n  const isFullfilledThunk = (0, import_toolkit.isFulfilled)(queryThunk, mutationThunk);\n  const lifecycleMap = {};\n  const handler = (action, mwApi) => {\n    if (isPendingThunk(action)) {\n      const {\n        requestId,\n        arg: {\n          endpointName,\n          originalArgs\n        }\n      } = action.meta;\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      const onQueryStarted = endpointDefinition?.onQueryStarted;\n      if (onQueryStarted) {\n        const lifecycle = {};\n        const queryFulfilled = new Promise((resolve, reject) => {\n          lifecycle.resolve = resolve;\n          lifecycle.reject = reject;\n        });\n        queryFulfilled.catch(() => {\n        });\n        lifecycleMap[requestId] = lifecycle;\n        const selector = api.endpoints[endpointName].select(endpointDefinition.type === \"query\" /* query */ ? originalArgs : requestId);\n        const extra = mwApi.dispatch((_, __, extra2) => extra2);\n        const lifecycleApi = {\n          ...mwApi,\n          getCacheEntry: () => selector(mwApi.getState()),\n          requestId,\n          extra,\n          updateCachedData: endpointDefinition.type === \"query\" /* query */ ? (updateRecipe) => mwApi.dispatch(api.util.updateQueryData(endpointName, originalArgs, updateRecipe)) : void 0,\n          queryFulfilled\n        };\n        onQueryStarted(originalArgs, lifecycleApi);\n      }\n    } else if (isFullfilledThunk(action)) {\n      const {\n        requestId,\n        baseQueryMeta\n      } = action.meta;\n      lifecycleMap[requestId]?.resolve({\n        data: action.payload,\n        meta: baseQueryMeta\n      });\n      delete lifecycleMap[requestId];\n    } else if (isRejectedThunk(action)) {\n      const {\n        requestId,\n        rejectedWithValue,\n        baseQueryMeta\n      } = action.meta;\n      lifecycleMap[requestId]?.reject({\n        error: action.payload ?? action.error,\n        isUnhandledError: !rejectedWithValue,\n        meta: baseQueryMeta\n      });\n      delete lifecycleMap[requestId];\n    }\n  };\n  return handler;\n};\n\n// src/query/core/buildMiddleware/windowEventHandling.ts\nvar buildWindowEventHandler = ({\n  reducerPath,\n  context,\n  api,\n  refetchQuery,\n  internalState\n}) => {\n  const {\n    removeQueryResult\n  } = api.internalActions;\n  const handler = (action, mwApi) => {\n    if (onFocus.match(action)) {\n      refetchValidQueries(mwApi, \"refetchOnFocus\");\n    }\n    if (onOnline.match(action)) {\n      refetchValidQueries(mwApi, \"refetchOnReconnect\");\n    }\n  };\n  function refetchValidQueries(api2, type) {\n    const state = api2.getState()[reducerPath];\n    const queries = state.queries;\n    const subscriptions = internalState.currentSubscriptions;\n    context.batch(() => {\n      for (const queryCacheKey of Object.keys(subscriptions)) {\n        const querySubState = queries[queryCacheKey];\n        const subscriptionSubState = subscriptions[queryCacheKey];\n        if (!subscriptionSubState || !querySubState) continue;\n        const shouldRefetch = Object.values(subscriptionSubState).some((sub) => sub[type] === true) || Object.values(subscriptionSubState).every((sub) => sub[type] === void 0) && state.config[type];\n        if (shouldRefetch) {\n          if (countObjectKeys(subscriptionSubState) === 0) {\n            api2.dispatch(removeQueryResult({\n              queryCacheKey\n            }));\n          } else if (querySubState.status !== \"uninitialized\" /* uninitialized */) {\n            api2.dispatch(refetchQuery(querySubState));\n          }\n        }\n      }\n    });\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/index.ts\nfunction buildMiddleware(input) {\n  const {\n    reducerPath,\n    queryThunk,\n    api,\n    context\n  } = input;\n  const {\n    apiUid\n  } = context;\n  const actions = {\n    invalidateTags: (0, import_toolkit.createAction)(`${reducerPath}/invalidateTags`)\n  };\n  const isThisApiSliceAction = (action) => action.type.startsWith(`${reducerPath}/`);\n  const handlerBuilders = [buildDevCheckHandler, buildCacheCollectionHandler, buildInvalidationByTagsHandler, buildPollingHandler, buildCacheLifecycleHandler, buildQueryLifecycleHandler];\n  const middleware = (mwApi) => {\n    let initialized2 = false;\n    const internalState = {\n      currentSubscriptions: {}\n    };\n    const builderArgs = {\n      ...input,\n      internalState,\n      refetchQuery,\n      isThisApiSliceAction\n    };\n    const handlers = handlerBuilders.map((build) => build(builderArgs));\n    const batchedActionsHandler = buildBatchedActionsHandler(builderArgs);\n    const windowEventsHandler = buildWindowEventHandler(builderArgs);\n    return (next) => {\n      return (action) => {\n        if (!(0, import_toolkit.isAction)(action)) {\n          return next(action);\n        }\n        if (!initialized2) {\n          initialized2 = true;\n          mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\n        }\n        const mwApiWithNext = {\n          ...mwApi,\n          next\n        };\n        const stateBefore = mwApi.getState();\n        const [actionShouldContinue, internalProbeResult] = batchedActionsHandler(action, mwApiWithNext, stateBefore);\n        let res;\n        if (actionShouldContinue) {\n          res = next(action);\n        } else {\n          res = internalProbeResult;\n        }\n        if (!!mwApi.getState()[reducerPath]) {\n          windowEventsHandler(action, mwApiWithNext, stateBefore);\n          if (isThisApiSliceAction(action) || context.hasRehydrationInfo(action)) {\n            for (const handler of handlers) {\n              handler(action, mwApiWithNext, stateBefore);\n            }\n          }\n        }\n        return res;\n      };\n    };\n  };\n  return {\n    middleware,\n    actions\n  };\n  function refetchQuery(querySubState) {\n    return input.api.endpoints[querySubState.endpointName].initiate(querySubState.originalArgs, {\n      subscribe: false,\n      forceRefetch: true\n    });\n  }\n}\n\n// src/query/core/module.ts\nvar coreModuleName = /* @__PURE__ */ Symbol();\nvar coreModule = ({\n  createSelector: createSelector2 = import_toolkit.createSelector\n} = {}) => ({\n  name: coreModuleName,\n  init(api, {\n    baseQuery,\n    tagTypes,\n    reducerPath,\n    serializeQueryArgs,\n    keepUnusedDataFor,\n    refetchOnMountOrArgChange,\n    refetchOnFocus,\n    refetchOnReconnect,\n    invalidationBehavior\n  }, context) {\n    (0, import_immer5.enablePatches)();\n    assertCast(serializeQueryArgs);\n    const assertTagType = (tag) => {\n      if (typeof process !== \"undefined\" && true) {\n        if (!tagTypes.includes(tag.type)) {\n          console.error(`Tag type '${tag.type}' was used, but not specified in \\`tagTypes\\`!`);\n        }\n      }\n      return tag;\n    };\n    Object.assign(api, {\n      reducerPath,\n      endpoints: {},\n      internalActions: {\n        onOnline,\n        onOffline,\n        onFocus,\n        onFocusLost\n      },\n      util: {}\n    });\n    const {\n      queryThunk,\n      mutationThunk,\n      patchQueryData,\n      updateQueryData,\n      upsertQueryData,\n      prefetch,\n      buildMatchThunkActions\n    } = buildThunks({\n      baseQuery,\n      reducerPath,\n      context,\n      api,\n      serializeQueryArgs,\n      assertTagType\n    });\n    const {\n      reducer,\n      actions: sliceActions\n    } = buildSlice({\n      context,\n      queryThunk,\n      mutationThunk,\n      serializeQueryArgs,\n      reducerPath,\n      assertTagType,\n      config: {\n        refetchOnFocus,\n        refetchOnReconnect,\n        refetchOnMountOrArgChange,\n        keepUnusedDataFor,\n        reducerPath,\n        invalidationBehavior\n      }\n    });\n    safeAssign(api.util, {\n      patchQueryData,\n      updateQueryData,\n      upsertQueryData,\n      prefetch,\n      resetApiState: sliceActions.resetApiState,\n      upsertQueryEntries: sliceActions.cacheEntriesUpserted\n    });\n    safeAssign(api.internalActions, sliceActions);\n    const {\n      middleware,\n      actions: middlewareActions\n    } = buildMiddleware({\n      reducerPath,\n      context,\n      queryThunk,\n      mutationThunk,\n      api,\n      assertTagType\n    });\n    safeAssign(api.util, middlewareActions);\n    safeAssign(api, {\n      reducer,\n      middleware\n    });\n    const {\n      buildQuerySelector,\n      buildMutationSelector,\n      selectInvalidatedBy,\n      selectCachedArgsForQuery\n    } = buildSelectors({\n      serializeQueryArgs,\n      reducerPath,\n      createSelector: createSelector2\n    });\n    safeAssign(api.util, {\n      selectInvalidatedBy,\n      selectCachedArgsForQuery\n    });\n    const {\n      buildInitiateQuery,\n      buildInitiateMutation,\n      getRunningMutationThunk,\n      getRunningMutationsThunk,\n      getRunningQueriesThunk,\n      getRunningQueryThunk\n    } = buildInitiate({\n      queryThunk,\n      mutationThunk,\n      api,\n      serializeQueryArgs,\n      context\n    });\n    safeAssign(api.util, {\n      getRunningMutationThunk,\n      getRunningMutationsThunk,\n      getRunningQueryThunk,\n      getRunningQueriesThunk\n    });\n    return {\n      name: coreModuleName,\n      injectEndpoint(endpointName, definition) {\n        const anyApi = api;\n        anyApi.endpoints[endpointName] ??= {};\n        if (isQueryDefinition(definition)) {\n          safeAssign(anyApi.endpoints[endpointName], {\n            name: endpointName,\n            select: buildQuerySelector(endpointName, definition),\n            initiate: buildInitiateQuery(endpointName, definition)\n          }, buildMatchThunkActions(queryThunk, endpointName));\n        } else if (isMutationDefinition(definition)) {\n          safeAssign(anyApi.endpoints[endpointName], {\n            name: endpointName,\n            select: buildMutationSelector(),\n            initiate: buildInitiateMutation(endpointName)\n          }, buildMatchThunkActions(mutationThunk, endpointName));\n        }\n      }\n    };\n  }\n});\n\n// src/query/core/index.ts\nvar createApi = /* @__PURE__ */ buildCreateApi(coreModule());\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=rtk-query.development.cjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@reduxjs/toolkit/dist/query/cjs/rtk-query.development.cjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/cjs/rtk-query-react.development.cjs":
/*!********************************************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/query/react/cjs/rtk-query-react.development.cjs ***!
  \********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/query/react/index.ts\nvar react_exports = {};\n__export(react_exports, {\n  ApiProvider: () => ApiProvider,\n  UNINITIALIZED_VALUE: () => UNINITIALIZED_VALUE,\n  createApi: () => createApi,\n  reactHooksModule: () => reactHooksModule,\n  reactHooksModuleName: () => reactHooksModuleName\n});\nmodule.exports = __toCommonJS(react_exports);\nvar import_query3 = __webpack_require__(/*! @reduxjs/toolkit/query */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/cjs/index.js\");\n\n// src/query/react/module.ts\nvar import_toolkit3 = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js\");\nvar import_react_redux3 = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/cjs/index.js\");\nvar import_reselect = __webpack_require__(/*! reselect */ \"(ssr)/./node_modules/reselect/dist/cjs/reselect.cjs\");\n\n// src/query/utils/capitalize.ts\nfunction capitalize(str) {\n  return str.replace(str[0], str[0].toUpperCase());\n}\n\n// src/query/core/rtkImports.ts\nvar import_toolkit = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js\");\n\n// src/query/utils/countObjectKeys.ts\nfunction countObjectKeys(obj) {\n  let count = 0;\n  for (const _key in obj) {\n    count++;\n  }\n  return count;\n}\n\n// src/query/endpointDefinitions.ts\nfunction isQueryDefinition(e) {\n  return e.type === \"query\" /* query */;\n}\nfunction isMutationDefinition(e) {\n  return e.type === \"mutation\" /* mutation */;\n}\n\n// src/query/tsHelpers.ts\nfunction safeAssign(target, ...args) {\n  return Object.assign(target, ...args);\n}\n\n// src/query/react/buildHooks.ts\nvar import_toolkit2 = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js\");\nvar import_query = __webpack_require__(/*! @reduxjs/toolkit/query */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/cjs/index.js\");\nvar import_react3 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar import_react_redux2 = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/cjs/index.js\");\n\n// src/query/defaultSerializeQueryArgs.ts\nvar cache = WeakMap ? /* @__PURE__ */ new WeakMap() : void 0;\nvar defaultSerializeQueryArgs = ({\n  endpointName,\n  queryArgs\n}) => {\n  let serialized = \"\";\n  const cached = cache?.get(queryArgs);\n  if (typeof cached === \"string\") {\n    serialized = cached;\n  } else {\n    const stringified = JSON.stringify(queryArgs, (key, value) => {\n      value = typeof value === \"bigint\" ? {\n        $bigint: value.toString()\n      } : value;\n      value = (0, import_toolkit.isPlainObject)(value) ? Object.keys(value).sort().reduce((acc, key2) => {\n        acc[key2] = value[key2];\n        return acc;\n      }, {}) : value;\n      return value;\n    });\n    if ((0, import_toolkit.isPlainObject)(queryArgs)) {\n      cache?.set(queryArgs, stringified);\n    }\n    serialized = stringified;\n  }\n  return `${endpointName}(${serialized})`;\n};\n\n// src/query/react/constants.ts\nvar UNINITIALIZED_VALUE = Symbol();\n\n// src/query/react/useSerializedStableValue.ts\nvar import_react = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction useStableQueryArgs(queryArgs, serialize, endpointDefinition, endpointName) {\n  const incoming = (0, import_react.useMemo)(() => ({\n    queryArgs,\n    serialized: typeof queryArgs == \"object\" ? serialize({\n      queryArgs,\n      endpointDefinition,\n      endpointName\n    }) : queryArgs\n  }), [queryArgs, serialize, endpointDefinition, endpointName]);\n  const cache2 = (0, import_react.useRef)(incoming);\n  (0, import_react.useEffect)(() => {\n    if (cache2.current.serialized !== incoming.serialized) {\n      cache2.current = incoming;\n    }\n  }, [incoming]);\n  return cache2.current.serialized === incoming.serialized ? cache2.current.queryArgs : queryArgs;\n}\n\n// src/query/react/useShallowStableValue.ts\nvar import_react2 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar import_react_redux = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/cjs/index.js\");\nfunction useShallowStableValue(value) {\n  const cache2 = (0, import_react2.useRef)(value);\n  (0, import_react2.useEffect)(() => {\n    if (!(0, import_react_redux.shallowEqual)(cache2.current, value)) {\n      cache2.current = value;\n    }\n  }, [value]);\n  return (0, import_react_redux.shallowEqual)(cache2.current, value) ? cache2.current : value;\n}\n\n// src/query/react/buildHooks.ts\nvar canUseDOM = () => !!(typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\");\nvar isDOM = /* @__PURE__ */ canUseDOM();\nvar isRunningInReactNative = () => typeof navigator !== \"undefined\" && navigator.product === \"ReactNative\";\nvar isReactNative = /* @__PURE__ */ isRunningInReactNative();\nvar getUseIsomorphicLayoutEffect = () => isDOM || isReactNative ? import_react3.useLayoutEffect : import_react3.useEffect;\nvar useIsomorphicLayoutEffect = /* @__PURE__ */ getUseIsomorphicLayoutEffect();\nvar noPendingQueryStateSelector = (selected) => {\n  if (selected.isUninitialized) {\n    return {\n      ...selected,\n      isUninitialized: false,\n      isFetching: true,\n      isLoading: selected.data !== void 0 ? false : true,\n      status: import_query.QueryStatus.pending\n    };\n  }\n  return selected;\n};\nfunction buildHooks({\n  api,\n  moduleOptions: {\n    batch,\n    hooks: {\n      useDispatch,\n      useSelector,\n      useStore\n    },\n    unstable__sideEffectsInRender,\n    createSelector: createSelector2\n  },\n  serializeQueryArgs,\n  context\n}) {\n  const usePossiblyImmediateEffect = unstable__sideEffectsInRender ? (cb) => cb() : import_react3.useEffect;\n  return {\n    buildQueryHooks,\n    buildMutationHook,\n    usePrefetch\n  };\n  function queryStatePreSelector(currentState, lastResult, queryArgs) {\n    if (lastResult?.endpointName && currentState.isUninitialized) {\n      const {\n        endpointName\n      } = lastResult;\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      if (queryArgs !== import_query.skipToken && serializeQueryArgs({\n        queryArgs: lastResult.originalArgs,\n        endpointDefinition,\n        endpointName\n      }) === serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      })) lastResult = void 0;\n    }\n    let data = currentState.isSuccess ? currentState.data : lastResult?.data;\n    if (data === void 0) data = currentState.data;\n    const hasData = data !== void 0;\n    const isFetching = currentState.isLoading;\n    const isLoading = (!lastResult || lastResult.isLoading || lastResult.isUninitialized) && !hasData && isFetching;\n    const isSuccess = currentState.isSuccess || hasData && (isFetching && !lastResult?.isError || currentState.isUninitialized);\n    return {\n      ...currentState,\n      data,\n      currentData: currentState.data,\n      isFetching,\n      isLoading,\n      isSuccess\n    };\n  }\n  function usePrefetch(endpointName, defaultOptions) {\n    const dispatch = useDispatch();\n    const stableDefaultOptions = useShallowStableValue(defaultOptions);\n    return (0, import_react3.useCallback)((arg, options) => dispatch(api.util.prefetch(endpointName, arg, {\n      ...stableDefaultOptions,\n      ...options\n    })), [endpointName, dispatch, stableDefaultOptions]);\n  }\n  function buildQueryHooks(name) {\n    const useQuerySubscription = (arg, {\n      refetchOnReconnect,\n      refetchOnFocus,\n      refetchOnMountOrArgChange,\n      skip = false,\n      pollingInterval = 0,\n      skipPollingIfUnfocused = false\n    } = {}) => {\n      const {\n        initiate\n      } = api.endpoints[name];\n      const dispatch = useDispatch();\n      const subscriptionSelectorsRef = (0, import_react3.useRef)(void 0);\n      if (!subscriptionSelectorsRef.current) {\n        const returnedValue = dispatch(api.internalActions.internal_getRTKQSubscriptions());\n        if (true) {\n          if (typeof returnedValue !== \"object\" || typeof returnedValue?.type === \"string\") {\n            throw new Error( false ? 0 : `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\n    You must add the middleware for RTK-Query to function correctly!`);\n          }\n        }\n        subscriptionSelectorsRef.current = returnedValue;\n      }\n      const stableArg = useStableQueryArgs(\n        skip ? import_query.skipToken : arg,\n        // Even if the user provided a per-endpoint `serializeQueryArgs` with\n        // a consistent return value, _here_ we want to use the default behavior\n        // so we can tell if _anything_ actually changed. Otherwise, we can end up\n        // with a case where the query args did change but the serialization doesn't,\n        // and then we never try to initiate a refetch.\n        defaultSerializeQueryArgs,\n        context.endpointDefinitions[name],\n        name\n      );\n      const stableSubscriptionOptions = useShallowStableValue({\n        refetchOnReconnect,\n        refetchOnFocus,\n        pollingInterval,\n        skipPollingIfUnfocused\n      });\n      const lastRenderHadSubscription = (0, import_react3.useRef)(false);\n      const promiseRef = (0, import_react3.useRef)(void 0);\n      let {\n        queryCacheKey,\n        requestId\n      } = promiseRef.current || {};\n      let currentRenderHasSubscription = false;\n      if (queryCacheKey && requestId) {\n        currentRenderHasSubscription = subscriptionSelectorsRef.current.isRequestSubscribed(queryCacheKey, requestId);\n      }\n      const subscriptionRemoved = !currentRenderHasSubscription && lastRenderHadSubscription.current;\n      usePossiblyImmediateEffect(() => {\n        lastRenderHadSubscription.current = currentRenderHasSubscription;\n      });\n      usePossiblyImmediateEffect(() => {\n        if (subscriptionRemoved) {\n          promiseRef.current = void 0;\n        }\n      }, [subscriptionRemoved]);\n      usePossiblyImmediateEffect(() => {\n        const lastPromise = promiseRef.current;\n        if (typeof process !== \"undefined\" && false) {}\n        if (stableArg === import_query.skipToken) {\n          lastPromise?.unsubscribe();\n          promiseRef.current = void 0;\n          return;\n        }\n        const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions;\n        if (!lastPromise || lastPromise.arg !== stableArg) {\n          lastPromise?.unsubscribe();\n          const promise = dispatch(initiate(stableArg, {\n            subscriptionOptions: stableSubscriptionOptions,\n            forceRefetch: refetchOnMountOrArgChange\n          }));\n          promiseRef.current = promise;\n        } else if (stableSubscriptionOptions !== lastSubscriptionOptions) {\n          lastPromise.updateSubscriptionOptions(stableSubscriptionOptions);\n        }\n      }, [dispatch, initiate, refetchOnMountOrArgChange, stableArg, stableSubscriptionOptions, subscriptionRemoved]);\n      (0, import_react3.useEffect)(() => {\n        return () => {\n          promiseRef.current?.unsubscribe();\n          promiseRef.current = void 0;\n        };\n      }, []);\n      return (0, import_react3.useMemo)(() => ({\n        /**\n         * A method to manually refetch data for the query\n         */\n        refetch: () => {\n          if (!promiseRef.current) throw new Error( false ? 0 : \"Cannot refetch a query that has not been started yet.\");\n          return promiseRef.current?.refetch();\n        }\n      }), []);\n    };\n    const useLazyQuerySubscription = ({\n      refetchOnReconnect,\n      refetchOnFocus,\n      pollingInterval = 0,\n      skipPollingIfUnfocused = false\n    } = {}) => {\n      const {\n        initiate\n      } = api.endpoints[name];\n      const dispatch = useDispatch();\n      const [arg, setArg] = (0, import_react3.useState)(UNINITIALIZED_VALUE);\n      const promiseRef = (0, import_react3.useRef)(void 0);\n      const stableSubscriptionOptions = useShallowStableValue({\n        refetchOnReconnect,\n        refetchOnFocus,\n        pollingInterval,\n        skipPollingIfUnfocused\n      });\n      usePossiblyImmediateEffect(() => {\n        const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions;\n        if (stableSubscriptionOptions !== lastSubscriptionOptions) {\n          promiseRef.current?.updateSubscriptionOptions(stableSubscriptionOptions);\n        }\n      }, [stableSubscriptionOptions]);\n      const subscriptionOptionsRef = (0, import_react3.useRef)(stableSubscriptionOptions);\n      usePossiblyImmediateEffect(() => {\n        subscriptionOptionsRef.current = stableSubscriptionOptions;\n      }, [stableSubscriptionOptions]);\n      const trigger = (0, import_react3.useCallback)(function(arg2, preferCacheValue = false) {\n        let promise;\n        batch(() => {\n          promiseRef.current?.unsubscribe();\n          promiseRef.current = promise = dispatch(initiate(arg2, {\n            subscriptionOptions: subscriptionOptionsRef.current,\n            forceRefetch: !preferCacheValue\n          }));\n          setArg(arg2);\n        });\n        return promise;\n      }, [dispatch, initiate]);\n      const reset = (0, import_react3.useCallback)(() => {\n        if (promiseRef.current?.queryCacheKey) {\n          dispatch(api.internalActions.removeQueryResult({\n            queryCacheKey: promiseRef.current?.queryCacheKey\n          }));\n        }\n      }, [dispatch]);\n      (0, import_react3.useEffect)(() => {\n        return () => {\n          promiseRef?.current?.unsubscribe();\n        };\n      }, []);\n      (0, import_react3.useEffect)(() => {\n        if (arg !== UNINITIALIZED_VALUE && !promiseRef.current) {\n          trigger(arg, true);\n        }\n      }, [arg, trigger]);\n      return (0, import_react3.useMemo)(() => [trigger, arg, {\n        reset\n      }], [trigger, arg, reset]);\n    };\n    const useQueryState = (arg, {\n      skip = false,\n      selectFromResult\n    } = {}) => {\n      const {\n        select\n      } = api.endpoints[name];\n      const stableArg = useStableQueryArgs(skip ? import_query.skipToken : arg, serializeQueryArgs, context.endpointDefinitions[name], name);\n      const lastValue = (0, import_react3.useRef)(void 0);\n      const selectDefaultResult = (0, import_react3.useMemo)(() => createSelector2([select(stableArg), (_, lastResult) => lastResult, (_) => stableArg], queryStatePreSelector, {\n        memoizeOptions: {\n          resultEqualityCheck: import_react_redux2.shallowEqual\n        }\n      }), [select, stableArg]);\n      const querySelector = (0, import_react3.useMemo)(() => selectFromResult ? createSelector2([selectDefaultResult], selectFromResult, {\n        devModeChecks: {\n          identityFunctionCheck: \"never\"\n        }\n      }) : selectDefaultResult, [selectDefaultResult, selectFromResult]);\n      const currentState = useSelector((state) => querySelector(state, lastValue.current), import_react_redux2.shallowEqual);\n      const store = useStore();\n      const newLastValue = selectDefaultResult(store.getState(), lastValue.current);\n      useIsomorphicLayoutEffect(() => {\n        lastValue.current = newLastValue;\n      }, [newLastValue]);\n      return currentState;\n    };\n    return {\n      useQueryState,\n      useQuerySubscription,\n      useLazyQuerySubscription,\n      useLazyQuery(options) {\n        const [trigger, arg, {\n          reset\n        }] = useLazyQuerySubscription(options);\n        const queryStateResults = useQueryState(arg, {\n          ...options,\n          skip: arg === UNINITIALIZED_VALUE\n        });\n        const info = (0, import_react3.useMemo)(() => ({\n          lastArg: arg\n        }), [arg]);\n        return (0, import_react3.useMemo)(() => [trigger, {\n          ...queryStateResults,\n          reset\n        }, info], [trigger, queryStateResults, reset, info]);\n      },\n      useQuery(arg, options) {\n        const querySubscriptionResults = useQuerySubscription(arg, options);\n        const queryStateResults = useQueryState(arg, {\n          selectFromResult: arg === import_query.skipToken || options?.skip ? void 0 : noPendingQueryStateSelector,\n          ...options\n        });\n        const {\n          data,\n          status,\n          isLoading,\n          isSuccess,\n          isError,\n          error\n        } = queryStateResults;\n        (0, import_react3.useDebugValue)({\n          data,\n          status,\n          isLoading,\n          isSuccess,\n          isError,\n          error\n        });\n        return (0, import_react3.useMemo)(() => ({\n          ...queryStateResults,\n          ...querySubscriptionResults\n        }), [queryStateResults, querySubscriptionResults]);\n      }\n    };\n  }\n  function buildMutationHook(name) {\n    return ({\n      selectFromResult,\n      fixedCacheKey\n    } = {}) => {\n      const {\n        select,\n        initiate\n      } = api.endpoints[name];\n      const dispatch = useDispatch();\n      const [promise, setPromise] = (0, import_react3.useState)();\n      (0, import_react3.useEffect)(() => () => {\n        if (!promise?.arg.fixedCacheKey) {\n          promise?.reset();\n        }\n      }, [promise]);\n      const triggerMutation = (0, import_react3.useCallback)(function(arg) {\n        const promise2 = dispatch(initiate(arg, {\n          fixedCacheKey\n        }));\n        setPromise(promise2);\n        return promise2;\n      }, [dispatch, initiate, fixedCacheKey]);\n      const {\n        requestId\n      } = promise || {};\n      const selectDefaultResult = (0, import_react3.useMemo)(() => select({\n        fixedCacheKey,\n        requestId: promise?.requestId\n      }), [fixedCacheKey, promise, select]);\n      const mutationSelector = (0, import_react3.useMemo)(() => selectFromResult ? createSelector2([selectDefaultResult], selectFromResult) : selectDefaultResult, [selectFromResult, selectDefaultResult]);\n      const currentState = useSelector(mutationSelector, import_react_redux2.shallowEqual);\n      const originalArgs = fixedCacheKey == null ? promise?.arg.originalArgs : void 0;\n      const reset = (0, import_react3.useCallback)(() => {\n        batch(() => {\n          if (promise) {\n            setPromise(void 0);\n          }\n          if (fixedCacheKey) {\n            dispatch(api.internalActions.removeMutationResult({\n              requestId,\n              fixedCacheKey\n            }));\n          }\n        });\n      }, [dispatch, fixedCacheKey, promise, requestId]);\n      const {\n        endpointName,\n        data,\n        status,\n        isLoading,\n        isSuccess,\n        isError,\n        error\n      } = currentState;\n      (0, import_react3.useDebugValue)({\n        endpointName,\n        data,\n        status,\n        isLoading,\n        isSuccess,\n        isError,\n        error\n      });\n      const finalState = (0, import_react3.useMemo)(() => ({\n        ...currentState,\n        originalArgs,\n        reset\n      }), [currentState, originalArgs, reset]);\n      return (0, import_react3.useMemo)(() => [triggerMutation, finalState], [triggerMutation, finalState]);\n    };\n  }\n}\n\n// src/query/react/module.ts\nvar reactHooksModuleName = /* @__PURE__ */ Symbol();\nvar reactHooksModule = ({\n  batch = import_react_redux3.batch,\n  hooks = {\n    useDispatch: import_react_redux3.useDispatch,\n    useSelector: import_react_redux3.useSelector,\n    useStore: import_react_redux3.useStore\n  },\n  createSelector: createSelector2 = import_reselect.createSelector,\n  unstable__sideEffectsInRender = false,\n  ...rest\n} = {}) => {\n  if (true) {\n    const hookNames = [\"useDispatch\", \"useSelector\", \"useStore\"];\n    let warned = false;\n    for (const hookName of hookNames) {\n      if (countObjectKeys(rest) > 0) {\n        if (rest[hookName]) {\n          if (!warned) {\n            console.warn(\"As of RTK 2.0, the hooks now need to be specified as one object, provided under a `hooks` key:\\n`reactHooksModule({ hooks: { useDispatch, useSelector, useStore } })`\");\n            warned = true;\n          }\n        }\n        hooks[hookName] = rest[hookName];\n      }\n      if (typeof hooks[hookName] !== \"function\") {\n        throw new Error( false ? 0 : `When using custom hooks for context, all ${hookNames.length} hooks need to be provided: ${hookNames.join(\", \")}.\nHook ${hookName} was either not provided or not a function.`);\n      }\n    }\n  }\n  return {\n    name: reactHooksModuleName,\n    init(api, {\n      serializeQueryArgs\n    }, context) {\n      const anyApi = api;\n      const {\n        buildQueryHooks,\n        buildMutationHook,\n        usePrefetch\n      } = buildHooks({\n        api,\n        moduleOptions: {\n          batch,\n          hooks,\n          unstable__sideEffectsInRender,\n          createSelector: createSelector2\n        },\n        serializeQueryArgs,\n        context\n      });\n      safeAssign(anyApi, {\n        usePrefetch\n      });\n      safeAssign(context, {\n        batch\n      });\n      return {\n        injectEndpoint(endpointName, definition) {\n          if (isQueryDefinition(definition)) {\n            const {\n              useQuery,\n              useLazyQuery,\n              useLazyQuerySubscription,\n              useQueryState,\n              useQuerySubscription\n            } = buildQueryHooks(endpointName);\n            safeAssign(anyApi.endpoints[endpointName], {\n              useQuery,\n              useLazyQuery,\n              useLazyQuerySubscription,\n              useQueryState,\n              useQuerySubscription\n            });\n            api[`use${capitalize(endpointName)}Query`] = useQuery;\n            api[`useLazy${capitalize(endpointName)}Query`] = useLazyQuery;\n          } else if (isMutationDefinition(definition)) {\n            const useMutation = buildMutationHook(endpointName);\n            safeAssign(anyApi.endpoints[endpointName], {\n              useMutation\n            });\n            api[`use${capitalize(endpointName)}Mutation`] = useMutation;\n          }\n        }\n      };\n    }\n  };\n};\n\n// src/query/react/index.ts\n__reExport(react_exports, __webpack_require__(/*! @reduxjs/toolkit/query */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/cjs/index.js\"), module.exports);\n\n// src/query/react/ApiProvider.tsx\nvar import_toolkit4 = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/cjs/index.js\");\nvar import_react4 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar import_react5 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar React = __toESM(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar import_react_redux4 = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/cjs/index.js\");\nvar import_query2 = __webpack_require__(/*! @reduxjs/toolkit/query */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/cjs/index.js\");\nfunction ApiProvider(props) {\n  const context = props.context || import_react_redux4.ReactReduxContext;\n  const existingContext = (0, import_react4.useContext)(context);\n  if (existingContext) {\n    throw new Error( false ? 0 : \"Existing Redux context detected. If you already have a store set up, please use the traditional Redux setup.\");\n  }\n  const [store] = React.useState(() => (0, import_toolkit4.configureStore)({\n    reducer: {\n      [props.api.reducerPath]: props.api.reducer\n    },\n    middleware: (gDM) => gDM().concat(props.api.middleware)\n  }));\n  (0, import_react5.useEffect)(() => props.setupListeners === false ? void 0 : (0, import_query2.setupListeners)(store.dispatch, props.setupListeners), [props.setupListeners, store.dispatch]);\n  return /* @__PURE__ */ React.createElement(import_react_redux4.Provider, { store, context }, props.children);\n}\n\n// src/query/react/index.ts\nvar createApi = /* @__PURE__ */ (0, import_query3.buildCreateApi)((0, import_query3.coreModule)(), reactHooksModule());\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=rtk-query-react.development.cjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/cjs/rtk-query-react.development.cjs\n");

/***/ })

};
;