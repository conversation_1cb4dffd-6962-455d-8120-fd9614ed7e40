import { ClnCircularProgress } from '@celanese/ui-lib'
import * as S from './styles'
import { Backdrop, Box } from '@mui/material'

export function FullLoaderCircular(isLoading: boolean) {
    return (
        <Backdrop sx={{ zIndex: 10, backgroundColor: 'transparent' }} open={isLoading}>
            <ClnCircularProgress />
        </Backdrop>
    )
}

export function LoaderCircularIcon(size?: number) {
    return <ClnCircularProgress size={size ?? 40} value={0} />
}

export function LoaderCircularTable({ size = 40, sxProps }) {
    return (
        <Box
            sx={{
                ...{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    margin: '12px',
                    width: '100%',
                },
                ...sxProps,
            }}
        >
            <ClnCircularProgress size={size} value={0} />
        </Box>
    )
}

export default function LoaderCircular(size?: number) {
    return (
        <S.LoaderContainer>
            <ClnCircularProgress size={size ?? 40} value={0} />
        </S.LoaderContainer>
    )
}

export { LoaderCircular }
