export const enviroment = {
    kpidomGraphqlUri: process.env.NEXT_PUBLIC_GRAPHQL_KPIDOM_URI,
    gkpiGraphqlUri: process.env.NEXT_PUBLIC_GRAPHQL_GKPI_URI,
    assetHierarchyGraphqlUri: process.env.NEXT_PUBLIC_GRAPHQL_ASSET_HIERARCHY_URI,
    executiveGraphqlUri: process.env.NEXT_PUBLIC_GRAPHQL_EXECUTIVE_URI,
    stewardshipGraphqlUri: process.env.NEXT_PUBLIC_GRAPHQL_STEWARDSHIP_URI,
    incidentGraphqlUri: process.env.NEXT_PUBLIC_GRAPHQL_INCIDENT_URI,
    qualityGraphqlUri: process.env.NEXT_PUBLIC_GRAPHQL_QUALITY_URI,
    ofwaGraphqlUri: process.env.NEXT_PUBLIC_GRAPHQL_OFWA_URI,
    msalClientId: process.env.NEXT_PUBLIC_MSAL_CLIENT_ID,
    msalAuthority: process.env.NEXT_PUBLIC_MSAL_AUTHORITY,
    msalScopes: process.env.NEXT_PUBLIC_MSAL_SCOPES?.split(',') ?? [],
    msalGraphScopes: process.env.NEXT_PUBLIC_MSAL_GRAPH_SCOPES?.split(',') ?? [],
    cogniteAppId: process.env.NEXT_PUBLIC_COGNITE_APP_ID,
    cogniteProject: process.env.NEXT_PUBLIC_COGNITE_PROJECT,
    cogniteBaseUrl: process.env.NEXT_PUBLIC_COGNITE_BASE_URL,
    cogniteApiVersion: process.env.NEXT_PUBLIC_COGNITE_API_VERSION,
    cogniteFdmSuffixModelSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_SUFFIX_MODEL_SPACE,
    cogniteFdmSuffixInstancesSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_SUFFIX_INSTANCES_SPACE,
    cogniteFdmSuffixProtectedSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_SUFFIX_PROTECTED_SPACE,
    cogniteFdmSuffixStaticSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_SUFFIX_STATIC_SPACE,
    cogniteFdmGlobalSiteSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_GLOBAL_SITE_SPACE,
    cogniteFdmGlobalUnitSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_GLOBAL_UNIT_SPACE,
    cogniteFdmProjectCode: process.env.NEXT_PUBLIC_COGNITE_FDM_PROJECT_CODE,
    cogniteFusionUrl: process.env.NEXT_PUBLIC_COGNITE_FUSION_URL,
    cogniteXCdpApp: process.env.NEXT_PUBLIC_COGNITE_X_CDP_APP,
    muiLicenseKey: process.env.NEXT_PUBLIC_MUI_LICENSE_KEY,
    cogniteDefaultGraphQlListLimit: process.env.NEXT_PUBLIC_COGNITE_DEFAULT_GRAPHQL_LIST_LIMIT,
    themeSelectionEnabled: process.env.NEXT_PUBLIC_THEME_SELECTION_ENABLED == 'true',
    cdfResourceSufix: process.env.NEXT_PUBLIC_COGNITE_CDF_RESOURCE_SUFIX,
    appsTranslationUrl: process.env.NEXT_PUBLIC_APPS_TRANSLATION_URL,
    appsTranslationAppId: process.env.NEXT_PUBLIC_APPS_TRANSLATION_APP_ID,
    userManagementUrl: process.env.NEXT_PUBLIC_USER_MANAGEMENT_URL,
    userManagementPortal: process.env.NEXT_PUBLIC_USER_MANAGEMENT_PORTAL,
    userManagementAppCode: process.env.NEXT_PUBLIC_USER_MANAGEMENT_APP_CODE,
    notificationPortalUrl: process.env.NEXT_PUBLIC_NOTIFICATION_PORTAL_URL,

    // NODE SERVICE
    //LOCALHOST
    //globalKpiApiAngular: 'http://127.0.0.1:8100',

    //DEV
    globalKpiApiAngular: 'https://app-dplantkpibanknodeservice-d-ussc-01.azurewebsites.net',

    //QA
    //globalKpiApiAngular: 'https://app-dplantkpibanknodeservice-qa-ussc-01.azurewebsites.net',

    //PROD
    // globalKpiApiAngular: 'https://app-dplantkpibanknodeservice-p-ussc-01.azurewebsites.net',

    // Angular
    //LOCALHOST
    //globalKpiAngular: 'http://localhost:3000',

    //DEV
    globalKpiAngular: 'https://app-dplantkpibankangular-d-ussc-01.azurewebsites.net',

    //QA
    //globalKpiAngular: 'https://app-dplantkpibankangular-qa-ussc-01.azurewebsites.net',

    //PROD
    // globalKpiAngular: 'https://globalkpibank.celanese.com/'
    //process.env.GLOBAL_KPI_API_ANGULAR,
}
