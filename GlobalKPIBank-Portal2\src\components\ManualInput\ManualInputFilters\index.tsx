import { translate } from '@celanese/celanese-sdk'
import { Autocomplete, Box, Grid, TextField, styled } from '@mui/material'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { useReportingSiteList } from '@/common/hooks/useReportingSiteList'
import { useManualInputKPI } from '@/common/hooks/useManualInputKPI'
import { ManualInputKPITable } from '../ManualInputTable'
import dayjs from 'dayjs'
import { useAuthSites } from '@/common/hooks/useAuthSites'

const manualInputFilterSchema = z.object({
    site: z.object({
        externalId: z.string(),
        description: z.string(),
    }),
    year: z.string(),
})
type ManualInputFilterSchema = z.infer<typeof manualInputFilterSchema>

const COMPONENT_NAME = 'ManualInput'
const Form = styled('form')({})

export const ManualInputFilters = () => {
    const generateYearsArray = (startYear: number): string[] => {
        const currentYear = dayjs().year()
        return Array.from({ length: currentYear - startYear + 1 }, (_, index) => (startYear + index).toString())
    }
    const { dataSiteList: sites } = useReportingSiteList([])
    const [years] = useState<string[]>(generateYearsArray(2024))
    const [manualTable, setManualTable] = useState<any>({ site: { externalId: '', description: '' }, year: '' })
    const { dataManualInput: result, loadingManualInput, refetchManualInput } = useManualInputKPI(manualTable)
    const { setValue, handleSubmit, control } = useForm<ManualInputFilterSchema>({
        defaultValues: {
            site: {},
            year: dayjs().year().toString(),
        },
        resolver: zodResolver(manualInputFilterSchema),
    })

    const onSubmit = (data: ManualInputFilterSchema) => {
        if (data) {
            setManualTable(data)
        }
    }

    const handleSiteSelectionChange = (value: ManualInputFilterSchema['site']) => {
        setValue('site', value)
        handleSubmit(onSubmit)()
    }

    const handleYearSelectionChange = (value: ManualInputFilterSchema['year']) => {
        setValue('year', value)
        handleSubmit(onSubmit)()
    }

    const { reportingSites: sitesWithPermission } = useAuthSites({
        component: COMPONENT_NAME,
        allSites: sites,
        featureLevelCodes: ['EditAccess'],
    })

    return (
        <Box sx={{ height: '100%', overflow: 'hidden' }}>
            <Form onSubmit={handleSubmit(onSubmit)}>
                <Grid container spacing={2} sx={{ height: '100%', marginTop: '5px' }}>
                    <Grid item xs={2}>
                        <Controller
                            control={control}
                            name="site"
                            render={({ field }) => (
                                <Autocomplete
                                    {...field}
                                    loading
                                    limitTags={1}
                                    size="small"
                                    id="site"
                                    options={sitesWithPermission}
                                    getOptionLabel={(option) => option.description || ''}
                                    value={field.value || null}
                                    onChange={(_, value) => {
                                        if (!value) {
                                            setValue('site', { externalId: '', description: '' })
                                        } else {
                                            handleSiteSelectionChange({
                                                externalId: value.externalId,
                                                description: value.description,
                                            })
                                        }
                                    }}
                                    renderInput={(params) => (
                                        <TextField {...params} label={translate('COMMONFILTER.SITE')} size="small" />
                                    )}
                                    renderOption={(props, option) => (
                                        <li {...props} key={option.externalId} data-value={option}>
                                            <span>{option.description}</span>
                                        </li>
                                    )}
                                />
                            )}
                        />
                    </Grid>
                    <Grid item xs={2}>
                        <Controller
                            control={control}
                            name="year"
                            render={({ field }) => (
                                <Autocomplete
                                    {...field}
                                    loading
                                    size="small"
                                    id="year"
                                    options={years}
                                    getOptionLabel={(option) => option || ''}
                                    value={field.value || null}
                                    onChange={(_, value) => {
                                        if (!value) {
                                            setValue('year', '')
                                        } else {
                                            handleYearSelectionChange(value)
                                        }
                                    }}
                                    renderInput={(params) => (
                                        <TextField {...params} label={translate('COMMONFILTER.YEAR')} size="small" />
                                    )}
                                    renderOption={(props, option) => (
                                        <li {...props} key={option} data-value={option}>
                                            <span>{option}</span>
                                        </li>
                                    )}
                                />
                            )}
                        />
                    </Grid>
                </Grid>
            </Form>
            {result.length <= 0 ? (
                ''
            ) : (
                <ManualInputKPITable
                    manualInputData={result}
                    loading={loadingManualInput}
                    refetchData={refetchManualInput}
                />
            )}
        </Box>
    )
}
