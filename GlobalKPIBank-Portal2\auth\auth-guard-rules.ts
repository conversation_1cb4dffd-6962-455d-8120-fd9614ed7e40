import permissionsDataJson from './auth-guard-rules.json'

export interface AuthGuardFeature {
    featureCode: string
    featureAccessLevelCode: string
}

export interface AuthGuardPermission {
    notAuthorizedMessage: string
    roleCodes: string[]
    features: AuthGuardFeature[]
}

export interface AuthGuardRoutePermission extends AuthGuardPermission {
    path: string
}

export interface AuthGuardComponentPermission extends AuthGuardPermission {
    name: string
}

export interface AuthGuardRules {
    routes: AuthGuardRoutePermission[]
    components: AuthGuardComponentPermission[]
}

export const authGuardRules: AuthGuardRules = permissionsDataJson as AuthGuardRules
