import { createGlobalStyle } from 'styled-components'

export const GlobalStyles = createGlobalStyle`
    @font-face {
    font-family: 'celanese-icon';
    src: url('/fonts/celanese-icon.eot?8uoog6');
    src: url('/fonts/celanese-icon.eot?8uoog6#iefix') format('embedded-opentype'),
        url('/fonts/celanese-icon.ttf?8uoog6') format('truetype'),
        url('/fonts/celanese-icon.woff?8uoog6') format('woff'),
        url('/fonts/celanese-icon.svg?8uoog6#celanese-icon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
  } 
    ::-webkit-scrollbar {
        width: 5px;
        height:5px;
    }

    .MuiAppBar-root {
        box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12) !important;
    }

    .logo-name-bold {
        font-size: 1.5rem;
    }

    .logo-name {
        font-size: 1.5rem;
    }

    ::-webkit-scrollbar-track {
        background: #ffffff;
    }

    ::-webkit-scrollbar-thumb {
        background: rgba(91, 91, 91, 0.5);
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    html {
      font-family: Roboto, sans-serif;
    }

    body {
        max-height: 100vh;
        background-color: #F8F8F8;
        margin:0;
    }

    @media print {
        .pagebreak { page-break-before: always; } /* page-break-after works, as well */
        .apexcharts-toolbar {
            display: none !important;
        }
        .apexcharts-canvas {
            zoom: 0.7;
        }
        .print-hide {
            display: none;
        }
    }

    .apexcharts-no-click {
        cursor: pointer !important;
    }

    a {
        text-decoration: inherit;
        color: inherit;
    }

    @media only screen {
        .print-show {
            display: none !important;
        }
    }

    .iconPolygonWithText {
        color: white;
        font-weight: 500 !important;
        font-size: 12px !important;
        width: 100px !important;
        height: 1px !important;
    }

    .tooptip-radius {
        border-radius: 15px !important;
    }

    path.leaflet-interactive:focus {
        outline: none;
    }

	.header-button-hover {
        color: #083d5b !important;
    }  
        
    .header-button-hover:hover {
        color: #fdfdfd !important;
    }  
    
    .cln-ico-notification {
        font-size: 1.5rem !important;
    }

    .cln-ico-language {
        font-size: 1.6rem !important;
    }

    .font-language {
        font-size: 13px !important;
    }

    .font-location {
        font-size: 13px !important;
    }
    
    .MuiAvatar-root {
        color: #fdfdfd;
        background-color: #083d5b;
    }

    .cln-ico-logo {
        margin-left: 0.16rem !important;
    }
        
    .cln-ico-double-arrow {
        color: #c9c9c9 !important;
    }

    [open] .navbar-item-icon {
        margin-right: 14px !important
    }
    
    .MuiTabs-root {
        width: 100%
    }

`
