import { cellStyleEven, cellStyleOdd, headerStyle } from './exportExcelStyles'
import * as XLSX from 'xlsx-js-style'
import { formatVariableName } from './stringFormat'
import { translate } from '@celanese/celanese-sdk'

const EXPORT_TYPE = '.xlsx'
const getOptionName = (range: number): string => {
    if (range === 1) return translate('COMMONBUTTON.EXPORT_OPTIONS.LAST_MONTH')

    if (range === 3) return translate('COMMONBUTTON.EXPORT_OPTIONS.THREE_MONTHS')

    if (range === 6) return translate('COMMONBUTTON.EXPORT_OPTIONS.SIX_MONTHS')

    if (range === 12) return translate('COMMONBUTTON.EXPORT_OPTIONS.CURRENT_YEAR')
}

const calculateColumnsWidths = (data: any, headers: string[][]) => {
    const columnWidths = headers[0].map((header) => header.length)
    const headersRaw = data.length > 0 ? [Object.keys(data[0]).map((variable) => variable)] : [[]]

    data.forEach((row) => {
        headersRaw[0].forEach((header, index) => {
            const cellValue = row[header]
            const cellLength = String(cellValue).length
            if (cellLength > columnWidths[index]) {
                columnWidths[index] = cellLength
            }
        })
    })

    return columnWidths.map((width) => width + 2)
}

export const exportExcel = (exportRequest: any, exportResult: any, kpisTabName: string[]) => {
    const headers =
        exportResult.length > 0 ? [Object.keys(exportResult[0]).map((variable) => formatVariableName(variable))] : [[]]

    const worksheet = XLSX.utils.json_to_sheet([])
    XLSX.utils.sheet_add_aoa(worksheet, headers, { origin: 'A1' })
    XLSX.utils.sheet_add_json(worksheet, exportResult, { skipHeader: true, origin: 'A2' })
    const workbook = XLSX.utils.book_new()

    if (exportResult.length > 0) {
        // Adds styles
        const range = XLSX.utils.decode_range(worksheet['!ref'])
        for (let row = 0; row <= range.e.r; row++) {
            for (let col = 0; col <= range.e.c; col++) {
                const cellRef = XLSX.utils.encode_cell({ r: row, c: col })
                if (cellRef) {
                    if (row === 0) {
                        worksheet[cellRef].t = 's'
                        worksheet[cellRef].s = headerStyle
                        if (worksheet[cellRef].v === null) {
                            worksheet[cellRef].v = ''
                        }
                    } else {
                        if (row % 2 === 0) {
                            if (worksheet[cellRef].v === null) {
                                worksheet[cellRef].t = 's'
                                worksheet[cellRef].v = ''
                            }
                            worksheet[cellRef].s = cellStyleEven
                        }
                        if (row % 2 !== 0) {
                            if (worksheet[cellRef].v === null) {
                                worksheet[cellRef].t = 's'
                                worksheet[cellRef].v = ''
                            }
                            worksheet[cellRef].s = cellStyleOdd
                        }
                    }
                }
            }
        }

        // Modifies column width
        const columnWidths = calculateColumnsWidths(exportResult, headers)
        worksheet['!cols'] = columnWidths.map((width) => ({ wch: width }))
    }
    const optionName = getOptionName(exportRequest.exportFilters.range)

    XLSX.utils.book_append_sheet(workbook, worksheet, kpisTabName[exportRequest.kpiFilters.currentTab])
    XLSX.writeFile(workbook, `${exportRequest.kpiFilters.kpiName} - ${optionName}${EXPORT_TYPE}`, { compression: true })
}
