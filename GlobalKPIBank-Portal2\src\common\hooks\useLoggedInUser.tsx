import { useMsal } from '@azure/msal-react'
import { useMsGraph } from './useMsGraph'
import { useCallback, useMemo } from 'react'

export const useLoggedInUser = () => {
    const { instance: msalInstance } = useMsal()
    const { profilePicture: profilePicture } = useMsGraph()

    const getActiveAccountUserName = useCallback((): string | null => {
        const account = msalInstance.getActiveAccount()
        if (!account) {
            return null
        }

        return account.username
    }, [msalInstance])

    const getActiveAccountName = useCallback((): string | undefined => {
        const account = msalInstance.getActiveAccount()
        if (!account) {
            return undefined
        }

        return account.name
    }, [msalInstance])

    const getActiveAccountPicture = useCallback((): string | undefined => {
        if (!profilePicture) {
            return undefined
        }

        return profilePicture
    }, [profilePicture])

    const getActiveAccount = useCallback(() => msalInstance.getActiveAccount(), [msalInstance])

    return useMemo(
        () => ({
            getActiveAccountUserName,
            getActiveAccountName,
            getActiveAccountPicture,
            getActiveAccount,
        }),
        [getActiveAccount, getActiveAccountName, getActiveAccountPicture, getActiveAccountUserName]
    )
}
