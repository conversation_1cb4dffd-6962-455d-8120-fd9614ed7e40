import { AuthenticationResult, IPublicClientApplication } from '@azure/msal-browser'
import dayjs from 'dayjs'
import { msalScopes } from '../configurations/auth'

export const unique = <T>(array: T[]) => Array.from(new Set(array))

export function removeTypename<T>(obj: T) {
    if (!obj || typeof obj !== 'object') {
        return obj
    }
    if (dayjs.isDayjs(obj)) {
        return dayjs(obj)
    }
    if (obj instanceof Date) {
        return new Date(obj)
    }
    const result: any = Array.isArray(obj) ? [] : {}
    for (const key in obj) {
        if (key !== '__typename') {
            result[key] = removeTypename(obj[key])
        }
    }
    return result
}

export function safeStringify(value: any) {
    try {
        return jsonStringifyRecursive(value)
    } catch (e) {
        console.error(e)
        return 'err'
    }
}

function jsonStringifyRecursive(obj: any) {
    const cache = new Set()
    return JSON.stringify(
        obj ?? '',
        (_key, value) => {
            if (typeof value === 'object' && value !== null) {
                if (cache.has(value)) {
                    return
                }
                cache.add(value)
            }
            return value
        },
        4
    )
}

export interface LanguageItem {
    externalId: string
    language: string
    code: string
}

export const availableLanguages: LanguageItem[] = [
    {
        externalId: 'English',
        language: 'English',
        code: 'EN',
    },
    {
        externalId: 'French',
        language: 'French',
        code: 'FR',
    },
    {
        externalId: 'Spanish',
        language: 'Spanish',
        code: 'ES',
    },
    {
        externalId: 'Portuguese',
        language: 'Portuguese',
        code: 'PT',
    },
    {
        externalId: 'Italian',
        language: 'Italian',
        code: 'IT',
    },
    {
        externalId: 'Mandarin',
        language: 'Mandarin',
        code: 'ZH',
    },
    {
        externalId: 'Japanese',
        language: 'Japanese',
        code: 'JA',
    },
    {
        externalId: 'Korean',
        language: 'Korean',
        code: 'KO',
    },
    {
        externalId: 'Dutch',
        language: 'Dutch',
        code: 'NL',
    },
    {
        externalId: 'German',
        language: 'German',
        code: 'DE',
    },
]

export const getUserInitials = (name: string) => {
    const nameArray = name.split(' ')
    return (nameArray[0]?.charAt(0) + nameArray[nameArray.length - 1]?.charAt(0)).toUpperCase()
}

export const getAuthenticationResult = async (
    msal: IPublicClientApplication
): Promise<AuthenticationResult | undefined> => {
    const account = msal.getActiveAccount()
    if (!account) {
        return undefined
    }

    const accessTokenRequest = {
        account,
        scopes: msalScopes,
    }

    try {
        return await msal.acquireTokenSilent(accessTokenRequest)
    } catch {
        try {
            await msal.acquireTokenRedirect(accessTokenRequest)
        } catch (e) {
            console.error('Error acquiring token silently:', e)
            return undefined
        }
    }
}

export const getIdTokenFromMsal = async (msal: IPublicClientApplication) => {
    const result = await getAuthenticationResult(msal)
    return result?.idToken ?? 'NOTOKENFOUND'
}
