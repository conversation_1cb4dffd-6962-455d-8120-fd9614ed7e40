// Cache system for KPI data to improve performance with multiple sites
const KPI_CACHE_KEY = 'GKPI_DATA_CACHE'
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes in milliseconds

type KpiCacheKey = {
    sites: string[]
    kpiGroup: string
    granularity: string
    businessSegment?: string
    page: string
}

type KpiCacheEntry = {
    key: string
    data: any
    timestamp: number
}

const generateCacheKey = (request: KpiCacheKey): string => {
    const sortedSites = [...request.sites].sort()
    return `${request.page}_${request.kpiGroup}_${request.granularity}_${sortedSites.join(',')}_${
        request.businessSegment || 'all'
    }`
}

export const getKpiFromCache = (request: KpiCacheKey): any | undefined => {
    if (typeof window === 'undefined') {
        return undefined
    }

    const cacheKey = generateCacheKey(request)
    const cached = sessionStorage.getItem(`${KPI_CACHE_KEY}_${cacheKey}`)

    if (!cached) {
        return undefined
    }

    const entry = JSON.parse(cached) as KpiCacheEntry
    const now = new Date().getTime()

    if (now - entry.timestamp > CACHE_DURATION) {
        sessionStorage.removeItem(`${KPI_CACHE_KEY}_${cacheKey}`)
        return undefined
    }

    return entry.data
}

export const setKpiCache = (request: KpiCacheKey, data: any) => {
    if (typeof window === 'undefined') {
        return
    }

    const cacheKey = generateCacheKey(request)
    const entry: KpiCacheEntry = {
        key: cacheKey,
        data,
        timestamp: new Date().getTime(),
    }

    try {
        sessionStorage.setItem(`${KPI_CACHE_KEY}_${cacheKey}`, JSON.stringify(entry))
    } catch (error) {
        // Clear old cache entries if storage is full
        clearOldKpiCache()
        try {
            sessionStorage.setItem(`${KPI_CACHE_KEY}_${cacheKey}`, JSON.stringify(entry))
        } catch {
            console.warn('Unable to set KPI cache - storage full')
        }
    }
}

const clearOldKpiCache = () => {
    const now = new Date().getTime()
    const keysToRemove: string[] = []

    for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i)
        if (key?.startsWith(KPI_CACHE_KEY)) {
            try {
                const entry = JSON.parse(sessionStorage.getItem(key) || '{}') as KpiCacheEntry
                if (now - entry.timestamp > CACHE_DURATION) {
                    keysToRemove.push(key)
                }
            } catch {
                keysToRemove.push(key)
            }
        }
    }

    keysToRemove.forEach((key) => sessionStorage.removeItem(key))
}

export const clearAllKpiCache = () => {
    const keysToRemove: string[] = []

    for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i)
        if (key?.startsWith(KPI_CACHE_KEY)) {
            keysToRemove.push(key)
        }
    }

    keysToRemove.forEach((key) => sessionStorage.removeItem(key))
}
