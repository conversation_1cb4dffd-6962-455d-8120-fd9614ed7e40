import { FdmClient } from '../clients'
import { cognite } from '../configurations/cognite'
import { EntityType, GetSpace } from '../utils/space-util'

export const createFdmClient = (getAuthToken: () => Promise<string>) => {
    return new FdmClient({
        cogniteProject: cognite.project,
        cogniteBaseUrl: cognite.baseUrl,
        cogniteFdmModelSpace: GetSpace(EntityType.INO),
        cogniteFdmInstancesSpace: GetSpace(EntityType.Instance),
        cogniteApiVersion: cognite.cogniteApiVersion,
        getToken: getAuthToken,
    })
}
