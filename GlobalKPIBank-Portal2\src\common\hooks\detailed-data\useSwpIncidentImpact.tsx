import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import { SwpIncidentImpactTableView } from '@/common/models/swpIncidentImpactTable'
import { SwpIncidentImpactKpi } from '@/common/models/swpIncidentImpact'
import dayjs from 'dayjs'
import { setExportDateRange } from '@/common/utils/kpis-data-table-export'

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let unitQ: string = ''
    let startDateQ: string = ''
    let endDateQ: string = ''
    let categoryQ: string = ''
    let limitQ: string = ''
    let cursorQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{reportingSite: {externalId: {in: [${sites}]}}},`
        unitQ = `{reportingUnit: {not: {externalId: {in: "UNT-${request.kpiFilters.site.replace('STS-', '')}GRN"}}}},`
    }
    if (request && request.kpiFilters.date !== '') {
        let sanitizedInitialDate = dayjs(request.kpiFilters.date).startOf('month').format('YYYY-MM-DD')
        let sanitizedEndDate = dayjs(request.kpiFilters.date).endOf('month').format('YYYY-MM-DD')
        const time = 'T00:00:00+00:00'

        if (request.exportFilters && request.exportFilters.isExport) {
            ;[sanitizedInitialDate, sanitizedEndDate] = setExportDateRange(
                request.exportFilters.range,
                request.kpiFilters.date
            )
        }

        startDateQ = `{reportedTime: {gte: "${sanitizedInitialDate}${time}"}},`
        endDateQ = `{reportedTime: {lte: "${sanitizedEndDate}${time}"}},`
    }

    if (request && request.kpiFilters.kpiName !== '') {
        const kpiName = request.kpiFilters.kpiName
        if (kpiName.indexOf('TRIR') != -1) categoryQ = `{category:{name: {in: ["PPS-T1","PPS-T2"]}}}`
        if (kpiName == 'People Safety - Tier 1/2 Recordables')
            categoryQ = `{category:{name: {in: ["PPS-T1","PPS-T2"]}}}`
        if (kpiName == 'People Safety - Tier 3 First Aids') categoryQ = `{category:{name: {in: ["PPS-T3"]}}}`
        if (kpiName == 'Process Safety - Tier 1/2') categoryQ = `{category:{name: {in: ["PCS-T1","PCS-T2"]}}}`
        if (kpiName == 'Environmental - Tier 1/2') categoryQ = `{category:{name: {in: ["ENV-T1","ENV-T2"]}}}`
        if (kpiName == 'Fire - Tier 1/2') categoryQ = `{category:{name: {in: ["FIR-T1","FIR-T2"]}}}`
        if (kpiName == 'High Potential Events') categoryQ = `{isHighPotential: {eq: true}}`
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listSwpIncidentImpact (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${startDateQ} ${endDateQ} ${unitQ} ${categoryQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                name
                description
                incidentParentId
                isHighPotential
                reportedTime
                reportingSite {
                    externalId
                    name
                }
                reportingUnit {
                    name
                }
                category {
                    name
                }
            }
        }
    }`
}

const mapSwpIncidentImpact = (data: SwpIncidentImpactKpi[]): SwpIncidentImpactTableView[] => {
    const mappedResult: SwpIncidentImpactTableView[] = []
    if (data && data.length > 0) {
        data.map((item) => {
            const result: SwpIncidentImpactTableView = {
                siteName: item.reportingSite?.name ?? '',
                unit: item.reportingUnit?.name ?? '',
                name: item.name ?? '',
                description: item.description ?? '',
                incidentParentId: item.incidentParentId,
                date: item.reportedTime?.toString().slice(0, 10) ?? '',
                category: item.category?.name ?? '',
                isHighPotential: item.isHighPotential?.toString() ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useSwpIncidentImpact = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<SwpIncidentImpactKpi>(gql(query), 'listSwpIncidentImpact', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'stewardshipView',
        },
    })

    const [resultData, setResultData] = useState<{ data: any[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapSwpIncidentImpact(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapSwpIncidentImpact(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
