import { ExternalEntity, Location } from '.'

export interface Notifications extends ExternalEntity {
    name: string
    description: string
    createdBy: string
    createdDate: string
    aboutFunctionalLocation: Location
    priority: string
    priorityDescription: string
    priorityType: string
    systemStatus: string
    userStatus: string
    workCenter: string
    workCenterDescription: string
    workOrderNumber: string
    lastUpdatedTime: string
    createdTime: string
    notificationType: string
    notificationTypeDescription: string
    reportedBy: string
}
