import { authGuardRules } from 'auth/auth-guard-rules'
import { useContext, useMemo } from 'react'
import { AuthGuardContext } from '../contexts/AuthGuardContext'
import { Site } from '../models/site'

const COR_SITE = 'STS-COR'

export interface UseAuthSitesResponse {
    loading: boolean
    reportingSites: Site[]
}

type FeatureAccessLevelCode = 'ViewAccess' | 'EditAccess'

type UseAuthSitesRequest = {
    path?: string
    component?: string
    allSites: Site[]
    featureLevelCodes?: FeatureAccessLevelCode[]
}

const DEFAULT_FEATURE_ACCESS_LEVEL = new Set<FeatureAccessLevelCode>(['ViewAccess', 'EditAccess'])

export const useAuthSites = ({
    path,
    component,
    allSites,
    featureLevelCodes,
}: UseAuthSitesRequest): UseAuthSitesResponse => {
    const { featureSitesMapping, loading } = useContext(AuthGuardContext)

    const featuresMapping = useMemo(() => {
        const featureLevelCodesMap = featureLevelCodes ? new Set(featureLevelCodes) : DEFAULT_FEATURE_ACCESS_LEVEL
        const routePermission =
            authGuardRules.routes
                .find((c) => c.path === path)
                ?.features.filter((p) => featureLevelCodesMap.has(p.featureAccessLevelCode as FeatureAccessLevelCode))
                .map((p) => p.featureCode) ?? []
        const componentPermission =
            authGuardRules.components
                .find((c) => c.name === component)
                ?.features?.filter((p) => featureLevelCodesMap.has(p.featureAccessLevelCode as FeatureAccessLevelCode))
                .map((p) => p.featureCode) ?? []

        const mapping = new Set(routePermission.concat(componentPermission))

        return mapping
    }, [path, component, featureLevelCodes])

    const reportingSites = useMemo(() => {
        const sites = featureSitesMapping
            .filter((p) => featuresMapping.has(p.featureCode))
            .flatMap((p) => p.reportingSites)

        const map = new Map(sites.map((p) => [p.siteId, p]))

        if (map.has(COR_SITE)) {
            return allSites
        }

        return allSites.filter((p) => map.has(p.externalId))
    }, [featuresMapping, allSites, featureSitesMapping])

    return { reportingSites, loading }
}
