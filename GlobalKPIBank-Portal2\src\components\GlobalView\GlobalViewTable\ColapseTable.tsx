import { translate } from '@celanese/celanese-sdk'
import { Box, Collapse, IconButton, Table, TableBody, TableCell, TableRow, Tooltip, Typography } from '@mui/material'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp'
import { Fragment, useEffect, useState } from 'react'
import { MatIcon } from '@celanese/ui-lib'
import LeaderboardIcon from '@mui/icons-material/Leaderboard'
import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import { numberFormat } from '@/common/utils/numberFormat'
import GlobalViewModal from '../GlobalViewModal'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { StripedRowEven, StripedRowOdd } from './styles'
import { FoundationalKpisKpidom } from '@/common/utils/foundational-kpis'
import HelpOutlineOutlinedIcon from '@mui/icons-material/HelpOutlineOutlined'
import { addsBreakLineOnText } from '@/common/utils/stringFormat'

interface ColapseTableProps {
    row: GlobalVisionKpiTableModel
    target?: GlobalVisionKpiTarget[]
    openAll: boolean
}

export const ColapseTable: React.FC<ColapseTableProps> = ({ row, target, openAll }) => {
    const [open, setOpen] = useState(false)
    const [openModal, setOpenModal] = useState(false)
    const [isProductivityKpi, setIsProductivityKpi] = useState(false)
    const [isFutureProductivityKpi, setIsFutureProductivityKpi] = useState(false)

    useEffect(() => {
        setOpen(openAll)
    }, [openAll])

    useEffect(() => {
        FoundationalKpisKpidom.PRODUCTIVITY_YEAR_FORECAST.EXTERNAL_ID == row.kpi.externalId ||
        FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_NEXT_YEAR.EXTERNAL_ID == row.kpi.externalId ||
        FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_PLUS_TWO_YEARS.EXTERNAL_ID == row.kpi.externalId
            ? setIsProductivityKpi(true)
            : setIsProductivityKpi(false)
    }, [])

    useEffect(() => {
        if (
            FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_NEXT_YEAR.EXTERNAL_ID == row.kpi.externalId ||
            FoundationalKpisKpidom.PRODUCTIVITY_PIPELINE_PLUS_TWO_YEARS.EXTERNAL_ID == row.kpi.externalId
        ) {
            setIsFutureProductivityKpi(true)
        }
    }, [])

    return (
        <Fragment>
            <StripedRowOdd index={row.kpi.order} sx={{ '& > *': { borderBottom: 'unset' } }}>
                <TableCell>
                    <IconButton aria-label="expand row" size="small" onClick={() => setOpen(!open)}>
                        {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                    </IconButton>
                </TableCell>
                <TableCell
                    sx={{
                        paddingX: '5px',
                        borderBottom: 'unset',
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            fontWeight: '500',
                        }}
                    >
                        {row.kpi.name +
                            (row.kpi.symbol != null && row.kpi.symbol != '' ? ' (' + row.kpi.symbol + ')' : '')}
                        <Tooltip
                            title={
                                <Typography style={{ fontSize: '14px', whiteSpace: 'pre-line' }}>
                                    {addsBreakLineOnText(row.kpi.description, 'Update frequency:')}
                                </Typography>
                            }
                        >
                            <div>
                                <MatIcon icon="info" fontSize="18px" />
                            </div>
                        </Tooltip>
                    </Box>
                    {open && isProductivityKpi && (
                        <Typography sx={{ fontSize: 10, textAlign: 'left' }}>
                            <HelpOutlineOutlinedIcon sx={{ color: '#083D5B', fontSize: 10 }} />
                            {translate('FOUNDATION_KPI_DETAILS.PRODUCTIVITY_KPIS_NOTE')}
                        </Typography>
                    )}
                </TableCell>
                <TableCell align="center"></TableCell>
                <TableCell align="center"></TableCell>
                <TableCell align="center"></TableCell>
                <TableCell align="center"></TableCell>
                <TableCell align="center"></TableCell>
                <TableCell align="center"></TableCell>
                <TableCell align="center"></TableCell>
                <TableCell align="center"></TableCell>
                <TableCell align="center">
                    <Box>
                        <IconButton color="primary" onClick={() => setOpenModal(true)}>
                            <LeaderboardIcon fontSize="small" />
                        </IconButton>
                    </Box>
                </TableCell>
            </StripedRowOdd>
            <StripedRowEven index={row.kpi.order}>
                <TableCell style={{ padding: 0, borderTop: '1px solid rgba(224, 224, 224, 1)' }} colSpan={12}>
                    <Collapse in={open} timeout="auto" unmountOnExit>
                        <Box sx={{ margin: 0, overflow: 'auto' }}>
                            <Table>
                                <TableBody>
                                    {row.data &&
                                        row.data.map((detailsRow) => (
                                            <TableRow key={detailsRow.externalId}>
                                                <TableCell sx={{ width: '5%' }} />
                                                <TableCell align="left" sx={{ width: '17%' }}>
                                                    {detailsRow.refReportingSite.name}
                                                </TableCell>
                                                <TableCell align="right" sx={{ width: '9%' }}>
                                                    {isFutureProductivityKpi
                                                        ? ''
                                                        : numberFormat(detailsRow.lastTwoYear)}
                                                </TableCell>
                                                <TableCell align="right" sx={{ width: '9%' }}>
                                                    {isFutureProductivityKpi ? '' : numberFormat(detailsRow.lastYear)}
                                                </TableCell>
                                                <TableCell align="right" sx={{ width: '9%' }}>
                                                    {numberFormat(detailsRow.ytd)}
                                                </TableCell>
                                                <TableCell align="right" sx={{ width: '9%' }}>
                                                    {target
                                                        ? target.find(
                                                              (item) =>
                                                                  item.refReportingSite.externalId ==
                                                                  detailsRow.refReportingSite.externalId
                                                          )?.value
                                                        : '-'}
                                                </TableCell>
                                                <TableCell align="right" sx={{ width: '9%' }}>
                                                    {numberFormat(detailsRow.lastThreeMonth)}
                                                </TableCell>
                                                <TableCell align="right" sx={{ width: '9%' }}>
                                                    {numberFormat(detailsRow.lastTwoMonth)}
                                                </TableCell>
                                                <TableCell align="right" sx={{ width: '9%' }}>
                                                    {numberFormat(detailsRow.lastMonth)}
                                                </TableCell>
                                                <TableCell align="right" sx={{ width: '9%' }}>
                                                    {numberFormat(detailsRow.actualMonth)}
                                                </TableCell>
                                                <TableCell sx={{ width: '6%' }} />
                                            </TableRow>
                                        ))}
                                </TableBody>
                            </Table>
                        </Box>
                    </Collapse>
                </TableCell>
            </StripedRowEven>
            <GlobalViewModal open={openModal} handleClose={() => setOpenModal(false)} data={row} target={target} />
        </Fragment>
    )
}

export default ColapseTable
