/**
 * Hack to avoid throwing an error about missing CSSHighlight feature on Firefox
 */
export function registerCssHighlighPollyfill() {
    if (typeof CSS === 'undefined') {
        ;(window as any).CSS = {}
    }

    if (typeof Highlight === 'undefined') {
        ;(window as any).Highlight = class {
            constructor() {}
        }
    }

    if (typeof CSS.highlights === 'undefined') {
        ;(window as any).CSS.highlights = new Map()
    }
}
