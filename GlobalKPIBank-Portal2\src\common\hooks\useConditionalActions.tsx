import { useCallback, useState } from 'react'
import { useCognite } from './useCognite'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export type ConditionalActionsHookResult = [ConditionalActionsFunction, ConditionalActionsState]

export type ConditionalActionsFunctionParams = {
    externalId: string[]
}

export type ConditionalActionsFunction = (params: ConditionalActionsFunctionParams) => Promise<void>

export type ConditionalActionsState = {
    loading: boolean
    called: boolean
    error: any
}

export function useConditionalActions(): ConditionalActionsHookResult {
    const { fdmClient: client } = useCognite()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)

    const getConditionalActions = useCallback(
        ({ externalId }: ConditionalActionsFunctionParams) => {
            const query = {
                with: {
                    parentObjectItems: {
                        limit: 1500,
                        nodes: {
                            filter: {
                                and: [
                                    {
                                        equals: {
                                            property: ['node', 'space'],
                                            value: GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode),
                                        },
                                    },
                                    {
                                        in: {
                                            property: ['node', 'externalId'],
                                            values: externalId,
                                        },
                                    },
                                ],
                            },
                        },
                    },
                    conditionalActions: {
                        limit: 1500,
                        nodes: {
                            from: 'parentObjectItems',
                            direction: 'inwards',
                            through: {
                                view: {
                                    type: 'view',
                                    space: 'cdf_apm',
                                    externalId: 'ConditionalAction',
                                    version: 'v1',
                                },
                                identifier: 'parentObject',
                            },
                        },
                    },
                    conditions: {
                        limit: 1500,
                        nodes: {
                            from: 'conditionalActions',
                            direction: 'inwards',
                            through: {
                                view: {
                                    type: 'view',
                                    space: 'cdf_apm',
                                    externalId: 'Condition',
                                    version: 'v1',
                                },
                                identifier: 'conditionalAction',
                            },
                        },
                    },
                    actions: {
                        limit: 1500,
                        nodes: {
                            from: 'conditionalActions',
                            direction: 'inwards',
                            through: {
                                view: {
                                    type: 'view',
                                    space: 'cdf_apm',
                                    externalId: 'Action',
                                    version: 'v1',
                                },
                                identifier: 'conditionalActions',
                            },
                        },
                    },
                },
                select: {
                    parentObjectItems: {
                        sources: [
                            {
                                source: {
                                    type: 'view',
                                    space: 'cdf_apm',
                                    externalId: 'ChecklistItem',
                                    version: 'v1',
                                },
                                properties: ['*'],
                            },
                        ],
                    },
                    conditionalActions: {
                        sources: [
                            {
                                source: {
                                    type: 'view',
                                    space: 'cdf_apm',
                                    externalId: 'ConditionalAction',
                                    version: 'v1',
                                },
                                properties: ['*'],
                            },
                        ],
                    },
                    conditions: {
                        sources: [
                            {
                                source: {
                                    type: 'view',
                                    space: 'cdf_apm',
                                    externalId: 'Condition',
                                    version: 'v1',
                                },
                                properties: ['*'],
                            },
                        ],
                    },
                    actions: {
                        sources: [
                            {
                                source: {
                                    type: 'view',
                                    space: 'cdf_apm',
                                    externalId: 'Action',
                                    version: 'v1',
                                },
                                properties: ['*'],
                            },
                        ],
                    },
                },
            }
            setLoading(true)
            setCalled(true)
            return client
                .getConditionalAction({ externalId, query: query })
                .catch((error) => setError(error))
                .finally(() => setLoading(false))
        },
        [client]
    )

    return [getConditionalActions, { error, loading, called }]
}
