import { CSSObject } from '@emotion/react'

export const chartTitle: CSSObject = {
    fontSize: '20px',
    lineHeight: '23px',
    letterSpacing: '0em',
    textAlign: 'left',
    color: '#9E9E9E',
}

export const chartContainer: CSSObject = {
    position: 'relative',
    width: '50%',
}

export const legendContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    width: '50%',
    height: '200px',
    overflowY: 'auto',
}

export const chartLegendBox: CSSObject = {
    display: 'flex',
    justifyContent: 'space-between',
}

export const chartLegendColumnNumber: CSSObject = {
    margin: '0 5px',
}

export const chartLegendColumn: CSSObject = {
    margin: '0 5px',
}

export const chartLegend: CSSObject = {
    fontSize: '14px',
    lineHeight: '16px',
    letterSpacing: '0em',
    textAlign: 'left',
    color: '#131313',
}

export const chartLegendNumber: CSSObject = {
    fontSize: '23px',
    lineHeight: '27px',
    letterSpacing: '0em',
    textAlign: 'right',
}

export const chartDefaultColors = [
    '#083D5B',
    '#46A0BE',
    '#3ACF78',
    '#D32F2F',
    '#ff5b01',
    '#FF9800',
    '#4E7389',
    '#017BA4',
]
