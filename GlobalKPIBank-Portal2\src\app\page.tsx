'use client'

import React, { useEffect } from 'react'
import HomeView from './home/<USER>'

export default function Home() {
    useEffect(() => {
        const queryString = window.location.search
        const urlParams = new URLSearchParams(queryString)
        const auth = urlParams.get('code')

        if (auth && auth.length > 0) {
            localStorage.setItem('token-react', auth ?? '')
        }

        window.history.replaceState({}, document.title, '/')
    }, [])

    return <HomeView />
}
