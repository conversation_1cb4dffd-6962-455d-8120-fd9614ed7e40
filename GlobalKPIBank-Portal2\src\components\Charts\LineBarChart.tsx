'use client'

import React, { useState } from 'react'
import { chartDefaultColors } from './styles'
import './styles.css'
import { usePathname } from 'next/navigation'
import { Box } from '@mui/material'
import { ApexOptions } from 'apexcharts'
import dynamic from 'next/dynamic'
import { numberFormat } from '@/common/utils/numberFormat'
const ReactApexChart = dynamic(() => import('react-apexcharts'), { ssr: false })

export interface QuantityLineBarItemChart {
    label: string
    value: number | number[]
}

export interface ClnLineBarChartConfig {
    id: string
    dataLine: QuantityLineBarItemChart[]
    dataLine2?: QuantityLineBarItemChart[]
    dataBar: QuantityLineBarItemChart[]
    groups?: Array<string | number>
    title?: string
    isHorizontal?: boolean
    width?: number | string
    heigth?: number | string
    labelSuffix?: string
    labelPrefix?: string
    colors?: string[]
    range?: number
    selectedKpi?: string
}

export function LineBarChart(config: ClnLineBarChartConfig) {
    const width = config.width || '100%'
    const heigth = config.heigth || '100%'
    const prefix = config.labelPrefix || ''
    const suffix = config.labelSuffix || ''
    const colors = config.colors ?? chartDefaultColors
    const pathname = usePathname().split('/')[1]
    const [offsetXValue, setOffsetXValue] = useState<number>(0)
    const maxDataValueBar = Math.max(
        ...config.dataBar.map((item) => (Array.isArray(item.value) ? Math.max(...item.value) : item.value))
    )
    const maxDataValueLine1 = Math.max(
        ...config.dataLine.map((item) => (Array.isArray(item.value) ? Math.max(...item.value) : item.value))
    )
    const maxDataValueLine2 = Math.max(
        ...config.dataLine2.map((item) => (Array.isArray(item.value) ? Math.max(...item.value) : item.value))
    )
    const maxDataValueLines = Math.max(maxDataValueLine1, maxDataValueLine2)

    const minDataValueBar = Math.min(
        ...config.dataBar.map((item) => (Array.isArray(item.value) ? Math.max(...item.value) : item.value))
    )
    const minDataValueLine1 = Math.min(
        ...config.dataLine.map((item) => (Array.isArray(item.value) ? Math.max(...item.value) : item.value))
    )
    const minDataValueLine2 = Math.min(
        ...config.dataLine2.map((item) => (Array.isArray(item.value) ? Math.max(...item.value) : item.value))
    )
    const minDataValueLines = Math.max(minDataValueLine1, minDataValueLine2)

    const title = {
        text: config.title,
        style: {
            fontSize: '14px',
            color: '#9E9E9E',
            fontFamily: 'Roboto',
        },
    }

    const foundationalGroup = ['GKPI-SOL-FLD']
    const stewardshipGroup = [
        'GKPI-SOL-RCI',
        'GKPI-SOL-NFA',
        'GKPI-SOL-PST',
        'GKPI-SOL-EVT',
        'GKPI-SOL-NFT',
        'GKPI-SOL-NNM',
        'GKPI-SOL-HPE',
    ]
    const qualityGroup = ['GKPI-SOL-QN1', 'GKPI-SOL-HQN', 'GKPI-SOL-MAF', 'GKPI-SOL-FGB']

    const yAxisLabelDecimals =
        foundationalGroup.includes(config.selectedKpi) ||
        stewardshipGroup.includes(config.selectedKpi) ||
        qualityGroup.includes(config.selectedKpi)
            ? 0
            : 2

    const apexchartsBarSeriesData = []
    const apexchartsLineSeriesData = []
    const apexchartsLine2SeriesData = []

    for (const item of config.dataBar) {
        apexchartsBarSeriesData.push({
            x: item.label,
            y: item.value,
        })
    }

    for (const item of config.dataLine) {
        apexchartsLineSeriesData.push({
            x: item.label,
            y: item.value,
        })
    }

    if (config.dataLine2 && config.dataLine2.length > 0) {
        for (const item of config.dataLine2) {
            apexchartsLine2SeriesData.push({
                x: item.label,
                y: item.value,
            })
        }
    }

    const series: ApexAxisChartSeries = [
        { name: 'Difference', type: 'bar', data: apexchartsBarSeriesData },
        { name: 'Actual', type: 'line', data: apexchartsLineSeriesData },
        apexchartsLine2SeriesData.length > 0 && { name: 'Forecast', type: 'line', data: apexchartsLine2SeriesData },
    ]

    const xaxis = {
        categories: config.dataLine.map((point: any) => point.label),
        labels: {
            formatter: function (val: any) {
                return config.isHorizontal ? `${prefix} ${val} ${suffix}` : val
            },
        },
    }

    const yaxis = [
        {
            title: { text: 'Difference' },
            labels: {
                formatter: function (val: any) {
                    return numberFormat(val) as any
                },
            },
            min: minDataValueBar * 1.1,
            max: maxDataValueBar * 1.1,
        },
        {
            title: { text: 'Actual & Forecast' },
            labels: {
                formatter: function (val: any) {
                    return numberFormat(val) as any
                },
            },
            min: minDataValueLines * 1.1,
            max: maxDataValueLines * 1.1,
            opposite: true,
        },
    ]

    const chart = {
        toolbar: {
            show: false,
        },
        events: {
            mounted: function (chartContext) {
                if (config.selectedKpi !== 'KPIG-PCM' && config.selectedKpi !== 'KPIG-MKG') {
                    const plotWidth = chartContext.w.globals.gridWidth * 0.25
                    setOffsetXValue(plotWidth)
                } else {
                    const plotWidth = chartContext.w.globals.gridWidth * 0.5
                    setOffsetXValue(plotWidth)
                }
            },
            updated: function (chartContext) {
                if (config.selectedKpi !== 'KPIG-PCM' && config.selectedKpi !== 'KPIG-MKG') {
                    const plotWidth = chartContext.w.globals.gridWidth * 0.25
                    setOffsetXValue(plotWidth)
                } else {
                    const plotWidth = chartContext.w.globals.gridWidth * 0.5
                    setOffsetXValue(plotWidth)
                }
            },
        },
    }

    const noData: ApexNoData = {
        text: 'No data',
        align: 'center',
        verticalAlign: 'middle',
        offsetX: 0,
        offsetY: -25,
        style: {
            color: '#373d3f',
            fontSize: '25px',
            fontFamily: 'Roboto',
        },
    }

    const plotOptions = {
        bar: {
            borderRadius: 0,
            horizontal: config.isHorizontal,
            barHeight: config.isHorizontal ? '20px' : 0,
        },
    }

    const dataLabels = {
        enabled: false,
    }

    const legend: ApexLegend = {
        show: true,
        offsetY: config.isHorizontal ? 0 : 10,
        position: 'top',
        horizontalAlign: 'left',
        markers: {
            offsetY: 1.5,
        },
    }

    const tooltip = {
        y: {
            title: {
                formatter: function () {
                    return ''
                },
            },
            formatter: function (value) {
                return value.toFixed(yAxisLabelDecimals)
            },
        },
    }

    const stroke = {
        width: [0, 3, 3],
    }

    const options: ApexOptions = {
        title,
        plotOptions,
        legend,
        // noData,
        dataLabels,
        colors,
        chart,
        xaxis,
        yaxis,
        tooltip,
        stroke,
        ...(config.range != undefined
            ? {
                  annotations: {
                      yaxis: [
                          {
                              y: `${config.range}`,
                              borderColor: '#ED6C02',
                              strokeDashArray: 10,
                              width: '100%',
                              offsetX: pathname == 'site-view' || pathname == '' ? offsetXValue : 0,
                          },
                      ],
                  },
              }
            : {}),
    }

    return (
        <Box
            sx={{
                height: 'inherit',
                minHeight: '50vh',
                maxHeight: '80vh',
                overflowX: 'hidden',
                overflowY:
                    config.isHorizontal && typeof heigth == 'number' ? (heigth < 300 ? 'hidden' : 'auto') : 'hidden',
            }}
        >
            <ReactApexChart options={options} series={series} type="line" width={width} height={heigth} />
        </Box>
    )
}
