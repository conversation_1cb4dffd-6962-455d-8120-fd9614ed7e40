import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from './useGraphqlQuery'
import { NotificationConfig } from '../models/notification-config'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface NotificationConfigQueryRequest {
    name?: string
    notificationType?: string
    end?: number
}

const buildNotificationConfigQuery = (request: NotificationConfigQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.name && request.name != '') {
        filter.push(`{ name: { eq: "${request.name}" }}`)
    }

    if (request.notificationType && request.notificationType != '') {
        filter.push(`{ notificationType: { name: { eq: "${request.notificationType}" }}}`)
    }

    if (request.end) {
        filter.push(`{ createdTime: { lte: ${request.end} }}`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.Static, getLocalUserSite()?.siteCode)}", "${GetSpace(EntityType.Static)}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetChecklistNotificationConfig {
            listChecklistNotificationConfig(
                filter: ${queryFilter}
            , first: 1000) {
                items {
                    externalId
                    space
                  	name
                  	description
                    createdTime
                    notificationType {
                        externalId
                        space
                  	    name
                  	    description
                    }
                }
            }
        }
    `

    return query
}

export const useNotificationConfigs = (request: NotificationConfigQueryRequest) => {
    const query = buildNotificationConfigQuery(request)
    const { data: fdmData } = useGraphqlQuery<NotificationConfig>(gql(query), 'listChecklistNotificationConfig', {})

    const [resultData, setResultData] = useState<{ data: NotificationConfig[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        notificationConfigs: resultData.data,
    }
}
