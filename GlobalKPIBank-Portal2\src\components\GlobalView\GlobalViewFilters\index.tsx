import { translate } from '@celanese/celanese-sdk'
import { Autocomplete, Box, Checkbox, Chip, TextField, styled } from '@mui/material'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { ClnButton } from '@celanese/ui-lib'
import { Site } from '@/common/models/site'
import { useEffect, useState, useContext, useRef, useMemo } from 'react'
import EditFiltersModal from './EditFilterModal'
import { AuthGuardContext } from '@/common/contexts/AuthGuardContext'
import { useGlobalVisionKPI } from '@/common/hooks/useGlobalVisionFavoritesFilters'
import { GlobalVisionFavoritesFilters } from '@/common/models/globalVisionFavoritesFilters'
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank'
import CheckBoxIcon from '@mui/icons-material/CheckBox'
import { BusinessSegment } from '@/common/models/business-segment'
import { useReportingUnitList } from '@/common/hooks/useReportingUnitList'

interface GlobalViewFiltersProps {
    businessSegments: BusinessSegment[]
    sites: Site[]
    setReportingUnits: React.Dispatch<React.SetStateAction<string[]>>
    onSubmit: (filters: any) => void
}

const globalViewFilterSchema = z.object({
    businessSegment: z.object({
        externalId: z.string(),
        description: z.string(),
    }),
    site: z.array(
        z.object({
            externalId: z.string(),
            description: z.string(),
        })
    ),
    customFilter: z.array(
        z.object({
            externalId: z.string(),
            name: z.string(),
            space: z.string(),
            refReportingSites: z.array(
                z.object({
                    externalId: z.string(),
                    name: z.string(),
                    description: z.string(),
                    space: z.string(),
                })
            ),
            refUser: z.object({
                externalId: z.string(),
                space: z.string(),
            }),
        })
    ),
    order: z.number().optional(),
})

type GlobalViewFilterSchema = z.infer<typeof globalViewFilterSchema>

const Form = styled('form')({})
const icon = <CheckBoxOutlineBlankIcon fontSize="small" />
const checkedIcon = <CheckBoxIcon fontSize="small" />

export const GlobalViewFilters: React.FC<GlobalViewFiltersProps> = ({
    sites,
    businessSegments,
    onSubmit,
    setReportingUnits,
}) => {
    const businessSegmentsFilterOptions = useMemo(() => [...businessSegments], [businessSegments])
    const [selectedBusinessSegment, setSelectedBusinessSegment] = useState<string>('')
    const prevCustomFiltersList = useRef<GlobalVisionFavoritesFilters[]>([])
    const [openModal, setOpenModal] = useState(false)
    const [customFiltersList, setCustomFiltersList] = useState<GlobalVisionFavoritesFilters[]>([])
    const savedFilters = localStorage.getItem('filters')
    const parsedFilters = savedFilters ? JSON.parse(savedFilters) : {}
    const { userInfo } = useContext(AuthGuardContext)
    const { dataReportingUnitsList: reportingUnits, refetchReportingUnitsList } = useReportingUnitList(
        selectedBusinessSegment != '' ? [selectedBusinessSegment] : []
    )
    const {
        dataGlobalVisionKPI: favoriteFiltersData,
        loadingGlobalVisionKPI,
        refetchGlobalVisionKPI,
    } = useGlobalVisionKPI({ user: userInfo.email })

    const { reset, resetField, setValue, handleSubmit, control, getValues } = useForm<GlobalViewFilterSchema>({
        defaultValues: {
            site: [],
            customFilter: [],
            businessSegment: { externalId: '', description: '' },
        },
        resolver: zodResolver(globalViewFilterSchema),
    })

    const [limitTags, setLimitTags] = useState<number>(1)

    useEffect(() => {
        const handleResize = () => {
            const width = window.innerWidth

            setLimitTags(() => {
                if (width >= 2900) return 7
                else if (width >= 2600) return 5
                else if (width >= 2200) return 4
                else if (width >= 1830) return 3
                else if (width >= 1480) return 2
                else return 1
            })
        }

        handleResize()
        window.addEventListener('resize', handleResize)

        return () => window.removeEventListener('resize', handleResize)
    }, [limitTags])

    useEffect(() => {
        setReportingUnits(reportingUnits)
    }, [reportingUnits])

    useEffect(() => {
        if (selectedBusinessSegment != undefined || selectedBusinessSegment != '') {
            refetchReportingUnitsList()
        }
    }, [selectedBusinessSegment])

    useEffect(() => {
        if (loadingGlobalVisionKPI) return
        const customFiltersList: GlobalVisionFavoritesFilters[] = favoriteFiltersData.map((filter) => ({
            externalId: filter.externalId,
            space: filter.space,
            name: filter.name,
            refUser: filter.refUser,
            refReportingSites: Object.values(filter.refReportingSites)
                .flat()
                .filter((value) => value instanceof Object),
            order: filter.order,
        }))
        setCustomFiltersList(customFiltersList)
        prevCustomFiltersList.current = customFiltersList
    }, [favoriteFiltersData, loadingGlobalVisionKPI, reset])

    useEffect(() => {
        const isCustomFilterListEqual =
            JSON.stringify(customFiltersList) === JSON.stringify(prevCustomFiltersList.current)

        isCustomFilterListEqual === false && getValues(`customFilter`)?.length > 0 && reset()
    }, [favoriteFiltersData])

    useEffect(() => {
        const aux = []
        if (customFiltersList && customFiltersList.length > 0) {
            parsedFilters?.customFilter?.map((item: { externalId: string }) => {
                const newListItem = customFiltersList.find((x) => x.externalId === item.externalId)
                if (newListItem) {
                    delete newListItem['order']
                    aux.push(newListItem)
                }
            })
            parsedFilters.customFilter = aux

            let parsedSitesFilter: any = []
            let refresh: boolean = false
            if (parsedFilters.customFilter.length > 0) {
                parsedSitesFilter = parsedFilters.customFilter.flatMap((filter: GlobalVisionFavoritesFilters) =>
                    filter.refReportingSites.map((reportingSite: Site) => {
                        return { externalId: reportingSite.externalId, description: reportingSite.description }
                    })
                )

                const isCustomFilterSitesEqual =
                    JSON.stringify(parsedFilters.site) === JSON.stringify(parsedSitesFilter)
                if (!isCustomFilterSitesEqual) {
                    refresh = true
                }
                parsedFilters.site = parsedSitesFilter
            }

            localStorage.setItem('filters', JSON.stringify(parsedFilters))
            setValue('customFilter', parsedFilters.customFilter)
            setValue('site', parsedFilters.site)
            setValue('businessSegment', parsedFilters.businessSegment)

            const hasName = customFiltersList.find((item) => item.name === '' || item.name === undefined)
            if (!hasName && refresh) handleSubmit(handleFiltersSubmit)(parsedFilters)
        }
    }, [customFiltersList])

    useEffect(() => {
        if (parsedFilters.customFilter && parsedFilters.customFilter.length > 0) {
            setValue('customFilter', parsedFilters.customFilter)
        } else {
            setValue('site', parsedFilters.site ?? [])
            setValue('businessSegment', parsedFilters.businessSegment ?? { externalId: '', description: '' })
        }

        handleSubmit(handleFiltersSubmit)(parsedFilters)
    }, [])

    const handleFiltersSubmit = (data: any) => {
        parsedFilters.customFilter = data.customFilter.length > 0 ? data.customFilter : []
        let parsedCustomFilters: any = []

        if (parsedFilters.customFilter.length > 0) {
            parsedCustomFilters = parsedFilters.customFilter.flatMap((filter: GlobalVisionFavoritesFilters) =>
                filter.refReportingSites.map((reportingSite: Site) => {
                    return { externalId: reportingSite.externalId, description: reportingSite.description }
                })
            )
            parsedFilters.site = parsedCustomFilters
            parsedFilters.businessSegment = data.businessSegment
        } else {
            parsedFilters.site = data.site.length > 0 ? data.site : parsedCustomFilters
            parsedFilters.businessSegment = data.businessSegment
        }

        localStorage.setItem('filters', JSON.stringify(parsedFilters))
        onSubmit(parsedFilters)
    }

    return (
        <Form onSubmit={handleSubmit(handleFiltersSubmit)}>
            <Box
                display="flex"
                gap={2}
                alignItems="center"
                flexWrap="wrap"
                sx={{
                    marginTop: '1rem',
                    marginBottom: '1rem',
                    width: '100%',
                }}
            >
                <Box sx={{ flex: '2 1 200px', minWidth: 200 }}>
                    <Controller
                        control={control}
                        name="customFilter"
                        render={({ field }) => (
                            <Autocomplete
                                {...field}
                                loading={customFiltersList.length > 0 ? true : false}
                                multiple
                                disableCloseOnSelect
                                limitTags={limitTags}
                                size="small"
                                id="customFilter"
                                options={customFiltersList}
                                isOptionEqualToValue={(selectedValues, value) => selectedValues.name === value.name}
                                getOptionLabel={(option) => option.name || ''}
                                value={field.value || []}
                                onChange={(event, value) => {
                                    setValue('customFilter', value)
                                    const currentFilters = getValues('customFilter')
                                        .flatMap((customFilter) => customFilter.refReportingSites)
                                        .filter(
                                            (site, index, self) =>
                                                index ===
                                                self.findIndex(
                                                    (duplicatedSite) => duplicatedSite.externalId === site.externalId
                                                )
                                        )

                                    setValue('site', currentFilters)
                                }}
                                renderOption={(props, option, { selected }) => {
                                    const { key, ...optionProps } = props
                                    return (
                                        <li key={key} {...optionProps} data-value={option}>
                                            <Checkbox
                                                icon={icon}
                                                checkedIcon={checkedIcon}
                                                style={{ marginRight: 8 }}
                                                checked={selected}
                                            />
                                            <span> {option.name} </span>
                                        </li>
                                    )
                                }}
                                renderTags={(value, getTagProps) => {
                                    const numTags = value.length
                                    return (
                                        <>
                                            {value.slice(0, limitTags).map((option, index) => (
                                                <Chip
                                                    {...getTagProps({ index })}
                                                    size="small"
                                                    key={index}
                                                    label={option.name}
                                                />
                                            ))}

                                            {numTags > limitTags && ` +${numTags - limitTags}`}
                                        </>
                                    )
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        label={translate('COMMONFILTER.PLACEHOLDER.PRE_SELECT_FILTER')}
                                        size="small"
                                    />
                                )}
                            />
                        )}
                    />
                </Box>

                <Box sx={{ flex: '1 1 120px' }}>
                    <ClnButton
                        type="button"
                        size="medium"
                        variant="outlined"
                        label={translate('MODAL.EDIT_FILTERS')}
                        onClick={() => setOpenModal(true)}
                        fullWidth
                        sx={{ paddingLeft: '22px', paddingRight: '22px', textWrap: 'nowrap' }}
                    />
                    <EditFiltersModal
                        open={openModal}
                        handleClose={() => setOpenModal(false)}
                        data={'test'}
                        customFiltersList={customFiltersList}
                        setCustomFiltersList={setCustomFiltersList}
                        refetchGlobalVisionKPI={refetchGlobalVisionKPI}
                        sites={sites}
                    />
                </Box>

                <Box sx={{ flex: '3 1 300px', minWidth: 240 }}>
                    <Controller
                        control={control}
                        name="site"
                        render={({ field }) => (
                            <Autocomplete
                                {...field}
                                loading
                                multiple
                                limitTags={limitTags}
                                size="small"
                                id="site"
                                disableCloseOnSelect
                                options={sites}
                                isOptionEqualToValue={(selectedValues, value) =>
                                    selectedValues.description === value.description
                                }
                                getOptionLabel={(option) => option.description || ''}
                                onChange={(_, value) => {
                                    setValue('site', value)
                                    setValue('customFilter', [])
                                }}
                                renderOption={(props, option, { selected }) => {
                                    const { key, ...optionProps } = props
                                    return (
                                        <li key={key} {...optionProps} data-value={option}>
                                            <Checkbox
                                                icon={icon}
                                                checkedIcon={checkedIcon}
                                                style={{ marginRight: 8 }}
                                                checked={selected}
                                            />
                                            <span> {option.description} </span>
                                        </li>
                                    )
                                }}
                                renderTags={(value, getTagProps) => {
                                    const numTags = value.length
                                    return (
                                        <>
                                            {value.slice(0, limitTags).map((option, index) => (
                                                <Chip
                                                    {...getTagProps({ index })}
                                                    size="small"
                                                    key={index}
                                                    label={option.description}
                                                />
                                            ))}

                                            {numTags > limitTags && ` +${numTags - limitTags}`}
                                        </>
                                    )
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        label={translate('COMMONFILTER.PLACEHOLDER.ALL_SITES')}
                                        size="small"
                                    />
                                )}
                            />
                        )}
                    />
                </Box>

                <Box sx={{ display: 'flex', gap: 2, flex: '2 1 300px' }}>
                    <Box sx={{ flex: 2 }}>
                        <Controller
                            control={control}
                            name="businessSegment"
                            render={({ field }) => (
                                <Autocomplete
                                    {...field}
                                    loading
                                    limitTags={1}
                                    size="small"
                                    id="businessSegment"
                                    options={businessSegmentsFilterOptions}
                                    isOptionEqualToValue={(selectedValues, value) =>
                                        selectedValues.description === value.description
                                    }
                                    getOptionLabel={(option) => option.description || ''}
                                    value={field.value || { externalId: '', description: '' }}
                                    onChange={(_, value) => {
                                        if (value === null) {
                                            resetField('businessSegment')
                                            setValue('businessSegment', { externalId: '', description: '' })
                                            setSelectedBusinessSegment('')
                                        } else {
                                            setSelectedBusinessSegment(value.externalId)
                                            setValue('businessSegment', value)
                                        }
                                    }}
                                    renderOption={(props, option, { selected }) => {
                                        const { key, ...optionProps } = props
                                        return (
                                            <li key={key} {...optionProps} data-value={option}>
                                                <Checkbox
                                                    icon={icon}
                                                    checkedIcon={checkedIcon}
                                                    style={{ marginRight: 8 }}
                                                    checked={selected}
                                                />
                                                <span> {option.description} </span>
                                            </li>
                                        )
                                    }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            label={translate('COMMONFILTER.BUSINESS_SEGMENT')}
                                            size="small"
                                        />
                                    )}
                                />
                            )}
                        />
                    </Box>

                    <Box sx={{ flex: 1, minWidth: '70px', maxWidth: '200px' }}>
                        <ClnButton
                            type="submit"
                            size="medium"
                            variant="contained"
                            label={translate('COMMONBUTTON.APPLY')}
                            fullWidth
                            sx={{ paddingLeft: '22px', paddingRight: '22px', textWrap: 'nowrap' }}
                        />
                    </Box>
                </Box>
            </Box>
        </Form>
    )
}
