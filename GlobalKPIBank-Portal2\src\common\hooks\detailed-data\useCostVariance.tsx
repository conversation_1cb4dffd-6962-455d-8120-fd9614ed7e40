import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import { CostVarianceKpi } from '@/common/models/costVariance'
import { CostVarianceTableView } from '@/common/models/costVarianceTable'
import { getCustomMonthAndYearQuery } from '@/common/utils/kpis-data-table-export'

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let businessSegmentQ = ''
    let dateQ: string = ''
    let currencyQ: string = ''
    let cursorQ: string = ''
    let limitQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{reportingSite: {externalId: {in: [${sites}]}}},`
        currencyQ = `{currency: {externalId: {eq: "CUR-USD"}}}`
    }

    if (request && request.kpiFilters.businessSegment !== '') {
        businessSegmentQ = `{businessSegment: {externalId: {eq: "${request.kpiFilters.businessSegment}"}}},`
    }

    if (request && request.kpiFilters.date !== '') {
        dateQ = getCustomMonthAndYearQuery(request.exportFilters.range, request.kpiFilters.date, 'month', false)
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listCostVariance (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${businessSegmentQ} ${dateQ} ${currencyQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                reportingSite {
                    externalId
                    name
                }
                pid
                pidDesc
                costType
                plantPeriodCost
                energyCost
                month
                year
                businessSegment {
                    externalId
                    description
                }
            }
        }
    }`
}

const mapCostVariance = (data: CostVarianceKpi[]): CostVarianceTableView[] => {
    const mappedResult: CostVarianceTableView[] = []

    if (data && data.length > 0) {
        data.map((item) => {
            const result: CostVarianceTableView = {
                siteName: item.reportingSite?.name ?? '',
                pid: item.pid ?? '',
                pidDescription: item.pidDesc ?? '',
                costType: item.costType ?? '',
                plantPeriodCost: item.plantPeriodCost,
                energyCost: item.energyCost,
                month: item.month ?? '',
                year: item.year,
                businessSegment: item.businessSegment?.description ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useCostVariance = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<CostVarianceKpi>(gql(query), 'listCostVariance', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'executiveView',
        },
    })

    const [resultData, setResultData] = useState<{ data: any[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapCostVariance(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapCostVariance(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore, request.exportFilters.isExport])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
