import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import { VolumeVarianceKpi } from '@/common/models/volumeVariance'
import { VolumeVarianceTableView } from '@/common/models/volumeVarianceTable'
import { getCustomMonthAndYearQuery } from '@/common/utils/kpis-data-table-export'

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let businessSegmentQ: string = ''
    let dateQ: string = ''
    let unitOfMeasurement: string = ''
    let limitQ: string = ''
    let cursorQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{reportingSite: {externalId: {in: [${sites}]}}},`
        unitOfMeasurement = `{volumeUnitOfMeasurement: {externalId: {eq: "UOM_KGM"}}}`
    }

    if (request && request.kpiFilters.businessSegment !== '') {
        businessSegmentQ = `{businessSegment: {externalId: {eq: "${request.kpiFilters.businessSegment}"}}},`
    }

    if (request.kpiFilters && request.kpiFilters.date !== '') {
        dateQ = getCustomMonthAndYearQuery(request.exportFilters.range, request.kpiFilters.date, 'month', false)
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    return `query getKpiDataView {
        listVolumeVariance (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${businessSegmentQ} ${dateQ} ${unitOfMeasurement}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                reportingSite {
                    externalId
                    name
                }
                pid
                pidDesc
                volumeType
                ProductionVolume
                month
                year
                businessSegment {
                    externalId
                    description
                }
            }
        }
    }`
}

const mapVolumeVariance = (data: VolumeVarianceKpi[]): VolumeVarianceTableView[] => {
    const mappedResult: VolumeVarianceTableView[] = []

    if (data && data.length > 0) {
        data.map((item) => {
            const result: VolumeVarianceTableView = {
                siteName: item.reportingSite?.name ?? '',
                pid: item.pid ?? '',
                pidDescription: item.pidDesc ?? '',
                volumeType: item.volumeType ?? '',
                productionVolume: item.ProductionVolume,
                month: item.month ?? '',
                year: item.year,
                businessSegment: item.businessSegment?.description ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useVolumeVariance = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<VolumeVarianceKpi>(gql(query), 'listVolumeVariance', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'executiveView',
        },
    })

    const [resultData, setResultData] = useState<{ data: VolumeVarianceTableView[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapVolumeVariance(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapVolumeVariance(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore, request.exportFilters.isExport])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
