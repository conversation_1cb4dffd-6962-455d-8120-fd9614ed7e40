import { gql } from '@apollo/client'
import { useMemo } from 'react'
import { useGraphqlQuery } from './useGraphqlQuery'
import { APMConfig } from '../models/apm-config'

export interface APMConfigQueryRequest {}

const buildLocationQuery = (request: APMConfigQueryRequest): string => {
    const query = `
        query GetAPMConfig {
            listAPM_Config(
            first: 1) {
                items {
                    externalId
                    featureConfiguration
                }
            }
        }
    `

    return query
}

export const useAPMConfig = (request: APMConfigQueryRequest) => {
    const query = useMemo(() => gql(buildLocationQuery(request)), [request])
    const result = useGraphqlQuery<APMConfig>(query, 'listAPM_Config')

    return {
        loading: result.loading,
        disciplines: result.data,
    }
}
