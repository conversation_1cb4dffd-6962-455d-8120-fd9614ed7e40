import { translate } from '@celanese/celanese-sdk'
import { CheckBox as CheckBoxIcon, CheckBoxOutlineBlank, IndeterminateCheckBoxOutlined } from '@mui/icons-material'
import {
    Autocomplete,
    AutocompleteChangeDetails,
    AutocompleteChangeReason,
    AutocompleteProps,
    AutocompleteRenderGetTagProps,
    AutocompleteRenderInputParams,
    AutocompleteRenderOptionState,
    Checkbox,
    Chip,
    Divider,
    TextField,
    TextFieldProps,
} from '@mui/material'
import React, {
    createContext,
    ForwardedRef,
    forwardRef,
    useCallback,
    useContext,
    useEffect,
    useImperativeHandle,
    useRef,
    useState,
} from 'react'

type CheckboxState = 'checked' | 'unchecked' | 'indeterminate'

const icon = <CheckBoxOutlineBlank fontSize="small" />
const checkedIcon = <CheckBoxIcon fontSize="small" />
const indeterminateIcon = <IndeterminateCheckBoxOutlined fontSize="small" />

const contextDefaultValue = {
    onSelectAll: (_selectedAll: boolean) => void null,
    selectedAll: false,
    indeterminate: false,
}
const LocalContext = createContext<typeof contextDefaultValue>(contextDefaultValue)

type NullableUlElement = HTMLUListElement | null

type ExtendedSlotProps = NonNullable<AutocompleteProps<any, true, false, false>['slotProps']> & {
    textField?: Partial<TextFieldProps>
}

export interface MultiAutoCompleteWithSelectAllProps<T>
    extends Omit<
        AutocompleteProps<T, true, false, false>,
        'renderInput' | 'renderOption' | 'renderTags' | 'label' | 'slotProps'
    > {
    id: string
    label: string
    labelField?: keyof T
    valueField?: keyof T
    slotProps?: ExtendedSlotProps
    onSelectAll: (value: T[]) => void
    onItemRemoved: (option: T) => void
}

function MultiAutoCompleteWithSelectAll<T>({
    label,
    valueField = 'externalId' as keyof T,
    labelField = 'name' as keyof T,
    onSelectAll,
    onItemRemoved,
    slotProps,
    ...props
}: MultiAutoCompleteWithSelectAllProps<T>) {
    const [checkboxState, setCheckboxState] = useState<CheckboxState>('indeterminate')
    const getCheckboxState = useCallback((value: T[], options: readonly T[]): CheckboxState => {
        if (value.length === options?.length) {
            return 'checked'
        }
        return value.length ? 'indeterminate' : 'unchecked'
    }, [])

    useEffect(() => {
        setCheckboxState(getCheckboxState(props.value, props.options))
    }, [props.value, props.options])

    const customInput = useCallback(
        (params: AutocompleteRenderInputParams) => (
            <TextField
                {...params}
                label={label}
                size="small"
                sx={{ backgroundColor: 'background.paper' }}
                {...(slotProps?.textField ?? {})}
            />
        ),
        [slotProps?.textField, label]
    )
    const customTags = useCallback(
        (value: T[], getTagProps: AutocompleteRenderGetTagProps) => {
            const numTags = value.length
            const limit = props.limitTags ?? numTags
            return (
                <>
                    {value.slice(0, limit).map((option, index) => (
                        <Chip
                            {...getTagProps({ index })}
                            size="small"
                            key={`${props.id}-chip-${String(option?.[valueField])}`}
                            label={option?.[labelField] as string}
                            onDelete={() => onItemRemoved?.(option)}
                        />
                    ))}
                    {numTags > limit && (
                        <Chip
                            size="small"
                            label={`+${numTags - limit}`}
                            sx={{ cursor: 'default', pointerEvents: 'none' }}
                        />
                    )}
                </>
            )
        },
        [onItemRemoved, props.id, labelField]
    )

    const customOptions = useCallback(
        (
            optionProps: React.HTMLAttributes<HTMLLIElement>,
            option: any,
            { selected }: AutocompleteRenderOptionState
        ) => (
            <li {...optionProps} key={`${optionProps.id}-${String(option[valueField])}`}>
                <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                <span>{option[labelField] as string}</span>
            </li>
        ),
        [valueField, labelField]
    )

    const customLabel = useCallback((option: T) => String(option[labelField]), [labelField])

    const handleChange = useCallback(
        (
            event: React.SyntheticEvent,
            newValue: T[],
            reason: AutocompleteChangeReason,
            details?: AutocompleteChangeDetails<T>
        ) => {
            setCheckboxState(getCheckboxState(newValue, props.options))
            props?.onChange?.(event, newValue, reason, details)
        },
        [props?.options, props?.onChange, setCheckboxState, getCheckboxState]
    )

    return (
        <LocalContext.Provider
            value={{
                onSelectAll: () => onSelectAll?.(props.value),
                selectedAll: checkboxState === 'checked',
                indeterminate: checkboxState === 'indeterminate',
            }}
        >
            <Autocomplete
                size="small"
                sx={{ width: '100%', height: '100%' }}
                disableCloseOnSelect
                disableClearable
                multiple
                {...props}
                onChange={handleChange}
                getOptionLabel={customLabel}
                renderOption={customOptions}
                renderTags={customTags}
                renderInput={customInput}
                ListboxComponent={CustomSelectListBox}
            />
        </LocalContext.Provider>
    )
}

const CustomSelectListBox = forwardRef(function ListBoxBase(
    props: React.HTMLAttributes<HTMLUListElement>,
    ref: ForwardedRef<HTMLUListElement>
) {
    const { children, ...rest } = props
    const innerRef = useRef<HTMLUListElement>(null)
    const { onSelectAll, selectedAll, indeterminate } = useContext(LocalContext)

    useImperativeHandle<NullableUlElement, NullableUlElement>(ref, () => innerRef.current)

    return (
        <ul {...rest} ref={innerRef} role="list-box">
            <li style={{ display: 'flex', alignItems: 'center' }}>
                <Checkbox
                    icon={icon}
                    checkedIcon={checkedIcon}
                    indeterminateIcon={indeterminateIcon}
                    indeterminate={indeterminate}
                    checked={selectedAll}
                    onChange={(_e) => onSelectAll(selectedAll)}
                    sx={{ ml: 2 }}
                />
                {translate('COMMONFILTER.ALL')}
            </li>
            <Divider />
            {children}
        </ul>
    )
})

export default MultiAutoCompleteWithSelectAll
