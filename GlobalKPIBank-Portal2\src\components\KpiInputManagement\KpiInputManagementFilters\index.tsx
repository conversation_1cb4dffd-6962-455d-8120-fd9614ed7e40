import { translate } from '@celanese/celanese-sdk'
import { Autocomplete, Box, Grid, TextField, styled } from '@mui/material'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useState } from 'react'
import { KpiInputManagementTable } from '../KpiInputManagementTable'
import dayjs from 'dayjs'
import { useKPIInputManagement } from '@/common/hooks/useKPIInputManagement'
import { useReportingSiteList } from '@/common/hooks/useReportingSiteList'
import { useAuthToken } from '@/common/hooks'

const kpiInputManagementFilterSchema = z.object({
    status: z.string(),
    year: z.string(),
})
type KpiInputManagementFilterSchema = z.infer<typeof kpiInputManagementFilterSchema>

const Form = styled('form')({})

const statusFilterOptions = [
    translate('COMMONFILTER.ALL'),
    translate('COMMONFILTER.NORMAL'),
    translate('COMMONFILTER.CRITICAL'),
]

export const KpiInputManagementFilters = () => {
    const { getAuthToken } = useAuthToken()

    const [resultData, setResultData] = useState<any>([])
    const [loading, setLoading] = useState<boolean>(true)
    const [selectedStatus, setSelectedStatus] = useState<string>(translate('COMMONFILTER.ALL'))

    const generateYearsArray = (startYear: number): string[] => {
        const currentYear = dayjs().year()
        return Array.from({ length: currentYear - startYear + 1 }, (_, index) => (startYear + index).toString())
    }

    const [years] = useState<string[]>(generateYearsArray(2024))
    const [manualTable, setManualTable] = useState<any>({
        site: { externalId: '', description: '' },
        year: dayjs().year().toString(),
    })
    const { dataSiteList } = useReportingSiteList([])

    const { dataManualInput, loadingManualInput } = useKPIInputManagement(manualTable, dataSiteList)

    useEffect(() => {
        getAuthToken().then((token) => {
            fetch('/api/listKpiInputManagement', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({ manualTable: manualTable, sites: dataSiteList }),
            }).then((r) =>
                r.json().then((data) => {
                    if (data.length > 0) {
                        setLoading(false)
                        setResultData(data)
                    }
                })
            )
        })
    }, [manualTable])

    const { setValue, handleSubmit, control } = useForm<KpiInputManagementFilterSchema>({
        defaultValues: {
            status: translate('COMMONFILTER.ALL'),
            year: dayjs().year().toString(),
        },
        resolver: zodResolver(kpiInputManagementFilterSchema),
    })

    const onSubmit = (data: KpiInputManagementFilterSchema) => {
        if (data) {
            setManualTable(data)
        }
    }

    const handleStatusSelectionChange = (value: KpiInputManagementFilterSchema['status']) => {
        setValue('status', value)
        setSelectedStatus(value)
        handleSubmit(onSubmit)()
    }

    const handleYearSelectionChange = (value: KpiInputManagementFilterSchema['year']) => {
        setValue('year', value)
        handleSubmit(onSubmit)()
    }

    return (
        <Box sx={{ height: '100%' }}>
            <Form onSubmit={handleSubmit(onSubmit)}>
                <Grid container spacing={2} sx={{ height: '100%', marginTop: '5px' }}>
                    <Grid item xs={2}>
                        <Controller
                            control={control}
                            name="year"
                            render={({ field }) => (
                                <Autocomplete
                                    {...field}
                                    loading
                                    size="small"
                                    id="year"
                                    options={years}
                                    getOptionLabel={(option) => option || ''}
                                    value={field.value || null}
                                    onChange={(_, value) => {
                                        if (!value) {
                                            setValue('year', '')
                                        } else {
                                            handleYearSelectionChange(value)
                                        }
                                    }}
                                    renderInput={(params) => (
                                        <TextField {...params} label={translate('COMMONFILTER.YEAR')} size="small" />
                                    )}
                                    renderOption={(props, option) => (
                                        <li {...props} key={option} data-value={option}>
                                            <span>{option}</span>
                                        </li>
                                    )}
                                />
                            )}
                        />
                    </Grid>
                    {/* <Grid item xs={2}>
                        <Controller
                            control={control}
                            name="status"
                            render={({ field }) => (
                                <Autocomplete
                                    {...field}
                                    limitTags={1}
                                    size="small"
                                    id="status"
                                    options={statusFilterOptions}
                                    getOptionLabel={(option) => option || ''}
                                    onChange={(event, value) => {
                                        value === null ? reset() : setValue('status', value)
                                        handleStatusSelectionChange(value)
                                    }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            label={translate('COMMONFILTER.STATUS')}
                                            placeholder={translate('COMMONFILTER.STATUS')}
                                            size="small"
                                        />
                                    )}
                                    renderOption={(props, option) => (
                                        <li {...props} data-value={option}>
                                            <span>{option}</span>
                                        </li>
                                    )}
                                />
                            )}
                        />
                    </Grid> */}
                </Grid>
            </Form>

            <KpiInputManagementTable manualInputData={resultData} loading={loading} />
        </Box>
    )
}
