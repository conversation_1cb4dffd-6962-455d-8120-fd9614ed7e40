import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from './useGraphqlQuery'

interface priorityStatus {
    externalId: string
    priority?: any
    status: string
}

const buildPrioritysStatus = (): string => {
    const query = `
        query GetPrioritysStatus {
            listObservation(
            , first: 1000) {
                items {
                    externalId
                    status
                    priority
                }
            }
        }
    `
    return query
}

export const usePrioritysStatus = () => {
    const query = buildPrioritysStatus()
    const { data: fdmData } = useGraphqlQuery<priorityStatus>(gql(query), 'listObservation', {})

    const [resultData, setResultData] = useState<{ data: priorityStatus[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        priorityStatus: fdmData,
    }
}
