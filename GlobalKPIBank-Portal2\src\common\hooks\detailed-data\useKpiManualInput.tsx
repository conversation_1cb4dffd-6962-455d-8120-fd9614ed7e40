import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { FlawlessDaysKpi } from '@/common/models/flawlessDays'
import { DataModelView } from '@/common/models/dataModelView'
import { FlawlessDaysTableView } from '@/common/models/flawlessDaysTable'
import { setExportDateRange } from '@/common/utils/kpis-data-table-export'
import dayjs from 'dayjs'

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let businessSegmentQ: string = ''
    let startDateQ: string = ''
    let endDateQ: string = ''
    let descriptionQ: string = ''
    let limitQ: string = ''
    let cursorQ: string = ''

    const description = (kpiName: string): string => {
        if (kpiName === 'Flawless Days') return `{description: { eq: "Flawless days"}}`
        if (kpiName === 'KG/Headcount')
            return `{description: { in: ["Contractor Headcount", "Celanese Employee Headcount"]}}`

        return ``
    }

    if (request.kpiFilters && request.kpiFilters.kpiName !== '') {
        descriptionQ = description(request.kpiFilters.kpiName)
    }

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{refSite: {externalId: {in: [${sites}]}}},`
    }

    if (request && request.kpiFilters.businessSegment !== '') {
        businessSegmentQ = `{businessSegment: {externalId: {eq: "${request.kpiFilters.businessSegment}"}}},`
    }

    if (request.kpiFilters && request.kpiFilters.date !== '') {
        let sanitizedStartDate = dayjs(request.kpiFilters.date).startOf('month').format('YYYY-MM-DD')
        let sanitizedEndDate = dayjs(request.kpiFilters.date).endOf('month').format('YYYY-MM-DD')

        if (request.exportFilters && request.exportFilters.isExport) {
            ;[sanitizedStartDate, sanitizedEndDate] = setExportDateRange(
                request.exportFilters.range,
                request.kpiFilters.date
            )
        }

        startDateQ = `{startDate: {gte: "${sanitizedStartDate}"}},`
        endDateQ = `{endDate: {lte: "${sanitizedEndDate}"}},`
    }

    if (request && request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    return `query getKpiDataView {
        listKPIManualInput (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${businessSegmentQ} ${startDateQ} ${endDateQ} ${descriptionQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                refSite {
                    externalId
                    name
                }
                startDate
                endDate
                kpiValue
                description
                createdAt
                createdBy
                refBusinessSegment {
                    externalId
                    description
                }
            }
        }
    }`
}

const mapFlawlessDays = (data: FlawlessDaysKpi[]): FlawlessDaysTableView[] => {
    const mappedResult: FlawlessDaysTableView[] = []

    if (data && data.length > 0) {
        data.map((item) => {
            const result: FlawlessDaysTableView = {
                siteName: item.refSite.name ?? '',
                month: dayjs(item.startDate).format('MMM'),
                year: dayjs(item.startDate).format('YYYY'),
                kpiValue: item.kpiValue,
                description: item.description ?? '',
                createdAt: item.createdAt?.toString().split('T')[0] ?? '',
                createdBy: item.createdBy,
                businessSegment: item.refBusinessSegment?.description ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useKpiManualInput = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<FlawlessDaysKpi>(gql(query), 'listKPIManualInput', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'gkpisol',
        },
    })

    const [resultData, setResultData] = useState<{ data: FlawlessDaysTableView[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)
    const loadMoreExport = () => {
        if (!fdmPageInfo.hasNextPage) return
        setLoadMore(!loadMore)
    }
    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapFlawlessDays(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapFlawlessDays(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore, request.exportFilters.range])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
