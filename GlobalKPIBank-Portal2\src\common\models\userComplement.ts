import { ExternalEntity, Location } from '.'
import { Role } from './role'
import { ShiftConfiguration } from './shiftConfiguration'
import { Site } from './site'
import { Unit } from './unit'

export interface UserComplement extends ExternalEntity {
    userAzureAttribute: UserAzureAttribute
    userRoleSite: UserRoleSite[]
    reportingLocations: Location[]
    reportingUnits: Unit[]
    shiftConfiguration: ShiftConfiguration
}

export interface UserAzureAttribute {
    user: UserAzure
}

export interface UserAzure extends ExternalEntity {
    displayName: string
    firstName: string
    lastName: string
    email: string
}

export interface UserRoleSite extends ExternalEntity {
    reportingSite: Site
    role: Role
}
