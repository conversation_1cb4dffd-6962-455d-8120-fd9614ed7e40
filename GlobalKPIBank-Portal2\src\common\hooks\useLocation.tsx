import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { ArrayEntity } from '../models'
import { ReportingLocation } from '../models/reportingLocation'
import { Site } from '../models/site'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface LocationQueryRequest {
    unitName?: string
    nextPage?: string
    unitExternalId?: string
}

const buildLocationQuery = (request: LocationQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.unitName && request.unitName != '') {
        filter.push(`{ reportingUnits: { name: { eq: "${request.unitName}" }}}`)
    }

    if (request.unitExternalId && request.unitExternalId != '') {
        filter.push(`{ reportingUnit: { externalId: { eq: "${request.unitExternalId}" }}}`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.REF, getLocalUserSite()?.siteCode)}", "${GetSpace(EntityType.REF)}"]
            }
        }`
    )

    filter.push(`{ isActive: { eq: ${true} } }`)

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetReportingLocation {
            listReportingLocation(
                filter: ${queryFilter}
            , first: 1000, after: ${request.nextPage ? `"${request.nextPage}"` : 'null'}) {
                items {
                    externalId
                    name
                    description
                    aliases
                    createdTime
                    space
                    reportingUnit {
                        externalId
                        name
                        description
                        space
                    }
                    reportingSites {
                        items {
                            externalId
                            name
                            reportingUnits {
                                items {
                                    externalId
                                    name
                                }
                            }
                        }
                    }
                }
                pageInfo {
                    hasPreviousPage
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    return query
}

export const useLocations = (request: LocationQueryRequest) => {
    const query = buildLocationQuery(request)
    const {
        data: fdmData,
        refetch,
        pageInfo,
    } = useGraphqlQuery<ReportingLocation>(gql(query), 'listReportingLocation', {})

    const [resultData, setResultData] = useState<{ data: ReportingLocation[]; loading: boolean; pageInfo: PageInfo }>({
        data: [],
        loading: true,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false, pageInfo })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayReportSiteEntity = d.reportingSites as any as ArrayEntity<Site>

                return {
                    ...d,
                    reportingSites: arrayReportSiteEntity.items,
                }
            })
            setResultData({ data: fdmDataParsed, loading: false, pageInfo })
        }
    }, [fdmData, pageInfo])

    return {
        loading: resultData.loading,
        locations: resultData.data,
        refetchLocation: refetch,
        pageInfo: resultData.pageInfo,
    }
}
