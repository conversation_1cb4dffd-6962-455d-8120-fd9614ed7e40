import { gql } from '@apollo/client'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useEffect, useState } from 'react'
import { ManualInputKpi } from '../models/manualInputKpi'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import updateLocale from 'dayjs/plugin/updateLocale'
import { ManualInputTableModel } from '../models/manualInputDataTableModel'
import { Site } from '../models/site'
dayjs.extend(utc)
dayjs.extend(updateLocale)

export interface KpiInputManagementRequest {
    status: string
    year: string
}

const queryBuilder = (request: KpiInputManagementRequest) => {
    let startDateQ: string = ''
    let endDateQ: string = ''

    if (request.year !== undefined || request.year != null) {
        startDateQ = `{startDate: {gte: "${request.year}-01-01"}}`
        endDateQ = `{endDate: {lte: "${request.year}-12-31"}}`
    }

    return `query getManualInputList {
        listKPIManualInput (
            first: 1000
            filter: {and: [${startDateQ}, ${endDateQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                externalId
                space
                refSite {
                    externalId
                    name
                }
                refKPICatogory {
                    externalId
                    name
                }
                kpiValue
                startDate
                endDate
                description
                period
                inputType
            }
        }
    }`
}

export const useKPIInputManagement = (request: KpiInputManagementRequest, sites: Site[]) => {
    const [resultData, setResultData] = useState<{ data: ManualInputTableModel[] }>({
        data: [],
    })

    const query = queryBuilder(request)
    const {
        data: fdmData,
        refetch,
        loading,
    } = useGraphqlQuery<ManualInputKpi>(gql(query), 'listKPIManualInput', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'gkpisol',
        },
    })

    useEffect(() => {
        if (request.year !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (fdmData.length == 0) {
                    const emptyGroup: any = []
                    sites.forEach((site) => {
                        emptyGroup.push(generateEmptyResult('Flawless days', site))
                        emptyGroup.push(generateEmptyResult('Celanese Employee Headcount', site))
                        emptyGroup.push(generateEmptyResult('Contractor Headcount', site))
                    })
                    const groupedResult = groupByKPI(emptyGroup)
                    const sanitizedResult = mapResult(groupedResult)
                    setResultData({ data: sanitizedResult })
                }
                if (fdmData.length > 0) {
                    const sanitizedResult = transformData(fdmData, sites)
                    setResultData({ data: sanitizedResult })
                }
            }
        }
    }, [fdmData, request.year, sites])

    return {
        loadingManualInput: loading,
        refetchManualInput: refetch,
        dataManualInput: resultData.data,
    }
}

function transformData(data, allSites) {
    const groupedData = {}

    const months = [
        'january',
        'february',
        'march',
        'april',
        'may',
        'june',
        'july',
        'august',
        'september',
        'october',
        'november',
        'december',
    ]
    const kpis = ['Flawless days', 'Celanese Employee Headcount', 'Contractor Headcount']

    allSites.forEach((site) => {
        groupedData[site.name] = {}
        kpis.forEach((description) => {
            groupedData[site.name][description] = {
                category: 'Foundational',
                name: site.name,
                description: description,
                ...Object.fromEntries(months.map((month) => [month, undefined])),
            }
        })
    })

    data.forEach((entry) => {
        const siteName = entry?.refSite?.name
        const description = entry.description

        if (!groupedData[siteName]) {
            groupedData[siteName] = {}
        }

        if (!groupedData[siteName][description]) {
            groupedData[siteName][description] = {
                category: entry.refKPICatogory.name,
                name: siteName,
                description: description,
                ...Object.fromEntries(months.map((month) => [month, undefined])),
            }
        }

        const month = new Date(entry.startDate).toLocaleString('default', { month: 'long' }).toLowerCase()

        groupedData[siteName][description][month] = entry.kpiValue
    })

    const result: any[] = Object.keys(groupedData).map((siteName) => {
        const kpiArray = Object.values(groupedData[siteName]).map((entry, index) => ({
            ...(entry as object),
            order: index + 1,
        }))

        return {
            kpi: kpiArray,
            data: kpiArray[0],
        }
    })

    return result
}

const groupByKPI = (resultData: any[]): any => {
    const groupedByKPI = Object.values(
        resultData.reduce((group, item) => {
            group[item.description] = group[item.description] ?? []
            group[item.description].push(item)
            return group
        }, {})
    )
    return groupedByKPI
}

const groupBySite = (resultData: any[]): any => {
    const groupedBySite = Object.values(
        resultData.reduce((group, item) => {
            group[item.name] = group[item.name] ?? []
            group[item.name].push(item)
            return group
        }, {})
    )
    return groupedBySite
}

const getOrder = (categoryName: string): number => {
    if (categoryName === 'Flawless days') return 1

    if (categoryName === 'Celanese Employee Headcount') return 2

    return 3
}

const getMonthValue = (res: any, item: any) => {
    if (!item.startDate) return

    const monthName = dayjs(item.startDate).format('MMMM').toLowerCase()

    if (res[item.description][monthName] === undefined) {
        res[item.description][monthName] = item.kpiValue
    }
}

const getMonthGroupedValue = (res: any, item: any) => {
    if (res[item.name].january === undefined) {
        res[item.name].january = item.january
    }
    if (res[item.name].february === undefined) {
        res[item.name].february = item.february
    }
    if (res[item.name].march === undefined) {
        res[item.name].march = item.march
    }
    if (res[item.name].april === undefined) {
        res[item.name].april = item.april
    }
    if (res[item.name].may === undefined) {
        res[item.name].may = item.may
    }
    if (res[item.name].june === undefined) {
        res[item.name].june = item.june
    }
    if (res[item.name].july === undefined) {
        res[item.name].july = item.july
    }
    if (res[item.name].august === undefined) {
        res[item.name].august = item.august
    }
    if (res[item.name].september === undefined) {
        res[item.name].september = item.september
    }
    if (res[item.name].october === undefined) {
        res[item.name].october = item.october
    }
    if (res[item.name].november === undefined) {
        res[item.name].november = item.november
    }
    if (res[item.name].december === undefined) {
        res[item.name].december = item.december
    }
}

const mapResult = (groupedResult: any[]): any => {
    const mappedResult = []
    const newMappedResult: { kpi: any; data: any }[] = []
    groupedResult.forEach((kpi) => {
        let kpiDescription = ''
        const accumulator = kpi.reduce(function (res: any, item: any) {
            if (!res[item.description]) {
                res[item.description] = {
                    category: item?.refKPICatogory?.name ?? '',
                    name: item?.refSite?.name ?? '',
                    description: item.description,
                    order: getOrder(item.description),
                    january: undefined,
                    february: undefined,
                    march: undefined,
                    april: undefined,
                    may: undefined,
                    june: undefined,
                    july: undefined,
                    august: undefined,
                    september: undefined,
                    october: undefined,
                    november: undefined,
                    december: undefined,
                }
            }
            getMonthValue(res, item)
            kpiDescription = item.description
            return res
        }, {})
        mappedResult.push(accumulator[kpiDescription])
    })

    const groupedMappedResult = groupBySite(mappedResult)

    groupedMappedResult.forEach((groupMap) => {
        let kpiName = ''
        const allKpiItems: any = []
        const accumulator = groupMap.reduce(function (res: any, item: any) {
            if (!res[item.name]) {
                res[item.name] = {
                    category: item?.category ?? '',
                    name: item?.name ?? '',
                    description: item.description,
                    order: item.order,
                    january: undefined,
                    february: undefined,
                    march: undefined,
                    april: undefined,
                    may: undefined,
                    june: undefined,
                    july: undefined,
                    august: undefined,
                    september: undefined,
                    october: undefined,
                    november: undefined,
                    december: undefined,
                }
            }
            getMonthGroupedValue(res, item)
            kpiName = item.name
            allKpiItems.push(item)
            return res
        }, {})
        newMappedResult.push({
            kpi: allKpiItems,
            data: accumulator[kpiName],
        })
    })
    newMappedResult.sort((a, b) => a.data.name - b.data.name)
    return newMappedResult
}

const generateEmptyResult = (kpiDescription: string, site: Site): ManualInputKpi => {
    const partialEmpty: any = {
        refSite: {
            externalId: site.externalId,
            name: site.name,
        },
        refKPICatogory: {
            externalId: '',
            space: 'GKPI-COR-ALL-DAT',
            name: 'Foundational',
        },
        kpiValue: 0,
        startDate: undefined,
        endDate: undefined,
        description: kpiDescription,
        space: '',
        externalId: '',
        period: 'Monthly',
        inputType: 'Manual',
    }
    return partialEmpty
}
