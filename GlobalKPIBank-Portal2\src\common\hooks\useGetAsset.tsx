import { useMemo, useState, useEffect } from 'react'
import { useCognite } from '.'
import { Asset, Timeseries } from '@cognite/sdk/dist/src/types'

export const useGetAsset = (assetDescription: string) => {
    const { cogniteClient } = useCognite()

    const assetsApi = useMemo(() => {
        return cogniteClient.assets
    }, [cogniteClient])

    const timeseriesApi = useMemo(() => {
        return cogniteClient.timeseries
    }, [cogniteClient])

    const dataPointsApi = useMemo(() => {
        return cogniteClient.datapoints
    }, [cogniteClient])

    const [asset, setAsset] = useState<Asset | undefined>(undefined)
    const [timeseries, setTimeseries] = useState<Timeseries[]>([])
    const [dataPoints, setDataPoints] = useState<any[]>([])

    const [returnData, setReturnData] = useState<any | undefined>(undefined)
    const [loading, setLoading] = useState<boolean>(true)

    useEffect(() => {
        async function getAssetByDescription(description: string) {
            if (description == '') {
                return undefined
            }

            const result = await assetsApi.retrieve([{ externalId: description }])
            return result.find((asset) => asset.externalId === description)
        }

        getAssetByDescription(assetDescription).then((result) => {
            setAsset(result)
        })
    }, [assetDescription, assetsApi])

    useEffect(() => {
        async function getTimeSeriesByExternalId(externalId: number) {
            if (externalId == null) {
                return undefined
            }

            const result = await timeseriesApi.search({ filter: { assetIds: [externalId] } })
            return result
        }

        if (asset?.id) {
            getTimeSeriesByExternalId(asset?.id).then((result) => {
                result?.forEach((resultData) => {
                    if (timeseries.some((ts) => ts.externalId == resultData.externalId) == false) {
                        setTimeseries((prev) => [...prev, resultData])
                    }
                })
            })
        }
    }, [asset, timeseriesApi])

    useEffect(() => {
        async function getDataPointsByTimeseriesId(timeseriesId: number) {
            if (timeseriesId == null) {
                return undefined
            }

            const result = await dataPointsApi.retrieve({ items: [{ id: timeseriesId }] })
            return result
        }

        if (timeseries != undefined) {
            timeseries?.forEach((timeseries) => {
                getDataPointsByTimeseriesId(timeseries?.id).then((result) => {
                    result?.forEach((resultData) => {
                        setDataPoints((prev) => [...prev, resultData])
                    })
                })
            })
        }
    }, [timeseries, dataPointsApi])

    useEffect(() => {
        async function getTimeSeriesData() {
            return {
                timeseries: timeseries,
                asset: asset,
                dataPoints: dataPoints,
            }
        }

        if (timeseries && asset) {
            getTimeSeriesData().then((result) => {
                setReturnData(result)
                setLoading(false)
            })
        }
    }, [asset, timeseries, dataPoints, cogniteClient])

    return { returnData, loading }
}
