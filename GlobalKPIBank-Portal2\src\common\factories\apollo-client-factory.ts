import {
    ApolloClient,
    ApolloLink,
    InMemory<PERSON>ache,
    NormalizedCacheObject,
    ServerError,
    createHttpLink,
    fromPromise,
} from '@apollo/client'
import { setContext } from '@apollo/client/link/context'
import { onError } from '@apollo/client/link/error'
import { enviroment } from '../configurations/enviroment'

export const createApolloClient = (getAuthToken: () => Promise<string>): ApolloClient<NormalizedCacheObject> => {
    const assetHierarchyLink = createHttpLink({
        uri: enviroment.assetHierarchyGraphqlUri,
    })

    const kpidomViewLink = createHttpLink({
        uri: enviroment.kpidomGraphqlUri,
    })

    const globalViewLink = createHttpLink({
        uri: enviroment.gkpiGraphqlUri,
    })

    const executiveViewLink = createHttpLink({
        uri: enviroment.executiveGraphqlUri,
    })

    const stewardshipViewLink = createHttpLink({
        uri: enviroment.stewardshipGraphqlUri,
    })

    const incidentViewLink = createHttpLink({
        uri: enviroment.incidentGraphqlUri,
    })

    const qualityViewLink = createHttpLink({
        uri: enviroment.qualityGraphqlUri,
    })

    const ofwaViewLink = createHttpLink({
        uri: enviroment.ofwaGraphqlUri,
    })

    const defaultLink = createHttpLink({
        uri: enviroment.gkpiGraphqlUri,
    })

    const authLink = setContext(async (_, { headers }) => {
        const accessToken = await getAuthToken()
        return {
            headers: {
                ...headers,
                authorization: `Bearer ${accessToken}`,
                'X-Cdp-App': enviroment.cogniteXCdpApp,
            },
        }
    })

    const errorLink = onError(({ networkError, operation, forward }) => {
        const serverError = networkError as ServerError
        if (serverError?.statusCode === 401) {
            return fromPromise(
                getAuthToken().catch((_error) => {
                    return
                })
            )
                .filter((value) => Boolean(value))
                .flatMap((accessToken) => {
                    const oldHeaders = operation.getContext().headers
                    operation.setContext({
                        headers: {
                            ...oldHeaders,
                            authorization: `Bearer ${accessToken}`,
                            'X-Cdp-App': enviroment.cogniteXCdpApp,
                        },
                    })
                    return forward(operation)
                })
        }
    })

    const cachePolicies = new InMemoryCache({
        typePolicies: {
            Query: {
                fields: {
                    listGlobalVisionKPITarget: {
                        keyArgs: ['filter'],
                        merge(existing, incoming, { args }) {
                            if (!args?.after) {
                                return incoming
                            }
                            return {
                                ...incoming,
                                items: [...(existing?.items || []), ...(incoming.items || [])],
                            }
                        },
                    },
                    listGlobalVisionKPIAggregation: {
                        keyArgs: ['filter'],
                        merge(existing, incoming, { args }) {
                            if (!args?.after) {
                                return incoming
                            }
                            return {
                                ...incoming,
                                items: [...(existing?.items || []), ...(incoming.items || [])],
                            }
                        },
                    },
                    listGlobalVisionKPIBusinessSegmentAggregation: {
                        keyArgs: ['filter'],
                        merge(existing, incoming, { args }) {
                            if (!args?.after) {
                                return incoming
                            }
                            return {
                                ...incoming,
                                items: [...(existing?.items || []), ...(incoming.items || [])],
                            }
                        },
                    },
                    listKpiResult: {
                        keyArgs: ['filter'],
                        merge(existing, incoming, { args }) {
                            if (!args?.after) {
                                return incoming
                            }
                            return {
                                ...incoming,
                                items: [...(existing?.items || []), ...(incoming.items || [])],
                            }
                        },
                    },
                },
            },
        },
    })

    return new ApolloClient({
        link: authLink.concat(errorLink).concat(
            ApolloLink.split(
                (operation) => operation.getContext().clientName === 'assetHierarchy',
                assetHierarchyLink,
                ApolloLink.split(
                    (operation) => operation.getContext().clientName === 'globalView',
                    globalViewLink,
                    ApolloLink.split(
                        (operation) => operation.getContext().clientName === 'executiveView',
                        executiveViewLink,
                        ApolloLink.split(
                            (operation) => operation.getContext().clientName === 'stewardshipView',
                            stewardshipViewLink,
                            ApolloLink.split(
                                (operation) => operation.getContext().clientName === 'incidentView',
                                incidentViewLink,
                                ApolloLink.split(
                                    (operation) => operation.getContext().clientName === 'qualityView',
                                    qualityViewLink,
                                    ApolloLink.split(
                                        (operation) => operation.getContext().clientName === 'ofwaView',
                                        ofwaViewLink,
                                        ApolloLink.split(
                                            (operation) => operation.getContext().clientName === 'kpidomView',
                                            kpidomViewLink,
                                            defaultLink
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        ),
        cache: cachePolicies,
        ssrMode: typeof window === 'undefined',
    })
}
