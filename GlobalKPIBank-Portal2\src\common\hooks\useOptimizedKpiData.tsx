import { useState, useEffect, useMemo, useCallback } from 'react'
import { useGlobalVisionKPISite } from './useGlobalVisionKPISite'
import { useGlobalVisionKPISegment } from './useGlobalVisionKPISegment'
import { useGlobalVisionKPITarget } from './useGlobalVisionKPITarget'
import { GlobalVisionKpiTableModel } from '../models/globalVisionKpiTableModel'
import { KpiGroup } from '../models/kpiGroup'
import { PageNames } from '../utils/page-names'
import { mergeSiteValues } from '../utils/kpiSitesValueMerger'
import { getKpiFromCache, setKpiCache } from '../clients/kpi-cache'

interface UseOptimizedKpiDataProps {
    filters: any
    tabGroup: KpiGroup | undefined
    debounceDelay?: number
}

interface OptimizedKpiData {
    siteData: GlobalVisionKpiTableModel[]
    segmentData: GlobalVisionKpiTableModel[]
    targetData: any[]
    loading: boolean
    error?: string
    refetch: () => void
}

// Debounce utility
const useDebounce = (value: any, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value)

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value)
        }, delay)

        return () => clearTimeout(handler)
    }, [value, delay])

    return debouncedValue
}

// Priority loading - load most important data first
const GRANULARITY_PRIORITY = ['MON', 'QRT', 'ANL'] as const
type Granularity = (typeof GRANULARITY_PRIORITY)[number]

export const useOptimizedKpiData = ({
    filters,
    tabGroup,
    debounceDelay = 500,
}: UseOptimizedKpiDataProps): OptimizedKpiData => {
    const debouncedFilters = useDebounce(filters, debounceDelay)

    const [siteData, setSiteData] = useState<GlobalVisionKpiTableModel[]>([])
    const [segmentData, setSegmentData] = useState<GlobalVisionKpiTableModel[]>([])
    const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})
    const [error, setError] = useState<string>()

    // Cache keys
    const cacheKeyBase = useMemo(
        () => ({
            sites: debouncedFilters?.siteList ?? [],
            kpiGroup: tabGroup !== undefined && tabGroup.name !== 'General' ? tabGroup.name : '',
            page: PageNames.HOME_VIEW.NAME,
            businessSegment: debouncedFilters?.businessSegment?.length
                ? debouncedFilters.businessSegment[0].externalId
                : undefined,
        }),
        [debouncedFilters, tabGroup]
    )

    // Cached data storage
    const [cachedSiteData, setCachedSiteData] = useState<Record<Granularity, GlobalVisionKpiTableModel[] | null>>({
        MON: null,
        QRT: null,
        ANL: null,
    })

    const [cachedSegmentData, setCachedSegmentData] = useState<Record<Granularity, GlobalVisionKpiTableModel[] | null>>(
        {
            MON: null,
            QRT: null,
            ANL: null,
        }
    )

    // Load cache on filter change
    useEffect(() => {
        if (!debouncedFilters?.siteList?.length) return

        GRANULARITY_PRIORITY.forEach((granularity) => {
            // Site data cache
            const siteCache = getKpiFromCache({ ...cacheKeyBase, granularity })
            if (siteCache) {
                setCachedSiteData((prev) => ({ ...prev, [granularity]: siteCache }))
            }

            // Segment data cache
            const segmentCacheKey = {
                ...cacheKeyBase,
                businessSegment: debouncedFilters?.businessSegment?.length
                    ? debouncedFilters.businessSegment[0].externalId
                    : 'segment',
            }
            const segmentCache = getKpiFromCache({ ...segmentCacheKey, granularity })
            if (segmentCache) {
                setCachedSegmentData((prev) => ({ ...prev, [granularity]: segmentCache }))
            }
        })
    }, [cacheKeyBase, debouncedFilters])

    // Request configuration
    const baseRequest = useMemo(
        () => ({
            site: debouncedFilters?.siteList ?? [],
            kpiGroup: tabGroup !== undefined && tabGroup.name !== 'General' ? tabGroup.name : '',
            page: PageNames.HOME_VIEW.NAME,
        }),
        [debouncedFilters, tabGroup]
    )

    const segmentRequest = useMemo(
        () => ({
            ...baseRequest,
            businessSegment: debouncedFilters?.businessSegment?.length
                ? debouncedFilters.businessSegment[0].externalId
                : [],
        }),
        [baseRequest, debouncedFilters]
    )

    // Determine what needs to be fetched
    const shouldFetch = useMemo(() => {
        const result: Record<string, boolean> = {}

        GRANULARITY_PRIORITY.forEach((granularity) => {
            result[`site_${granularity}`] = !cachedSiteData[granularity] && baseRequest.site.length > 0
            result[`segment_${granularity}`] = !cachedSegmentData[granularity] && baseRequest.site.length > 0
        })

        return result
    }, [cachedSiteData, cachedSegmentData, baseRequest])

    // Site data hooks with conditional fetching
    const siteHooks = {
        MON: useGlobalVisionKPISite({
            ...baseRequest,
            granularity: 'MON',
            skip: !shouldFetch.site_MON,
        }),
        QRT: useGlobalVisionKPISite({
            ...baseRequest,
            granularity: 'QRT',
            skip: !shouldFetch.site_QRT,
        }),
        ANL: useGlobalVisionKPISite({
            ...baseRequest,
            granularity: 'ANL',
            skip: !shouldFetch.site_ANL,
        }),
    }

    // Segment data hooks with conditional fetching
    const segmentHooks = {
        MON: useGlobalVisionKPISegment({
            ...segmentRequest,
            granularity: 'MON',
            skip: !shouldFetch.segment_MON,
        }),
        QRT: useGlobalVisionKPISegment({
            ...segmentRequest,
            granularity: 'QRT',
            skip: !shouldFetch.segment_QRT,
        }),
        ANL: useGlobalVisionKPISegment({
            ...segmentRequest,
            granularity: 'ANL',
            skip: !shouldFetch.segment_ANL,
        }),
    }

    // Target data hook
    const targetRequest = useMemo(
        () => ({
            skip: !debouncedFilters,
            site: debouncedFilters?.siteList ?? [],
            kpiGroupExternalId: tabGroup !== undefined && tabGroup.name !== 'General' ? tabGroup.externalId : '',
            year: new Date(debouncedFilters?.period).getFullYear(),
        }),
        [debouncedFilters, tabGroup]
    )

    const targetHook = useGlobalVisionKPITarget(targetRequest)

    // Cache fetched data
    useEffect(() => {
        GRANULARITY_PRIORITY.forEach((granularity) => {
            const siteHookData = siteHooks[granularity].dataGlobalVisionKPI
            if (siteHookData?.length > 0) {
                setKpiCache({ ...cacheKeyBase, granularity }, siteHookData)
                setCachedSiteData((prev) => ({ ...prev, [granularity]: siteHookData }))
            }

            const segmentHookData = segmentHooks[granularity].dataGlobalVisionKPI
            if (segmentHookData?.length > 0) {
                const segmentCacheKey = {
                    ...cacheKeyBase,
                    businessSegment: debouncedFilters?.businessSegment?.length
                        ? debouncedFilters.businessSegment[0].externalId
                        : 'segment',
                }
                setKpiCache({ ...segmentCacheKey, granularity }, segmentHookData)
                setCachedSegmentData((prev) => ({ ...prev, [granularity]: segmentHookData }))
            }
        })
    }, [
        siteHooks.MON.dataGlobalVisionKPI,
        siteHooks.QRT.dataGlobalVisionKPI,
        siteHooks.ANL.dataGlobalVisionKPI,
        segmentHooks.MON.dataGlobalVisionKPI,
        segmentHooks.QRT.dataGlobalVisionKPI,
        segmentHooks.ANL.dataGlobalVisionKPI,
        cacheKeyBase,
        debouncedFilters,
    ])

    // Merge site data
    useEffect(() => {
        const monthData = cachedSiteData.MON || siteHooks.MON.dataGlobalVisionKPI
        const quarterData = cachedSiteData.QRT || siteHooks.QRT.dataGlobalVisionKPI
        const annualData = cachedSiteData.ANL || siteHooks.ANL.dataGlobalVisionKPI

        if (monthData?.length > 0 && quarterData?.length > 0 && annualData?.length > 0) {
            try {
                const allIds = new Set<string>()
                monthData.forEach((item) => allIds.add(item.kpi.externalId))
                quarterData.forEach((item) => allIds.add(item.kpi.externalId))
                annualData.forEach((item) => allIds.add(item.kpi.externalId))

                const merged = mergeSiteValues(
                    allIds,
                    monthData,
                    quarterData,
                    annualData,
                    debouncedFilters?.siteList?.length || 0
                )
                const result = merged.sort((a, b) => a.kpi.order - b.kpi.order)
                setSiteData(result)
                setError(undefined)
            } catch (err) {
                setError('Error merging site data')
                console.error('Error merging site data:', err)
            }
        }
    }, [
        cachedSiteData,
        siteHooks.MON.dataGlobalVisionKPI,
        siteHooks.QRT.dataGlobalVisionKPI,
        siteHooks.ANL.dataGlobalVisionKPI,
        debouncedFilters,
    ])

    // Merge segment data
    useEffect(() => {
        const monthData = cachedSegmentData.MON || segmentHooks.MON.dataGlobalVisionKPI
        const quarterData = cachedSegmentData.QRT || segmentHooks.QRT.dataGlobalVisionKPI
        const annualData = cachedSegmentData.ANL || segmentHooks.ANL.dataGlobalVisionKPI

        if (monthData?.length > 0 && quarterData?.length > 0 && annualData?.length > 0) {
            try {
                const allIds = new Set<string>()
                monthData.forEach((item) => allIds.add(item.kpi.externalId))
                quarterData.forEach((item) => allIds.add(item.kpi.externalId))
                annualData.forEach((item) => allIds.add(item.kpi.externalId))

                const merged = mergeSiteValues(
                    allIds,
                    monthData,
                    quarterData,
                    annualData,
                    debouncedFilters?.siteList?.length || 0
                )
                const result = merged.sort((a, b) => a.kpi.order - b.kpi.order)
                setSegmentData(result)
                setError(undefined)
            } catch (err) {
                setError('Error merging segment data')
                console.error('Error merging segment data:', err)
            }
        }
    }, [
        cachedSegmentData,
        segmentHooks.MON.dataGlobalVisionKPI,
        segmentHooks.QRT.dataGlobalVisionKPI,
        segmentHooks.ANL.dataGlobalVisionKPI,
        debouncedFilters,
    ])

    // Calculate loading state
    const loading = useMemo(() => {
        const siteLoading = GRANULARITY_PRIORITY.some(
            (granularity) => shouldFetch[`site_${granularity}`] && siteHooks[granularity].loadingGlobalVisionKPI
        )

        const segmentLoading = GRANULARITY_PRIORITY.some(
            (granularity) => shouldFetch[`segment_${granularity}`] && segmentHooks[granularity].loadingGlobalVisionKPI
        )

        return siteLoading || segmentLoading || targetHook.loadingGlobalVisionKPITarget
    }, [shouldFetch, siteHooks, segmentHooks, targetHook.loadingGlobalVisionKPITarget])

    // Refetch function
    const refetch = useCallback(() => {
        // Clear cache for current filters
        GRANULARITY_PRIORITY.forEach((granularity) => {
            setCachedSiteData((prev) => ({ ...prev, [granularity]: null }))
            setCachedSegmentData((prev) => ({ ...prev, [granularity]: null }))
        })

        // Refetch all data
        Object.values(siteHooks).forEach((hook) => hook.refetchGlobalVisionKPI())
        Object.values(segmentHooks).forEach((hook) => hook.refetchGlobalVisionKPI())
        targetHook.refetchGlobalVisionKPITarget()
    }, [siteHooks, segmentHooks, targetHook])

    return {
        siteData,
        segmentData,
        targetData: targetHook.dataGlobalVisionKPITarget,
        loading,
        error,
        refetch,
    }
}
