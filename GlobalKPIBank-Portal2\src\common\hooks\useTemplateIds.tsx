import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { Template } from '../models/template'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface TemplateIdQueryRequest {
    externalIds?: string[]
    title?: string
    status?: string
    assignedTo?: string
    start?: number
    end?: number
    nextPage?: string
    retriveTemplateItems?: boolean
    includeArchived?: boolean
    first?: number
}

const buildTemplateIdQuery = (request: TemplateIdQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.externalIds?.length) {
        filter.push(`{ externalId: { in: [${request.externalIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.title && request.title != '') {
        filter.push(`{ title: { prefix: "${request.title}" }}`)
    }

    if (request.status && request.status != '') {
        filter.push(`{ status: { eq: "${request.status}" }}`)
    }

    if (request.assignedTo && request.assignedTo != '') {
        filter.push(`{ assignedTo: { eq: "${request.assignedTo}" }}`)
    }

    if (request.start) {
        filter.push(`{ createdTime: { gte: ${request.start} }}`)
    }

    if (request.end) {
        filter.push(`{ createdTime: { lte: ${request.end} }}`)
    }

    if (!request.includeArchived) {
        filter.push(`{ isArchived: { isNull: true } }`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    if (!request.first) {
        request.first = 100
    }

    const query = `
        query GetTemplateIds {
            listTemplate(
                filter: ${queryFilter},
                first: ${request.first},
                after: ${request.nextPage ? `"${request.nextPage}"` : 'null'},
                sort: { title: ASC })
            {
                items {
                    externalId
                    space
                }
                pageInfo {
                    hasPreviousPage
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    return query
}

export const useTemplateIds = (request: TemplateIdQueryRequest) => {
    const query = buildTemplateIdQuery(request)

    const {
        data: fdmData,
        refetch,
        pageInfo,
    } = useGraphqlQuery<Template>(gql(query), 'listTemplate', { fetchPolicy: 'no-cache' })

    const [resultData, setResultData] = useState<{ data: string[]; loading: boolean; pageInfo: PageInfo }>({
        data: [],
        loading: true,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false, pageInfo })
        } else {
            setResultData({ data: fdmData.map((t) => t.externalId), loading: false, pageInfo })
        }
    }, [fdmData, pageInfo])

    return {
        loadingTemplate: resultData.loading,
        templateIds: resultData.data,
        refetchTemplate: refetch,
        pageInfo: resultData.pageInfo,
    }
}
