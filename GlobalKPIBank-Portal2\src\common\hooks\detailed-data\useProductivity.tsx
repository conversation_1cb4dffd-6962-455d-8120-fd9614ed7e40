import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import { ProductivityKpi } from '@/common/models/productivity'
import { ProductivityTableView } from '@/common/models/productivityTable'
import dayjs from 'dayjs'

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let monthQ: string = ''
    let yearQ: string = ''
    const fixedFilters: string = `
        {projectStatus: {in: ["Committed", "Closed", "Placeholder"]}},
        {contributionType: {in: ["Cost Reduction YOY", "M&A CR/Rev", "Revenue Optimization"]}},
        {not:{projectType: {eq: "Strategic Growth"}}},
        {functionCategory: {eq: "Manufacturing"}}
    `
    let limitQ: string = ''
    let cursorQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{reportingSite: {externalId: {in: [${sites}]}}},`
    }

    if (request && request.kpiFilters.date !== '') {
        const date = dayjs(request.kpiFilters.date)

        const monthName = date.format('MMM').toLowerCase()
        const completeName = monthName.concat('Amount')
        monthQ = `{${completeName}: {gt: 0}},`

        const year = Number(date.format('YYYY'))
        yearQ = `{year:{eq: ${year}}}`
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listProductivity (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${yearQ} ${monthQ} ${fixedFilters}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                reportingSite {
                    externalId
                    name
                }
                projectName
                projectNumber
                projectStatus
                year
                contributionType
                functionCategory
                projectType
                annualAmount
                janAmount
                febAmount
                marAmount
                aprAmount
                mayAmount
                junAmount
                julAmount
                augAmount
                sepAmount
                octAmount
                novAmount
                decAmount
            }
        }
    }`
}

const mapProductivity = (data: ProductivityKpi[]): ProductivityTableView[] => {
    const mappedResult: ProductivityTableView[] = []

    if (data && data.length > 0) {
        data.map((item) => {
            const result: ProductivityTableView = {
                siteName: item.reportingSite?.name ?? '',
                projectName: item.projectName ?? '',
                projectNumber: item.projectNumber ?? '',
                projectStatus: item.projectStatus ?? '',
                year: item.year,
                contributionType: item.contributionType ?? '',
                function: item.functionCategory ?? '',
                projectType: item.projectType ?? '',
                annualAmount: item.annualAmount,
                janAmount: item.janAmount,
                febAmount: item.febAmount,
                marAmount: item.marAmount,
                aprAmount: item.aprAmount,
                mayAmount: item.mayAmount,
                junAmount: item.junAmount,
                julAmount: item.julAmount,
                augAmount: item.augAmount,
                sepAmount: item.sepAmount,
                octAmount: item.octAmount,
                novAmount: item.novAmount,
                decAmount: item.decAmount,
                businessSegment: item.businessSegment?.description ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useProductivity = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<any>(gql(query), 'listProductivity', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'executiveView',
        },
    })

    const [resultData, setResultData] = useState<{ data: any[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapProductivity(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapProductivity(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
