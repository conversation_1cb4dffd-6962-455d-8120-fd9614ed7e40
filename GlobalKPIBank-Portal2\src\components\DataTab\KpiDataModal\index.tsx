import { Box } from '@mui/material'
import { ModalWrapper } from '../../Modal/ModalWrapper'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { KpiDataChart } from '../KpiDataChart'
import { VisionKpiAggregation } from '@/common/models/visionKpiAggregation'
import { translate } from '@celanese/celanese-sdk'
import { HomeViewTable } from '../../../components/Home/HomeViewTable'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'

interface GlobalViewModalProps {
    data: VisionKpiAggregation[]
    open: boolean
    handleClose: () => void
    selectedKPI: string
    target?: GlobalVisionKpiTarget[]
    range?: number
}

export const KpiDataModal: React.FC<GlobalViewModalProps> = ({
    open,
    handleClose,
    data,
    selectedKPI,
    target,
    range,
}) => {
    const { selectedKpiData } = useGlobalKpiContext()

    return (
        <ModalWrapper
            openModal={open}
            closeModal={handleClose}
            title={
                translate('MODAL.EXPAND_CHART') +
                selectedKpiData.name +
                ' (' +
                data?.length +
                translate('MODAL.SITES_SELECTED') +
                ')'
            }
            content={
                <Box
                    sx={{
                        width: '80vw',
                        minWidth: '1000px',
                        height: '90vh',
                        padding: '0px 20px',
                        fontFamily: 'Roboto',
                        overflowY: 'auto',
                        margin: '0px 10px',
                        display: 'block',
                    }}
                >
                    <Box
                        sx={{
                            height: '80%',
                            display: 'flex',
                        }}
                    >
                        <KpiDataChart data={data} selectedKpi={selectedKPI} range={range} target={target} />
                    </Box>
                    <Box>
                        <HomeViewTable data={data} target={target} isInModal={true} />
                    </Box>
                </Box>
            }
        />
    )
}

export default KpiDataModal
