{"name": "global-kpi-app", "version": "0.1.0", "private": true, "scripts": {"prepare": "cd .. && husky install GlobalKPIBank-Portal2/.husky", "dev": "next dev -p 4900", "build": "next build", "build:dev": "env-cmd -f .env.dev next build", "build:qa": "env-cmd -f .env.qa next build", "build:prod": "env-cmd -f .env.prod next build", "start": "node_modules/next/dist/bin/next start", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "check-lint": "eslint . --ext ts --ext tsx --ext js", "format": "prettier --write .", "test-all": "npm run check-format && npm run check-lint && npm run check-types", "lint": "next lint", "test": "jest --watch --passWithNoTests --coverage", "test:ci": "jest --ci --passWithNoTests --coverage"}, "dependencies": {"@apollo/client": "^3.7.14", "@azure/msal-browser": "^3.17.0", "@azure/msal-react": "^2.0.19", "@celanese/celanese-sdk": "1.5.1", "@celanese/contextualization-lib": "1.14.11", "@celanese/ui-lib": "^2.4.1", "@cognite/sdk": "^8.3.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.1.1", "@hookform/resolvers": "^3.3.2", "@loadable/component": "^5.16.4", "@material-symbols/font-400": "^0.23.0", "@mui/base": "5.0.0-beta.18", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.16.14", "@mui/x-data-grid-pro": "^7.24.0", "@mui/x-date-pickers-pro": "^7.24.0", "@mui/x-tree-view": "^7.24.0", "@reduxjs/toolkit": "^2.2.5", "apexcharts": "3.49.1", "dayjs": "^1.11.13", "dslb-app": "file:", "env-cmd": "^10.1.0", "filefy": "^0.1.11", "global-kpi-app": "file:", "graphql": "^16.6.0", "material-ui-popup-state": "^5.1.2", "next": "^13.5.4", "react": "^18.3.1", "react-apexcharts": "1.4.1", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-icons": "^4.11.0", "react-infinite-scroll-component": "^6.1.0", "react-intl": "^6.6.8", "react-loadable-visibility": "^3.0.2", "react-redux": "^9.1.2", "react-virtuoso": "^4.7.11", "styled-components": "^6.1.1", "xlsx-js-style": "^1.2.0", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "6.1.2", "@testing-library/react": "14.0.0", "@testing-library/user-event": "14.4.3", "@types/jest": "^29.5.5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.16", "encoding": "^0.1.13", "eslint": "^8", "eslint-config-next": "13.5.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.14", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^9.1.7", "jest": "29.6.4", "jest-environment-jsdom": "29.6.4", "postcss": "^8.4.31", "prettier": "2.8.8", "typescript": "^5"}}