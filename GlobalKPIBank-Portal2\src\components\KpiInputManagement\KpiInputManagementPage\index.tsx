import { Box, Typography } from '@mui/material'
import { translate } from '@celanese/celanese-sdk'
import { KpiInputManagementFilters } from '../KpiInputManagementFilters'

const styles = {
    tabWrapper: {
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: '8px',
        width: 'auto',
        height: 'calc(100% - 44px)',
        padding: '30px',
    },
}

export const KpiInputManagementPage = () => {
    return (
        <div style={{ height: '100%', overflow: 'hidden' }}>
            <Typography variant="h3" sx={{ color: '#083D5B', marginBottom: '1rem', fontWeight: 'bold' }}>
                {translate('KPI_INPUT_MANAGEMENT.TITLE')}
            </Typography>
            <Box sx={styles.tabWrapper}>
                <KpiInputManagementFilters />
            </Box>
        </div>
    )
}
