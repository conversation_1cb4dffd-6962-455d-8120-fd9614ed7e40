import { ExternalEntity } from '.'
import { Dayjs } from 'dayjs'

export interface Equipment extends ExternalEntity {
    name: string
    description: string
    parent: Equipment
    actualInstallationDate: Dayjs
    actualPurchaseDate: Dayjs
    actualStartupDate: Dayjs
    catalogProfile: string
    changedBy: string
    changedDate: Dayjs
    companyCode: string
    costCenter: string
    createdBy: string
    createdDate: Dayjs
    equipmentClass: string
    equipmentClassDescription: string
    equipmentSystemStatus: string
    functionalLocationDescription: string
    location: string
    maintenancePlanningPlant: string
    maintenancePlant: string
    manufacturerCompany: string
    manufacturerSerialNumber: string
    modelNumber: string
    number: string
    plannerGroup: string
    plantSection: string
    price: number
    priceCurrencyKey: string
    room: string
    sortField: string
    techIdNumber: string
    techObjectAbcIndicator: string
    techObjectType: string
    warrantyEndDate: Dayjs
    workCenter: string
}
