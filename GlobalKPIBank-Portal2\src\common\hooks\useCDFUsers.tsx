import { gql } from '@apollo/client'
import { useGraphqlQuery } from './useGraphqlQuery'

export interface ChecklistQueryRequest {
    email?: string
}

export interface CDFUser {
    externalId: string
    email: string
}

const buildChecklistQuery = (request: ChecklistQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.email) {
        filter.push(`{ email: { eq: "${request.email}" }}`)
    }

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
    query GetCDF_User {
        listCDF_User(
            filter: ${queryFilter}
        , first: 1000 ) {
          items {
            externalId
            email
          }
        }
    }
    `

    return query
}

export const useCDFUser = (request: ChecklistQueryRequest) => {
    const query = buildChecklistQuery(request)

    const { data: fdmData } = useGraphqlQuery<CDFUser>(gql(query), 'listCDF_User', {})

    return {
        cdfUser: fdmData,
    }
}
