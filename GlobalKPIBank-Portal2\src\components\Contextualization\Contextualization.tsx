'use client'
import { PropsWithChildren } from 'react'

import { ContextualizationProvider } from '@celanese/contextualization-lib'

import { AppSpeedDial } from '../SpeedDial/SpeedDial'
import { useAuthToken } from '@/common/hooks'

export const Contextualization = ({ children }: PropsWithChildren) => {
    const { getAuthToken } = useAuthToken()

    return (
        <ContextualizationProvider turnOffHighlightByDefault getAuthToken={getAuthToken}>
            <AppSpeedDial />
            <div className="contextualizableArea">{children}</div>
        </ContextualizationProvider>
    )
}
