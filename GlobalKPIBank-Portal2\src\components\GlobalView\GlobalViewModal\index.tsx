import { Box, Grid } from '@mui/material'
import GlobalViewChart from '../GlobalViewChart'
import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import { ModalWrapper } from '../../Modal/ModalWrapper'
import './styles.css'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { useEffect, useState } from 'react'

interface GlobalViewModalProps {
    data: GlobalVisionKpiTableModel
    open: boolean
    handleClose: () => void
    target?: GlobalVisionKpiTarget[]
}

export const GlobalViewModal: React.FC<GlobalViewModalProps> = ({ open, handleClose, data, target }) => {
    const [selectedKpi, setSelectedKpi] = useState<string>('')
    useEffect(() => {
        if (data) {
            setSelectedKpi(data.kpi.externalId)
        }
    }, [data])
    return (
        <ModalWrapper
            openModal={open}
            closeModal={handleClose}
            title={data.kpi.name}
            content={
                <Box
                    sx={{
                        width: '1000px',
                        height: '80vh',
                        maxHeight: '700px',
                        padding: '0px 20px',
                        fontFamily: 'Roboto',
                        overflowY: data.data.length < 5 ? 'auto' : 'scroll',
                        margin: '0px 10px',
                    }}
                >
                    <Grid container spacing={4}>
                        {data.data.map((chartData, index) => (
                            <Grid key={index} item xs={data.data.length > 1 ? 6 : 12}>
                                <GlobalViewChart
                                    data={chartData}
                                    range={
                                        target &&
                                        (target.length == 0
                                            ? 0
                                            : target.find(
                                                  (item) =>
                                                      item.refReportingSite.externalId ===
                                                      chartData.refReportingSite.externalId
                                              )?.value)
                                    }
                                    selectedKpi={selectedKpi}
                                />
                            </Grid>
                        ))}
                    </Grid>
                </Box>
            }
        />
    )
}

export default GlobalViewModal
