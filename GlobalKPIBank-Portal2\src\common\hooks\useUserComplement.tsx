import { gql } from '@apollo/client'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { UserComplement, UserRoleSite } from '../models/userComplement'
import { ArrayEntity, Location } from '../models'
import { useEffect, useState } from 'react'
import { Unit } from '../models/unit'

export interface UserComplementRequest {
    email?: string
    userExternalIds?: string[]
    nextPage?: string
}

const buildUserComplementQuery = (request: UserComplementRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.email) {
        filter.push(`{ userAzureAttribute: { user: { email: { eq: "${request.email}" }}}}`)
    }

    if (request.userExternalIds?.length) {
        filter.push(
            `{
              externalId: {
                  in: [${request.userExternalIds.map((e) => `"UserComplement_${e}"`).join(',')}]
              }
          }`
        )
    }

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
    query GetUserComplement {
      listUserComplement(filter: ${queryFilter}, first: 1000, after: ${
        request.nextPage ? `"${request.nextPage}"` : 'null'
    }) {
        items {
          externalId
          space
          userAzureAttribute {
            user {
              externalId
              space
              email
              displayName
              firstName
              lastName
            }
          }
          shiftConfiguration {
            externalId
          }
          userRoleSite {
            items {
              role {
                externalId
                name
              }
            }
          }
          reportingUnits {
            items {
              externalId
              description
            }
          }
          reportingLocations {
            items {
              externalId
              description
            }
          }
        }
        pageInfo {
          hasPreviousPage
          hasNextPage
          startCursor
          endCursor
        }
      }
    }
    `

    return query
}

export const useUserComplement = (request: UserComplementRequest) => {
    const query = buildUserComplementQuery(request)

    const { data: fdmData, pageInfo } = useGraphqlQuery<UserComplement>(gql(query), 'listUserComplement', {})

    const [resultData, setResultData] = useState<{
        data: UserComplement[]
        loading: boolean
        pagging: boolean
        pageInfo: PageInfo
    }>({
        data: [],
        loading: true,
        pagging: false,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false, pageInfo, pagging: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayUserRoleSiteItemEntity = d.userRoleSite as any as ArrayEntity<UserRoleSite>
                const arrayUnitsItemEntity = d.reportingUnits as any as ArrayEntity<Unit>
                const arrayLocationsItemEntity = d.reportingLocations as any as ArrayEntity<Location>

                const userRoleSites = d.userRoleSite as any as ArrayEntity<UserRoleSite>

                const userComplement: UserComplement = {
                    ...d,
                    userRoleSite: userRoleSites.items,
                    reportingUnits: arrayUnitsItemEntity.items,
                    reportingLocations: arrayLocationsItemEntity.items,
                }

                return userComplement
            })

            setResultData({ data: fdmDataParsed, loading: false, pageInfo, pagging: Boolean(request.nextPage) })
        }
    }, [fdmData, pageInfo])

    return {
        loading: resultData.loading,
        usersComplement: resultData.data,
        pageInfoUC: resultData.pageInfo,
        pagging: resultData.pagging,
    }
}
