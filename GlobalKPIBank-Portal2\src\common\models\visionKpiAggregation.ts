import { ExternalEntity } from '.'
import { BusinessSegment } from './business-segment'
import { GlobalVisionKpi } from './globalVisionKpi'
import { Site } from './site'

export interface VisionKpiAggregation extends ExternalEntity {
    refGlobalVisionKPI: GlobalVisionKpi
    refReportingSite: Site
    refBusinessSegment: BusinessSegment
    january: number
    february: number
    march: number
    april: number
    may: number
    june: number
    july: number
    august: number
    september: number
    october: number
    november: number
    december: number
    ytd: number
    lastYear: number
    lastTwoYear: number
    actualMonth: number
    lastMonth: number
    lastTwoMonth: number
    lastThreeMonth: number
    lastUpdatedTime: Date
    createdTime: Date
}
