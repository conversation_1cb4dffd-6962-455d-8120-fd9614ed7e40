import React from 'react'
import * as Styled from './styles'
import { useRouter } from 'next/navigation'
import { translate } from '@celanese/celanese-sdk'
import { enviroment } from '@/common/configurations/enviroment'

export default function ErrorScreen() {
    const router = useRouter()
    const redirectUser = () => {
        router.replace(`${enviroment.userManagementPortal}/user-settings/new-request`)
    }

    return (
        <Styled.ContainerErrorScreen>
            <Styled.ErrorIcon />
            <Styled.HeadingPrimary>{translate('ERROR_SCREEN.NO_ACCESS')}</Styled.HeadingPrimary>
            <br />
            <Styled.Secondary>
                {translate('ERROR_SCREEN.REQUEST_ACCESS')}{' '}
                <Styled.Link onClick={redirectUser}>{translate('ERROR_SCREEN.CLICK_HERE')}</Styled.Link>
            </Styled.Secondary>
        </Styled.ContainerErrorScreen>
    )
}
