import { useTheme, Box, DialogActions, DialogTitle, type SxProps, type Theme, IconButton, Modal } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import React from 'react'

interface ModalPopoverProps {
    title?: string
    content: React.ReactNode
    openModal: boolean
    closeModal: () => void
    sxProps?: SxProps<Theme>
    sxPropsTitle?: SxProps<Theme>
}
export const ModalWrapper = ({ title, content, openModal, closeModal, sxProps, sxPropsTitle }: ModalPopoverProps) => {
    const theme = useTheme()

    return (
        <Modal
            open={openModal}
            onClose={closeModal}
            sx={{ ...sxProps, alignItems: 'center', justifyContent: 'center', display: 'flex' }}
            hideBackdrop={false}
        >
            <Box
                sx={{
                    bgcolor: 'background.paper',
                    borderRadius: '8px',
                }}
            >
                {title && (
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}
                    >
                        <DialogTitle
                            sx={
                                sxPropsTitle
                                    ? { ...sxPropsTitle }
                                    : {
                                          color: theme.palette.grey[600],
                                          fontSize: '19px',
                                          marginRight: 'auto',
                                      }
                            }
                        >
                            {title}
                        </DialogTitle>
                        <DialogActions>
                            <IconButton onClick={closeModal} sx={{ ml: 'auto' }}>
                                <CloseIcon />
                            </IconButton>
                        </DialogActions>
                    </Box>
                )}
                {content}
            </Box>
        </Modal>
    )
}
