FROM node:20-slim AS build

ARG ARG_ENVIRONMENT
ENV ENVIRONMENT=$ARG_ENVIRONMENT

WORKDIR /app
COPY package*.json ./
COPY .npmrc ./
RUN npm install --legacy-peer-deps
COPY . .

RUN npm run build:${ENVIRONMENT}

FROM node:18-slim AS runtime

WORKDIR /app
COPY ./package*.json ./
COPY ./.npmrc ./
RUN npm i
COPY --from=build /app/.next ./.next
COPY --from=build /app/public ./public

EXPOSE 3000
ENV PORT 3000

USER node
CMD ["npm", "start"]