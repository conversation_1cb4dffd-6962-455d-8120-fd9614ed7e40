import { useEffect, useState } from 'react'
import { useQueryResultsFunction } from '../general-functions/useQueryResultsFunction'
import { EntityType, GetSpace } from '@/common/utils/space-util'
import { getLocalUserSite } from '@/common/utils'

export interface TemplateConfigQueryRequest {
    units?: string[]
}

interface TemplateConfigQueryUnit {
    unit: string
}

const buildTemplateConfigUnitQuery = (request: TemplateConfigQueryUnit): any => {
    const query = {
        template: {
            nodes: {
                filter: {
                    and: [
                        {
                            equals: {
                                property: ['node', 'space'],
                                value: GetSpace(EntityType.CKM, getLocalUserSite()?.siteCode),
                            },
                        },
                        request.unit && {
                            in: {
                                property: [
                                    'CKM-COR-ALL-DML',
                                    'ChecklistTemplateConfig/74c8110d31a406',
                                    'reportingUnit',
                                ],
                                values: [{ externalId: request.unit, space: 'REF-COR-ALL-DAT' }],
                            },
                        },
                    ].filter(Boolean),
                },
                chainTo: 'destination',
                direction: 'outwards',
            },
            limit: 10000,
        },
    }

    return query
}

export const useTemplateConfigUnit = (request: TemplateConfigQueryRequest) => {
    const select = {
        template: {
            limit: 10000,
        },
    }

    const cursors = {
        template: null,
    }

    const { getAllResults: getAllData } = useQueryResultsFunction<any>(select, cursors)

    const [unitChecklists, setUnitChecklists] = useState<any[]>([])

    useEffect(() => {
        setUnitChecklists([])

        request.units &&
            request.units.map((unit) => {
                getAllData(buildTemplateConfigUnitQuery({ unit: unit })).then((res) => {
                    setUnitChecklists((prev) => [
                        ...prev,
                        {
                            unit: unit,
                            checklists: res?.items.template.map((item: any) => item.externalId.replace('CTC-', '')),
                        },
                    ])
                })
            })
    }, [request])

    return {
        loading: true,
        templateConfigs: unitChecklists,
    }
}
