import { LoaderCircular, LoaderCircularTable } from '../../../components/Loader'
import {
    Box,
    Button,
    CircularProgress,
    Paper,
    Popper,
    TableCell,
    TableContainer,
    TableRow,
    Tooltip,
    Typography,
} from '@mui/material'
import { DataModelView } from '@/common/models/dataModelView'
import { StripedCell } from './styles'
import { translate } from '@celanese/celanese-sdk'
import React, { useEffect, useState } from 'react'
import { TableVirtuoso } from 'react-virtuoso'
import { useGenericDataViewHook } from '@/common/hooks/detailed-data/useGenericDataView'
import { formatVariableName } from '@/common/utils/stringFormat'
import { numberFormat } from '@/common/utils/numberFormat'
import { ClnDatePicker, ClnSelect } from '@celanese/ui-lib'
import dayjs from 'dayjs'
import { useForm } from 'react-hook-form'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { KpisDataViews } from '@/common/utils/kpis-data-table'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import { ClickAwayListener } from '@mui/base/ClickAwayListener'
import { exportExcel } from '@/common/utils/exportExcel'
import { usePathname } from 'next/navigation'

export const KpiDataTable = () => {
    const { requestData, setRequestData, selectedKpi, selectedKpiData, currentTab } = useGlobalKpiContext()
    const {
        kpiDataModelView: result,
        pageInfoDataModelView: page,
        loadingDataModelView,
        refetchDataModelView,
        loadMoreExport,
    } = useGenericDataViewHook(requestData)
    const [headerCells, setHeaderCells] = useState<string[]>([])
    const [headerCount, setHeaderCount] = useState<number>(0)
    const kpisModels = Object.values(KpisDataViews)
    const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null)
    const [openExport, setOpenExport] = useState<boolean>(false)
    const pathName = usePathname().split('/')[1]
    const [isMonthPickerOpen, setIsMonthPickerOpen] = useState(false)
    const kpiName = selectedKpiData.name
    const kpiTabs = (kpisModels.find((kpi) => kpi.NAME === kpiName)?.TAB_NAME || []).filter(
        (tab) => tab !== 'Definition'
    )
    const [selectedView, setSelectedView] = useState<string>(kpiTabs.length > 0 ? kpiTabs[0] : '')

    const [exportRequestData, setExportRequestData] = useState<DataModelView>({
        kpiFilters: {
            kpiName: '',
            date: dayjs().format('YYYY-MM-DD'),
            views: [''],
            info: '',
            currentTab: requestData.kpiFilters.currentTab,
            site: '',
            businessSegment: '',
        },
        exportFilters: {
            isExport: false,
            range: 1,
        },
        cursor: '',
        limit: '1000',
    })

    const { kpiDataModelView: exportResult, loadingDataModelView: exportLoading } =
        useGenericDataViewHook(exportRequestData)
    const { setValue: handleSetValue } = useForm<DataModelView>({
        defaultValues: {
            kpiFilters: {
                date: '',
                views: [''],
                info: '',
                currentTab: undefined,
                site: '',
                businessSegment: '',
            },
            exportFilters: {
                isExport: false,
                range: 0,
            },
            cursor: '',
            limit: '10',
        },
    })

    useEffect(() => {
        setRequestData((prevState) => ({
            ...prevState,
            kpiFilters: {
                ...prevState.kpiFilters,
                currentTab: 0,
            },
        }))
    }, [])

    useEffect(() => {
        if (requestData) {
            const currentTabIndex = kpiTabs.indexOf(selectedView)
            handleSetValue('kpiFilters', {
                ...requestData.kpiFilters,
                currentTab: currentTabIndex,
                kpiName: requestData.kpiFilters.kpiName,
                info: requestData.kpiFilters.info,
                views: requestData.kpiFilters.views,
            })
            handleSetValue('limit', requestData.limit)
            handleSetValue('exportFilters.isExport', requestData.exportFilters.isExport)
        }
    }, [requestData, handleSetValue])

    const handleExportPopover = (event: React.MouseEvent<HTMLElement>) => {
        setOpenExport((prevState) => !prevState)
        setAnchorEl(event.currentTarget)
    }
    const handleClosePopover = () => {
        setOpenExport(false)
    }

    const handleExport = (range: number) => {
        setOpenExport(false)
        setExportRequestData(() => ({
            kpiFilters: {
                ...requestData.kpiFilters,
                date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            },
            cursor: '',
            limit: '1000',
            exportFilters: {
                isExport: true,
                range: range,
            },
        }))
    }
    useEffect(() => {
        setRequestData((prevData) => ({
            ...prevData,
            cursor: '',
        }))

        refetchDataModelView()
    }, [
        requestData.kpiFilters.currentTab,
        requestData.kpiFilters.date,
        currentTab,
        selectedKpi,
        requestData.kpiFilters.refSite,
    ])

    useEffect(() => {
        if (exportResult && exportRequestData.kpiFilters.kpiName !== '' && !exportLoading) {
            exportExcel(exportRequestData, exportResult, kpiTabs)
        }
    }, [exportResult, exportRequestData.kpiFilters.date])

    useEffect(() => {
        if (result && result.length > 0 && page && page.endCursor) {
            const headerCells = Object.keys(result[0]).map((columnName) => formatVariableName(columnName))
            setHeaderCells(headerCells)
            setHeaderCount(headerCells.length)
        }
    }, [result])

    useEffect(() => {
        setHeaderCells([])
        setHeaderCount(0)
    }, [requestData.kpiFilters.date, requestData.kpiFilters.kpiName, requestData.kpiFilters.currentTab])

    useEffect(() => {
        if (!kpiTabs.includes(selectedView)) {
            setSelectedView(kpiTabs.length > 0 ? kpiTabs[0] : '')
        }
    }, [kpiTabs, selectedView])

    useEffect(() => {
        if (kpiTabs.length > 0 && !kpiTabs.includes(selectedView)) {
            setSelectedView(kpiTabs[0])
        }
    }, [kpiTabs])

    const LoadingFooter = () => {
        return (
            <TableRow>
                <TableCell align="center" colSpan={headerCount}>
                    <LoaderCircularTable
                        sxProps={{ width: '100%', display: 'flex', justifyContent: 'center !important' }}
                    />
                </TableCell>
            </TableRow>
        )
    }
    return (
        <Box
            sx={{
                ...(pathName === 'site-view'
                    ? {
                          height: '100%',
                      }
                    : {
                          height: '60vh',
                      }),

                overflow: 'hidden',
            }}
        >
            <Box sx={{ pt: '16px', display: 'flex', gap: 2, justifyContent: 'space-between', width: '100%' }}>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-start' }}>
                    <ClnDatePicker
                        key="month-picker"
                        views={['month']}
                        label={translate('COMMONFILTER.MONTH')}
                        defaultValue={dayjs(requestData.kpiFilters.date)}
                        maxDate={dayjs().endOf('month')}
                        size="small"
                        open={isMonthPickerOpen}
                        onOpen={() => {
                            setIsMonthPickerOpen(true)
                        }}
                        onClose={() => setIsMonthPickerOpen(false)}
                        sx={{
                            height: '40px',
                            '.MuiInputBase-root': {
                                minHeight: '32px',
                                padding: '4px 8px',
                                fontSize: '0.875rem',
                            },
                            '.MuiOutlinedInput-root': {
                                minHeight: '32px',
                            },
                            '.MuiButtonBase-root': {
                                padding: '4px',
                            },
                        }}
                        onAccept={(date) => {
                            const referenceYear = dayjs(requestData.kpiFilters.date).year()

                            const newDate = dayjs()
                                .year(referenceYear)
                                .month(date.month())
                                .startOf('month')
                                .format('YYYY-MM-DD')

                            setRequestData((prevData) => ({
                                ...prevData,
                                cursor: '',
                                kpiFilters: {
                                    ...prevData.kpiFilters,
                                    date: newDate,
                                },
                            }))

                            setTimeout(() => setIsMonthPickerOpen(false), 100)
                        }}
                        slotProps={{
                            textField: {
                                onClick: () => setIsMonthPickerOpen(true),
                            },
                            field: {
                                readOnly: true,
                            },
                        }}
                    />
                    <ClnSelect
                        label={translate('COMMONFILTER.KPI_SOURCES_LIST')}
                        options={kpiTabs}
                        value={selectedView === '' ? kpiTabs[0] : selectedView}
                        size="small"
                        sxProps={{ width: '180px' }}
                        onChange={(value) => {
                            setSelectedView(value)
                            const currentTabIndex = kpiTabs.indexOf(value)

                            setRequestData((prevData) => ({
                                ...prevData,
                                cursor: '',
                                kpiFilters: {
                                    ...prevData.kpiFilters,
                                    currentTab: currentTabIndex,
                                },
                            }))
                        }}
                    />
                </Box>
                <Button
                    sx={{ width: '120px' }}
                    size="small"
                    startIcon={
                        exportLoading ? <CircularProgress size={20} /> : <FileDownloadOutlinedIcon fontSize="small" />
                    }
                    variant="outlined"
                    onClick={handleExportPopover}
                >
                    {translate('COMMONBUTTON.EXPORT')}
                </Button>
                <Popper
                    open={openExport}
                    anchorEl={anchorEl}
                    placement="bottom"
                    disablePortal
                    sx={{
                        zIndex: 3,
                        margin: '7px 0px !important',
                    }}
                >
                    <ClickAwayListener onClickAway={handleClosePopover}>
                        <Paper sx={{ padding: '3px 0px', width: '100% !important' }}>
                            <Box padding="2px 0px">
                                <Button sx={{ width: '100%' }} onClick={() => handleExport(1)}>
                                    {translate('COMMONBUTTON.EXPORT_OPTIONS.LAST_MONTH')}
                                </Button>
                            </Box>
                            <Box padding="2px 0px">
                                <Button sx={{ width: '100%' }} onClick={() => handleExport(3)}>
                                    {translate('COMMONBUTTON.EXPORT_OPTIONS.THREE_MONTHS')}
                                </Button>
                            </Box>
                            <Box padding="2px 0px">
                                <Button sx={{ width: '100%' }} onClick={() => handleExport(6)}>
                                    {translate('COMMONBUTTON.EXPORT_OPTIONS.SIX_MONTHS')}
                                </Button>
                            </Box>
                            <Box padding="2px 0px">
                                <Button sx={{ width: '100%' }} onClick={() => handleExport(12)}>
                                    {translate('COMMONBUTTON.EXPORT_OPTIONS.CURRENT_YEAR')}
                                </Button>
                            </Box>
                        </Paper>
                    </ClickAwayListener>
                </Popper>
            </Box>

            {result.length === 0 && headerCells.length === 0 && loadingDataModelView ? (
                LoaderCircular()
            ) : (
                <Box sx={{ height: 'calc(100% - 59px)', overflow: 'hidden' }}>
                    <TableContainer
                        sx={{
                            overflow: 'auto',
                            height: '100%',
                        }}
                    >
                        {result.length === 0 && headerCells?.length === 0 && !loadingDataModelView ? (
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontWeight: 'bold',
                                    fontSize: '1.5rem',
                                    textAlign: 'center',
                                    margin: '2rem',
                                }}
                            >
                                {translate('DATA_TAB.NO_DATA')}
                            </Typography>
                        ) : (
                            <TableVirtuoso
                                data={result}
                                endReached={() => {
                                    if (page.endCursor && page.endCursor !== '') {
                                        setRequestData((prevData) => ({
                                            ...prevData,
                                            cursor: page.endCursor,
                                        }))

                                        loadMoreExport()
                                    }
                                }}
                                fixedFooterContent={() => (loadingDataModelView ? <LoadingFooter /> : null)}
                                components={{
                                    Table: ({ style, ...props }) => (
                                        <table {...props} style={{ ...style, width: '100%' }} />
                                    ),
                                    TableFoot: ({ style, ...props }) => (
                                        <tfoot {...props} style={{ ...style, position: 'relative' }} />
                                    ),
                                }}
                                fixedHeaderContent={() => (
                                    <TableRow>
                                        {headerCells.map((columnName, index) => (
                                            <TableCell
                                                key={index}
                                                align="center"
                                                sx={{
                                                    maxWidth: '85px',
                                                    fontWeight: 'bold',
                                                    backgroundColor: '#ffffff',
                                                }}
                                            >
                                                {columnName}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                )}
                                itemContent={(rowIndex: number, row: any) => {
                                    return (
                                        <>
                                            {Object.keys(row).map((key, cellIndex) => (
                                                <StripedCell
                                                    index={rowIndex}
                                                    key={cellIndex}
                                                    align="center"
                                                    sx={{
                                                        maxWidth: '120px',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                    }}
                                                >
                                                    {!row[key] || row[key] === '' ? (
                                                        '--'
                                                    ) : (
                                                        <Tooltip title={row[key]} arrow>
                                                            <span style={{ display: 'inline-block' }}>
                                                                {typeof row[key] === 'number' && key !== 'year'
                                                                    ? numberFormat(row[key])
                                                                    : row[key]}
                                                            </span>
                                                        </Tooltip>
                                                    )}
                                                </StripedCell>
                                            ))}
                                        </>
                                    )
                                }}
                            ></TableVirtuoso>
                        )}
                    </TableContainer>
                </Box>
            )}
        </Box>
    )
}
