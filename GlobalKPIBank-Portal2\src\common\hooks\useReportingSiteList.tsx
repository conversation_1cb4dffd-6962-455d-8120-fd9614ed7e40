import { gql } from '@apollo/client'
import { Site } from '../models/site'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useState, useEffect, useMemo } from 'react'
import { CgnFilter } from '../utils/gqlQueryUtils'
import { Entity } from '../models'

const MANUFACTURING_SITE_TYPE = 'STYPE-MFG'

const GET_SITES_QUERY = gql`
    query getSites($filter: _ListReportingSiteFilter, $cursor: String, $first: Int) {
        listReportingSite(first: $first, filter: $filter, after: $cursor) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                externalId
                space
                name
                description
                siteCode
                siteType (filter: { externalId: { eq: "${MANUFACTURING_SITE_TYPE}" }}) {
                    items {
                        externalId
                    }
                }
                reportingUnits (
                    first: 1000,
                    filter: { isActive: { eq: true } }
                ) {
                    items {
                        externalId
                        businessSegment {
                            externalId
                        }
                    }
                }
            }
        }
    }
`

export const useReportingSiteList = (id?: string[]) => {
    const variables = useMemo(() => {
        const filter: CgnFilter<Site>[] = [{ isActive: { eq: true } }]
        if (id?.length) {
            filter.push({ externalId: { in: id } })
        }
        return {
            filter: { and: filter },
            first: 1_000,
        }
    }, [id])

    const {
        data: sites,
        refetch,
        loading,
    } = useGraphqlQuery<Site>(GET_SITES_QUERY, 'listReportingSite', {
        context: {
            clientName: 'assetHierarchy',
        },
        variables,
    })

    const [resultData, setResultData] = useState<{ data: Site[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (!sites || loading) {
            return setResultData((prevState) => ({ ...prevState, loading: true }))
        }
        const manufacturingSites = sites
            .filter((x) => x.siteType.items.some((type: Entity) => type.externalId === MANUFACTURING_SITE_TYPE))
            .sort((a, b) => a.name.localeCompare(b.name))
            .map(
                (site: any) =>
                    ({
                        ...site,
                        reportingUnits: site.reportingUnits?.items ?? [],
                        businessSegments: [
                            ...new Set(site.reportingUnits?.items.map((u: any) => u.businessSegment?.externalId)),
                        ],
                    } as Site)
            )

        setResultData({ data: manufacturingSites, loading: false })
    }, [sites, loading])

    return useMemo(
        () => ({
            loadingSiteList: resultData.loading,
            refetchSiteList: refetch,
            dataSiteList: resultData.data,
        }),
        [resultData, refetch]
    )
}
