'use client'
import { useMediaQuery } from '@mui/material'

const useResponsiveChipLimit = (): number => {
    const isXXXL = useMediaQuery('(min-width:2560px)')
    const isXXL = useMediaQuery('(min-width:2400px) and (max-width:2559px)')
    const isXLPlus = useMediaQuery('(min-width:2048px) and (max-width:2399px)')
    const isXL = useMediaQuery('(min-width:1920px) and (max-width:2047px)')
    const isLGPlus = useMediaQuery('(min-width:1680px) and (max-width:1919px)')
    const isLG = useMediaQuery('(min-width:1536px) and (max-width:1679px)')
    const isMDPlus = useMediaQuery('(min-width:1440px) and (max-width:1535px)')
    const isMD = useMediaQuery('(min-width:1360px) and (max-width:1439px)')
    const isSM = useMediaQuery('(min-width:1280px) and (max-width:1359px)')

    if (isXXXL) return 12
    if (isXXL) return 11
    if (isXLPlus) return 10
    if (isXL) return 9
    if (isLGPlus) return 8
    if (isLG) return 7
    if (isMDPlus) return 6
    if (isMD) return 5
    if (isSM) return 4

    return 3
}

export default useResponsiveChipLimit
