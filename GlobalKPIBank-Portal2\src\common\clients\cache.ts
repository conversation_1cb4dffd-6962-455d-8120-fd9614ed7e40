const CACHE_KEY = 'GKPI_UM_CACHE'
const CACHE_DURATION = 60 * 60 * 1000 * 2 // 2 hour in milliseconds

type GetCacheRequest = {
    userName: string
}

type SetCacheRequest = GetCacheRequest & {
    data: any
}

type CacheData = SetCacheRequest & {
    timestamp: number
}

export const fetchFromCache = (request: GetCacheRequest): any | undefined => {
    if (typeof window === 'undefined') {
        return undefined
    }

    const cached = sessionStorage.getItem(CACHE_KEY)
    if (!cached) {
        return undefined
    }

    const entry = JSON.parse(cached) as CacheData

    const now = new Date().getTime()
    const validTime = now - entry.timestamp < CACHE_DURATION
    if (entry.userName !== request.userName || !validTime) {
        return undefined
    }

    return entry.data
}

export const setCache = (request: SetCacheRequest) => {
    if (typeof window === 'undefined') {
        return
    }
    const entry: CacheData = {
        timestamp: new Date().getTime(),
        userName: request.userName,
        data: request.data,
    }

    sessionStorage.setItem(CACHE_KEY, JSON.stringify(entry))
}
