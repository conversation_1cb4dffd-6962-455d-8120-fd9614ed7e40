import { useGetKpiGroups } from '@/common/hooks/useGetKpiGroups'
import { useGlobalVisionKPITarget } from '@/common/hooks/useGlobalVisionKPITarget'
import { useReportingSiteList } from '@/common/hooks/useReportingSiteList'
import { translate } from '@celanese/celanese-sdk'
import { Box, Grid, Typography } from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import { KPITargetFilters } from '../KPITargetFilters'
import { Controller, useFieldArray, useForm, Control } from 'react-hook-form'
import { StripedRow } from './styles'
import { ClnButton, ClnSelect, ClnTextField } from '@celanese/ui-lib'
import { ApplyAllSitesModal } from '../ApplyAllSitesModal'
import { KpiTargetMutation, useKpiTargetMutation } from '@/common/hooks/useKpiTargetMutation'
import { Toastr, AlertData } from '../../Toastr'
import LoaderCircular from '../../Loader'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { KpiGroup } from '@/common/models/kpiGroup'
import { useAuthSites } from '@/common/hooks/useAuthSites'

type GlobalVisionKpiTarget = {
    externalId: string
    space: string
    refGlobalVisionKPI: {
        name: string
        externalId: string
        order: number
    }
    refReportingSite: {
        externalId: string
    }
    value?: number
    rangeDirection?: string
    year: number
    allSitesChecked: boolean
}

interface KpiTargetField {
    kpiTargets: GlobalVisionKpiTarget[] | null
}
const COMPONENT_NAME = 'KPITarget'
interface KpiTargetsListProps {
    kpiFields: any[]
    control: Control<KpiTargetField>
    checkPermissionsFromRoutes: (permission: string) => boolean
    setSelectedValue: (value: string | null) => void
    setRowIndex: (index: number | null) => void
    setFieldToUpdate: (field: any) => void
    setIsModalOpen: (isOpen: boolean) => void
}

const KpiTargetsList = ({
    kpiFields,
    control,
    checkPermissionsFromRoutes,
    setSelectedValue,
    setRowIndex,
    setFieldToUpdate,
    setIsModalOpen,
}: KpiTargetsListProps) => {
    return (
        <>
            <Grid container spacing={2} style={{ padding: '1rem', color: 'black' }}>
                <Grid item xs={3}>
                    <h4>{translate('TABLE_COLS.SUB_CATEGORY')}</h4>
                </Grid>
                <Grid item xs={3}>
                    <h4>{translate('TABLE_COLS.RANGE_DIRECTION')}</h4>
                </Grid>
                <Grid item xs={3}>
                    <h4>{translate('TABLE_COLS.TARGET')}</h4>
                </Grid>
                {/* <Grid item xs={3}></Grid> */}
            </Grid>
            {kpiFields.map((kpiRow, index) => (
                <StripedRow key={kpiRow.id} index={index}>
                    <Grid container spacing={2} alignItems="center" style={{ height: '40px' }}>
                        <Grid item xs={4} sx={{ paddingTop: '8px !important' }}>
                            <Controller
                                name={`kpiTargets.${index}.refGlobalVisionKPI.name`}
                                control={control}
                                render={({ field }) => <div>{field.value}</div>}
                            />
                        </Grid>
                        <Grid item xs={4} sx={{ paddingTop: '8px !important' }}>
                            <Controller
                                name={`kpiTargets.${index}.rangeDirection`}
                                control={control}
                                render={({ field, fieldState: { error } }) => (
                                    <ClnSelect
                                        className="no-translate"
                                        fullWidth={true}
                                        variant="outlined"
                                        size="small"
                                        helperText={error?.message}
                                        options={['Above', 'Below']}
                                        disabled={!checkPermissionsFromRoutes('editKpiTargets')}
                                        value={field.value}
                                        // {...field}
                                        onChange={(value) => {
                                            const selectedValue = value
                                            setSelectedValue(selectedValue)
                                            setRowIndex(index)
                                            setFieldToUpdate(field)
                                            setIsModalOpen(true)
                                        }}
                                    />
                                )}
                            />
                        </Grid>
                        <Grid item xs={4} sx={{ paddingTop: '8px !important' }}>
                            <Controller
                                name={`kpiTargets.${index}.value`}
                                control={control}
                                defaultValue={kpiRow.value}
                                render={({ field }) => (
                                    <ClnTextField
                                        type="number"
                                        label=""
                                        fullWidth={true}
                                        size="small"
                                        variant="outlined"
                                        helperText=""
                                        value={field.value}
                                        onChange={(e) => {
                                            field.onChange(e.target.value)
                                        }}
                                        disabled={!checkPermissionsFromRoutes('editKpiTargets')}
                                    />
                                )}
                            />
                        </Grid>
                        {/* <Grid
                                item
                                xs={3}
                                sx={{ display: 'flex', justifyContent: 'center', paddingTop: '0px !important' }}
                            >
                                <Controller
                                    name={`kpiTargets.${index}.allSitesChecked`}
                                    control={control}
                                    defaultValue={kpiRow.allSitesChecked}
                                    render={({ field }) => (
                                        <FormControlLabel
                                            label={translate('KPI_TARGET.APPLY_ALL_SITES')}
                                            control={
                                                <Checkbox
                                                    {...field}
                                                    checked={field.value}
                                                    onChange={(e) => {
                                                        handleClickCheckbox(field)
                                                    }}
                                                    disabled={!checkPermissionsFromRoutes('kpiTargetApplyAllSites')}
                                                />
                                            }
                                        />
                                    )}
                                />
                            </Grid> */}
                    </Grid>
                </StripedRow>
            ))}
        </>
    )
}
export const KPITargetTable = () => {
    const [filters, setFilters] = useState<any>({ site: '', group: '', year: undefined })
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [fieldToUpdate, setFieldToUpdate] = useState<any>(null)
    const [selectedValue, setSelectedValue] = useState<string | null>(null)
    const [rowIndex, setRowIndex] = useState<number | null>(null)
    const [filteredKpiGroupsList, setFilteredKpiGroupsList] = useState<KpiGroup[]>([])
    const [alertData, setAlertData] = useState<AlertData>({
        isOpen: false,
        message: '',
        severity: 'error',
        autoHideDuration: 6000,
    })
    const { dataSiteList: sites } = useReportingSiteList([])
    const { reportingSites: sitesWithPermission } = useAuthSites({
        component: COMPONENT_NAME,
        allSites: sites,
        featureLevelCodes: ['EditAccess'],
    })
    const { kpiGroupsList } = useGetKpiGroups()
    const { updateKpiTargetMutationFn } = useKpiTargetMutation()
    const { checkPermissionsFromRoutes } = useAuthGuard()

    const {
        dataGlobalVisionKPITarget: kpiTargetsData,
        loadingGlobalVisionKPITarget,
        refetchGlobalVisionKPITarget,
    } = useGlobalVisionKPITarget({
        site: [filters.site],
        kpiGroupExternalId: filters.group,
        year: filters.year,
    })

    const { control, handleSubmit, reset, setValue, formState } = useForm<KpiTargetField>({
        mode: 'onSubmit',
        defaultValues: { kpiTargets: [] },
    })

    const { fields: kpiFields } = useFieldArray({
        control,
        name: 'kpiTargets',
    })

    const hasKpiFields = useMemo(() => Boolean(kpiFields.length), [kpiFields])

    useEffect(() => {
        if (loadingGlobalVisionKPITarget) return

        const kpiTargetList: GlobalVisionKpiTarget[] = kpiTargetsData.map((kpi) => ({
            externalId: kpi.externalId,
            space: kpi.space,
            refGlobalVisionKPI: {
                name: kpi.refGlobalVisionKPI.name,
                externalId: kpi.refGlobalVisionKPI.externalId,
                order: kpi.refGlobalVisionKPI.order,
            },
            refReportingSite: {
                externalId: kpi.refReportingSite.externalId,
            },
            value: kpi.value ?? 0,
            rangeDirection: kpi.rangeDirection ?? '',
            year: kpi.year,
            allSitesChecked: false,
        }))

        reset({ kpiTargets: kpiTargetList })
    }, [kpiTargetsData, loadingGlobalVisionKPITarget, reset])

    useEffect(() => {
        if (kpiGroupsList.length > 0) {
            const aux = kpiGroupsList.filter((group) => group.name !== 'Engagement')
            setFilteredKpiGroupsList(aux)
        }
    }, [kpiGroupsList])

    const applyFilters = async (filters: any) => {
        const filterAux = {
            site: filters.site.externalId,
            group: filters.group.externalId,
            year: Number(filters.year),
        }
        setFilters(filterAux)
    }

    function getEditedKpis(formData: KpiTargetField, defaultValues: any[]) {
        const editedKpiTargets: GlobalVisionKpiTarget[] = []

        formData.kpiTargets?.forEach((kpiTarget, index) => {
            const originalKpiTarget = defaultValues[index]
            if (
                kpiTarget.value !== originalKpiTarget.value ||
                kpiTarget.rangeDirection !== originalKpiTarget.rangeDirection
            ) {
                if (kpiTarget.allSitesChecked) {
                    sitesWithPermission.forEach((site) => {
                        const siteCode = site.siteCode
                        const [prefix, preffix2, group, , year] = kpiTarget.externalId.split('-')
                        const newExternalId = `${prefix}-${preffix2}-${group}-${siteCode}-${year}`
                        const newKpiTarget = {
                            ...kpiTarget,
                            externalId: newExternalId,
                            refReportingSite: {
                                externalId: site.externalId,
                            },
                        }
                        editedKpiTargets.push(newKpiTarget)
                    })
                } else {
                    editedKpiTargets.push(kpiTarget)
                }
            }
        })

        return {
            editedKpiTargets,
        }
    }

    const onSubmit = async (formData: KpiTargetField) => {
        const kpiDefaultValues = formState.defaultValues?.kpiTargets ?? []
        const { editedKpiTargets } = getEditedKpis(formData, kpiDefaultValues)

        if (editedKpiTargets.length > 0) {
            const kpiToMutation: KpiTargetMutation[] = []
            const space = editedKpiTargets[0].space
            editedKpiTargets.forEach((kpi) => {
                kpiToMutation.push({
                    externalId: kpi.externalId,
                    value: kpi.value ? parseFloat(kpi.value.toString()) : 0,
                    rangeDirection: kpi.rangeDirection ?? '',
                    year: kpi.year,
                })
            })
            if (kpiToMutation.length > 0 && space) {
                const mutationKpiTargetResult = await updateKpiTargetMutationFn(kpiToMutation, space)
                if (mutationKpiTargetResult.ok) {
                    setAlertData({
                        isOpen: true,
                        message: 'Success',
                        severity: 'success',
                        autoHideDuration: 6000,
                    })
                    refetchGlobalVisionKPITarget()
                }
            }
        }
    }

    return (
        <>
            <Toastr
                alertData={alertData}
                onClose={() => {
                    setAlertData({ ...alertData, isOpen: false })
                }}
            />
            <Typography sx={{ marginBottom: '1rem', color: 'black', padding: '5px' }}>
                {translate('KPI_TARGET.SCREEN_INFO')}
            </Typography>
            <KPITargetFilters
                sites={sitesWithPermission ?? []}
                groups={filteredKpiGroupsList ?? []}
                onSubmit={(filters) => applyFilters(filters)}
            />
            {loadingGlobalVisionKPITarget ? (
                LoaderCircular()
            ) : hasKpiFields ? (
                <>
                    <KpiTargetsList
                        kpiFields={kpiFields}
                        control={control}
                        checkPermissionsFromRoutes={checkPermissionsFromRoutes}
                        setSelectedValue={setSelectedValue}
                        setRowIndex={setRowIndex}
                        setFieldToUpdate={setFieldToUpdate}
                        setIsModalOpen={setIsModalOpen}
                    />
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: '15px' }}>
                        <ClnButton
                            variant="contained"
                            label={translate('COMMONBUTTON.SAVE')}
                            type="button"
                            disabled={!formState.isDirty}
                            onClick={() => {
                                void handleSubmit(onSubmit)()
                            }}
                        />
                    </Box>
                </>
            ) : (
                <></>
            )}

            <ApplyAllSitesModal
                openPopover={isModalOpen}
                closePopover={() => {
                    if (selectedValue) {
                        fieldToUpdate.onChange(selectedValue)
                        setValue(`kpiTargets.${rowIndex}.allSitesChecked`, false, {
                            shouldDirty: true,
                            shouldValidate: true,
                        })
                        setIsModalOpen(false)
                    } else {
                        setIsModalOpen(false)
                    }
                }}
                confirmPopover={() => {
                    if (fieldToUpdate) {
                        if (selectedValue) {
                            fieldToUpdate.onChange(selectedValue)
                            setValue(`kpiTargets.${rowIndex}.allSitesChecked`, true, {
                                shouldDirty: true,
                                shouldValidate: true,
                            })
                            setIsModalOpen(false)
                        } else {
                            setIsModalOpen(false)
                        }
                    }
                }}
            />
        </>
    )
}

export default KPITargetTable
