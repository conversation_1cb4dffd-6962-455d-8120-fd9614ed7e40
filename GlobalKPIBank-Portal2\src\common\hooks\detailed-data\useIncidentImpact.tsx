import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import dayjs from 'dayjs'
import { IncidentImpactKpi } from '@/common/models/IncidentImpact'
import { IncidentImpactTableView } from '@/common/models/IncidentImpactTable'
import { setExportDateRange } from '@/common/utils/kpis-data-table-export'

const queryBuilder = (request: DataModelView) => {
    let externalIdQ: string = ''
    let siteQ: string = ''
    let unitQ: string = ''
    let startDateQ: string = ''
    let endDateQ: string = ''
    let categoryQ: string = ''
    let limitQ: string = ''
    let cursorQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{reportingSite: {externalId: {in: [${sites}]}}},`
        unitQ = `{reportingUnit: {not: {externalId: {in: "UNT-${request.kpiFilters.site.replace('STS-', '')}GRN"}}}},`
    }
    if (request && request.kpiFilters.date !== '') {
        let sanitizedInitialDate = dayjs(request.kpiFilters.date).startOf('month').format('YYYY-MM-DD')
        let sanitizedEndDate = dayjs(request.kpiFilters.date).endOf('month').format('YYYY-MM-DD')
        const time = 'T00:00:00+00:00'

        if (request.exportFilters && request.exportFilters.isExport) {
            ;[sanitizedInitialDate, sanitizedEndDate] = setExportDateRange(
                request.exportFilters.range,
                request.kpiFilters.date
            )
        }

        startDateQ = `{reportedTime: {gte: "${sanitizedInitialDate}${time}"}},`
        endDateQ = `{reportedTime: {lte: "${sanitizedEndDate}${time}"}},`
    }
    if (request && request.kpiFilters.kpiName !== '') {
        if (request.kpiFilters.kpiName == 'Near Misses') {
            categoryQ = `{category:{name: {in: ["PPS-NM","PCS-NM","ENV-NM","FIR-NM","RLB-NM","SEC-NM"]}}}`
            externalIdQ = `{not: {externalId: {prefix: "ENA"}}}`
        }
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listIncidentImpact (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${categoryQ} ${startDateQ} ${endDateQ} ${unitQ} ${externalIdQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                incidentParentId
                description
                impactStartTime 
                reportingSite {
                    externalId
                    name
                }
                reportingUnit {
                    name
                }
                category {
                    name
                }
            }
        }
    }`
}

const mapIncidentImpact = (data: IncidentImpactKpi[]): IncidentImpactTableView[] => {
    const mappedResult: IncidentImpactTableView[] = []
    if (data && data.length > 0) {
        data.map((item) => {
            const result: IncidentImpactTableView = {
                siteName: item.reportingSite?.name ?? '',
                unit: item.reportingUnit?.name ?? '',
                description: item.description ?? '',
                category: item.category?.name ?? '',
                incidentParentId: item.incidentParentId,
                ImpactStartTime: item.impactStartTime?.toString().slice(0, 10) ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useIncidentImpact = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<IncidentImpactKpi>(gql(query), 'listIncidentImpact', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'stewardshipView',
        },
    })
    const [resultData, setResultData] = useState<{ data: any[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapIncidentImpact(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapIncidentImpact(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
