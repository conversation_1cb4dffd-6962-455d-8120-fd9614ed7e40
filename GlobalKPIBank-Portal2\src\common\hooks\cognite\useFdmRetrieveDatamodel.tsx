import { useCallback, useState } from 'react'
import { useCognite } from '../useCognite'

export type RetrieveDatamodelHookResult = [RetrieveDatamodelFunction, RetrieveDatamodelState]

export type RetrieveDatamodelFunctionParams = {
    entityName: string
}

export type RetrieveDatamodelFunction = (params: RetrieveDatamodelFunctionParams) => Promise<string>

export type RetrieveDatamodelState = {
    loading: boolean
    called: boolean
    error: any
}

export function useFdmRetrieveDatamodel(): RetrieveDatamodelHookResult {
    const { fdmClient: client } = useCognite()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)
    const retrieveFunction = useCallback(
        ({ entityName }: RetrieveDatamodelFunctionParams) => {
            setLoading(true)
            setCalled(true)
            return client
                .getEntityVersion(entityName)
                .catch(() => '')
                .finally(() => setLoading(false))
        },
        [client]
    )
    return [retrieveFunction, { error, loading, called }]
}
