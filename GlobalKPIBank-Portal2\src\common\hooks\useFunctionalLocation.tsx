import { gql } from '@apollo/client'
import { useGraphqlQuery } from './useGraphqlQuery'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'
import { FunctionalLocation } from '../models/functionalLocation'
import { useEffect, useState } from 'react'
import { Unit } from '../models/unit'
import { ArrayEntity } from '../models'

export interface LocationQueryRequest {
    unitName?: string
    externalId?: string
    nextFuntionalLocationPage?: string
}

export interface PageInfo {
    hasPreviousPage: boolean
    hasNextPage: boolean
    startCursor: string
    endCursor: string
}

const buildLocationQuery = (request: LocationQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.externalId && request.externalId != '') {
        filter.push(`{ externalId: { eq: "${request.externalId}" }}`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.SAP, getLocalUserSite()?.siteCode)}",  "${GetSpace(EntityType.SAP)}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetFunctionalLocation {
            listFunctionalLocation(
                filter: ${queryFilter}
            , first: 1000 , after: ${
                request.nextFuntionalLocationPage ? `"${request.nextFuntionalLocationPage}"` : 'null'
            }) {
                items {
                    externalId
                    space
                    reportingUnits {
                        items {
                            externalId
                            space
                            description
                        }
                    }
                }
                pageInfo {
                    hasPreviousPage
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    return query
}

export const useFunctionalLocation = (request: LocationQueryRequest) => {
    const query = buildLocationQuery(request)
    const { data: fdmData, pageInfo } = useGraphqlQuery<FunctionalLocation>(gql(query), 'listFunctionalLocation', {})

    const [resultData, setResultData] = useState<{
        data: FunctionalLocation[]
        loading: boolean
        pagging: boolean
        pageInfo: PageInfo
    }>({
        data: [],
        loading: true,
        pagging: false,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false, pageInfo, pagging: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayReportingUnitsEntity = d.reportingUnits as any as ArrayEntity<Unit>

                return {
                    ...d,
                    reportingUnits: arrayReportingUnitsEntity.items,
                }
            })

            setResultData({
                data: fdmDataParsed,
                loading: false,
                pageInfo,
                pagging: Boolean(request.nextFuntionalLocationPage),
            })
        }
    }, [fdmData, pageInfo])

    return {
        functionalLocations: resultData.data,
        functionalLocationPageInfo: resultData.pageInfo,
    }
}
