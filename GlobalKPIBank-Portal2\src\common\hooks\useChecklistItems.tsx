import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { ArrayEntity } from '../models'
import { ChecklistItem } from '../models/checklist-item'
import { Observation } from '../models/observation'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'
import { Measurement } from '../models/measurement'

export interface ChecklistItemQueryRequest {
    ids?: string[]
    title?: string
    start?: number
    end?: number
    nextItems?: string
    first?: number
}

const buildChecklistItemQuery = (request: ChecklistItemQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.ids?.length) {
        filter.push(`{ externalId: { in: [${request.ids.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.title) {
        filter.push(`{ title: { prefix: "${request.title}" }}`)
    }

    if (request.start) {
        filter.push(`{ startTime: { gte: ${request.start} }}`)
    }

    if (request.end) {
        filter.push(`{ endTime: { lte: ${request.end} }}`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
            }
        }`
    )

    filter.push(`{ not: { isArchived: { eq: true } } }`)

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    if (!request.first) {
        request.first = 100
    }

    const query = `
        query GetChecklistItem {
            listChecklistItem(
                filter: ${queryFilter}
            , first: ${request.first}, after: ${request.nextItems ? `"${request.nextItems}"` : 'null'}) {
                items {
                    externalId
                    space
                    sourceId
                    title
                    description
                    labels
                    order
                    status
                    note
                    startTime
                    endTime
                    asset {
                        externalId
                        space
                        title
                        description
                    }
                    files {
                        externalId
                        mimeType
                        name
                        metadata
                        downloadLink {
                            downloadUrl
                        }
                    }
                    measurements (sort: {order: ASC}) {
                        items {
                            externalId
                            space
                            type
                            order
                            timeseries {
                                externalId
                                id
                                assetId
                                name
                                description
                                unit
                            }
                            measuredAt
                            min
                            max
                            numericReading
                            stringReading
                            options
                            title
                        }
                    }
                    lastUpdatedTime
                    observations (filter: {isArchived: {isNull: true}}) {
                        items {
                            externalId
                            description
                            troubleshooting
                            files {
                                externalId
                                mimeType
                                name
                                metadata
                                downloadLink {
                                    downloadUrl
                                }
                            }
                        }
                    }
                }
                pageInfo {
                    hasPreviousPage
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    return query
}

export const useChecklistItems = (request: ChecklistItemQueryRequest) => {
    const query = buildChecklistItemQuery(request)
    const { data: fdmData, pageInfo } = useGraphqlQuery<ChecklistItem>(gql(query), 'listChecklistItem', {
        fetchPolicy: 'no-cache',
    })

    const [resultData, setResultData] = useState<{ data: ChecklistItem[]; loading: boolean; pageInfoItems: PageInfo }>({
        data: [],
        loading: true,
        pageInfoItems: pageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false, pageInfoItems: pageInfo })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayMeasurementsEntity = d.measurements as any as ArrayEntity<Measurement>
                const arrayObservationsEntity = d.observations as any as ArrayEntity<Observation>

                return {
                    ...d,
                    measurements: arrayMeasurementsEntity.items,
                    observations: arrayObservationsEntity.items,
                }
            })

            setResultData({ data: fdmDataParsed, loading: false, pageInfoItems: pageInfo })
        }
    }, [fdmData, pageInfo])

    return {
        loading: resultData.loading,
        checklistsItems: resultData.data,
        pageInfoItems: pageInfo,
    }
}
