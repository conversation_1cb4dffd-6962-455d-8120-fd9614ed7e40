import { Dayjs } from 'dayjs'

export interface DataPoint {
    value: number
    timestamp: string
}

export interface TimeSeries {
    externalId: string
    id: string
    assetId: string
    name: string
    description: string
    unit: string
    datasetId: string
    getDataPoints: DataPoint[]
    metadata: any
    isStep: boolean
    isString: boolean
    assetName: string
    createdAt?: Dayjs
    updatedAt?: Dayjs
    lastReading?: Dayjs
}

export interface TimeSeriesRequest {
    name?: string
    description?: string
    units?: string
    id?: number
    externalId?: string
    isString: boolean
    isStep: boolean
    datasetId?: string
    datasetName?: string
    assetName?: string
    createdAt?: string
    updatedAt?: string
    lastReading?: number
    datapointsItems?: DataPoint[]
    urlTimeSeriesDetails: string
    urlDataSetDetails: string
    urlLinkedAssets: string
}
