import { QueryHookOptions } from '@apollo/client'

export type CgnFilter<T> =
    | {
          [K in keyof T]?: ComparisonOperators<T[K]> | SpecialOperators<T> | CgnFilter<T[K]>
      }
    | SpecialOperators<T>

type ComparisonOperators<T> =
    | { eq: T }
    | { lt: T }
    | { lte: T }
    | { gt: T }
    | { gte: T }
    | { isNull: boolean }
    | { in: T[] }

type SpecialOperators<T> = { and: CgnFilter<T>[] } | { or: CgnFilter<T>[] } | { not: CgnFilter<T> }

/**
 * Use this when utilizing the `query myQuery(filter: _SomeTypeFilter)` in cognite graphql queries
 */
export type CogniteGraphqlTypeFilter<T> = {
    variables: { filter: CgnFilter<T>; first?: number }
}

export type BaseHookRequest = QueryHookOptions<any, any> & {}
