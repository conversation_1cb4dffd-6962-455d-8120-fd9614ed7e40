"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ts-invariant";
exports.ids = ["vendor-chunks/ts-invariant"];
exports.modules = {

/***/ "(ssr)/./node_modules/ts-invariant/lib/invariant.cjs":
/*!*****************************************************!*\
  !*** ./node_modules/ts-invariant/lib/invariant.cjs ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar tslib = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar genericMessage = \"Invariant Violation\";\nvar _a = Object.setPrototypeOf, setPrototypeOf = _a === void 0 ? function (obj, proto) {\n    obj.__proto__ = proto;\n    return obj;\n} : _a;\nvar InvariantError = /** @class */ (function (_super) {\n    tslib.__extends(InvariantError, _super);\n    function InvariantError(message) {\n        if (message === void 0) { message = genericMessage; }\n        var _this = _super.call(this, typeof message === \"number\"\n            ? genericMessage + \": \" + message + \" (see https://github.com/apollographql/invariant-packages)\"\n            : message) || this;\n        _this.framesToPop = 1;\n        _this.name = genericMessage;\n        setPrototypeOf(_this, InvariantError.prototype);\n        return _this;\n    }\n    return InvariantError;\n}(Error));\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new InvariantError(message);\n    }\n}\nvar verbosityLevels = [\"debug\", \"log\", \"warn\", \"error\", \"silent\"];\nvar verbosityLevel = verbosityLevels.indexOf(\"log\");\nfunction wrapConsoleMethod(name) {\n    return function () {\n        if (verbosityLevels.indexOf(name) >= verbosityLevel) {\n            // Default to console.log if this host environment happens not to provide\n            // all the console.* methods we need.\n            var method = console[name] || console.log;\n            return method.apply(console, arguments);\n        }\n    };\n}\n(function (invariant) {\n    invariant.debug = wrapConsoleMethod(\"debug\");\n    invariant.log = wrapConsoleMethod(\"log\");\n    invariant.warn = wrapConsoleMethod(\"warn\");\n    invariant.error = wrapConsoleMethod(\"error\");\n})(invariant || (invariant = {}));\nfunction setVerbosity(level) {\n    var old = verbosityLevels[verbosityLevel];\n    verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));\n    return old;\n}\nvar invariant$1 = invariant;\n\nexports.InvariantError = InvariantError;\nexports[\"default\"] = invariant$1;\nexports.invariant = invariant;\nexports.setVerbosity = setVerbosity;\n//# sourceMappingURL=invariant.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ts-invariant/lib/invariant.cjs\n");

/***/ })

};
;