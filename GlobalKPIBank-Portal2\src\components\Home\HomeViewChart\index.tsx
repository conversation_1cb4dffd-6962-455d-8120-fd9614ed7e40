import { VisionKpiAggregation } from '@/common/models/visionKpiAggregation'
import { Bar<PERSON>hart, QuantityBarItemChart } from '../../Charts/BarChart'
import { useEffect, useState } from 'react'
import { translate } from '@celanese/celanese-sdk'
import { Box } from '@mui/material'

interface HomeViewChartProps {
    data: VisionKpiAggregation
    range?: number
    selectedKpi?: string
}

export const HomeViewChart: React.FC<HomeViewChartProps> = ({ data, range, selectedKpi }) => {
    const [formattedData, setFormattedData] = useState<QuantityBarItemChart[]>([])

    useEffect(() => {
        const formattedDataAux: QuantityBarItemChart[] = []
        if (selectedKpi == 'KPIG-PCM' || selectedKpi == 'KPIG-MKG') {
            formattedDataAux.push({ label: 'Q1', value: data.january })
            formattedDataAux.push({ label: 'Q2', value: data.april })
            formattedDataAux.push({ label: 'Q3', value: data.july })
            formattedDataAux.push({ label: 'Q4', value: data.october })
        } else {
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.JANUARY'), value: data.january })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.FEBRUARY'), value: data.february })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.MARCH'), value: data.march })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.APRIL'), value: data.april })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.MAY'), value: data.may })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.JUNE'), value: data.june })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.JULY'), value: data.july })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.AUGUST'), value: data.august })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.SEPTEMBER'), value: data.september })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.OCTOBER'), value: data.october })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.NOVEMBER'), value: data.november })
            formattedDataAux.push({ label: translate('TABLE_COLS.MONTHS.DECEMBER'), value: data.december })
        }

        const formattedValues = formattedDataAux.map((item) => Number(item.value))
        if (formattedValues.every((value) => value === 0)) {
            setFormattedData([])
        } else {
            setFormattedData(formattedDataAux)
        }
    }, [data])

    return (
        <Box sx={{ border: '1px solid #ccc', borderRadius: '16px', marginTop: '20px' }}>
            <BarChart id={data.refReportingSite.externalId} data={formattedData} range={range} />
        </Box>
    )
}

export default HomeViewChart
