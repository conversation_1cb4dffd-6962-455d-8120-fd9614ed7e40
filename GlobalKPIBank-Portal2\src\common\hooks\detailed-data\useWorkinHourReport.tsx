import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import dayjs from 'dayjs'
import { WorkingHourReportKpi } from '@/common/models/workingHourReport'
import { WorkingHourReportTableView } from '@/common/models/workingHourReportTable'
import { setExportDateRange } from '@/common/utils/kpis-data-table-export'

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let unitQ: string = ''
    let startDateQ: string = ''
    let endDateQ: string = ''
    let limitQ: string = ''
    let cursorQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{reportingSite: {externalId: {in: [${sites}]}}},`
        unitQ = `{reportingUnit: {not: {externalId: {in: "UNT-${request.kpiFilters.site.replace('STS-', '')}GRN"}}}},`
    }

    if (request && request.kpiFilters.date !== '') {
        let sanitizedInitialDate = dayjs(request.kpiFilters.date).startOf('month').format('YYYY-MM-DD')
        let sanitizedEndDate = dayjs(request.kpiFilters.date).endOf('month').format('YYYY-MM-DD')

        if (request.exportFilters && request.exportFilters.isExport) {
            ;[sanitizedInitialDate, sanitizedEndDate] = setExportDateRange(
                request.exportFilters.range,
                request.kpiFilters.date
            )
        }

        startDateQ = `{firstDayOfMonth: {gte: "${sanitizedInitialDate}"}},`
        endDateQ = `{firstDayOfMonth: {lte: "${sanitizedEndDate}"}},`
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listWorkingHourReport (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${startDateQ} ${endDateQ} ${unitQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                reportingUnit {
                    name
                }
                reportingSite {
                    externalId
                    name
                }
                totalWorkingHours
                employeeWorkingHours
                contractorWorkingHours
                firstDayOfMonth
            }      
        }
    }`
}

const mapWorkingHourReport = (data: WorkingHourReportKpi[]): WorkingHourReportTableView[] => {
    const mappedResult: WorkingHourReportTableView[] = []
    if (data && data.length > 0) {
        data.map((item) => {
            const result: WorkingHourReportTableView = {
                siteName: item.reportingSite?.name ?? '',
                reportingUnit: item.reportingUnit?.name ?? '',
                month: dayjs(item.firstDayOfMonth).format('MMMM'),
                year: dayjs(item.firstDayOfMonth).format('YYYY'),
                employeeWorkingHours:
                    typeof item.employeeWorkingHours == 'number' ? item.employeeWorkingHours.toString() : '',
                contractorWorkingHours:
                    typeof item.contractorWorkingHours == 'number' ? item.contractorWorkingHours.toString() : '',
                totalWorkingHours: typeof item.totalWorkingHours == 'number' ? item.totalWorkingHours.toString() : '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useWorkingHourReport = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<WorkingHourReportKpi>(gql(query), 'listWorkingHourReport', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'incidentView',
        },
    })

    const [resultData, setResultData] = useState<{ data: any[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapWorkingHourReport(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapWorkingHourReport(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
