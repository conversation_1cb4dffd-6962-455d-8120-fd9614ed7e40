import dayjs from 'dayjs'
const advancedFormat = require('dayjs/plugin/advancedFormat')
dayjs.extend(advancedFormat)

export const setExportDateRange = (range: number, endDate: string): [string, string] => {
    if (range === 1) {
        const start = dayjs(endDate).subtract(range, 'month').startOf('month').format('YYYY-MM-DD')
        const end = dayjs(endDate).subtract(range, 'month').endOf('month').format('YYYY-MM-DD')
        return [start, end]
    }

    if (range === 3) {
        const start = dayjs(endDate).subtract(range, 'month').startOf('month').format('YYYY-MM-DD')
        const end = dayjs(endDate).endOf('month').format('YYYY-MM-DD')
        return [start, end]
    }

    if (range === 6) {
        const start = dayjs(endDate).subtract(range, 'month').startOf('month').format('YYYY-MM-DD')
        const end = dayjs(endDate).endOf('month').format('YYYY-MM-DD')
        return [start, end]
    }

    if (range === 12) {
        const start = dayjs(endDate).startOf('year').format('YYYY-MM-DD')
        const end = dayjs(endDate).endOf('year').format('YYYY-MM-DD')
        return [start, end]
    }

    const start = dayjs(endDate).startOf('month').format('YYYY-MM-DD')
    const end = dayjs(endDate).endOf('month').format('YYYY-MM-DD')
    return [start, end]
}

const calculateMonthQuarterAndYear = (range: number, date: string) => {
    const listMonths = []
    const listYears = []
    const listQuarters = []

    if (range === 0) {
        const endDate = dayjs(date).startOf('month')
        const auxQuarters = 'Q' + dayjs(endDate).format('Q')

        listMonths.push(endDate.format('MMM').toUpperCase())
        listQuarters.push(auxQuarters)
        listYears.push(endDate.format('YYYY'))
    }

    if (range === 1) {
        const endDate = dayjs(date).subtract(1, 'month').startOf('month')
        const auxQuarters = 'Q' + dayjs(endDate).format('Q')

        listMonths.push(endDate.format('MMM').toUpperCase())
        listQuarters.push(auxQuarters)
        listYears.push(endDate.format('YYYY'))
    }

    if (range === 3 || range === 6) {
        const endDate = dayjs(date).startOf('month')
        const startDate = endDate.subtract(range, 'month').startOf('month')
        const monthsDiff = endDate.diff(startDate, 'month')

        for (let i = 0; i <= monthsDiff; i++) {
            const currentDate = startDate.add(i, 'month')
            const auxQuarters = 'Q' + dayjs(currentDate).format('Q')

            listMonths.push(currentDate.format('MMM').toUpperCase())
            listQuarters.push(auxQuarters)
            listYears.push(currentDate.format('YYYY'))
        }
    }

    if (range === 12) {
        const endDate = dayjs(date).startOf('month')
        const startDate = endDate.startOf('year')
        const monthsDiff = endDate.diff(startDate, 'month')

        for (let i = 0; i <= monthsDiff; i++) {
            const currentDate = startDate.add(i, 'month')
            const auxQuarters = 'Q' + dayjs(currentDate).format('Q')

            listMonths.push(currentDate.format('MMM').toUpperCase())
            listQuarters.push(auxQuarters)
            listYears.push(currentDate.format('YYYY'))
        }
    }

    return [listMonths, listQuarters, listYears]
}

const formatMonthName = (listMonth: string[]): any[] => {
    return listMonth.map((month) => {
        return month.charAt(0).toUpperCase() + month.slice(1).toLowerCase()
    })
}

export const getCustomMonthAndYearQuery = (
    range: number,
    date: string,
    monthName: string,
    isProductivity: boolean
): string => {
    const [listMonths, _, listYears] = calculateMonthQuarterAndYear(range, date)
    const listProdMonths = formatMonthName(listMonths)
    const aux = []
    const customDateQuery = []
    let result = ''

    if (listYears.length > 0) {
        for (let i = 0; i < listYears.length; i++) {
            let month = []
            if (isProductivity) month = listProdMonths[i]
            else month = listMonths[i]

            const year = listYears[i]

            const pair = month + '/' + String(year)
            if (aux.includes(pair)) {
                continue
            }
            aux.push(pair)

            const segmentedQuery = `{and:[{${monthName}:{eq:"${month}"}},{year:{eq:${year}}}]}`
            customDateQuery.push(segmentedQuery)
        }
        result = `{or:[${customDateQuery}]}`
    }

    return result
}

export const getCustomQuarterAndYearQuery = (range: number, date: string): string => {
    const [_, listQuarters, listYears] = calculateMonthQuarterAndYear(range, date)
    const aux = []
    const customDateQuery = []
    let result = ''

    if (listYears.length > 0) {
        for (let i = 0; i < listYears.length; i++) {
            const quarter = listQuarters[i]
            const year = listYears[i]

            const pair = quarter + '/' + String(year)
            if (aux.includes(pair)) {
                continue
            }
            aux.push(pair)

            const segmentedQuery = `{and:[{period:{eq:"${quarter}"}},{year:{eq:${year}}}]}`
            customDateQuery.push(segmentedQuery)
        }
        result = `{or:[${customDateQuery}]}`
    }

    return result
}
