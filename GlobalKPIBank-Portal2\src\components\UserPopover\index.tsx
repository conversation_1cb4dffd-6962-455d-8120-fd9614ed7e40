import { getUserInitials } from '@/common/utils/general'
import { Avatar, Box, Divider, Tooltip, Typography } from '@mui/material'
import React, { useContext } from 'react'
import Popover from '@mui/material/Popover'
import { AuthGuardContext } from '@/common/contexts/AuthGuardContext'
import LogOutButton from '../LogOutButton'

export interface UserPopoverProps {
    anchorEl: HTMLElement | null
    onClose: () => void
}

export const UserPopover = (props: UserPopoverProps) => {
    const { anchorEl, onClose } = props
    const open = Boolean(anchorEl)
    const { userInfo } = useContext(AuthGuardContext)

    const userName = userInfo.lastName + ', ' + userInfo.firstName
    const picture = userInfo.avatar
    const userEmail = userInfo.email
    const userInitials = getUserInitials(userName || 'Unknown')

    return (
        <Popover
            open={open}
            anchorEl={anchorEl}
            onClose={onClose}
            PaperProps={{ sx: { borderRadius: 5, marginTop: '35px' } }}
        >
            <Box sx={{ display: 'flex', flexDirection: 'column', p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar
                        src={picture}
                        alt={userName}
                        sx={{
                            width: 65,
                            height: 65,
                            backgroundColor: 'background.paper',
                            color: 'primary.contrastText',
                        }}
                    >
                        {userInitials}
                    </Avatar>
                    <Box sx={{ px: 1 }}>
                        <Tooltip title={userName} placement="bottom">
                            <Typography
                                sx={{
                                    display: 'block',
                                    width: '135px',
                                    fontSize: '14px',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                }}
                            >
                                {userName}
                            </Typography>
                        </Tooltip>
                        <Tooltip title={userEmail} placement="bottom">
                            <Typography
                                variant="caption"
                                sx={{
                                    display: 'block',
                                    width: '135px',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                }}
                            >
                                {userEmail}
                            </Typography>
                        </Tooltip>
                    </Box>
                </Box>
                <Divider sx={{ my: 2 }} />
                <LogOutButton />
            </Box>
        </Popover>
    )
}
