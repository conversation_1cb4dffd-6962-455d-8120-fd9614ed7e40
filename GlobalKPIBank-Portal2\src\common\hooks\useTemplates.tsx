import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { Template } from '../models/template'
import { ArrayEntity } from '../models'
import { TemplateItem } from '../models/template-item'
import { Schedule } from '../models/schedule'
import { Measurement } from '../models/measurement'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface TemplateQueryRequest {
    externalIds?: string[]
    title?: string
    status?: string
    assignedTo?: string
    start?: number
    end?: number
    nextPage?: string
    retriveTemplateItems?: boolean
    includeArchived?: boolean
    first?: number
}

const buildTemplateQuery = (request: TemplateQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.externalIds?.length) {
        filter.push(`{ externalId: { in: [${request.externalIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.title && request.title != '') {
        filter.push(`{ title: { prefix: "${request.title}" }}`)
    }

    if (request.status && request.status != '') {
        filter.push(`{ status: { eq: "${request.status}" }}`)
    }

    if (request.assignedTo && request.assignedTo != '') {
        filter.push(`{ assignedTo: { eq: "${request.assignedTo}" }}`)
    }

    if (request.start) {
        filter.push(`{ createdTime: { gte: ${request.start} }}`)
    }

    if (request.end) {
        filter.push(`{ createdTime: { lte: ${request.end} }}`)
    }

    if (!request.includeArchived) {
        filter.push(`{ not: { isArchived: { eq: true } } }`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    if (!request.first) {
        request.first = 100
    }

    const query = `
        query GetTemplates {
            listTemplate(
                filter: ${queryFilter},
                first: ${request.first},
                after: ${request.nextPage ? `"${request.nextPage}"` : 'null'},
                sort: { title: ASC })
            {
                items {
                    externalId
                    space
                    title
                    description
                    labels
                    visibility
                    status
                    createdTime
                    lastUpdatedTime
                    isArchived
                    rootLocation {
                        externalId
                        space
                        title
                        description
                    }
                    assignedTo
                    ${
                        request.retriveTemplateItems
                            ? `
                    templateItems (filter: {isArchived: {isNull: true}}, first: 1000) {
                        items {
                            externalId
                            space
                            title
                            description
                            labels
                            visibility
                            order
                            asset {
                                externalId
                                space
                                title
                                description
                            }
                            schedules (filter: {isArchived: {isNull: true}}) {
                                items {
                                    startTime
                                    endTime
                                    visibility
                                    status
                                    timezone
                                    exceptionDates
                                    freq
                                    interval
                                    byDay
                                    until
                                }
                            }
                            measurements (filter: {isArchived: {isNull: true}}) {
                                items {
                                    externalId
                                    space
                                    visibility
                                    title
                                    description
                                    labels
                                    type
                                    order
                                    timeseries {
                                        externalId
                                        id
                                        assetId
                                        name
                                        description
                                        unit
                                    }
                                    min
                                    max
                                    options
                                    measuredAt
                                    numericReading
                                    stringReading
                                }
                            }
                        }
                    }
                    `
                            : ''
                    }
                }
                pageInfo {
                    hasPreviousPage
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    return query
}

export const useTemplates = (request: TemplateQueryRequest) => {
    const query = gql(buildTemplateQuery(request))

    const result = useGraphqlQuery<Template>(query, 'listTemplate', { fetchPolicy: 'no-cache' })

    const [resultData, setResultData] = useState<{ data: Template[]; loading: boolean; pageInfo: PageInfo }>({
        data: [],
        loading: true,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (result.data.length == 0) {
            setResultData({ data: [], loading: result.loading, pageInfo: result.pageInfo })
        } else {
            const fdmDataParsed = result.data.map((d) => {
                if (!d.templateItems) return d

                const arrayTemplateItemEntity = d.templateItems as any as ArrayEntity<TemplateItem>
                const templateItems = arrayTemplateItemEntity.items.map((i) => {
                    const arrayMeasurementsEntity = i.measurements as any as ArrayEntity<Measurement>
                    const arraySchedulesEntity = i.schedules as any as ArrayEntity<Schedule>

                    return {
                        ...i,
                        measurements: arrayMeasurementsEntity.items,
                        schedules: arraySchedulesEntity.items,
                    }
                })

                return {
                    ...d,
                    templateItems: templateItems,
                }
            })

            setResultData({ data: fdmDataParsed, loading: result.loading, pageInfo: result.pageInfo })
        }
    }, [result.data, result.loading, result.pageInfo])

    return {
        loadingTemplate: resultData.loading,
        templates: resultData.data,
        refetchTemplate: result.refetch,
        pageInfo: resultData.pageInfo,
    }
}
