import { useEffect, useState } from 'react'
import { useQueryResultsFunction } from '../general-functions/useQueryResultsFunction'
import { EntityType, GetSpace } from '@/common/utils/space-util'
import { getLocalUserSite } from '@/common/utils'
import { Dayjs } from 'dayjs'

export interface ItemsQueryRequest {
    removeStatus: boolean
    currentStatusValues?: string[]
    currentTaskStatusValues?: string
    assignedToValue?: string
    relatedToValue?: string
    units?: string[]
    startDate?: Dayjs
    endDate?: Dayjs
    dataModelTable?: string
    overdue?: boolean
    title?: string
    discipline?: string[]
    location?: string[]
    configs: string[]
    shift: string[]
}

const buildItemsQuery = (request: ItemsQueryRequest): any => {
    const mapFilters = (items: string[] | undefined, space: string) => {
        return items?.map((item) => ({
            externalId: item,
            space,
        }))
    }

    const configsFilter = request.configs.length > 0 ? request.configs : undefined

    const query = {
        checklists: {
            nodes: {
                filter: {
                    and: [
                        {
                            or: [
                                {
                                    equals: {
                                        property: ['node', 'space'],
                                        value: GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode),
                                    },
                                },
                            ],
                        },
                        {
                            and: [
                                {
                                    range: {
                                        property: ['cdf_apm', 'Checklist/v4', 'startTime'],
                                        gte: request.startDate,
                                        lte: request.endDate,
                                    },
                                },
                                {
                                    range: {
                                        property: ['cdf_apm', 'Checklist/v4', 'endTime'],
                                        gte: request.startDate,
                                        lte: request.endDate,
                                    },
                                },
                            ],
                        },
                        ...(configsFilter
                            ? [
                                  {
                                      in: {
                                          property: ['cdf_apm', 'Checklist/v4', 'sourceId'],
                                          values: configsFilter.map((item) => item.replace('CTC-', '')),
                                      },
                                  },
                              ]
                            : []),
                    ],
                },
                chainTo: 'destination',
                direction: 'outwards',
            },
            limit: 10000,
        },
        checklist_to_items: {
            edges: {
                from: 'checklist',
                direction: 'inwards',
                filter: {
                    equals: {
                        property: ['edge', 'type'],
                        value: {
                            space: 'cdf_apm',
                            externalId: 'referenceChecklist',
                        },
                    },
                },
                chainTo: 'destination',
            },
            limit: 10000,
        },
        tasks: {
            from: 'checklist_to_items',
            nodes: {
                filter: {
                    and: [
                        {
                            or: [
                                {
                                    equals: {
                                        property: ['node', 'space'],
                                        value: GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode),
                                    },
                                },
                            ],
                        },
                        {
                            equals: {
                                property: ['cdf_apm', 'ChecklistItem/v4', 'status'],
                                value: request.currentTaskStatusValues,
                            },
                        },
                    ],
                },
                chainTo: 'destination',
                direction: 'outwards',
            },
            limit: 10000,
        },
    }

    return query
}

export const useItemsKpis = (request: ItemsQueryRequest) => {
    const unitsByFilter = request.units && request.units?.length > 0 ? true : false
    const locationByFilter = request.location && request.location?.length > 0 ? true : false
    const disciplineByFilter = request.discipline && request.discipline?.length > 0 ? true : false
    const shiftByFilter = request.shift.length > 0 ? true : false

    const [filter, setFilter] = useState<any>()
    const [resultData, setResultData] = useState<{ data: number; loading: boolean }>({
        data: 0,
        loading: true,
    })

    const select = {
        checklists: {
            sources: [
                {
                    source: {
                        space: 'cdf_apm',
                        externalId: 'Checklist',
                        version: 'v4',
                        type: 'view',
                    },
                    properties: ['*'],
                },
            ],
            limit: 10000,
        },
        checklist_to_itens: {},
        tasks: {
            sources: [
                {
                    source: {
                        space: 'cdf_apm',
                        externalId: 'ChecklistItem',
                        version: 'v4',
                        type: 'view',
                    },
                    properties: ['*'],
                },
            ],
            limit: 10000,
        },
    }

    const cursors = {
        tasks: null,
        checklist_to_items: null,
        checklists: null,
    }

    const { getAllResults: getAllData } = useQueryResultsFunction<any>(select, cursors)

    useEffect(() => {
        setFilter(buildItemsQuery(request))
    }, [request])

    useEffect(() => {
        filter &&
            getAllData(filter)
                .then((res) => {
                    if (
                        res.items.items.length === 0 ||
                        (request.configs.length === 0 &&
                            (unitsByFilter || locationByFilter || disciplineByFilter || shiftByFilter))
                    ) {
                        setResultData({ data: 0, loading: false })
                    } else {
                        setResultData({ data: res.items.tasks.length, loading: false })
                    }
                })
                .catch((e) => console.log('error', e, 'filter', filter))
    }, [filter])

    return {
        loading: resultData.loading,
        countResult: resultData.data,
    }
}
