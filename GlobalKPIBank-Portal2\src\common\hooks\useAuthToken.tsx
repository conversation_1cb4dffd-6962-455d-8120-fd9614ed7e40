import { useCallback } from 'react'
import { useMsal } from '@azure/msal-react'
import { msalScopes, msalGraphScopes } from '../configurations/auth'
import { AuthenticationResult } from '@azure/msal-browser'

function useAuthTokenScoped(scopes: string[]): { getAuthToken: () => Promise<string> } {
    const msal = useMsal()
    const getAuthToken = useCallback(async () => {
        const envToken = process.env.NEXT_PUBLIC_AUTH_TOKEN
        if (envToken) {
            return envToken
        }

        const account = msal.instance.getActiveAccount()
        if (!account) {
            throw Error('No active account! Verify a user has been signed in and setActiveAccount has been called.')
        }

        const accessTokenRequest = {
            account,
            scopes: scopes,
        }

        let result: AuthenticationResult | undefined

        try {
            result = await msal.instance.acquireTokenSilent(accessTokenRequest).then((accessTokenResponse) => {
                return accessTokenResponse
            })
        } catch {
            try {
                await msal.instance.acquireTokenRedirect(accessTokenRequest)
            } catch {
                result = undefined
            }
        }

        return result ? result.accessToken : 'NOTOKENFOUND'
    }, [msal.instance, scopes])

    return { getAuthToken }
}

export function useAuthToken(): { getAuthToken: () => Promise<string> } {
    return useAuthTokenScoped(msalScopes)
}

export function useGraphAuthToken(): { getAuthToken: () => Promise<string> } {
    return useAuthTokenScoped(msalGraphScopes)
}
