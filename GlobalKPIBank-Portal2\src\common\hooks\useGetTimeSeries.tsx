import { gql } from '@apollo/client'
import { useGraphqlQuery } from './useGraphqlQuery'
import { ChecklistItem } from '../models/checklist-item'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'
import { DataPoint, TimeSeries } from '../models/timeseries'
import { Measurement } from '../models/measurement'
import { ArrayEntity } from '../models'
import { useEffect, useState } from 'react'

export interface TimeSeriesQueryRequest {
    externalId: string | undefined
}

const buildChecklistItemQuery = (request: TimeSeriesQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.externalId) {
        filter.push(`{ externalId: { eq: "${request.externalId}" }}`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
    query GetChecklistData {
        listChecklistItem (
          filter: ${queryFilter}
        ) {
          items {
            externalId
            status
            asset {
              externalId
              space
              title
              description
              createdTime
              lastUpdatedTime
            }
            measurements (sort: {order: ASC}) {
              items {
                timeseries {
                  externalId
                  id
                  assetId
                  name
                  description
                  unit
                  datasetId
                  getDataPoints(first: 1000) {
                    items {
                      value
                      timestamp
                    }
                  }
                  metadata
                  isStep
                  isString
                }
              }
            }
          }
        }
      }
    `

    return query
}

export const useGetTimeSeries = (request: TimeSeriesQueryRequest) => {
    const query = buildChecklistItemQuery(request)
    const { data: fdmData } = useGraphqlQuery<ChecklistItem>(gql(query), 'listChecklistItem', {})

    const [resultData, setResultData] = useState<{ data: TimeSeries | undefined; loading: boolean }>({
        data: undefined,
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: undefined, loading: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const measurements = d.measurements as any as ArrayEntity<Measurement>
                const dataPoints = measurements.items[0]?.timeseries?.getDataPoints as any as { items: DataPoint[] }

                return {
                    externalId: measurements.items[0]?.timeseries?.externalId,
                    id: measurements.items[0]?.timeseries?.id,
                    assetId: measurements.items[0]?.timeseries?.assetId,
                    name: measurements.items[0]?.timeseries?.name,
                    description: measurements.items[0]?.timeseries?.description,
                    unit: measurements.items[0]?.timeseries?.unit,
                    datasetId: measurements.items[0]?.timeseries?.datasetId,
                    getDataPoints: dataPoints?.items,
                    metadata: measurements.items[0]?.timeseries?.metadata,
                    isStep: measurements.items[0]?.timeseries?.isStep,
                    isString: measurements.items[0]?.timeseries?.isString,
                    assetName: d.asset?.title,
                    createdAt: d.asset?.createdTime,
                    updatedAt: d.asset?.lastUpdatedTime,
                    lastReading: d.asset?.lastUpdatedTime,
                }
            })

            setResultData({ data: fdmDataParsed[0], loading: false })
        }
    }, [fdmData, request])

    return {
        loading: resultData.loading,
        timeSeries: resultData.data,
    }
}
