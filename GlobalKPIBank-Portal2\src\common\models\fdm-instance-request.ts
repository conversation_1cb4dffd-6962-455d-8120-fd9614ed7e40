type FdmId = {
    externalId: string
    space: string
}
export interface FdmInstanceRequest {
    externalId: string
    instanceType: string
    sources?: FdmInstanceRequestSource[]
    space: string
    type?: FdmId
    startNode?: FdmId
    endNode?: FdmId
}

export interface FdmInstanceRequestSource {
    properties: any
    source: FdmInstanceRequestSourceDefinition
}

export interface FdmInstanceRequestSourceDefinition {
    externalId: string
    space: string
    type: string
    version: string
}
