import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface TemplateAggregateQueryRequest {
    title?: string
    externalIds?: string[]
    status?: string
    assignedTo?: string
    start?: number
    end?: number
    nextPage?: string
    includeArchived?: boolean
}

const buildTemplateAggregateQuery = (request: TemplateAggregateQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.externalIds?.length) {
        filter.push(`{ externalId: { in: [${request.externalIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.title && request.title != '') {
        filter.push(`{ title: { prefix: "${request.title}" }}`)
    }

    if (request.status && request.status != '') {
        filter.push(`{ status: { eq: "${request.status}" }}`)
    }

    if (request.assignedTo && request.assignedTo != '') {
        filter.push(`{ assignedTo: { eq: "${request.assignedTo}" }}`)
    }

    if (request.start) {
        filter.push(`{ createdTime: { gte: ${request.start} }}`)
    }

    if (request.end) {
        filter.push(`{ createdTime: { lte: ${request.end} }}`)
    }

    if (!request.includeArchived) {
        filter.push(`{ not: { isArchived: { eq: true } } }`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetTemplatesAggregate {
            aggregateTemplate(
                filter: ${queryFilter},
                after: ${request.nextPage ? `"${request.nextPage}"` : 'null'})
            {
                items {
                    count {
                        externalId
                    }
                }
                pageInfo {
                    hasPreviousPage
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    return query
}

export const useTemplatesAggregate = (request: TemplateAggregateQueryRequest) => {
    const query = buildTemplateAggregateQuery(request)

    const {
        data: fdmData,
        refetch,
        pageInfo,
    } = useGraphqlQuery<any>(gql(query), 'aggregateTemplate', { fetchPolicy: 'no-cache' })

    const [resultData, setResultData] = useState<{ data: number; loading: boolean; pageInfo: PageInfo }>({
        data: 0,
        loading: true,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: 0, loading: false, pageInfo })
        } else {
            setResultData({ data: fdmData[0].count.externalId, loading: false, pageInfo })
        }
    }, [fdmData, pageInfo])

    return {
        loadingCountTemplate: resultData.loading,
        countTemplate: resultData.data,
        refetchCountTemplate: refetch,
        pageInfo: resultData.pageInfo,
    }
}
