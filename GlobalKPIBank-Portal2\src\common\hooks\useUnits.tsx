import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from './useGraphqlQuery'
import { Unit } from '../models/unit'
import { ArrayEntity, Location } from '../models'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface UnitQueryRequest {
    externalIds?: string[]
}

const buildUnitQuery = (request: UnitQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.REF, getLocalUserSite()?.siteCode)}", "${GetSpace(EntityType.REF)}"]
            }
        }`
    )

    filter.push(`{ isActive: { eq: ${true} } }`)

    if (request.externalIds?.length) {
        filter.push(`{ externalId: { in: [${request.externalIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetReportingUnit {
            listReportingUnit(
                filter: ${queryFilter}
            , first: 1000) {
                items {
                    externalId
                    name
                    space
                    businessSegment{
                        name
                        externalId
                        space
                        description
                    }
                }
            }
        }
    `

    return query
}

export const useUnits = (request: UnitQueryRequest) => {
    const query = buildUnitQuery(request)
    const { data: fdmData } = useGraphqlQuery<Unit>(gql(query), 'listReportingUnit', {})

    const [resultData, setResultData] = useState<{ data: Unit[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayLocationEntity = d.refersTo as any as ArrayEntity<Location>

                if (arrayLocationEntity.items.length) {
                    return {
                        ...d,
                        refersTo: arrayLocationEntity.items,
                    }
                }

                return {
                    ...d,
                    refersTo: [],
                }
            })
            setResultData({ data: fdmDataParsed, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        units: resultData.data,
    }
}
