import { translate } from '@celanese/celanese-sdk'
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'
import { ManualInputTableModel } from '@/common/models/manualInputDataTableModel'
import './styles.css'
import LoaderCircular from '../../Loader'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import updateLocale from 'dayjs/plugin/updateLocale'
import { MatIcon } from '@celanese/ui-lib'
dayjs.extend(utc)
dayjs.extend(updateLocale)

interface ManualInputKPI {
    manualInputData: ManualInputTableModel[]
    loading: boolean
}

export const KpiInputManagementTable: React.FC<ManualInputKPI> = ({ manualInputData, loading }) => {
    const listMonths = [
        'JANUARY',
        'FEBRUARY',
        'MARCH',
        'APRIL',
        'MAY',
        'JUNE',
        'JULY',
        'AUGUST',
        'SEPTEMBER',
        'OCTOBER',
        'NOVEMBER',
        'DECEMBER',
    ]

    function isKPIValidForMonth(month, kpiData) {
        const flawlessDaysValid = kpiData.some(
            (entry) => entry.description === 'Flawless days' && entry[month] !== undefined && entry[month] !== 0
        )

        const employeeHeadcountValid = kpiData.some(
            (entry) =>
                entry.description === 'Celanese Employee Headcount' && entry[month] !== undefined && entry[month] !== 0
        )

        const contractorHeadcountValid = kpiData.some(
            (entry) => entry.description === 'Contractor Headcount' && entry[month] !== undefined && entry[month] !== 0
        )

        return flawlessDaysValid && employeeHeadcountValid && contractorHeadcountValid
    }

    return (
        <>
            <TableContainer sx={{ overflow: 'auto', height: 'calc(100% - 60px)' }}>
                <Table stickyHeader>
                    <TableHead>
                        <TableRow>
                            <TableCell align="left" sx={{ width: '10%' }}>
                                {translate('Site')}
                            </TableCell>
                            {Array.from({ length: 12 }).map((_, index) => (
                                <TableCell key={index} align="center" sx={{ minWidth: '5%', padding: '0px 3px' }}>
                                    {translate(`TABLE_COLS.MONTHS.${listMonths[index]}`)}
                                </TableCell>
                            ))}
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {loading
                            ? LoaderCircular()
                            : manualInputData &&
                              manualInputData.length > 0 &&
                              manualInputData.map((row, index) => {
                                  return (
                                      <>
                                          <TableRow key={index}>
                                              <TableCell align="left" sx={{ width: '7%', border: 'none' }}>
                                                  {row.data.name}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('january', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('february', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('march', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('april', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('may', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('june', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('july', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('august', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('september', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('october', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('november', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                              <TableCell
                                                  align="center"
                                                  sx={{
                                                      width: '5%',
                                                      border: 'none',
                                                  }}
                                              >
                                                  {isKPIValidForMonth('december', row.kpi) ? (
                                                      <MatIcon
                                                          icon="check_circle"
                                                          sx={{ color: '#1F8248' }}
                                                          fontSize="18px"
                                                      />
                                                  ) : (
                                                      <MatIcon icon="error" sx={{ color: '#ED6C02' }} fontSize="18px" />
                                                  )}
                                              </TableCell>
                                          </TableRow>
                                      </>
                                  )
                              })}
                    </TableBody>
                </Table>
            </TableContainer>
        </>
    )
}
