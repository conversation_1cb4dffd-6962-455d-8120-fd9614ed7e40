'use client'
import { useState } from 'react'

import ExtensionOutlinedIcon from '@mui/icons-material/ExtensionOutlined'
import ManageSearchIcon from '@mui/icons-material/ManageSearch'
import SpeedDial from '@mui/material/SpeedDial'
import SpeedDialAction from '@mui/material/SpeedDialAction'
import { useTheme } from '@mui/material/styles'

import { useContextualizationControl } from '@celanese/contextualization-lib'
import { translate } from '@celanese/celanese-sdk'

export const AppSpeedDial = () => {
    const [open, setOpen] = useState(false)
    const theme = useTheme()

    const { handleToggleModal } = useContextualizationControl()

    const toggleOpen = () => setOpen(!open)

    return (
        <SpeedDial
            icon={<ExtensionOutlinedIcon />}
            ariaLabel="Celanese tools speed dial"
            open={open}
            onClick={toggleOpen}
            sx={{
                position: 'fixed',
                bottom: 16,
                right: 16,

                '.MuiSpeedDial-fab': {
                    background: theme.palette.info.main,

                    '&: hover': {
                        background: theme.palette.info.main,
                    },
                },
            }}
        >
            <SpeedDialAction
                icon={<ManageSearchIcon />}
                onClick={() => handleToggleModal(true)}
                tooltipTitle={translate('CONTEXTUALIZATION')}
            ></SpeedDialAction>
        </SpeedDial>
    )
}
