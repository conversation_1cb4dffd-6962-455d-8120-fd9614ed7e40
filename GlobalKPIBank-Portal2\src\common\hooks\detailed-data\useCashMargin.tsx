import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '../useGraphqlQuery'
import { DataModelView } from '@/common/models/dataModelView'
import { CashMarginTableView } from '@/common/models/cashMarginTable'
import { CashMarginKpi } from '@/common/models/cashMargin'
import dayjs from 'dayjs'
import { getCustomQuarterAndYearQuery } from '@/common/utils/kpis-data-table-export'
const advancedFormat = require('dayjs/plugin/advancedFormat')
dayjs.extend(advancedFormat)

const queryBuilder = (request: DataModelView) => {
    let siteQ: string = ''
    let businessSegmentQ: string = ''
    let dateQ: string = ''
    let limitQ: string = ''
    let cursorQ: string = ''

    if (request && request.kpiFilters.refSite.length > 0) {
        const sites = request.kpiFilters.refSite.map((site) => `"${site}"`).join(',')
        siteQ = `{reportingSite: {externalId: {in: [${sites}]}}},`
    }

    if (request && request.kpiFilters.businessSegment !== '') {
        businessSegmentQ = `{businessSegment: {externalId: {eq: "${request.kpiFilters.businessSegment}"}}},`
    }

    if (request && request.kpiFilters.date !== '') {
        dateQ = getCustomQuarterAndYearQuery(request.exportFilters.range, request.kpiFilters.date)
    }

    if (request && request.limit && request.limit !== '') {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor && request.cursor !== '') {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getKpiDataView {
        listCashMargin (
            ${limitQ}
            ${cursorQ}
            filter: {and: [${siteQ} ${businessSegmentQ} ${dateQ}]}
        ) {
            pageInfo {
                hasNextPage
                endCursor
            }
            items {
                reportingSite {
                    externalId
                    name
                }
                pid
                pidDesc
                variableMargin
                fixedDistributionCost
                otherFixedCost
                plantPeriodCost
                energyCost
                salesVolume
                businessSegment {
                    externalId
                    description
                }
            }
        }
    }`
}

const mapCashMargin = (data: CashMarginKpi[]): CashMarginTableView[] => {
    const mappedResult: CashMarginTableView[] = []

    if (data && data.length > 0) {
        data.map((item) => {
            const result: CashMarginTableView = {
                siteName: item.reportingSite?.name ?? '',
                pid: item.pid ?? '',
                pidDescription: item.pidDesc ?? '',
                variableMargin: item.variableMargin,
                fixedDistributionCost: item.fixedDistributionCost,
                otherFixedCost: item.otherFixedCost,
                plantPeriodCost: item.plantPeriodCost,
                energyCost: item.energyCost,
                salesVolume: item.salesVolume,
                businessSegment: item.businessSegment?.description ?? '',
            }
            mappedResult.push(result)
        })
    }
    return mappedResult
}

export const useCashMargin = (request: DataModelView) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        pageInfo: fdmPageInfo,
        loading,
        refetch,
    } = useGraphqlQuery<any>(gql(query), 'listCashMargin', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'executiveView',
        },
    })

    const [resultData, setResultData] = useState<{ data: any[] }>({
        data: [],
    })

    const [pageInfoData, setPageInfoData] = useState<PageInfo>({
        hasPreviousPage: false,
        hasNextPage: false,
        startCursor: '',
        endCursor: '',
    })

    const [loadMore, setLoadMore] = useState<boolean>(false)

    const loadMoreExport = () => {
        if (!pageInfoData.hasNextPage) return
        setLoadMore(!loadMore)
    }

    const triggerFilter = () => {
        setPageInfoData({
            hasPreviousPage: false,
            hasNextPage: false,
            startCursor: '',
            endCursor: '',
        })
        setResultData({ data: [] })
        setLoadMore(!refetch)
    }

    useEffect(() => {
        if (request.kpiFilters.kpiName !== '') {
            if (fdmData === undefined) {
                setResultData((prevState) => ({ ...prevState, loading: true }))
            } else {
                if (request.exportFilters.isExport) {
                    const mappedResult = mapCashMargin(fdmData)
                    setResultData({ data: mappedResult })
                    setPageInfoData(fdmPageInfo)
                }
                if (!request.exportFilters.isExport && request.exportFilters.range === 0) {
                    const mappedResult = mapCashMargin(fdmData)
                    setResultData((prevData) => ({ data: [...(prevData.data ?? []), ...mappedResult] }))
                    setPageInfoData(fdmPageInfo)
                }
            }
        }
    }, [fdmData, request.kpiFilters.kpiName, loadMore])

    return {
        kpiDataModelView: resultData.data,
        pageInfoDataModelView: pageInfoData,
        loadingDataModelView: loading,
        refetchDataModelView: triggerFilter,
        loadMoreExport: loadMoreExport,
    }
}
