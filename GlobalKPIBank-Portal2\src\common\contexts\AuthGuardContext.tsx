import { UserRolesPermission, FeatureSitesMapping, UserSite } from '@/common/clients/user-management-client'
import { createContext } from 'react'

interface AuthGuardContextState {
    userInfo: UserRolesPermission
    featureSitesMapping: FeatureSitesMapping[]
    updateSiteInfo: (siteInfo: UserSite) => void
    loading: boolean
}

export const AuthGuardContext = createContext<AuthGuardContextState>({
    userInfo: {
        displayName: '',
        firstName: '',
        lastName: '',
        email: '',
        companyName: '',
        department: '',
        jobTitle: '',
        lanId: '',
        avatar: '',
        applications: [],
        sites: [],
        units: [],
    },
    featureSitesMapping: [],
    updateSiteInfo: (siteInfo: UserSite) => {},
    loading: false,
})
