import React, { useEffect, useState } from 'react'
import {
    Autocomplete,
    AutocompleteProps,
    Box,
    Checkbox,
    Chip,
    Divider,
    FormControlLabel,
    Paper,
    TextField,
    TextFieldProps,
} from '@mui/material'
import { CheckBox as CheckBoxIcon, CheckBoxOutlineBlank, IndeterminateCheckBoxOutlined } from '@mui/icons-material'
import { translate } from '@celanese/celanese-sdk'

const icon = <CheckBoxOutlineBlank fontSize="small" />
const checkedIcon = <CheckBoxIcon fontSize="small" />
const indeterminateIcon = <IndeterminateCheckBoxOutlined fontSize="small" />

type CheckboxState = 'checked' | 'unchecked' | 'indeterminate'

type ExtendedSlotProps = NonNullable<AutocompleteProps<any, true, false, false>['slotProps']> & {
    textField?: Partial<TextFieldProps>
}

export interface StandaloneMultiAutoCompleteProps<T>
    extends Omit<
        AutocompleteProps<T, true, false, false>,
        'renderInput' | 'renderOption' | 'renderTags' | 'label' | 'slotProps'
    > {
    id: string
    label: string
    labelField?: keyof T
    valueField?: keyof T
    slotProps?: ExtendedSlotProps
    onSelectAll: (value: T[]) => void
    onItemRemoved: (option: T) => void
}

function StandaloneMultiAutoComplete<T>({
    label,
    valueField = 'externalId' as keyof T,
    labelField = 'name' as keyof T,
    onSelectAll,
    onItemRemoved,
    slotProps,
    ...props
}: StandaloneMultiAutoCompleteProps<T>) {
    const [checkboxState, setCheckboxState] = useState<CheckboxState>('checked')
    const getCheckboxState = (value: T[], options: readonly T[]): CheckboxState => {
        if (value.length === options?.length) {
            return 'checked'
        }
        return value.length ? 'indeterminate' : 'unchecked'
    }

    useEffect(() => {
        setCheckboxState(getCheckboxState(props.value, props.options))
    }, [props.value, props.options])

    return (
        <Box>
            <Autocomplete
                size="small"
                sx={{ width: '100%', height: '100%' }}
                disableCloseOnSelect
                disableClearable
                multiple
                {...props}
                onChange={(event, newValue, reason, details) => {
                    setCheckboxState(getCheckboxState(newValue, props.options))
                    props.onChange?.(event, newValue, reason, details)
                }}
                getOptionLabel={(option) => String(option[labelField])}
                renderOption={(optionProps, option, { selected }) => (
                    <li {...optionProps} key={`${optionProps.id}-${String(option[valueField])}`}>
                        <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                        <span>{option[labelField] as string}</span>
                    </li>
                )}
                renderTags={(value, getTagProps) => {
                    const numTags = value.length
                    const limit = props.limitTags ?? numTags
                    return (
                        <>
                            {value.slice(0, limit).map((option, index) => (
                                <Chip
                                    {...getTagProps({ index })}
                                    size="small"
                                    key={`${props.id}-chip-${String(option?.[valueField])}`}
                                    label={option?.[labelField] as string}
                                    onDelete={() => onItemRemoved?.(option)}
                                />
                            ))}
                            {numTags > limit && (
                                <Chip
                                    size="small"
                                    label={`+${numTags - limit}`}
                                    sx={{ cursor: 'default', pointerEvents: 'none' }}
                                />
                            )}
                        </>
                    )
                }}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        label={label}
                        size="small"
                        sx={{ backgroundColor: 'background.paper' }}
                        {...(slotProps?.textField ?? {})}
                    />
                )}
                PaperComponent={({ children, ...rest }) => (
                    <Paper {...rest}>
                        <Box onMouseDown={(e) => e.preventDefault()} pl={1.5} py={0.5}>
                            <FormControlLabel
                                onClick={(e) => {
                                    e.preventDefault()
                                    onSelectAll?.(props.value)
                                }}
                                label={translate('COMMONFILTER.ALL')}
                                control={
                                    <Checkbox
                                        id="select-all-checkbox"
                                        icon={icon}
                                        checkedIcon={checkedIcon}
                                        indeterminateIcon={indeterminateIcon}
                                        checked={checkboxState === 'checked'}
                                        indeterminate={checkboxState === 'indeterminate'}
                                    />
                                }
                            />
                        </Box>
                        <Divider />
                        {children}
                    </Paper>
                )}
            />
        </Box>
    )
}

export default StandaloneMultiAutoComplete
