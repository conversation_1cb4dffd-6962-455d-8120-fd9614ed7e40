import { gql } from '@apollo/client'
import { GlobalVisionFavoritesFilters } from '../models/globalVisionFavoritesFilters'
import { useGraphqlQuery } from './useGraphqlQuery'
import { useEffect, useState } from 'react'

export interface GlobalVisionFavoritesFiltersRequest {
    user: string
    limit?: string
    cursor?: string
}

const queryBuilder = (request: GlobalVisionFavoritesFiltersRequest) => {
    let userQ: string = ''
    let limitQ: string = 'first: 1000'
    let cursorQ: string = ''

    if (request.user !== '' || request.user !== null || request.user !== undefined) {
        userQ = `{refUser: {externalId: {eq: "${request.user}"}}},`
    }

    if (request.limit && request.limit.length > 0) {
        limitQ = `first: ${request.limit}`
    }

    if (request.cursor) {
        cursorQ = `after: "${request.cursor}"`
    }

    return `query getGlobalViewFavoritesFilters {
        listGlobalVisionFavoritesFilters (
          ${limitQ}
          ${cursorQ}
          filter: {and: [${userQ}]}
          ) {
          pageInfo {
            hasNextPage
            endCursor
          }
          items {
            externalId
            name
            space
            refReportingSites {
                items{
                    externalId
                    name
                    description
                    space
                }
            }
            refUser {
                externalId
                space
            }
            order
          }
        }
      }
  `
}

export const useGlobalVisionKPI = (request: GlobalVisionFavoritesFiltersRequest) => {
    const query = queryBuilder(request)
    const {
        data: fdmData,
        refetch,
        loading,
    } = useGraphqlQuery<GlobalVisionFavoritesFilters>(gql(query), 'listGlobalVisionFavoritesFilters', {
        fetchPolicy: 'network-only',
        context: {
            clientName: 'globalView',
        },
    })

    const [resultData, setResultData] = useState<{ data: GlobalVisionFavoritesFilters[] }>({
        data: [],
    })

    useEffect(() => {
        if (fdmData === undefined) {
            setResultData((prevState) => ({ ...prevState }))
        } else {
            if (fdmData && fdmData.length > 0) {
                const result_aggregation = [...fdmData].sort((a, b) => a.name.localeCompare(b.name))
                setResultData({ data: result_aggregation })
            } else {
                setResultData({ data: [] })
            }
        }
    }, [fdmData, request.user])

    return {
        loadingGlobalVisionKPI: loading,
        refetchGlobalVisionKPI: refetch,
        dataGlobalVisionKPI: resultData.data,
    }
}
