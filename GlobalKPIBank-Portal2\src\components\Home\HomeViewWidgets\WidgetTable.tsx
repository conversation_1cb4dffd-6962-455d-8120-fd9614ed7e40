import { Box, TableCell, TableRow } from '@mui/material'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { WidgetSingleSite } from './WidgetSingleSite'
import { WidgetMultiSites } from './WidgetMultiSites'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'

export const WidgetTable = ({
    openRows,
    handleToggle,
    filters,
    globalViewTarget,
    data,
    setOpenModal,
    setSelectedKpi,
}) => {
    const { setSelectedSiteId } = useGlobalKpiContext()
    const [filteredData, setFilteredData] = useState([])
    const productivity = {
        'Productivity - Year Forecast': `Productivity - ${dayjs().year()}`,
        'Productivity Pipeline - Next Year': `Productivity Pipeline - ${dayjs().add(1, 'year').year()}`,
        'Productivity Pipeline - +2 Years': `Productivity Pipeline - ${dayjs().add(2, 'year').year()}`,
    }

    const widgetClick = (data: any) => {
        setOpenModal(data.openModal)
        setSelectedKpi(data.kpi)
        setSelectedSiteId(data.site.externalId)
    }

    useEffect(() => {
        const excludedKpis = ['KPIG-FPV', 'KPIG-APV', 'KPIG-ACS', 'KPIG-FCS']
        const cleanedData = data.filter((kpi) => !excludedKpis.includes(kpi.kpi.externalId))

        const selectedKpis = filters.kpiList?.map((kpi) => kpi.externalId)
        const indexToReplace = Object.keys(productivity).map((item) => selectedKpis.indexOf(item))
        indexToReplace.forEach((value, index) => {
            if (value !== -1) {
                selectedKpis[value] = Object.values(productivity)[index]
            }
        })

        const filteredResult = cleanedData.filter((kpi) => selectedKpis?.includes(kpi.kpi.externalId))
        if (filteredResult.length === 0) {
            setFilteredData(cleanedData)
        } else {
            setFilteredData(filteredResult)
        }
    }, [data])

    return (
        <>
            {filters.siteList.length === 1 ? (
                <TableRow sx={{ borderBottom: 'unset' }}>
                    <TableCell sx={{ borderBottom: 'unset' }}>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                gap: '1px',
                                padding: '0px 0px',
                            }}
                        >
                            {filteredData.map((kpi, index) => {
                                const currentKpi = kpi.kpi.name
                                const currentTargetKpi = globalViewTarget.filter(
                                    (targetKpi) => targetKpi.refGlobalVisionKPI.name === currentKpi
                                )
                                const segmentIndex = kpi.data.find(
                                    (segment) =>
                                        segment.refBusinessSegment?.externalId === filters.businessSegment.externalId
                                )
                                let kpiValue = 0
                                if (segmentIndex)
                                    kpiValue = Number.parseFloat(
                                        segmentIndex[dayjs(filters.period).format('MMMM').toLowerCase()]
                                    )
                                else
                                    kpiValue = Number.parseFloat(
                                        kpi.data[0][dayjs(filters.period).format('MMMM').toLowerCase()]
                                    )
                                const targetValue = currentTargetKpi[0]?.value ?? 0
                                const isRed =
                                    currentTargetKpi[0]?.rangeDirection === 'Above'
                                        ? kpiValue < targetValue
                                        : kpiValue > targetValue
                                const diff = kpiValue - targetValue

                                return (
                                    <WidgetSingleSite
                                        key={index}
                                        isRed={isRed}
                                        kpiValue={kpiValue}
                                        kpi={kpi}
                                        diff={diff}
                                        onClick={(data) => widgetClick(data)}
                                    />
                                )
                            })}
                        </Box>
                    </TableCell>
                </TableRow>
            ) : (
                <>
                    {filteredData.map((kpi, index) => {
                        const currentKpi = kpi.kpi.name
                        const currentTargetKpi = globalViewTarget.filter(
                            (targetKpi) => targetKpi.refGlobalVisionKPI.name === currentKpi
                        )
                        const hasSegmentFilter = kpi.data.filter(
                            (segment) => segment.refBusinessSegment?.externalId === filters.businessSegment.externalId
                        )
                        const filteredData = hasSegmentFilter.length > 0 ? hasSegmentFilter : kpi.data

                        return (
                            <WidgetMultiSites
                                openRows={openRows}
                                handleToggle={handleToggle}
                                key={index}
                                index={index}
                                filters={filters}
                                filteredData={filteredData}
                                kpi={kpi}
                                currentTargetKpi={currentTargetKpi}
                                onClick={(data) => widgetClick(data)}
                            />
                        )
                    })}
                </>
            )}
        </>
    )
}
