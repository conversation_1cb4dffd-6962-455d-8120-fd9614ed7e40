import { TemplateConfig, ArrayEntity } from '@/common/models'
import { ShiftConfiguration } from '@/common/models/shiftConfiguration'
import { getLocalUserSite } from '@/common/utils'
import { GetSpace, EntityType } from '@/common/utils/space-util'
import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { PageInfo, useGraphqlQuery } from '..'

export interface TemplateConfigHookQueryRequest {
    templateIds?: string[]
    limit?: number
    skip?: boolean
    end?: number
    nextPage?: string
}

const buildTemplateConfigsQuery = (request: TemplateConfigHookQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.templateIds?.length) {
        filter.push(`{ template: { externalId: { in: [${request.templateIds.map((e) => `"${e}"`).join(',')}] }}}`)
    }

    if (request.end) {
        filter.push(`{ createdTime: { lte: ${request.end} }}`)
    }

    filter.push(
        `{
            space: {
                in: [ "${GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.Instance
        )}"]
            }
        }`
    )

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    const query = `
        query GetChecklistTemplateConfig {
            listChecklistTemplateConfig(
                filter: ${queryFilter}, after: ${request.nextPage ? `"${request.nextPage}"` : 'null'} 
            , first: 1000) {
                items {
                    externalId
                    reportingUnit {
                      externalId
                    }
                  	reportingLocation {
                      externalId
                    }
                    daysUntilDue
                    shiftConfigurations {
                        items {
                            externalId
                            shiftName
                        }
                    }
                }
                pageInfo {
                    hasPreviousPage
                    hasNextPage
                    startCursor
                    endCursor
                }
            }
        }
    `

    return query
}

export const useTemplateConfigsHook = (request: TemplateConfigHookQueryRequest) => {
    const query = buildTemplateConfigsQuery(request)
    const {
        data: fdmData,
        refetch,
        pageInfo,
    } = useGraphqlQuery<TemplateConfig>(gql(query), 'listChecklistTemplateConfig', {})

    const [resultData, setResultData] = useState<{ data: TemplateConfig[]; loading: boolean; pageInfo: PageInfo }>({
        data: [],
        loading: true,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false, pageInfo })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayShiftConfigurationsEntity = d.shiftConfigurations as any as ArrayEntity<ShiftConfiguration>

                return {
                    ...d,
                    shiftConfigurations: arrayShiftConfigurationsEntity.items,
                }
            })

            setResultData({ data: fdmDataParsed, loading: false, pageInfo })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        templateConfigs: resultData.data,
        pageInfoTemplateHook: resultData.pageInfo,
        refetchTemplateConfig: refetch,
    }
}
