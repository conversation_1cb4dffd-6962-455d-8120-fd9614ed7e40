import { useCognite } from './useCognite'
import { useCallback, useState } from 'react'
import { EdgeInstance } from '../models/edge-instance'
import { Entity, MutationFunction, MutationFunctionResult, MutationHookResult } from '../models'
import { DeleteNodeOptions } from '../clients'

export function useFdmMutation<T extends Entity>(
    entityName: string,
    autoCreateDirectRelations?: boolean,
    replace?: boolean
): MutationHookResult<T> {
    const { fdmClient: client } = useCognite()
    const [data, setData] = useState<any | undefined>()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)

    const reset = useCallback(() => {
        setData(undefined)
        setError(undefined)
        setLoading(false)
        setCalled(false)
    }, [])

    const mutationFunction: MutationFunction<T> = useCallback(
        (
            nodes: T[],
            edges: EdgeInstance[],
            nodesToDelete?: DeleteNodeOptions,
            nodeSpace?: string,
            sourceSpace?: string,
            version?: string
        ): Promise<MutationFunctionResult<T>> => {
            setLoading(true)
            setCalled(true)
            if (nodesToDelete) {
                return client
                    .deleteNodesOrEdges(nodesToDelete)
                    .then((response) => {
                        setData(response)
                        return {
                            ok: true,
                            data: response,
                        }
                    })
                    .catch((error) => {
                        console.error(error)
                        setError(error)
                        throw error
                    })
                    .finally(() => setLoading(false))
            }

            if (!nodes?.length) {
                return Promise.resolve({ ok: true })
            }

            if (edges?.length) {
                return client
                    .upsertNodesAndEdges({ entityName, nodes, edges, autoCreateDirectRelations })
                    .then((response) => {
                        setData(response)
                        return {
                            ok: true,
                            data: response,
                        }
                    })
                    .catch((error) => {
                        console.error(error)
                        setError(error)
                        throw error
                    })
                    .finally(() => setLoading(false))
            }

            return client
                .upsertNodes({ entityName, nodes, autoCreateDirectRelations, replace, nodeSpace, sourceSpace, version })
                .then((response) => {
                    setData(response)
                    return {
                        ok: true,
                        data: response,
                    }
                })
                .catch((error) => {
                    console.error(error)
                    setError(error)
                    throw error
                })
                .finally(() => setLoading(false))
        },
        [client, entityName, autoCreateDirectRelations]
    )

    return [mutationFunction, { data, error, loading, called, client, reset }]
}
