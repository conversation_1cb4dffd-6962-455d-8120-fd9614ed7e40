import { useCallback, useState } from 'react'
import { useCognite } from '../useCognite'

export type AggregateHookResult = [AggregateFunction, AggregateState]

export type AggregateFunctionParams = {
    externalId: string
    filter?: any
    groupBy?: string[]
    propetyCount?: string
    space?: string
}

export type ItensFunctionPromise = {
    items: AggregateFunctionPromise[]
}

export type AggregateFunctionPromise = {
    aggregates: AggregateParams[]
    group: any
}

export type AggregateParams = {
    aggregate: string
    property: string
    value: string
}

export type AggregateFunction = (params: AggregateFunctionParams) => Promise<ItensFunctionPromise>

export type AggregateState = {
    loading: boolean
    called: boolean
    error: any
}

export function useFdmAgregate(): AggregateHookResult {
    const { fdmClient: client } = useCognite()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)
    const testFunction = useCallback(
        ({ externalId, filter, propetyCount, space, groupBy }: AggregateFunctionParams) => {
            setLoading(true)
            setCalled(true)
            return client
                .aggregateNodesOrEdges({
                    externalId: externalId,
                    propetyCount: propetyCount ?? 'externalId',
                    filter,
                    groupBy,
                    space: space ?? 'cdf_apm',
                })
                .catch((error) => setError(error))
                .finally(() => setLoading(false))
        },
        [client]
    )
    return [testFunction, { error, loading, called }]
}
