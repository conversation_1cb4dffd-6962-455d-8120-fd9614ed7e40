import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'
import { GlobalVisionKpiTableModel } from '@/common/models/globalVisionKpiTableModel'
import { GlobalVisionKpiTarget } from '@/common/models/globalVisionKpiTarget'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { StripedRow } from '@/common/utils/stripedRowTable'
import { numberFormat } from '@/common/utils/numberFormat'
import { useGlobalKpiContext } from '@/common/contexts/GlobalKpiContext'
import { calculateSingleTarget, calculateMultiTarget } from '@/common/utils/targetCalculator'
import { translate } from '@celanese/celanese-sdk'
import { getKpiValue } from '@/common/utils/kpiCalculator'
import { GeneralFilter } from '@/common/models/homeGeneralFilter'

interface WidgetGeneralTableProps {
    data: GlobalVisionKpiTableModel[]
    target: GlobalVisionKpiTarget[]
    filters: any
    generalFilter: GeneralFilter
    generalViewMore: boolean
    setOpenModal: Dispatch<SetStateAction<boolean>>
    setSelectedKpi: Dispatch<SetStateAction<GlobalVisionKpiTableModel>>
}

export const WidgetGeneralTable: React.FC<WidgetGeneralTableProps> = ({
    data,
    target,
    filters,
    generalFilter,
    generalViewMore,
    setOpenModal,
    setSelectedKpi,
}) => {
    const { setSelectedSiteId, setSelectedMultipleSiteIds } = useGlobalKpiContext()
    const [sanitizedData, setSanitizedData] = useState<GlobalVisionKpiTableModel[]>([])

    const getTargetValue = (kpi: GlobalVisionKpiTableModel): number => {
        const targetObject = getTargetObject(kpi)
        if (!targetObject || targetObject.length === 0 || !targetObject[0]) {
            return 0
        }
        if (filters.siteList.length === 1) {
            return calculateSingleTarget(kpi.kpi.externalId, targetObject[0], generalFilter)
        } else {
            return calculateMultiTarget(kpi, targetObject, generalFilter)
        }
    }
    const getTargetObject = (kpi: GlobalVisionKpiTableModel): GlobalVisionKpiTarget[] => {
        if (!target || target.length === 0) {
            return []
        }

        if (filters.siteList.length === 1) {
            const matchedKpi = target.filter(
                (item) => item && item.refGlobalVisionKPI && item.refGlobalVisionKPI.externalId === kpi.kpi.externalId
            )
            const matchedSite = matchedKpi?.filter(
                (item) =>
                    item &&
                    item.refReportingSite &&
                    kpi.data &&
                    kpi.data[0] &&
                    kpi.data[0].refReportingSite &&
                    item.refReportingSite.externalId === kpi.data[0].refReportingSite.externalId
            )

            return matchedSite || []
        } else {
            const matchedKpi = target.filter(
                (item) => item && item.refGlobalVisionKPI && item.refGlobalVisionKPI.externalId === kpi.kpi.externalId
            )
            const matchedSites = matchedKpi.filter(
                (item) => item && item.refReportingSite && filters.siteList.includes(item.refReportingSite.externalId)
            )

            return matchedSites || []
        }
    }
    const isRedKpi = (kpi: GlobalVisionKpiTableModel): boolean => {
        const currentTarget = getTargetObject(kpi)
        let targetValue: number = 0
        let kpiValue: number = 0

        if (!currentTarget || currentTarget.length === 0 || !currentTarget[0]) {
            return false
        }

        if (filters.siteList.length === 1) {
            targetValue = calculateSingleTarget(kpi.kpi.externalId, currentTarget[0], generalFilter)
            kpiValue = getKpiValue(kpi, filters.period, generalFilter)
        } else {
            targetValue = calculateMultiTarget(kpi, currentTarget, generalFilter)
            kpiValue = getKpiValue(kpi, filters.period, generalFilter)
        }

        return currentTarget[0]?.rangeDirection === 'Above' ? kpiValue < targetValue : kpiValue > targetValue
    }
    const widgetClick = (kpi: GlobalVisionKpiTableModel) => {
        setOpenModal(true)
        setSelectedKpi(kpi)
        if (kpi.data.length === 1) {
            setSelectedSiteId(kpi.data[0].refReportingSite.externalId)
            setSelectedMultipleSiteIds([])
        } else {
            setSelectedSiteId('')
            setSelectedMultipleSiteIds(filters.siteList)
        }
    }

    useEffect(() => {
        if (data && data.length > 0) {
            try {
                data.map((kpi: GlobalVisionKpiTableModel) => {
                    try {
                        kpi.redKpi = isRedKpi(kpi)
                    } catch (error) {
                        kpi.redKpi = false
                    }
                })

                const mappedResult = [...data]
                mappedResult.sort((a, b) => {
                    if (a.redKpi === b.redKpi) {
                        const orderA = a.kpi.order || 0
                        const orderB = b.kpi.order || 0
                        return orderA - orderB
                    }
                    return a.redKpi ? -1 : 1
                })

                setSanitizedData(mappedResult)
                console.log('**********', mappedResult)
            } catch (error) {
                setSanitizedData([...data])
                console.log('**********', [...data])
            }
        } else {
            setSanitizedData([])
        }
    }, [data])

    return (
        <TableContainer sx={{ overflowX: 'initial' }}>
            <Table>
                <TableHead
                    sx={{
                        position: 'sticky',
                        top: generalViewMore ? '45.97px' : 0,
                        backgroundColor: '#ffffff',
                        fontWeight: 'bold',
                    }}
                >
                    <TableRow>
                        <TableCell align="left" sx={{ width: '60%', padding: '8px 16px' }}>
                            {translate('TABLE_COLS.KPI')}
                        </TableCell>
                        <TableCell align="right" sx={{ width: '20%', padding: '8px 16px' }}>
                            {translate('TABLE_COLS.TARGET')}
                        </TableCell>
                        <TableCell align="right" sx={{ width: '20%', padding: '8px 16px' }}>
                            {translate('TABLE_COLS.ACTUAL')}
                        </TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {sanitizedData.map((kpi: GlobalVisionKpiTableModel, index: number) => (
                        <StripedRow
                            index={index}
                            key={index}
                            sx={{
                                borderBottom: kpi.redKpi ? '1px solid #D32F2F' : '1px solid rgba(224, 224, 224, 1)',
                                backgroundColor: kpi.redKpi ? '#FCF1EE !important' : 'inherit',
                            }}
                            onClick={() => widgetClick(kpi)}
                            style={{ cursor: 'pointer' }}
                        >
                            <TableCell
                                align="left"
                                sx={{
                                    padding: '8px 16px',
                                    borderBottom: 'unset',
                                    width: '60%',
                                }}
                            >
                                <Box
                                    sx={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                        fontWeight: '500',
                                        color: kpi.redKpi ? '#D32F2F' : 'inherit',
                                    }}
                                >
                                    {kpi.kpi.name +
                                        (kpi.kpi.symbol != null && kpi.kpi.symbol != ''
                                            ? ' (' + kpi.kpi.symbol + ')'
                                            : '')}
                                </Box>
                            </TableCell>
                            <TableCell
                                align="right"
                                sx={{
                                    width: '20%',
                                    color: kpi.redKpi ? '#D32F2F' : '#1F8248',
                                    fontWeight: '600',
                                    borderBottom: 'inherit',
                                    padding: '8px 16px',
                                }}
                            >
                                {(() => {
                                    try {
                                        return numberFormat(getTargetValue(kpi))
                                    } catch (error) {
                                        return '-'
                                    }
                                })()}
                            </TableCell>
                            <TableCell
                                align="right"
                                sx={{
                                    width: '20%',
                                    color: kpi.redKpi ? '#D32F2F' : '#1F8248',
                                    fontWeight: '600',
                                    borderBottom: 'inherit',
                                    padding: '8px 16px',
                                }}
                            >
                                {(() => {
                                    try {
                                        return numberFormat(getKpiValue(kpi, filters.period, generalFilter))
                                    } catch (error) {
                                        return '-'
                                    }
                                })()}
                            </TableCell>
                        </StripedRow>
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    )
}
