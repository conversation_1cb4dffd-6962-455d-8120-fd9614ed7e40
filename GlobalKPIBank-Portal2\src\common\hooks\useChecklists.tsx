import { gql } from '@apollo/client'
import { useEffect, useMemo, useState } from 'react'
import { PageInfo, useGraphqlQuery } from './useGraphqlQuery'
import { Checklist } from '../models/checklist'
import { ArrayEntity } from '../models'
import { ChecklistItem } from '../models/checklist-item'
import { Observation } from '../models/observation'
import { Measurement } from '../models/measurement'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export interface ChecklistQueryRequest {
    title?: string
    status?: string
    assignedTo?: string
    templateIds?: string[]
    externalIds?: string[]
    start?: number
    end?: number
    nextPage?: string
    first?: number
    rangeByLastUpdatedTime?: boolean
    limitForItems?: number
}

const buildChecklistQuery = (request: ChecklistQueryRequest): string => {
    const filter = []

    let queryFilter = '{ }'

    if (request.title && request.title != '') {
        filter.push(`{ title: { eq: "${request.title}" }}`)
    }

    if (request.externalIds?.length) {
        filter.push(`{ externalId: { in: [${request.externalIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.templateIds?.length) {
        filter.push(`{ sourceId: { in: [${request.templateIds.map((e) => `"${e}"`).join(',')}] }}`)
    }

    if (request.status && request.status != '') {
        filter.push(`{ status: { eq: "${request.status}" }}`)
    }

    if (request.assignedTo && request.assignedTo != '') {
        filter.push(`{ assignedTo: { containsAny: ["${request.assignedTo}"] }}`)
    }

    if (request.end && request.start) {
        if (request.rangeByLastUpdatedTime) {
            filter.push(`{ lastUpdatedTime: { gte: ${request.start} }}`)
            filter.push(`{ lastUpdatedTime: { lte: ${request.end} }}`)
        } else {
            filter.push(`{
            or: [
              {
                and: [
                  { startTime: { gte: ${request.start} }},
                  { startTime: { lte: ${request.end} }}
                ]
              },
              {
                and: [
                  { endTime: { gte: ${request.start} }},
                  { endTime: { lte: ${request.end} }}
                ]
              }
            ]
          }`)
        }
    }

    filter.push(
        `{
          space: {
            in: [ "${GetSpace(EntityType.APMA, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.APMATEMP,
            getLocalUserSite()?.siteCode
        )}", "${GetSpace(EntityType.APMATEMP)}", "${GetSpace(EntityType.APMA)}"]
          }
      }`
    )

    filter.push(`{ not: { isArchived: { eq: true } } }`)

    if (filter.length > 0) {
        queryFilter = `{ and: [ ${filter.join(',')} ]}`
    }

    if (!request.first) {
        request.first = 100
    }

    const query = `
    query GetChecklistData {
        listChecklist(
            filter: ${queryFilter}
        , first: ${request.first}, after: ${request.nextPage ? `"${request.nextPage}"` : 'null'}) {
          items {
            externalId
            space
            sourceId
            title
            description
            type
            status
            startTime
            lastUpdatedTime
            endTime
            assignedTo
            updatedBy {
              name
            }
            checklistItems (first: ${request.limitForItems ?? 1000}) {
              items {
                externalId
                space
                sourceId
                title
                description
                labels
                order
                status
                note
                startTime
                endTime
                asset {
                  externalId
                  space
                  title
                  description
                }
                files {
                  externalId
                  mimeType
                  name
                  metadata
                  downloadLink {
                      downloadUrl
                  }
                }
                measurements (sort: {order: ASC}) {
                  items {
                    externalId
                    space
                    type
                    order
                    timeseries {
                      externalId
                      id
                      assetId
                      name
                      description
                      unit
                    }
                    measuredAt
                    min
                    max
                    numericReading
                    stringReading
                    options
                    title
                  }
                }
                lastUpdatedTime
                observations (filter: {isArchived: {isNull: true}}) {
                  items {
                    externalId
                    description
                    troubleshooting
                    files {
                      externalId
                      mimeType
                      name
                      metadata
                      downloadLink {
                          downloadUrl
                      }
                    }
                  }
                }
              }
            }
          }
          pageInfo {
            hasPreviousPage
            hasNextPage
            startCursor
            endCursor
          }
        }
      }
    `

    return query
}

export const useChecklists = (request: ChecklistQueryRequest) => {
    const query = useMemo(() => gql(buildChecklistQuery(request)), [request])

    const result = useGraphqlQuery<Checklist>(query, 'listChecklist', { fetchPolicy: 'no-cache' })

    const [resultData, setResultData] = useState<{
        data: Checklist[]
        loading: boolean
        pagging: boolean
        pageInfo: PageInfo
    }>({
        data: [],
        loading: true,
        pagging: false,
        pageInfo: {} as PageInfo,
    })

    useEffect(() => {
        if (result.data.length == 0) {
            setResultData({ data: [], loading: result.loading, pageInfo: result.pageInfo, pagging: false })
        } else {
            const fdmDataParsed = result.data.map((d) => {
                const arrayChecklistItemEntity = d.checklistItems as any as ArrayEntity<ChecklistItem>
                const checklistItems = arrayChecklistItemEntity.items.map((i) => {
                    const arrayMeasurementsEntity = i.measurements as any as ArrayEntity<Measurement>
                    const arrayObservationsEntity = i.observations as any as ArrayEntity<Observation>

                    return {
                        ...i,
                        measurements: arrayMeasurementsEntity.items,
                        observations: arrayObservationsEntity.items,
                    }
                })

                return {
                    ...d,
                    checklistItems: checklistItems,
                }
            })

            setResultData({
                data: fdmDataParsed,
                loading: result.loading,
                pageInfo: result.pageInfo,
                pagging: Boolean(request.nextPage),
            })
        }
    }, [result.data, result.loading, result.pageInfo])

    return resultData
}
