import { TableRow } from '@mui/material'
import styled from 'styled-components'

interface StripedRowProps {
    index: number
}

export const StripedRowOdd = styled(TableRow)<StripedRowProps>(({ index }) => ({
    '&:nth-of-type(odd)': {
        backgroundColor: index % 2 === 0 ? '#f9f9f9' : '#ffffff',
    },
}))

export const StripedRowEven = styled(TableRow)<StripedRowProps>(({ index }) => ({
    '&:nth-of-type(even)': {
        backgroundColor: index % 2 === 0 ? '#f9f9f9' : '#ffffff',
    },
}))
