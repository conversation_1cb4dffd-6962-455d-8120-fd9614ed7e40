import { enviroment } from './enviroment'

export const cognite = {
    appId: enviroment.cogniteAppId ?? '',
    project: enviroment.cogniteProject ?? '',
    baseUrl: enviroment.cogniteBaseUrl ?? '',
    cogniteFdmGlobalSiteSpace: enviroment.cogniteFdmGlobalSiteSpace ?? '',
    cogniteFdmGlobalUnitSpace: enviroment.cogniteFdmGlobalUnitSpace ?? '',
    cogniteFdmProjectCode: enviroment.cogniteFdmProjectCode ?? '',
    cogniteFdmSuffixModelSpace: enviroment.cogniteFdmSuffixModelSpace ?? '',
    cogniteFdmSuffixInstancesSpace: enviroment.cogniteFdmSuffixInstancesSpace ?? '',
    cogniteFdmSuffixProtectedSpace: enviroment.cogniteFdmSuffixProtectedSpace ?? '',
    cogniteFdmSuffixStaticSpace: enviroment.cogniteFdmSuffixStaticSpace ?? '',
    cogniteFusionUrl: enviroment.cogniteFusionUrl ?? '',
    cogniteApiVersion: enviroment.cogniteApiVersion ?? '',
    cogniteXCdpApp: enviroment.cogniteXCdpApp ?? '',
    exploreTimeSeriesUrl: '',
    exploreDataSetUrl: '',
    exploreAssetUrl: '',
    exploreEventUrl: '',
    explorerFilesUrl: `${enviroment.cogniteFusionUrl}/explore/files`,
    defaultGraphQlListLimit: Number(enviroment.cogniteDefaultGraphQlListLimit ?? 1000),
    cdfResourceSufix: enviroment.cdfResourceSufix ?? '',
}

cognite.exploreTimeSeriesUrl = `${cognite.cogniteFusionUrl}/explore/timeSeries`
cognite.exploreDataSetUrl = `${cognite.cogniteFusionUrl}/data-sets/data-set`
cognite.exploreAssetUrl = `${cognite.cogniteFusionUrl}/explore/asset`
cognite.exploreEventUrl = `${cognite.cogniteFusionUrl}/explore/event`
