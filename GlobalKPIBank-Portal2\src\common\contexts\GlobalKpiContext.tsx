import { createContext, ReactNode, useContext, useState } from 'react'
import { GlobalVisionKpi } from '../models/globalVisionKpi'
import { DataModelView } from '../models/dataModelView'
import dayjs from 'dayjs'

const GlobalKpiContext = createContext<any>(undefined)

export const GlobalKpiProvider = ({ children }: { children: ReactNode }) => {
    const [doesKPIHasBusinessSegmentFilter, setDoesKPIHasBusinessSegmentFilter] = useState(false)
    const [selectedBusinessSegment, setSelectedBusinessSegment] = useState<string>()
    const [isBusinessSegmentFilterActive, setIsBusinessSegmentFilterActive] = useState(false)
    const [selectedKpi, setSelectedKpi] = useState<string>()
    const [isProductivityKpi, setIsProductivityKpi] = useState(false)
    const [isFutureProductivityKpi, setIsFutureProductivityKpi] = useState(false)
    const [selectedKpiData, setSelectedKpiData] = useState<GlobalVisionKpi>()
    const [currentTab, setCurrentTab] = useState(0)
    const [currentSitePageTab, setCurrentSitePageTab] = useState(0)
    const [selectedSiteId, setSelectedSiteId] = useState<string>('')
    const [selectedMultipleSiteIds, setSelectedMultipleSiteIds] = useState<string[]>([])

    const [requestData, setRequestData] = useState<DataModelView>({
        kpiFilters: {
            kpiName: '',
            date: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
            views: [''],
            info: '',
            currentTab: currentTab,
            site: '',
            refSite: [''],
            businessSegment: '',
        },
        exportFilters: {
            isExport: false,
            range: 0,
        },
        cursor: '',
        limit: '10',
    })

    return (
        <GlobalKpiContext.Provider
            value={{
                currentSitePageTab,
                setCurrentSitePageTab,
                selectedSiteId,
                setSelectedSiteId,
                selectedMultipleSiteIds,
                setSelectedMultipleSiteIds,
                selectedKpiData,
                setSelectedKpiData,
                doesKPIHasBusinessSegmentFilter,
                setDoesKPIHasBusinessSegmentFilter,
                selectedBusinessSegment,
                setSelectedBusinessSegment,
                isBusinessSegmentFilterActive,
                setIsBusinessSegmentFilterActive,
                selectedKpi,
                setSelectedKpi,
                isProductivityKpi,
                setIsProductivityKpi,
                isFutureProductivityKpi,
                setIsFutureProductivityKpi,
                requestData,
                setRequestData,
                currentTab,
                setCurrentTab,
            }}
        >
            {children}
        </GlobalKpiContext.Provider>
    )
}

export const useGlobalKpiContext = () => {
    const context = useContext(GlobalKpiContext)

    if (!context) {
        throw new Error('useGlobalKpi must be used within a GlobalKpiProvider')
    }
    return context
}
