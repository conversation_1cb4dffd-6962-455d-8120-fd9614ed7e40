import { translate } from '@celanese/celanese-sdk'
import { ModalWrapper } from '../../Modal/ModalWrapper'
import { ClnButton } from '@celanese/ui-lib'
import { Box, Typography } from '@mui/material'

interface ApplyAllSitesModalProps {
    openPopover: boolean
    closePopover: () => void
    confirmPopover: () => void
}
export const ApplyAllSitesModal = (props: ApplyAllSitesModalProps) => {
    return (
        <ModalWrapper
            title={translate('KPI_TARGET.APPLY_ALL_SITES')}
            content={ApplyAllSites(props.closePopover, props.confirmPopover)}
            openModal={props.openPopover}
            closeModal={props.closePopover}
            sxProps={{
                '& .MuiPaper-root': {
                    width: '30%',
                },
            }}
        ></ModalWrapper>
    )
}

function ApplyAllSites(closeFn: () => void, confirmSaveFn: () => void) {
    return (
        <>
            <Box sx={{ padding: '0px 24px' }}>
                <Typography
                    variant="subtitle1"
                    sx={{
                        color: 'black',
                    }}
                >
                    {translate('KPI_TARGET.MODAL.QUESTION')}
                </Typography>
                <Typography
                    variant="subtitle1"
                    sx={{
                        color: 'black',
                    }}
                >
                    {translate('KPI_TARGET.MODAL.INFO')}
                </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', padding: '24px', gap: '10px' }}>
                <ClnButton
                    size="small"
                    variant="text"
                    label={translate('COMMONBUTTON.CANCEL')}
                    onClick={() => {
                        closeFn()
                    }}
                    sxProps={{
                        width: '100px',
                    }}
                />
                <ClnButton
                    size="small"
                    variant="contained"
                    label={translate('COMMONBUTTON.SUBMIT')}
                    onClick={() => {
                        confirmSaveFn()
                    }}
                    sxProps={{
                        width: '100px',
                        color: `primary.error !important`,
                    }}
                />
            </Box>
        </>
    )
}
