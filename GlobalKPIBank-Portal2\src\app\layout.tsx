'use client'
import { GlobalKpiProvider } from '@/common/contexts/GlobalKpiContext'
import { createPublicClientApplication } from '@/common/factories/msal-factory'
import { useLocale } from '@/common/hooks/useLocale'
import { ApolloClientProvider } from '@/common/providers/ApolloClientProvider'
import { CogniteClientProvider } from '@/common/providers/CogniteClientProvider'
import { UserRoleContextProvider } from '@/common/providers/UserRoleContextProvider'
import { getIdTokenFromMsal } from '@/common/utils'
import { registerCssHighlighPollyfill } from '@/common/utils/polyfills'
import { AccountInfo, EventType, InteractionType } from '@azure/msal-browser'
import { MsalAuthenticationTemplate, MsalProvider } from '@azure/msal-react'
import { DynamicTranslationArea, getMessages, TranslationContextProvider } from '@celanese/celanese-sdk'
import { ThemeProvider, Wrapper } from '@celanese/ui-lib'
import '@celanese/ui-lib/src/common/styles/icons.css'
import '@fontsource/roboto/300.css'
import '@fontsource/roboto/400.css'
import '@fontsource/roboto/500.css'
import '@fontsource/roboto/700.css'
import '@material-symbols/font-400/outlined.css'
import { CssBaseline } from '@mui/material'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import { useEffect, useState } from 'react'
import { IntlProvider } from 'react-intl'
import { Contextualization } from '../components/Contextualization'
import { HeaderNavBar } from '../components/LayoutHeaderNavBar'
import './globals.css'
import { DataChartProvider } from '@/common/contexts/DataChartContext'

dayjs.extend(utc)
dayjs.extend(timezone)

const msalInstance = createPublicClientApplication()

const accounts = msalInstance.getAllAccounts()
if (accounts.length > 0) {
    msalInstance.setActiveAccount(accounts[0])
}

const getAuthToken = (): Promise<string> => getIdTokenFromMsal(msalInstance)

msalInstance.addEventCallback((event) => {
    if (!event) {
        return
    }
    if (event.eventType === EventType.LOGIN_SUCCESS && event.payload) {
        const account: AccountInfo = event.payload as AccountInfo
        msalInstance.setActiveAccount(account)
    }
})

export default function RootLayout({ children }: { children: React.ReactNode }) {
    const [shouldTranslateDynamic, setShouldTranslateDynamic] = useState<boolean>()
    const [dynamicTranslationLoading, setDynamicTranslationLoading] = useState(false)
    const cacheNameShouldTranslate = 'shouldTranslateDynamic'
    const { locale, switchLocale } = useLocale()
    const [localeCode, setLocaleCode] = useState<string>(locale)
    const [messages, setMessages] = useState<any>()

    useEffect(() => {
        getMessages(localeCode, getAuthToken)
            .then((m) => {
                if (m) {
                    setMessages(m)
                }
            })
            .then(() => switchLocale(localeCode))
    }, [localeCode])

    useEffect(() => {
        const cacheValue = window.localStorage.getItem(cacheNameShouldTranslate)
        if (cacheValue && cacheValue === 'true') {
            setShouldTranslateDynamic(true)
        } else {
            setShouldTranslateDynamic(false)
        }
    }, [])

    useEffect(() => {
        if (shouldTranslateDynamic !== undefined) {
            window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(shouldTranslateDynamic))
        }
    }, [shouldTranslateDynamic])

    useEffect(registerCssHighlighPollyfill, [])

    return (
        <html>
            <body>
                <IntlProvider locale={locale as string} messages={messages}>
                    <MsalProvider instance={msalInstance}>
                        <MsalAuthenticationTemplate interactionType={InteractionType.Redirect}>
                            <UserRoleContextProvider>
                                <ApolloClientProvider>
                                    <CogniteClientProvider>
                                        <TranslationContextProvider getAuthToken={getAuthToken}>
                                            <ThemeProvider>
                                                <HeaderNavBar
                                                    setLocaleCode={setLocaleCode}
                                                    shouldTranslateDynamicState={{
                                                        shouldTranslateDynamic,
                                                        setShouldTranslateDynamic,
                                                    }}
                                                    dynamicTranslationLoadingState={{
                                                        dynamicTranslationLoading,
                                                        setDynamicTranslationLoading,
                                                    }}
                                                >
                                                    {children}
                                                </HeaderNavBar>
                                                <CssBaseline />
                                                <DynamicTranslationArea
                                                    getAuthToken={getAuthToken}
                                                    dynamicTranslationLoadingState={{
                                                        dynamicTranslationLoading,
                                                        setDynamicTranslationLoading,
                                                    }}
                                                    shouldTranslateDynamicState={{
                                                        shouldTranslateDynamic,
                                                        setShouldTranslateDynamic,
                                                    }}
                                                >
                                                    <GlobalKpiProvider>
                                                        <DataChartProvider>
                                                            <Contextualization>
                                                                <Wrapper>{children}</Wrapper>
                                                            </Contextualization>
                                                        </DataChartProvider>
                                                    </GlobalKpiProvider>
                                                </DynamicTranslationArea>
                                            </ThemeProvider>
                                        </TranslationContextProvider>
                                    </CogniteClientProvider>
                                </ApolloClientProvider>
                            </UserRoleContextProvider>
                        </MsalAuthenticationTemplate>
                    </MsalProvider>
                </IntlProvider>
            </body>
        </html>
    )
}
