import { Cln<PERSON><PERSON>er<PERSON><PERSON><PERSON>, HeaderNavbarProps, LangItem, MatIcon, PlantItem } from '@celanese/ui-lib'
import { useEffect, useContext, useMemo, useState, Dispatch, SetStateAction, ReactNode } from 'react'
import LanguageIcon from '@mui/icons-material/Language'
import { useRouter } from 'next/navigation'
import { translate } from '@celanese/celanese-sdk'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'
import { UserPopover } from '../UserPopover'
import { DEFAULT_LOCALE } from '@/common/hooks/useLocale'
import { AuthGuardContext } from '@/common/contexts/AuthGuardContext'
import { enviroment } from '@/common/configurations/enviroment'
import { UserSite } from '@/common/clients/user-management-client'
import { NoTranslate, TranslationContext, TranslationContextState } from '@celanese/celanese-sdk'
import { CircularProgress } from '@mui/material'
import TranslateIcon from '@mui/icons-material/Translate'
import * as styles from './BaseLayout.style'
import { GlobalStyles } from '@/common/theme/globalStyles'
import { GlobalKPINavbarItemConfig } from '@/common/models/navBarItemConfig'

const DEFAULT_SITE: PlantItem = {
    externalId: 'STS-COR',
    value: 'All Sites',
}

interface BaseLayoutProps {
    setLocaleCode: Dispatch<SetStateAction<string>>
    shouldTranslateDynamicState: {
        shouldTranslateDynamic: boolean | undefined
        setShouldTranslateDynamic: Dispatch<SetStateAction<boolean | undefined>>
    }
    dynamicTranslationLoadingState: {
        dynamicTranslationLoading: boolean
        setDynamicTranslationLoading: Dispatch<SetStateAction<boolean>>
    }
    children: ReactNode
}

export const HeaderNavBar = ({
    children,
    setLocaleCode,
    shouldTranslateDynamicState,
    dynamicTranslationLoadingState,
}: BaseLayoutProps) => {
    const router = useRouter()
    const [authorizedMenuItems, setAuthorizedMenuItems] = useState<GlobalKPINavbarItemConfig[]>([])
    const { checkPermissionsFromRoutes } = useAuthGuard()
    const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null)
    const [selectedReportingSite, setSelectedReportingSite] = useState<UserSite | undefined>(undefined)
    const { userInfo, updateSiteInfo } = useContext(AuthGuardContext)
    const { availableLanguages, locale, updateLocale } = useContext<TranslationContextState>(TranslationContext)
    const [disableTranslateDynamic, setDisableTranslateDynamic] = useState<boolean>(true)
    const { shouldTranslateDynamic, setShouldTranslateDynamic } = shouldTranslateDynamicState
    const { dynamicTranslationLoading, setDynamicTranslationLoading } = dynamicTranslationLoadingState
    const [isTranslated, setIsTranslated] = useState<boolean>(false)

    useEffect(() => {
        if (userInfo?.availableReportingSites && userInfo.availableReportingSites.length > 0) {
            setSelectedReportingSite(userInfo.selectedSite)
        }
    }, [userInfo])

    const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorElUser(event.currentTarget)
    }

    const handleCloseUserMenu = () => {
        setAnchorElUser(null)
    }

    const selectedLanguage = useMemo(() => {
        if (availableLanguages?.length) {
            return (
                availableLanguages.find((l) => l?.code == locale) ??
                availableLanguages.find((l) => l?.code == DEFAULT_LOCALE)
            )
        }

        return {
            externalId: 'EN',
            language: 'English',
            code: 'EN',
        }
    }, [locale, availableLanguages])

    const availableMenuItems: GlobalKPINavbarItemConfig[] = [
        {
            route: '/home',
            title: translate('MENU.HOME'),
            icon: 'home',
            onClickHandler: () => {
                router.push('/home')
            },
        },
        {
            route: '/site-view',
            title: translate('MENU.SITE_VIEW'),
            icon: 'factory',
            onClickHandler: () => {
                router.push('/site-view')
            },
        },
        {
            route: '/global-view',
            title: translate('MENU.GLOBAL_VIEW'),
            icon: 'language',
            onClickHandler: () => {
                router.push('/global-view')
            },
        },
        {
            route: '/kpi-target',
            title: translate('KPI_TARGET.TARGET_SETTINGS'),
            icon: 'adjust',
            onClickHandler: () => {
                router.push('/kpi-target')
            },
        },
        {
            route: '/manual-input',
            title: translate('MENU.MANUAL_INPUT'),
            icon: 'settings',
            onClickHandler: () => {
                router.push('/manual-input')
            },
        },
        {
            route: '/kpi-input-management',
            title: translate('MENU.KPI_INPUT_MANAGEMENT'),
            icon: 'data_check',
            onClickHandler: () => {
                router.push('/kpi-input-management')
            },
        },
    ]

    useEffect(() => {
        const isTitleTranslated = availableMenuItems.every((item) => !item.title.includes('MENU.'))
        setIsTranslated(isTitleTranslated)
    }, [availableMenuItems, locale])

    useEffect(() => {
        const items = availableMenuItems.filter((item) => checkPermissionsFromRoutes(item.route))

        setAuthorizedMenuItems(items)
    }, [userInfo, isTranslated, locale])

    const DynamicTranslationIcon = () => {
        return (
            <>
                {dynamicTranslationLoading && <CircularProgress size={20} color="inherit" />}

                {disableTranslateDynamic ? (
                    <TranslateIcon className="translate-ico" sx={styles.dynamicTranslationDisabled} />
                ) : shouldTranslateDynamic ? (
                    <TranslateIcon className="translate-ico" sx={styles.dynamicTranslationOff} />
                ) : (
                    <TranslateIcon className="translate-ico" sx={styles.dynamicTranslationOn} />
                )}
            </>
        )
    }

    const headerNavbarProps: HeaderNavbarProps = useMemo(
        () => ({
            disableDarkMode: true,
            logo: <LanguageIcon sx={{ fontSize: '26px', marginTop: '5px' }} />,
            alt: userInfo.lastName + ', ' + userInfo.firstName,
            src: userInfo.avatar,
            nameBold: 'Digital Global',
            nameLight: 'KPI Bank',
            language: selectedLanguage as any,
            languages:
                availableLanguages?.map((language) => ({
                    ...language,
                    language: <NoTranslate>{language.language}</NoTranslate>,
                })) ?? [],

            selectedPlants: [DEFAULT_SITE],
            plants: [DEFAULT_SITE],
            multPlants: false,
            items: authorizedMenuItems,
            buttonConfigurations: [
                {
                    Icon: <MatIcon icon="notifications" />,
                    Tooltip: 'Notifications',
                    onClick: () => {
                        typeof window !== 'undefined' && window.open(enviroment.notificationPortalUrl, '_blank')
                    },
                },
                {
                    Icon: <DynamicTranslationIcon />,
                    Tooltip: '',
                    onClick: () => {},
                    disabled: true,
                },
                {
                    Icon: <MatIcon icon="dark_mode" sx={{ color: '#00000042', fontSize: '1.5rem', mt: 0.5 }} />,
                    Tooltip: '',
                    onClick: () => {},
                    disabled: true,
                },
            ],
            onChangeLang: (li: LangItem) => {
                if (li) {
                    updateLocale(li.code)
                    setLocaleCode(li.code)
                }
            },
            onChangePlant: (_: PlantItem[]) => {},
            userMenuOnClick: handleOpenUserMenu,
            sxProps: { fontFamily: 'Roboto, "Segoe UI", Arial, Helvetica, sans-serif' },
        }),
        [authorizedMenuItems, selectedLanguage, selectedReportingSite, userInfo]
    )

    return (
        <>
            <GlobalStyles />
            <NoTranslate>
                <ClnHeaderNavbar {...headerNavbarProps} />
                <UserPopover anchorEl={anchorElUser} onClose={handleCloseUserMenu}></UserPopover>
            </NoTranslate>
        </>
    )
}
