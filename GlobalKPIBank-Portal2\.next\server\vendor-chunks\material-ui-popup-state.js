"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/material-ui-popup-state";
exports.ids = ["vendor-chunks/material-ui-popup-state"];
exports.modules = {

/***/ "(ssr)/./node_modules/material-ui-popup-state/hooks.js":
/*!*******************************************************!*\
  !*** ./node_modules/material-ui-popup-state/hooks.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(ssr)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nvar _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/typeof.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.anchorRef = anchorRef;\nexports.bindContextMenu = bindContextMenu;\nexports.bindDialog = bindDialog;\nexports.bindDoubleClick = bindDoubleClick;\nexports.bindFocus = bindFocus;\nexports.bindHover = bindHover;\nexports.bindMenu = bindMenu;\nexports.bindPopover = bindPopover;\nexports.bindPopper = bindPopper;\nexports.bindToggle = bindToggle;\nexports.bindTrigger = bindTrigger;\nexports.initCoreState = void 0;\nexports.usePopupState = usePopupState;\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/slicedToArray.js\"));\nvar _react2 = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar React = _react2;\nvar _useEvent = __webpack_require__(/*! ./useEvent.js */ \"(ssr)/./node_modules/material-ui-popup-state/useEvent.js\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return { \"default\": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n[\"default\"] = e, t && t.set(e, n), n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2[\"default\"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; } /* eslint-env browser */\nvar printedWarnings = {};\nfunction warn(key, message) {\n  if (printedWarnings[key]) return;\n  printedWarnings[key] = true;\n  console.error('[material-ui-popup-state] WARNING', message); // eslint-disable-line no-console\n}\nvar initCoreState = exports.initCoreState = {\n  isOpen: false,\n  setAnchorElUsed: false,\n  anchorEl: undefined,\n  anchorPosition: undefined,\n  hovered: false,\n  focused: false,\n  _openEventType: null,\n  _childPopupState: null,\n  _deferNextOpen: false,\n  _deferNextClose: false\n};\n\n// https://github.com/jcoreio/material-ui-popup-state/issues/138\n// Webpack prod build doesn't like it if we refer to React.useId conditionally,\n// but aliasing to a variable like this works\nvar _react = React;\nvar defaultPopupId = 'useId' in _react ? function () {\n  return _react.useId();\n} :\n// istanbul ignore next\nfunction () {\n  return undefined;\n};\nfunction usePopupState(_ref) {\n  var parentPopupState = _ref.parentPopupState,\n    _ref$popupId = _ref.popupId,\n    popupId = _ref$popupId === void 0 ? defaultPopupId() : _ref$popupId,\n    variant = _ref.variant,\n    disableAutoFocus = _ref.disableAutoFocus;\n  var isMounted = (0, _react2.useRef)(true);\n  (0, _react2.useEffect)(function () {\n    isMounted.current = true;\n    return function () {\n      isMounted.current = false;\n    };\n  }, []);\n  var _useState = (0, _react2.useState)(initCoreState),\n    _useState2 = (0, _slicedToArray2[\"default\"])(_useState, 2),\n    state = _useState2[0],\n    _setState = _useState2[1];\n  var setState = (0, _react2.useCallback)(function (state) {\n    if (isMounted.current) _setState(state);\n  }, []);\n  var setAnchorEl = (0, _react2.useCallback)(function (anchorEl) {\n    return setState(function (state) {\n      return _objectSpread(_objectSpread({}, state), {}, {\n        setAnchorElUsed: true,\n        anchorEl: anchorEl !== null && anchorEl !== void 0 ? anchorEl : undefined\n      });\n    });\n  }, []);\n  var toggle = (0, _useEvent.useEvent)(function (eventOrAnchorEl) {\n    if (state.isOpen) close(eventOrAnchorEl);else open(eventOrAnchorEl);\n    return state;\n  });\n  var open = (0, _useEvent.useEvent)(function (eventOrAnchorEl) {\n    var event = eventOrAnchorEl instanceof Element ? undefined : eventOrAnchorEl;\n    var element = eventOrAnchorEl instanceof Element ? eventOrAnchorEl : (eventOrAnchorEl === null || eventOrAnchorEl === void 0 ? void 0 : eventOrAnchorEl.currentTarget) instanceof Element ? eventOrAnchorEl.currentTarget : undefined;\n    if ((event === null || event === void 0 ? void 0 : event.type) === 'touchstart') {\n      setState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          _deferNextOpen: true\n        });\n      });\n      return;\n    }\n    var clientX = event === null || event === void 0 ? void 0 : event.clientX;\n    var clientY = event === null || event === void 0 ? void 0 : event.clientY;\n    var anchorPosition = typeof clientX === 'number' && typeof clientY === 'number' ? {\n      left: clientX,\n      top: clientY\n    } : undefined;\n    var doOpen = function doOpen(state) {\n      if (!eventOrAnchorEl && !state.setAnchorElUsed && variant !== 'dialog') {\n        warn('missingEventOrAnchorEl', 'eventOrAnchorEl should be defined if setAnchorEl is not used');\n      }\n      if (parentPopupState) {\n        if (!parentPopupState.isOpen) return state;\n        setTimeout(function () {\n          return parentPopupState._setChildPopupState(popupState);\n        });\n      }\n      var newState = _objectSpread(_objectSpread({}, state), {}, {\n        isOpen: true,\n        anchorPosition: anchorPosition,\n        hovered: (event === null || event === void 0 ? void 0 : event.type) === 'mouseover' || state.hovered,\n        focused: (event === null || event === void 0 ? void 0 : event.type) === 'focus' || state.focused,\n        _openEventType: event === null || event === void 0 ? void 0 : event.type\n      });\n      if (!state.setAnchorElUsed) {\n        if (event !== null && event !== void 0 && event.currentTarget) {\n          newState.anchorEl = event === null || event === void 0 ? void 0 : event.currentTarget;\n        } else if (element) {\n          newState.anchorEl = element;\n        }\n      }\n      return newState;\n    };\n    setState(function (state) {\n      if (state._deferNextOpen) {\n        setTimeout(function () {\n          return setState(doOpen);\n        }, 0);\n        return _objectSpread(_objectSpread({}, state), {}, {\n          _deferNextOpen: false\n        });\n      } else {\n        return doOpen(state);\n      }\n    });\n  });\n  var doClose = function doClose(state) {\n    var _childPopupState = state._childPopupState;\n    setTimeout(function () {\n      _childPopupState === null || _childPopupState === void 0 || _childPopupState.close();\n      parentPopupState === null || parentPopupState === void 0 || parentPopupState._setChildPopupState(null);\n    });\n    return _objectSpread(_objectSpread({}, state), {}, {\n      isOpen: false,\n      hovered: false,\n      focused: false\n    });\n  };\n  var close = (0, _useEvent.useEvent)(function (eventOrAnchorEl) {\n    var event = eventOrAnchorEl instanceof Element ? undefined : eventOrAnchorEl;\n    if ((event === null || event === void 0 ? void 0 : event.type) === 'touchstart') {\n      setState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          _deferNextClose: true\n        });\n      });\n      return;\n    }\n    setState(function (state) {\n      if (state._deferNextClose) {\n        setTimeout(function () {\n          return setState(doClose);\n        }, 0);\n        return _objectSpread(_objectSpread({}, state), {}, {\n          _deferNextClose: false\n        });\n      } else {\n        return doClose(state);\n      }\n    });\n  });\n  var setOpen = (0, _react2.useCallback)(function (nextOpen, eventOrAnchorEl) {\n    if (nextOpen) {\n      open(eventOrAnchorEl);\n    } else {\n      close(eventOrAnchorEl);\n    }\n  }, []);\n  var onMouseLeave = (0, _useEvent.useEvent)(function (event) {\n    var relatedTarget = event.relatedTarget;\n    setState(function (state) {\n      if (state.hovered && !(relatedTarget instanceof Element && isElementInPopup(relatedTarget, popupState))) {\n        if (state.focused) {\n          return _objectSpread(_objectSpread({}, state), {}, {\n            hovered: false\n          });\n        } else {\n          return doClose(state);\n        }\n      }\n      return state;\n    });\n  });\n  var onBlur = (0, _useEvent.useEvent)(function (event) {\n    if (!event) return;\n    var relatedTarget = event.relatedTarget;\n    setState(function (state) {\n      if (state.focused && !(relatedTarget instanceof Element && isElementInPopup(relatedTarget, popupState))) {\n        if (state.hovered) {\n          return _objectSpread(_objectSpread({}, state), {}, {\n            focused: false\n          });\n        } else {\n          return doClose(state);\n        }\n      }\n      return state;\n    });\n  });\n  var _setChildPopupState = (0, _react2.useCallback)(function (_childPopupState) {\n    return setState(function (state) {\n      return _objectSpread(_objectSpread({}, state), {}, {\n        _childPopupState: _childPopupState\n      });\n    });\n  }, []);\n  var popupState = _objectSpread(_objectSpread({}, state), {}, {\n    setAnchorEl: setAnchorEl,\n    popupId: popupId !== null && popupId !== void 0 ? popupId : undefined,\n    variant: variant,\n    open: open,\n    close: close,\n    toggle: toggle,\n    setOpen: setOpen,\n    onBlur: onBlur,\n    onMouseLeave: onMouseLeave,\n    disableAutoFocus: disableAutoFocus !== null && disableAutoFocus !== void 0 ? disableAutoFocus : Boolean(state.hovered || state.focused),\n    _setChildPopupState: _setChildPopupState\n  });\n  return popupState;\n}\n\n/**\n * Creates a ref that sets the anchorEl for the popup.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction anchorRef(_ref2) {\n  var setAnchorEl = _ref2.setAnchorEl;\n  return setAnchorEl;\n}\nfunction controlAriaProps(_ref3) {\n  var isOpen = _ref3.isOpen,\n    popupId = _ref3.popupId,\n    variant = _ref3.variant;\n  return _objectSpread({}, variant === 'popover' ? {\n    'aria-haspopup': true,\n    'aria-controls': isOpen ? popupId : undefined\n  } : variant === 'popper' ? {\n    'aria-describedby': isOpen ? popupId : undefined\n  } : undefined);\n}\n\n/**\n * Creates props for a component that opens the popup when clicked.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindTrigger(popupState) {\n  return _objectSpread(_objectSpread({}, controlAriaProps(popupState)), {}, {\n    onClick: popupState.open,\n    onTouchStart: popupState.open\n  });\n}\n\n/**\n * Creates props for a component that opens the popup on its contextmenu event (right click).\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindContextMenu(popupState) {\n  return _objectSpread(_objectSpread({}, controlAriaProps(popupState)), {}, {\n    onContextMenu: function onContextMenu(e) {\n      e.preventDefault();\n      popupState.open(e);\n    }\n  });\n}\n\n/**\n * Creates props for a component that toggles the popup when clicked.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindToggle(popupState) {\n  return _objectSpread(_objectSpread({}, controlAriaProps(popupState)), {}, {\n    onClick: popupState.toggle,\n    onTouchStart: popupState.toggle\n  });\n}\n\n/**\n * Creates props for a component that opens the popup while hovered.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindHover(popupState) {\n  var open = popupState.open,\n    onMouseLeave = popupState.onMouseLeave;\n  return _objectSpread(_objectSpread({}, controlAriaProps(popupState)), {}, {\n    onTouchStart: open,\n    onMouseOver: open,\n    onMouseLeave: onMouseLeave\n  });\n}\n\n/**\n * Creates props for a component that opens the popup while focused.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindFocus(popupState) {\n  var open = popupState.open,\n    onBlur = popupState.onBlur;\n  return _objectSpread(_objectSpread({}, controlAriaProps(popupState)), {}, {\n    onFocus: open,\n    onBlur: onBlur\n  });\n}\n\n/**\n * Creates props for a component that opens the popup while double click.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindDoubleClick(_ref4) {\n  var isOpen = _ref4.isOpen,\n    open = _ref4.open,\n    popupId = _ref4.popupId,\n    variant = _ref4.variant;\n  return (0, _defineProperty2[\"default\"])((0, _defineProperty2[\"default\"])((0, _defineProperty2[\"default\"])({}, variant === 'popover' ? 'aria-controls' : 'aria-describedby', isOpen ? popupId : null), 'aria-haspopup', variant === 'popover' ? true : undefined), \"onDoubleClick\", open);\n}\n\n/**\n * Creates props for a `Popover` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindPopover(_ref6) {\n  var isOpen = _ref6.isOpen,\n    anchorEl = _ref6.anchorEl,\n    anchorPosition = _ref6.anchorPosition,\n    close = _ref6.close,\n    popupId = _ref6.popupId,\n    onMouseLeave = _ref6.onMouseLeave,\n    disableAutoFocus = _ref6.disableAutoFocus,\n    _openEventType = _ref6._openEventType;\n  var usePopoverPosition = _openEventType === 'contextmenu';\n  return _objectSpread({\n    id: popupId,\n    anchorEl: anchorEl,\n    anchorPosition: anchorPosition,\n    anchorReference: usePopoverPosition ? 'anchorPosition' : 'anchorEl',\n    open: isOpen,\n    onClose: close,\n    onMouseLeave: onMouseLeave\n  }, disableAutoFocus && {\n    disableAutoFocus: true,\n    disableEnforceFocus: true,\n    disableRestoreFocus: true\n  });\n}\n\n/**\n * Creates props for a `Menu` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\n\n/**\n * Creates props for a `Popover` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindMenu(_ref7) {\n  var isOpen = _ref7.isOpen,\n    anchorEl = _ref7.anchorEl,\n    anchorPosition = _ref7.anchorPosition,\n    close = _ref7.close,\n    popupId = _ref7.popupId,\n    onMouseLeave = _ref7.onMouseLeave,\n    disableAutoFocus = _ref7.disableAutoFocus,\n    _openEventType = _ref7._openEventType;\n  var usePopoverPosition = _openEventType === 'contextmenu';\n  return _objectSpread({\n    id: popupId,\n    anchorEl: anchorEl,\n    anchorPosition: anchorPosition,\n    anchorReference: usePopoverPosition ? 'anchorPosition' : 'anchorEl',\n    open: isOpen,\n    onClose: close,\n    onMouseLeave: onMouseLeave\n  }, disableAutoFocus && {\n    autoFocus: false,\n    disableAutoFocusItem: true,\n    disableAutoFocus: true,\n    disableEnforceFocus: true,\n    disableRestoreFocus: true\n  });\n}\n/**\n * Creates props for a `Popper` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindPopper(_ref8) {\n  var isOpen = _ref8.isOpen,\n    anchorEl = _ref8.anchorEl,\n    popupId = _ref8.popupId,\n    onMouseLeave = _ref8.onMouseLeave;\n  return {\n    id: popupId,\n    anchorEl: anchorEl,\n    open: isOpen,\n    onMouseLeave: onMouseLeave\n  };\n}\n\n/**\n * Creates props for a `Dialog` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */\nfunction bindDialog(_ref9) {\n  var isOpen = _ref9.isOpen,\n    close = _ref9.close;\n  return {\n    open: isOpen,\n    onClose: close\n  };\n}\nfunction getPopup(element, _ref10) {\n  var popupId = _ref10.popupId;\n  if (!popupId) return null;\n  var rootNode = typeof element.getRootNode === 'function' ? element.getRootNode() : document;\n  if (typeof rootNode.getElementById === 'function') {\n    return rootNode.getElementById(popupId);\n  }\n  return null;\n}\nfunction isElementInPopup(element, popupState) {\n  var anchorEl = popupState.anchorEl,\n    _childPopupState = popupState._childPopupState;\n  return isAncestor(anchorEl, element) || isAncestor(getPopup(element, popupState), element) || _childPopupState != null && isElementInPopup(element, _childPopupState);\n}\nfunction isAncestor(parent, child) {\n  if (!parent) return false;\n  while (child) {\n    if (child === parent) return true;\n    child = child.parentElement;\n  }\n  return false;\n}\n//# sourceMappingURL=hooks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWF0ZXJpYWwtdWktcG9wdXAtc3RhdGUvaG9va3MuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsNkJBQTZCLG1CQUFPLENBQUMsMEhBQThDO0FBQ25GLGNBQWMsbUJBQU8sQ0FBQyw0RkFBK0I7QUFDckQsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsaUJBQWlCO0FBQ2pCLHVCQUF1QjtBQUN2QixrQkFBa0I7QUFDbEIsdUJBQXVCO0FBQ3ZCLGlCQUFpQjtBQUNqQixpQkFBaUI7QUFDakIsZ0JBQWdCO0FBQ2hCLG1CQUFtQjtBQUNuQixrQkFBa0I7QUFDbEIsa0JBQWtCO0FBQ2xCLG1CQUFtQjtBQUNuQixxQkFBcUI7QUFDckIscUJBQXFCO0FBQ3JCLDhDQUE4QyxtQkFBTyxDQUFDLDRHQUF1QztBQUM3Riw2Q0FBNkMsbUJBQU8sQ0FBQywwR0FBc0M7QUFDM0Ysc0NBQXNDLG1CQUFPLENBQUMsd0dBQU87QUFDckQ7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQywrRUFBZTtBQUN2Qyx1Q0FBdUMsK0NBQStDLDBDQUEwQywwRUFBMEUsbUJBQW1CO0FBQzdOLHlDQUF5Qyx1Q0FBdUMsNkVBQTZFLGdCQUFnQixxQ0FBcUMsb0NBQW9DLFVBQVUsaUJBQWlCLGdFQUFnRSwwQ0FBMEMsOEJBQThCLDBEQUEwRCx3RUFBd0U7QUFDM2hCLHlCQUF5Qix3QkFBd0Isb0NBQW9DLHlDQUF5QyxrQ0FBa0MsMERBQTBELDBCQUEwQjtBQUNwUCw0QkFBNEIsZ0JBQWdCLHNCQUFzQixPQUFPLGtEQUFrRCxzREFBc0QsK0NBQStDLG1KQUFtSixxRUFBcUUsS0FBSyxZQUFZO0FBQ3pjO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0RBQStEO0FBQy9EO0FBQ0Esb0JBQW9CLHFCQUFxQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLDJDQUEyQyxZQUFZO0FBQ3ZEO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBLDZDQUE2QztBQUM3QztBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLFlBQVk7QUFDekQ7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxtREFBbUQsWUFBWTtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsNkNBQTZDLFlBQVk7QUFDekQ7QUFDQSxTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wseUNBQXlDLFlBQVk7QUFDckQ7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsWUFBWTtBQUN6RDtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDZDQUE2QyxZQUFZO0FBQ3pEO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLFlBQVk7QUFDM0Q7QUFDQSxXQUFXO0FBQ1gsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsWUFBWTtBQUMzRDtBQUNBLFdBQVc7QUFDWCxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIO0FBQ0E7QUFDQSwyQ0FBMkMsWUFBWTtBQUN2RDtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0wsR0FBRztBQUNILGlEQUFpRCxZQUFZO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLG1DQUFtQztBQUMxRTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsbUNBQW1DO0FBQzFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsbUNBQW1DO0FBQzFFO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsbUNBQW1DO0FBQzFFO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxtQ0FBbUM7QUFDMUU7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4R0FBOEc7QUFDOUc7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2xvYmFsLWtwaS1hcHAvLi9ub2RlX21vZHVsZXMvbWF0ZXJpYWwtdWktcG9wdXAtc3RhdGUvaG9va3MuanM/MjdmMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHRcIik7XG52YXIgX3R5cGVvZiA9IHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL3R5cGVvZlwiKTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmFuY2hvclJlZiA9IGFuY2hvclJlZjtcbmV4cG9ydHMuYmluZENvbnRleHRNZW51ID0gYmluZENvbnRleHRNZW51O1xuZXhwb3J0cy5iaW5kRGlhbG9nID0gYmluZERpYWxvZztcbmV4cG9ydHMuYmluZERvdWJsZUNsaWNrID0gYmluZERvdWJsZUNsaWNrO1xuZXhwb3J0cy5iaW5kRm9jdXMgPSBiaW5kRm9jdXM7XG5leHBvcnRzLmJpbmRIb3ZlciA9IGJpbmRIb3ZlcjtcbmV4cG9ydHMuYmluZE1lbnUgPSBiaW5kTWVudTtcbmV4cG9ydHMuYmluZFBvcG92ZXIgPSBiaW5kUG9wb3ZlcjtcbmV4cG9ydHMuYmluZFBvcHBlciA9IGJpbmRQb3BwZXI7XG5leHBvcnRzLmJpbmRUb2dnbGUgPSBiaW5kVG9nZ2xlO1xuZXhwb3J0cy5iaW5kVHJpZ2dlciA9IGJpbmRUcmlnZ2VyO1xuZXhwb3J0cy5pbml0Q29yZVN0YXRlID0gdm9pZCAwO1xuZXhwb3J0cy51c2VQb3B1cFN0YXRlID0gdXNlUG9wdXBTdGF0ZTtcbnZhciBfZGVmaW5lUHJvcGVydHkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eVwiKSk7XG52YXIgX3NsaWNlZFRvQXJyYXkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9zbGljZWRUb0FycmF5XCIpKTtcbnZhciBfcmVhY3QyID0gX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciBSZWFjdCA9IF9yZWFjdDI7XG52YXIgX3VzZUV2ZW50ID0gcmVxdWlyZShcIi4vdXNlRXZlbnQuanNcIik7XG5mdW5jdGlvbiBfZ2V0UmVxdWlyZVdpbGRjYXJkQ2FjaGUoZSkgeyBpZiAoXCJmdW5jdGlvblwiICE9IHR5cGVvZiBXZWFrTWFwKSByZXR1cm4gbnVsbDsgdmFyIHIgPSBuZXcgV2Vha01hcCgpLCB0ID0gbmV3IFdlYWtNYXAoKTsgcmV0dXJuIChfZ2V0UmVxdWlyZVdpbGRjYXJkQ2FjaGUgPSBmdW5jdGlvbiBfZ2V0UmVxdWlyZVdpbGRjYXJkQ2FjaGUoZSkgeyByZXR1cm4gZSA/IHQgOiByOyB9KShlKTsgfVxuZnVuY3Rpb24gX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQoZSwgcikgeyBpZiAoIXIgJiYgZSAmJiBlLl9fZXNNb2R1bGUpIHJldHVybiBlOyBpZiAobnVsbCA9PT0gZSB8fCBcIm9iamVjdFwiICE9IF90eXBlb2YoZSkgJiYgXCJmdW5jdGlvblwiICE9IHR5cGVvZiBlKSByZXR1cm4geyBcImRlZmF1bHRcIjogZSB9OyB2YXIgdCA9IF9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZShyKTsgaWYgKHQgJiYgdC5oYXMoZSkpIHJldHVybiB0LmdldChlKTsgdmFyIG4gPSB7IF9fcHJvdG9fXzogbnVsbCB9LCBhID0gT2JqZWN0LmRlZmluZVByb3BlcnR5ICYmIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3I7IGZvciAodmFyIHUgaW4gZSkgaWYgKFwiZGVmYXVsdFwiICE9PSB1ICYmIHt9Lmhhc093blByb3BlcnR5LmNhbGwoZSwgdSkpIHsgdmFyIGkgPSBhID8gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihlLCB1KSA6IG51bGw7IGkgJiYgKGkuZ2V0IHx8IGkuc2V0KSA/IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuLCB1LCBpKSA6IG5bdV0gPSBlW3VdOyB9IHJldHVybiBuW1wiZGVmYXVsdFwiXSA9IGUsIHQgJiYgdC5zZXQoZSwgbiksIG47IH1cbmZ1bmN0aW9uIG93bktleXMoZSwgcikgeyB2YXIgdCA9IE9iamVjdC5rZXlzKGUpOyBpZiAoT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scykgeyB2YXIgbyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMoZSk7IHIgJiYgKG8gPSBvLmZpbHRlcihmdW5jdGlvbiAocikgeyByZXR1cm4gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihlLCByKS5lbnVtZXJhYmxlOyB9KSksIHQucHVzaC5hcHBseSh0LCBvKTsgfSByZXR1cm4gdDsgfVxuZnVuY3Rpb24gX29iamVjdFNwcmVhZChlKSB7IGZvciAodmFyIHIgPSAxOyByIDwgYXJndW1lbnRzLmxlbmd0aDsgcisrKSB7IHZhciB0ID0gbnVsbCAhPSBhcmd1bWVudHNbcl0gPyBhcmd1bWVudHNbcl0gOiB7fTsgciAlIDIgPyBvd25LZXlzKE9iamVjdCh0KSwgITApLmZvckVhY2goZnVuY3Rpb24gKHIpIHsgKDAsIF9kZWZpbmVQcm9wZXJ0eTJbXCJkZWZhdWx0XCJdKShlLCByLCB0W3JdKTsgfSkgOiBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyA/IE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKGUsIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzKHQpKSA6IG93bktleXMoT2JqZWN0KHQpKS5mb3JFYWNoKGZ1bmN0aW9uIChyKSB7IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShlLCByLCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHQsIHIpKTsgfSk7IH0gcmV0dXJuIGU7IH0gLyogZXNsaW50LWVudiBicm93c2VyICovXG52YXIgcHJpbnRlZFdhcm5pbmdzID0ge307XG5mdW5jdGlvbiB3YXJuKGtleSwgbWVzc2FnZSkge1xuICBpZiAocHJpbnRlZFdhcm5pbmdzW2tleV0pIHJldHVybjtcbiAgcHJpbnRlZFdhcm5pbmdzW2tleV0gPSB0cnVlO1xuICBjb25zb2xlLmVycm9yKCdbbWF0ZXJpYWwtdWktcG9wdXAtc3RhdGVdIFdBUk5JTkcnLCBtZXNzYWdlKTsgLy8gZXNsaW50LWRpc2FibGUtbGluZSBuby1jb25zb2xlXG59XG52YXIgaW5pdENvcmVTdGF0ZSA9IGV4cG9ydHMuaW5pdENvcmVTdGF0ZSA9IHtcbiAgaXNPcGVuOiBmYWxzZSxcbiAgc2V0QW5jaG9yRWxVc2VkOiBmYWxzZSxcbiAgYW5jaG9yRWw6IHVuZGVmaW5lZCxcbiAgYW5jaG9yUG9zaXRpb246IHVuZGVmaW5lZCxcbiAgaG92ZXJlZDogZmFsc2UsXG4gIGZvY3VzZWQ6IGZhbHNlLFxuICBfb3BlbkV2ZW50VHlwZTogbnVsbCxcbiAgX2NoaWxkUG9wdXBTdGF0ZTogbnVsbCxcbiAgX2RlZmVyTmV4dE9wZW46IGZhbHNlLFxuICBfZGVmZXJOZXh0Q2xvc2U6IGZhbHNlXG59O1xuXG4vLyBodHRwczovL2dpdGh1Yi5jb20vamNvcmVpby9tYXRlcmlhbC11aS1wb3B1cC1zdGF0ZS9pc3N1ZXMvMTM4XG4vLyBXZWJwYWNrIHByb2QgYnVpbGQgZG9lc24ndCBsaWtlIGl0IGlmIHdlIHJlZmVyIHRvIFJlYWN0LnVzZUlkIGNvbmRpdGlvbmFsbHksXG4vLyBidXQgYWxpYXNpbmcgdG8gYSB2YXJpYWJsZSBsaWtlIHRoaXMgd29ya3NcbnZhciBfcmVhY3QgPSBSZWFjdDtcbnZhciBkZWZhdWx0UG9wdXBJZCA9ICd1c2VJZCcgaW4gX3JlYWN0ID8gZnVuY3Rpb24gKCkge1xuICByZXR1cm4gX3JlYWN0LnVzZUlkKCk7XG59IDpcbi8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG5mdW5jdGlvbiAoKSB7XG4gIHJldHVybiB1bmRlZmluZWQ7XG59O1xuZnVuY3Rpb24gdXNlUG9wdXBTdGF0ZShfcmVmKSB7XG4gIHZhciBwYXJlbnRQb3B1cFN0YXRlID0gX3JlZi5wYXJlbnRQb3B1cFN0YXRlLFxuICAgIF9yZWYkcG9wdXBJZCA9IF9yZWYucG9wdXBJZCxcbiAgICBwb3B1cElkID0gX3JlZiRwb3B1cElkID09PSB2b2lkIDAgPyBkZWZhdWx0UG9wdXBJZCgpIDogX3JlZiRwb3B1cElkLFxuICAgIHZhcmlhbnQgPSBfcmVmLnZhcmlhbnQsXG4gICAgZGlzYWJsZUF1dG9Gb2N1cyA9IF9yZWYuZGlzYWJsZUF1dG9Gb2N1cztcbiAgdmFyIGlzTW91bnRlZCA9ICgwLCBfcmVhY3QyLnVzZVJlZikodHJ1ZSk7XG4gICgwLCBfcmVhY3QyLnVzZUVmZmVjdCkoZnVuY3Rpb24gKCkge1xuICAgIGlzTW91bnRlZC5jdXJyZW50ID0gdHJ1ZTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgaXNNb3VudGVkLmN1cnJlbnQgPSBmYWxzZTtcbiAgICB9O1xuICB9LCBbXSk7XG4gIHZhciBfdXNlU3RhdGUgPSAoMCwgX3JlYWN0Mi51c2VTdGF0ZSkoaW5pdENvcmVTdGF0ZSksXG4gICAgX3VzZVN0YXRlMiA9ICgwLCBfc2xpY2VkVG9BcnJheTJbXCJkZWZhdWx0XCJdKShfdXNlU3RhdGUsIDIpLFxuICAgIHN0YXRlID0gX3VzZVN0YXRlMlswXSxcbiAgICBfc2V0U3RhdGUgPSBfdXNlU3RhdGUyWzFdO1xuICB2YXIgc2V0U3RhdGUgPSAoMCwgX3JlYWN0Mi51c2VDYWxsYmFjaykoZnVuY3Rpb24gKHN0YXRlKSB7XG4gICAgaWYgKGlzTW91bnRlZC5jdXJyZW50KSBfc2V0U3RhdGUoc3RhdGUpO1xuICB9LCBbXSk7XG4gIHZhciBzZXRBbmNob3JFbCA9ICgwLCBfcmVhY3QyLnVzZUNhbGxiYWNrKShmdW5jdGlvbiAoYW5jaG9yRWwpIHtcbiAgICByZXR1cm4gc2V0U3RhdGUoZnVuY3Rpb24gKHN0YXRlKSB7XG4gICAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBzdGF0ZSksIHt9LCB7XG4gICAgICAgIHNldEFuY2hvckVsVXNlZDogdHJ1ZSxcbiAgICAgICAgYW5jaG9yRWw6IGFuY2hvckVsICE9PSBudWxsICYmIGFuY2hvckVsICE9PSB2b2lkIDAgPyBhbmNob3JFbCA6IHVuZGVmaW5lZFxuICAgICAgfSk7XG4gICAgfSk7XG4gIH0sIFtdKTtcbiAgdmFyIHRvZ2dsZSA9ICgwLCBfdXNlRXZlbnQudXNlRXZlbnQpKGZ1bmN0aW9uIChldmVudE9yQW5jaG9yRWwpIHtcbiAgICBpZiAoc3RhdGUuaXNPcGVuKSBjbG9zZShldmVudE9yQW5jaG9yRWwpO2Vsc2Ugb3BlbihldmVudE9yQW5jaG9yRWwpO1xuICAgIHJldHVybiBzdGF0ZTtcbiAgfSk7XG4gIHZhciBvcGVuID0gKDAsIF91c2VFdmVudC51c2VFdmVudCkoZnVuY3Rpb24gKGV2ZW50T3JBbmNob3JFbCkge1xuICAgIHZhciBldmVudCA9IGV2ZW50T3JBbmNob3JFbCBpbnN0YW5jZW9mIEVsZW1lbnQgPyB1bmRlZmluZWQgOiBldmVudE9yQW5jaG9yRWw7XG4gICAgdmFyIGVsZW1lbnQgPSBldmVudE9yQW5jaG9yRWwgaW5zdGFuY2VvZiBFbGVtZW50ID8gZXZlbnRPckFuY2hvckVsIDogKGV2ZW50T3JBbmNob3JFbCA9PT0gbnVsbCB8fCBldmVudE9yQW5jaG9yRWwgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGV2ZW50T3JBbmNob3JFbC5jdXJyZW50VGFyZ2V0KSBpbnN0YW5jZW9mIEVsZW1lbnQgPyBldmVudE9yQW5jaG9yRWwuY3VycmVudFRhcmdldCA6IHVuZGVmaW5lZDtcbiAgICBpZiAoKGV2ZW50ID09PSBudWxsIHx8IGV2ZW50ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBldmVudC50eXBlKSA9PT0gJ3RvdWNoc3RhcnQnKSB7XG4gICAgICBzZXRTdGF0ZShmdW5jdGlvbiAoc3RhdGUpIHtcbiAgICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgc3RhdGUpLCB7fSwge1xuICAgICAgICAgIF9kZWZlck5leHRPcGVuOiB0cnVlXG4gICAgICAgIH0pO1xuICAgICAgfSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHZhciBjbGllbnRYID0gZXZlbnQgPT09IG51bGwgfHwgZXZlbnQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGV2ZW50LmNsaWVudFg7XG4gICAgdmFyIGNsaWVudFkgPSBldmVudCA9PT0gbnVsbCB8fCBldmVudCA9PT0gdm9pZCAwID8gdm9pZCAwIDogZXZlbnQuY2xpZW50WTtcbiAgICB2YXIgYW5jaG9yUG9zaXRpb24gPSB0eXBlb2YgY2xpZW50WCA9PT0gJ251bWJlcicgJiYgdHlwZW9mIGNsaWVudFkgPT09ICdudW1iZXInID8ge1xuICAgICAgbGVmdDogY2xpZW50WCxcbiAgICAgIHRvcDogY2xpZW50WVxuICAgIH0gOiB1bmRlZmluZWQ7XG4gICAgdmFyIGRvT3BlbiA9IGZ1bmN0aW9uIGRvT3BlbihzdGF0ZSkge1xuICAgICAgaWYgKCFldmVudE9yQW5jaG9yRWwgJiYgIXN0YXRlLnNldEFuY2hvckVsVXNlZCAmJiB2YXJpYW50ICE9PSAnZGlhbG9nJykge1xuICAgICAgICB3YXJuKCdtaXNzaW5nRXZlbnRPckFuY2hvckVsJywgJ2V2ZW50T3JBbmNob3JFbCBzaG91bGQgYmUgZGVmaW5lZCBpZiBzZXRBbmNob3JFbCBpcyBub3QgdXNlZCcpO1xuICAgICAgfVxuICAgICAgaWYgKHBhcmVudFBvcHVwU3RhdGUpIHtcbiAgICAgICAgaWYgKCFwYXJlbnRQb3B1cFN0YXRlLmlzT3BlbikgcmV0dXJuIHN0YXRlO1xuICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICByZXR1cm4gcGFyZW50UG9wdXBTdGF0ZS5fc2V0Q2hpbGRQb3B1cFN0YXRlKHBvcHVwU3RhdGUpO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIHZhciBuZXdTdGF0ZSA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgc3RhdGUpLCB7fSwge1xuICAgICAgICBpc09wZW46IHRydWUsXG4gICAgICAgIGFuY2hvclBvc2l0aW9uOiBhbmNob3JQb3NpdGlvbixcbiAgICAgICAgaG92ZXJlZDogKGV2ZW50ID09PSBudWxsIHx8IGV2ZW50ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBldmVudC50eXBlKSA9PT0gJ21vdXNlb3ZlcicgfHwgc3RhdGUuaG92ZXJlZCxcbiAgICAgICAgZm9jdXNlZDogKGV2ZW50ID09PSBudWxsIHx8IGV2ZW50ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBldmVudC50eXBlKSA9PT0gJ2ZvY3VzJyB8fCBzdGF0ZS5mb2N1c2VkLFxuICAgICAgICBfb3BlbkV2ZW50VHlwZTogZXZlbnQgPT09IG51bGwgfHwgZXZlbnQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGV2ZW50LnR5cGVcbiAgICAgIH0pO1xuICAgICAgaWYgKCFzdGF0ZS5zZXRBbmNob3JFbFVzZWQpIHtcbiAgICAgICAgaWYgKGV2ZW50ICE9PSBudWxsICYmIGV2ZW50ICE9PSB2b2lkIDAgJiYgZXZlbnQuY3VycmVudFRhcmdldCkge1xuICAgICAgICAgIG5ld1N0YXRlLmFuY2hvckVsID0gZXZlbnQgPT09IG51bGwgfHwgZXZlbnQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGV2ZW50LmN1cnJlbnRUYXJnZXQ7XG4gICAgICAgIH0gZWxzZSBpZiAoZWxlbWVudCkge1xuICAgICAgICAgIG5ld1N0YXRlLmFuY2hvckVsID0gZWxlbWVudDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIG5ld1N0YXRlO1xuICAgIH07XG4gICAgc2V0U3RhdGUoZnVuY3Rpb24gKHN0YXRlKSB7XG4gICAgICBpZiAoc3RhdGUuX2RlZmVyTmV4dE9wZW4pIHtcbiAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgcmV0dXJuIHNldFN0YXRlKGRvT3Blbik7XG4gICAgICAgIH0sIDApO1xuICAgICAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBzdGF0ZSksIHt9LCB7XG4gICAgICAgICAgX2RlZmVyTmV4dE9wZW46IGZhbHNlXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGRvT3BlbihzdGF0ZSk7XG4gICAgICB9XG4gICAgfSk7XG4gIH0pO1xuICB2YXIgZG9DbG9zZSA9IGZ1bmN0aW9uIGRvQ2xvc2Uoc3RhdGUpIHtcbiAgICB2YXIgX2NoaWxkUG9wdXBTdGF0ZSA9IHN0YXRlLl9jaGlsZFBvcHVwU3RhdGU7XG4gICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7XG4gICAgICBfY2hpbGRQb3B1cFN0YXRlID09PSBudWxsIHx8IF9jaGlsZFBvcHVwU3RhdGUgPT09IHZvaWQgMCB8fCBfY2hpbGRQb3B1cFN0YXRlLmNsb3NlKCk7XG4gICAgICBwYXJlbnRQb3B1cFN0YXRlID09PSBudWxsIHx8IHBhcmVudFBvcHVwU3RhdGUgPT09IHZvaWQgMCB8fCBwYXJlbnRQb3B1cFN0YXRlLl9zZXRDaGlsZFBvcHVwU3RhdGUobnVsbCk7XG4gICAgfSk7XG4gICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgc3RhdGUpLCB7fSwge1xuICAgICAgaXNPcGVuOiBmYWxzZSxcbiAgICAgIGhvdmVyZWQ6IGZhbHNlLFxuICAgICAgZm9jdXNlZDogZmFsc2VcbiAgICB9KTtcbiAgfTtcbiAgdmFyIGNsb3NlID0gKDAsIF91c2VFdmVudC51c2VFdmVudCkoZnVuY3Rpb24gKGV2ZW50T3JBbmNob3JFbCkge1xuICAgIHZhciBldmVudCA9IGV2ZW50T3JBbmNob3JFbCBpbnN0YW5jZW9mIEVsZW1lbnQgPyB1bmRlZmluZWQgOiBldmVudE9yQW5jaG9yRWw7XG4gICAgaWYgKChldmVudCA9PT0gbnVsbCB8fCBldmVudCA9PT0gdm9pZCAwID8gdm9pZCAwIDogZXZlbnQudHlwZSkgPT09ICd0b3VjaHN0YXJ0Jykge1xuICAgICAgc2V0U3RhdGUoZnVuY3Rpb24gKHN0YXRlKSB7XG4gICAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHN0YXRlKSwge30sIHtcbiAgICAgICAgICBfZGVmZXJOZXh0Q2xvc2U6IHRydWVcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgc2V0U3RhdGUoZnVuY3Rpb24gKHN0YXRlKSB7XG4gICAgICBpZiAoc3RhdGUuX2RlZmVyTmV4dENsb3NlKSB7XG4gICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkge1xuICAgICAgICAgIHJldHVybiBzZXRTdGF0ZShkb0Nsb3NlKTtcbiAgICAgICAgfSwgMCk7XG4gICAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHN0YXRlKSwge30sIHtcbiAgICAgICAgICBfZGVmZXJOZXh0Q2xvc2U6IGZhbHNlXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGRvQ2xvc2Uoc3RhdGUpO1xuICAgICAgfVxuICAgIH0pO1xuICB9KTtcbiAgdmFyIHNldE9wZW4gPSAoMCwgX3JlYWN0Mi51c2VDYWxsYmFjaykoZnVuY3Rpb24gKG5leHRPcGVuLCBldmVudE9yQW5jaG9yRWwpIHtcbiAgICBpZiAobmV4dE9wZW4pIHtcbiAgICAgIG9wZW4oZXZlbnRPckFuY2hvckVsKTtcbiAgICB9IGVsc2Uge1xuICAgICAgY2xvc2UoZXZlbnRPckFuY2hvckVsKTtcbiAgICB9XG4gIH0sIFtdKTtcbiAgdmFyIG9uTW91c2VMZWF2ZSA9ICgwLCBfdXNlRXZlbnQudXNlRXZlbnQpKGZ1bmN0aW9uIChldmVudCkge1xuICAgIHZhciByZWxhdGVkVGFyZ2V0ID0gZXZlbnQucmVsYXRlZFRhcmdldDtcbiAgICBzZXRTdGF0ZShmdW5jdGlvbiAoc3RhdGUpIHtcbiAgICAgIGlmIChzdGF0ZS5ob3ZlcmVkICYmICEocmVsYXRlZFRhcmdldCBpbnN0YW5jZW9mIEVsZW1lbnQgJiYgaXNFbGVtZW50SW5Qb3B1cChyZWxhdGVkVGFyZ2V0LCBwb3B1cFN0YXRlKSkpIHtcbiAgICAgICAgaWYgKHN0YXRlLmZvY3VzZWQpIHtcbiAgICAgICAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBzdGF0ZSksIHt9LCB7XG4gICAgICAgICAgICBob3ZlcmVkOiBmYWxzZVxuICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiBkb0Nsb3NlKHN0YXRlKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIHN0YXRlO1xuICAgIH0pO1xuICB9KTtcbiAgdmFyIG9uQmx1ciA9ICgwLCBfdXNlRXZlbnQudXNlRXZlbnQpKGZ1bmN0aW9uIChldmVudCkge1xuICAgIGlmICghZXZlbnQpIHJldHVybjtcbiAgICB2YXIgcmVsYXRlZFRhcmdldCA9IGV2ZW50LnJlbGF0ZWRUYXJnZXQ7XG4gICAgc2V0U3RhdGUoZnVuY3Rpb24gKHN0YXRlKSB7XG4gICAgICBpZiAoc3RhdGUuZm9jdXNlZCAmJiAhKHJlbGF0ZWRUYXJnZXQgaW5zdGFuY2VvZiBFbGVtZW50ICYmIGlzRWxlbWVudEluUG9wdXAocmVsYXRlZFRhcmdldCwgcG9wdXBTdGF0ZSkpKSB7XG4gICAgICAgIGlmIChzdGF0ZS5ob3ZlcmVkKSB7XG4gICAgICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgc3RhdGUpLCB7fSwge1xuICAgICAgICAgICAgZm9jdXNlZDogZmFsc2VcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gZG9DbG9zZShzdGF0ZSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJldHVybiBzdGF0ZTtcbiAgICB9KTtcbiAgfSk7XG4gIHZhciBfc2V0Q2hpbGRQb3B1cFN0YXRlID0gKDAsIF9yZWFjdDIudXNlQ2FsbGJhY2spKGZ1bmN0aW9uIChfY2hpbGRQb3B1cFN0YXRlKSB7XG4gICAgcmV0dXJuIHNldFN0YXRlKGZ1bmN0aW9uIChzdGF0ZSkge1xuICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgc3RhdGUpLCB7fSwge1xuICAgICAgICBfY2hpbGRQb3B1cFN0YXRlOiBfY2hpbGRQb3B1cFN0YXRlXG4gICAgICB9KTtcbiAgICB9KTtcbiAgfSwgW10pO1xuICB2YXIgcG9wdXBTdGF0ZSA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgc3RhdGUpLCB7fSwge1xuICAgIHNldEFuY2hvckVsOiBzZXRBbmNob3JFbCxcbiAgICBwb3B1cElkOiBwb3B1cElkICE9PSBudWxsICYmIHBvcHVwSWQgIT09IHZvaWQgMCA/IHBvcHVwSWQgOiB1bmRlZmluZWQsXG4gICAgdmFyaWFudDogdmFyaWFudCxcbiAgICBvcGVuOiBvcGVuLFxuICAgIGNsb3NlOiBjbG9zZSxcbiAgICB0b2dnbGU6IHRvZ2dsZSxcbiAgICBzZXRPcGVuOiBzZXRPcGVuLFxuICAgIG9uQmx1cjogb25CbHVyLFxuICAgIG9uTW91c2VMZWF2ZTogb25Nb3VzZUxlYXZlLFxuICAgIGRpc2FibGVBdXRvRm9jdXM6IGRpc2FibGVBdXRvRm9jdXMgIT09IG51bGwgJiYgZGlzYWJsZUF1dG9Gb2N1cyAhPT0gdm9pZCAwID8gZGlzYWJsZUF1dG9Gb2N1cyA6IEJvb2xlYW4oc3RhdGUuaG92ZXJlZCB8fCBzdGF0ZS5mb2N1c2VkKSxcbiAgICBfc2V0Q2hpbGRQb3B1cFN0YXRlOiBfc2V0Q2hpbGRQb3B1cFN0YXRlXG4gIH0pO1xuICByZXR1cm4gcG9wdXBTdGF0ZTtcbn1cblxuLyoqXG4gKiBDcmVhdGVzIGEgcmVmIHRoYXQgc2V0cyB0aGUgYW5jaG9yRWwgZm9yIHRoZSBwb3B1cC5cbiAqXG4gKiBAcGFyYW0ge29iamVjdH0gcG9wdXBTdGF0ZSB0aGUgYXJndW1lbnQgcGFzc2VkIHRvIHRoZSBjaGlsZCBmdW5jdGlvbiBvZlxuICogYFBvcHVwU3RhdGVgXG4gKi9cbmZ1bmN0aW9uIGFuY2hvclJlZihfcmVmMikge1xuICB2YXIgc2V0QW5jaG9yRWwgPSBfcmVmMi5zZXRBbmNob3JFbDtcbiAgcmV0dXJuIHNldEFuY2hvckVsO1xufVxuZnVuY3Rpb24gY29udHJvbEFyaWFQcm9wcyhfcmVmMykge1xuICB2YXIgaXNPcGVuID0gX3JlZjMuaXNPcGVuLFxuICAgIHBvcHVwSWQgPSBfcmVmMy5wb3B1cElkLFxuICAgIHZhcmlhbnQgPSBfcmVmMy52YXJpYW50O1xuICByZXR1cm4gX29iamVjdFNwcmVhZCh7fSwgdmFyaWFudCA9PT0gJ3BvcG92ZXInID8ge1xuICAgICdhcmlhLWhhc3BvcHVwJzogdHJ1ZSxcbiAgICAnYXJpYS1jb250cm9scyc6IGlzT3BlbiA/IHBvcHVwSWQgOiB1bmRlZmluZWRcbiAgfSA6IHZhcmlhbnQgPT09ICdwb3BwZXInID8ge1xuICAgICdhcmlhLWRlc2NyaWJlZGJ5JzogaXNPcGVuID8gcG9wdXBJZCA6IHVuZGVmaW5lZFxuICB9IDogdW5kZWZpbmVkKTtcbn1cblxuLyoqXG4gKiBDcmVhdGVzIHByb3BzIGZvciBhIGNvbXBvbmVudCB0aGF0IG9wZW5zIHRoZSBwb3B1cCB3aGVuIGNsaWNrZWQuXG4gKlxuICogQHBhcmFtIHtvYmplY3R9IHBvcHVwU3RhdGUgdGhlIGFyZ3VtZW50IHBhc3NlZCB0byB0aGUgY2hpbGQgZnVuY3Rpb24gb2ZcbiAqIGBQb3B1cFN0YXRlYFxuICovXG5mdW5jdGlvbiBiaW5kVHJpZ2dlcihwb3B1cFN0YXRlKSB7XG4gIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGNvbnRyb2xBcmlhUHJvcHMocG9wdXBTdGF0ZSkpLCB7fSwge1xuICAgIG9uQ2xpY2s6IHBvcHVwU3RhdGUub3BlbixcbiAgICBvblRvdWNoU3RhcnQ6IHBvcHVwU3RhdGUub3BlblxuICB9KTtcbn1cblxuLyoqXG4gKiBDcmVhdGVzIHByb3BzIGZvciBhIGNvbXBvbmVudCB0aGF0IG9wZW5zIHRoZSBwb3B1cCBvbiBpdHMgY29udGV4dG1lbnUgZXZlbnQgKHJpZ2h0IGNsaWNrKS5cbiAqXG4gKiBAcGFyYW0ge29iamVjdH0gcG9wdXBTdGF0ZSB0aGUgYXJndW1lbnQgcGFzc2VkIHRvIHRoZSBjaGlsZCBmdW5jdGlvbiBvZlxuICogYFBvcHVwU3RhdGVgXG4gKi9cbmZ1bmN0aW9uIGJpbmRDb250ZXh0TWVudShwb3B1cFN0YXRlKSB7XG4gIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGNvbnRyb2xBcmlhUHJvcHMocG9wdXBTdGF0ZSkpLCB7fSwge1xuICAgIG9uQ29udGV4dE1lbnU6IGZ1bmN0aW9uIG9uQ29udGV4dE1lbnUoZSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgcG9wdXBTdGF0ZS5vcGVuKGUpO1xuICAgIH1cbiAgfSk7XG59XG5cbi8qKlxuICogQ3JlYXRlcyBwcm9wcyBmb3IgYSBjb21wb25lbnQgdGhhdCB0b2dnbGVzIHRoZSBwb3B1cCB3aGVuIGNsaWNrZWQuXG4gKlxuICogQHBhcmFtIHtvYmplY3R9IHBvcHVwU3RhdGUgdGhlIGFyZ3VtZW50IHBhc3NlZCB0byB0aGUgY2hpbGQgZnVuY3Rpb24gb2ZcbiAqIGBQb3B1cFN0YXRlYFxuICovXG5mdW5jdGlvbiBiaW5kVG9nZ2xlKHBvcHVwU3RhdGUpIHtcbiAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgY29udHJvbEFyaWFQcm9wcyhwb3B1cFN0YXRlKSksIHt9LCB7XG4gICAgb25DbGljazogcG9wdXBTdGF0ZS50b2dnbGUsXG4gICAgb25Ub3VjaFN0YXJ0OiBwb3B1cFN0YXRlLnRvZ2dsZVxuICB9KTtcbn1cblxuLyoqXG4gKiBDcmVhdGVzIHByb3BzIGZvciBhIGNvbXBvbmVudCB0aGF0IG9wZW5zIHRoZSBwb3B1cCB3aGlsZSBob3ZlcmVkLlxuICpcbiAqIEBwYXJhbSB7b2JqZWN0fSBwb3B1cFN0YXRlIHRoZSBhcmd1bWVudCBwYXNzZWQgdG8gdGhlIGNoaWxkIGZ1bmN0aW9uIG9mXG4gKiBgUG9wdXBTdGF0ZWBcbiAqL1xuZnVuY3Rpb24gYmluZEhvdmVyKHBvcHVwU3RhdGUpIHtcbiAgdmFyIG9wZW4gPSBwb3B1cFN0YXRlLm9wZW4sXG4gICAgb25Nb3VzZUxlYXZlID0gcG9wdXBTdGF0ZS5vbk1vdXNlTGVhdmU7XG4gIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGNvbnRyb2xBcmlhUHJvcHMocG9wdXBTdGF0ZSkpLCB7fSwge1xuICAgIG9uVG91Y2hTdGFydDogb3BlbixcbiAgICBvbk1vdXNlT3Zlcjogb3BlbixcbiAgICBvbk1vdXNlTGVhdmU6IG9uTW91c2VMZWF2ZVxuICB9KTtcbn1cblxuLyoqXG4gKiBDcmVhdGVzIHByb3BzIGZvciBhIGNvbXBvbmVudCB0aGF0IG9wZW5zIHRoZSBwb3B1cCB3aGlsZSBmb2N1c2VkLlxuICpcbiAqIEBwYXJhbSB7b2JqZWN0fSBwb3B1cFN0YXRlIHRoZSBhcmd1bWVudCBwYXNzZWQgdG8gdGhlIGNoaWxkIGZ1bmN0aW9uIG9mXG4gKiBgUG9wdXBTdGF0ZWBcbiAqL1xuZnVuY3Rpb24gYmluZEZvY3VzKHBvcHVwU3RhdGUpIHtcbiAgdmFyIG9wZW4gPSBwb3B1cFN0YXRlLm9wZW4sXG4gICAgb25CbHVyID0gcG9wdXBTdGF0ZS5vbkJsdXI7XG4gIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGNvbnRyb2xBcmlhUHJvcHMocG9wdXBTdGF0ZSkpLCB7fSwge1xuICAgIG9uRm9jdXM6IG9wZW4sXG4gICAgb25CbHVyOiBvbkJsdXJcbiAgfSk7XG59XG5cbi8qKlxuICogQ3JlYXRlcyBwcm9wcyBmb3IgYSBjb21wb25lbnQgdGhhdCBvcGVucyB0aGUgcG9wdXAgd2hpbGUgZG91YmxlIGNsaWNrLlxuICpcbiAqIEBwYXJhbSB7b2JqZWN0fSBwb3B1cFN0YXRlIHRoZSBhcmd1bWVudCBwYXNzZWQgdG8gdGhlIGNoaWxkIGZ1bmN0aW9uIG9mXG4gKiBgUG9wdXBTdGF0ZWBcbiAqL1xuZnVuY3Rpb24gYmluZERvdWJsZUNsaWNrKF9yZWY0KSB7XG4gIHZhciBpc09wZW4gPSBfcmVmNC5pc09wZW4sXG4gICAgb3BlbiA9IF9yZWY0Lm9wZW4sXG4gICAgcG9wdXBJZCA9IF9yZWY0LnBvcHVwSWQsXG4gICAgdmFyaWFudCA9IF9yZWY0LnZhcmlhbnQ7XG4gIHJldHVybiAoMCwgX2RlZmluZVByb3BlcnR5MltcImRlZmF1bHRcIl0pKCgwLCBfZGVmaW5lUHJvcGVydHkyW1wiZGVmYXVsdFwiXSkoKDAsIF9kZWZpbmVQcm9wZXJ0eTJbXCJkZWZhdWx0XCJdKSh7fSwgdmFyaWFudCA9PT0gJ3BvcG92ZXInID8gJ2FyaWEtY29udHJvbHMnIDogJ2FyaWEtZGVzY3JpYmVkYnknLCBpc09wZW4gPyBwb3B1cElkIDogbnVsbCksICdhcmlhLWhhc3BvcHVwJywgdmFyaWFudCA9PT0gJ3BvcG92ZXInID8gdHJ1ZSA6IHVuZGVmaW5lZCksIFwib25Eb3VibGVDbGlja1wiLCBvcGVuKTtcbn1cblxuLyoqXG4gKiBDcmVhdGVzIHByb3BzIGZvciBhIGBQb3BvdmVyYCBjb21wb25lbnQuXG4gKlxuICogQHBhcmFtIHtvYmplY3R9IHBvcHVwU3RhdGUgdGhlIGFyZ3VtZW50IHBhc3NlZCB0byB0aGUgY2hpbGQgZnVuY3Rpb24gb2ZcbiAqIGBQb3B1cFN0YXRlYFxuICovXG5mdW5jdGlvbiBiaW5kUG9wb3ZlcihfcmVmNikge1xuICB2YXIgaXNPcGVuID0gX3JlZjYuaXNPcGVuLFxuICAgIGFuY2hvckVsID0gX3JlZjYuYW5jaG9yRWwsXG4gICAgYW5jaG9yUG9zaXRpb24gPSBfcmVmNi5hbmNob3JQb3NpdGlvbixcbiAgICBjbG9zZSA9IF9yZWY2LmNsb3NlLFxuICAgIHBvcHVwSWQgPSBfcmVmNi5wb3B1cElkLFxuICAgIG9uTW91c2VMZWF2ZSA9IF9yZWY2Lm9uTW91c2VMZWF2ZSxcbiAgICBkaXNhYmxlQXV0b0ZvY3VzID0gX3JlZjYuZGlzYWJsZUF1dG9Gb2N1cyxcbiAgICBfb3BlbkV2ZW50VHlwZSA9IF9yZWY2Ll9vcGVuRXZlbnRUeXBlO1xuICB2YXIgdXNlUG9wb3ZlclBvc2l0aW9uID0gX29wZW5FdmVudFR5cGUgPT09ICdjb250ZXh0bWVudSc7XG4gIHJldHVybiBfb2JqZWN0U3ByZWFkKHtcbiAgICBpZDogcG9wdXBJZCxcbiAgICBhbmNob3JFbDogYW5jaG9yRWwsXG4gICAgYW5jaG9yUG9zaXRpb246IGFuY2hvclBvc2l0aW9uLFxuICAgIGFuY2hvclJlZmVyZW5jZTogdXNlUG9wb3ZlclBvc2l0aW9uID8gJ2FuY2hvclBvc2l0aW9uJyA6ICdhbmNob3JFbCcsXG4gICAgb3BlbjogaXNPcGVuLFxuICAgIG9uQ2xvc2U6IGNsb3NlLFxuICAgIG9uTW91c2VMZWF2ZTogb25Nb3VzZUxlYXZlXG4gIH0sIGRpc2FibGVBdXRvRm9jdXMgJiYge1xuICAgIGRpc2FibGVBdXRvRm9jdXM6IHRydWUsXG4gICAgZGlzYWJsZUVuZm9yY2VGb2N1czogdHJ1ZSxcbiAgICBkaXNhYmxlUmVzdG9yZUZvY3VzOiB0cnVlXG4gIH0pO1xufVxuXG4vKipcbiAqIENyZWF0ZXMgcHJvcHMgZm9yIGEgYE1lbnVgIGNvbXBvbmVudC5cbiAqXG4gKiBAcGFyYW0ge29iamVjdH0gcG9wdXBTdGF0ZSB0aGUgYXJndW1lbnQgcGFzc2VkIHRvIHRoZSBjaGlsZCBmdW5jdGlvbiBvZlxuICogYFBvcHVwU3RhdGVgXG4gKi9cblxuLyoqXG4gKiBDcmVhdGVzIHByb3BzIGZvciBhIGBQb3BvdmVyYCBjb21wb25lbnQuXG4gKlxuICogQHBhcmFtIHtvYmplY3R9IHBvcHVwU3RhdGUgdGhlIGFyZ3VtZW50IHBhc3NlZCB0byB0aGUgY2hpbGQgZnVuY3Rpb24gb2ZcbiAqIGBQb3B1cFN0YXRlYFxuICovXG5mdW5jdGlvbiBiaW5kTWVudShfcmVmNykge1xuICB2YXIgaXNPcGVuID0gX3JlZjcuaXNPcGVuLFxuICAgIGFuY2hvckVsID0gX3JlZjcuYW5jaG9yRWwsXG4gICAgYW5jaG9yUG9zaXRpb24gPSBfcmVmNy5hbmNob3JQb3NpdGlvbixcbiAgICBjbG9zZSA9IF9yZWY3LmNsb3NlLFxuICAgIHBvcHVwSWQgPSBfcmVmNy5wb3B1cElkLFxuICAgIG9uTW91c2VMZWF2ZSA9IF9yZWY3Lm9uTW91c2VMZWF2ZSxcbiAgICBkaXNhYmxlQXV0b0ZvY3VzID0gX3JlZjcuZGlzYWJsZUF1dG9Gb2N1cyxcbiAgICBfb3BlbkV2ZW50VHlwZSA9IF9yZWY3Ll9vcGVuRXZlbnRUeXBlO1xuICB2YXIgdXNlUG9wb3ZlclBvc2l0aW9uID0gX29wZW5FdmVudFR5cGUgPT09ICdjb250ZXh0bWVudSc7XG4gIHJldHVybiBfb2JqZWN0U3ByZWFkKHtcbiAgICBpZDogcG9wdXBJZCxcbiAgICBhbmNob3JFbDogYW5jaG9yRWwsXG4gICAgYW5jaG9yUG9zaXRpb246IGFuY2hvclBvc2l0aW9uLFxuICAgIGFuY2hvclJlZmVyZW5jZTogdXNlUG9wb3ZlclBvc2l0aW9uID8gJ2FuY2hvclBvc2l0aW9uJyA6ICdhbmNob3JFbCcsXG4gICAgb3BlbjogaXNPcGVuLFxuICAgIG9uQ2xvc2U6IGNsb3NlLFxuICAgIG9uTW91c2VMZWF2ZTogb25Nb3VzZUxlYXZlXG4gIH0sIGRpc2FibGVBdXRvRm9jdXMgJiYge1xuICAgIGF1dG9Gb2N1czogZmFsc2UsXG4gICAgZGlzYWJsZUF1dG9Gb2N1c0l0ZW06IHRydWUsXG4gICAgZGlzYWJsZUF1dG9Gb2N1czogdHJ1ZSxcbiAgICBkaXNhYmxlRW5mb3JjZUZvY3VzOiB0cnVlLFxuICAgIGRpc2FibGVSZXN0b3JlRm9jdXM6IHRydWVcbiAgfSk7XG59XG4vKipcbiAqIENyZWF0ZXMgcHJvcHMgZm9yIGEgYFBvcHBlcmAgY29tcG9uZW50LlxuICpcbiAqIEBwYXJhbSB7b2JqZWN0fSBwb3B1cFN0YXRlIHRoZSBhcmd1bWVudCBwYXNzZWQgdG8gdGhlIGNoaWxkIGZ1bmN0aW9uIG9mXG4gKiBgUG9wdXBTdGF0ZWBcbiAqL1xuZnVuY3Rpb24gYmluZFBvcHBlcihfcmVmOCkge1xuICB2YXIgaXNPcGVuID0gX3JlZjguaXNPcGVuLFxuICAgIGFuY2hvckVsID0gX3JlZjguYW5jaG9yRWwsXG4gICAgcG9wdXBJZCA9IF9yZWY4LnBvcHVwSWQsXG4gICAgb25Nb3VzZUxlYXZlID0gX3JlZjgub25Nb3VzZUxlYXZlO1xuICByZXR1cm4ge1xuICAgIGlkOiBwb3B1cElkLFxuICAgIGFuY2hvckVsOiBhbmNob3JFbCxcbiAgICBvcGVuOiBpc09wZW4sXG4gICAgb25Nb3VzZUxlYXZlOiBvbk1vdXNlTGVhdmVcbiAgfTtcbn1cblxuLyoqXG4gKiBDcmVhdGVzIHByb3BzIGZvciBhIGBEaWFsb2dgIGNvbXBvbmVudC5cbiAqXG4gKiBAcGFyYW0ge29iamVjdH0gcG9wdXBTdGF0ZSB0aGUgYXJndW1lbnQgcGFzc2VkIHRvIHRoZSBjaGlsZCBmdW5jdGlvbiBvZlxuICogYFBvcHVwU3RhdGVgXG4gKi9cbmZ1bmN0aW9uIGJpbmREaWFsb2coX3JlZjkpIHtcbiAgdmFyIGlzT3BlbiA9IF9yZWY5LmlzT3BlbixcbiAgICBjbG9zZSA9IF9yZWY5LmNsb3NlO1xuICByZXR1cm4ge1xuICAgIG9wZW46IGlzT3BlbixcbiAgICBvbkNsb3NlOiBjbG9zZVxuICB9O1xufVxuZnVuY3Rpb24gZ2V0UG9wdXAoZWxlbWVudCwgX3JlZjEwKSB7XG4gIHZhciBwb3B1cElkID0gX3JlZjEwLnBvcHVwSWQ7XG4gIGlmICghcG9wdXBJZCkgcmV0dXJuIG51bGw7XG4gIHZhciByb290Tm9kZSA9IHR5cGVvZiBlbGVtZW50LmdldFJvb3ROb2RlID09PSAnZnVuY3Rpb24nID8gZWxlbWVudC5nZXRSb290Tm9kZSgpIDogZG9jdW1lbnQ7XG4gIGlmICh0eXBlb2Ygcm9vdE5vZGUuZ2V0RWxlbWVudEJ5SWQgPT09ICdmdW5jdGlvbicpIHtcbiAgICByZXR1cm4gcm9vdE5vZGUuZ2V0RWxlbWVudEJ5SWQocG9wdXBJZCk7XG4gIH1cbiAgcmV0dXJuIG51bGw7XG59XG5mdW5jdGlvbiBpc0VsZW1lbnRJblBvcHVwKGVsZW1lbnQsIHBvcHVwU3RhdGUpIHtcbiAgdmFyIGFuY2hvckVsID0gcG9wdXBTdGF0ZS5hbmNob3JFbCxcbiAgICBfY2hpbGRQb3B1cFN0YXRlID0gcG9wdXBTdGF0ZS5fY2hpbGRQb3B1cFN0YXRlO1xuICByZXR1cm4gaXNBbmNlc3RvcihhbmNob3JFbCwgZWxlbWVudCkgfHwgaXNBbmNlc3RvcihnZXRQb3B1cChlbGVtZW50LCBwb3B1cFN0YXRlKSwgZWxlbWVudCkgfHwgX2NoaWxkUG9wdXBTdGF0ZSAhPSBudWxsICYmIGlzRWxlbWVudEluUG9wdXAoZWxlbWVudCwgX2NoaWxkUG9wdXBTdGF0ZSk7XG59XG5mdW5jdGlvbiBpc0FuY2VzdG9yKHBhcmVudCwgY2hpbGQpIHtcbiAgaWYgKCFwYXJlbnQpIHJldHVybiBmYWxzZTtcbiAgd2hpbGUgKGNoaWxkKSB7XG4gICAgaWYgKGNoaWxkID09PSBwYXJlbnQpIHJldHVybiB0cnVlO1xuICAgIGNoaWxkID0gY2hpbGQucGFyZW50RWxlbWVudDtcbiAgfVxuICByZXR1cm4gZmFsc2U7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ob29rcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/material-ui-popup-state/hooks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/material-ui-popup-state/useEvent.js":
/*!**********************************************************!*\
  !*** ./node_modules/material-ui-popup-state/useEvent.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/typeof.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.useEvent = useEvent;\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return { \"default\": e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n[\"default\"] = e, t && t.set(e, n), n; }\nfunction useEvent(handler) {\n  // istanbul ignore next\n  if (typeof window === 'undefined') {\n    // useLayoutEffect doesn't work on the server side, don't bother\n    // trying to make callback functions stable\n    return handler;\n  }\n  var handlerRef = React.useRef(null);\n  React.useLayoutEffect(function () {\n    handlerRef.current = handler;\n  });\n  return React.useCallback(function () {\n    var _handlerRef$current;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    (_handlerRef$current = handlerRef.current) === null || _handlerRef$current === void 0 || _handlerRef$current.call.apply(_handlerRef$current, [handlerRef].concat(args));\n  }, []);\n}\n//# sourceMappingURL=useEvent.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/material-ui-popup-state/useEvent.js\n");

/***/ })

};
;