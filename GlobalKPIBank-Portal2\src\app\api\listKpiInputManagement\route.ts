import { NextResponse } from 'next/server'
import { CogniteClient } from '@cognite/sdk'
import { cognite } from '../../../common/configurations/cognite'

async function postGraphQL(client, queryObj, allData = []) {
    try {
        let responseData: any = {}
        await client
            .post(
                '/api/v1/projects/celanese/userapis/spaces/INO-COR-ALL-DML/datamodels/GKPISOL/versions/2_0_3/graphql',
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Cdp-App': cognite.cogniteXCdpApp,
                    },
                    data: queryObj.reqBody,
                }
            )
            .then((result) => {
                responseData = result
            })
            .catch((error) => {
                console.log('catch error', error)
                responseData = error
            })

        if (responseData.data?.data?.listKPIManualInput?.items?.length > 0) {
            allData.push(...responseData.data.data.listKPIManualInput.items)
        }

        if (responseData.data?.data?.listKPIManualInput?.pageInfo?.hasNextPage) {
            const cursorQuery = {
                query: `{
                    listKPIManualInput(first:1000, after:"${responseData.data.data.listKPIManualInput.pageInfo.endCursor}", filter:{and: [{startDate: {gte: "2024-01-01"}},{endDate: {lte: "2024-12-31"}}]}){
                        pageInfo {
                        hasNextPage,
                        endCursor
                        }
                        items{
                        externalId,
                        refSite{
                            externalId,
                            name
                        }
                        description,
                        kpiValue,
                        startDate,
                        endDate
                        }
                    }
                }`,
            }

            const nextQuery = {
                project: queryObj.project,
                space: queryObj.space,
                datamodel: queryObj.datamodel,
                version: queryObj.version,
                reqBody: cursorQuery,
            }

            return postGraphQL(client, nextQuery, allData)
        } else {
            return allData
        }
    } catch (error) {
        console.log('catch error', error)
        throw error
    }
}

function transformData(data, allSites) {
    const groupedData = {}

    const months = [
        'january',
        'february',
        'march',
        'april',
        'may',
        'june',
        'july',
        'august',
        'september',
        'october',
        'november',
        'december',
    ]
    const kpis = ['Flawless days', 'Celanese Employee Headcount', 'Contractor Headcount']

    const sortedSites = [...allSites].sort((a, b) => {
        return a?.name?.localeCompare(b?.name) || 0
    })

    sortedSites.forEach((site) => {
        groupedData[site?.name] = {}
        kpis.forEach((description) => {
            groupedData[site?.name][description] = {
                category: 'Foundational',
                name: site?.name,
                description: description,
                ...Object.fromEntries(months.map((month) => [month, undefined])),
            }
        })
    })

    data.forEach((entry) => {
        const siteName = entry?.refSite?.name
        const description = entry.description

        if (!groupedData[siteName]) {
            groupedData[siteName] = {}
        }

        if (!groupedData[siteName][description]) {
            groupedData[siteName][description] = {
                category: entry.refKPICatogory?.name,
                name: siteName,
                description: description,
                ...Object.fromEntries(months.map((month) => [month, undefined])),
            }
        }

        const month = new Date(entry.startDate).toLocaleString('default', { month: 'long' }).toLowerCase()

        groupedData[siteName][description][month] = entry.kpiValue
    })

    const result: any[] = Object.keys(groupedData)
        .sort((a, b) => a.localeCompare(b))
        .map((siteName) => {
            const kpiArray = Object.values(groupedData[siteName]).map((entry, index) => ({
                ...(entry as object),
                order: index + 1,
            }))

            return {
                kpi: kpiArray,
                data: kpiArray[0],
            }
        })

    return result
}

export async function POST(req: Request) {
    const data = await req.json()
    const authHeader = req.headers.get('authorization')
    const token: any = authHeader?.split('Bearer ')[1]
    const reqQuery = {
        query: `{
        listKPIManualInput(first:1000,filter:{and: [{startDate: {gte: "${data.manualTable.year}-01-01"}},{endDate: {lte: "${data.manualTable.year}-12-31"}}]}){
            pageInfo {
            hasNextPage,
            endCursor
            }
            items{
            externalId,
            refSite{
                externalId,
                name
            }
            description,
            kpiValue,
            startDate,
            endDate
            }
        }
    }`,
    }

    const client = new CogniteClient({
        appId: cognite.appId,
        baseUrl: cognite.baseUrl,
        project: cognite.project,
        getToken: () => token,
    })

    const queryObj = {
        project: cognite.project,
        space: 'INO-COR-ALL-DML',
        datamodel: 'GKPISOL',
        version: cognite.cogniteApiVersion,
        reqBody: reqQuery,
    }

    const result = await postGraphQL(client, queryObj)
    const sanitizedResult = transformData(result, data.sites)
    return NextResponse.json(sanitizedResult, { status: 200 })
}
