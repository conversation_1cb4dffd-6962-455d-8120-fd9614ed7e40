import { UserSite } from '../clients/user-management-client'

const UserSiteCacheName = 'UserSiteData'

export const getLocalUserSite = (): UserSite | undefined => {
    let userSite = undefined

    if (typeof window !== 'undefined') {
        const jsonLocal = window.localStorage.getItem(UserSiteCacheName)

        if (jsonLocal && jsonLocal !== 'undefined') userSite = JSON.parse(jsonLocal) as UserSite
    }

    return userSite
}

export const saveLocalUserSite = (userSite: UserSite) => {
    if (typeof window !== 'undefined' && userSite) {
        window.localStorage.setItem(UserSiteCacheName, JSON.stringify(userSite))
    }
}
