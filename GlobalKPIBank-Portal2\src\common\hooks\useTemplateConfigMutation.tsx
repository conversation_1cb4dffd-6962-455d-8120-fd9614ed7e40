import { Entity, ExternalEntity, MutationState, TemplateConfig } from '../models'
import { useCallback } from 'react'
import { EdgeInstance } from '../models/edge-instance'
import { useFdmMutation } from '.'
import { EntityType, GetSpace } from '../utils/space-util'
import { getLocalUserSite } from '../utils'

export type TemplateConfigMutationArgs<T extends Entity> = {
    newEntities: T[]
    fieldsToIgnore?: (keyof T)[]
}

export type TemplateConfigDeleterArgs<T extends Entity> = {
    entitiesToDelete: T[]
}

export type UseTemplateConfigMutationResult<T extends Entity> = [
    (args: TemplateConfigMutationArgs<T>) => Promise<any>,
    MutationState
]

export interface TemplateConfigMutation extends ExternalEntity {
    checklistNotificationConfigs?: ExternalEntity[]
    reportingUnit: ExternalEntity
    reportingLocation: ExternalEntity
    userManagementRoles?: ExternalEntity[]
    userManagementRolesToBeNotified?: ExternalEntity[]
    template: ExternalEntity
    disciplines: string[]
    daysUntilDue?: string
    shiftConfigurations?: ExternalEntity[]
}

export const useTemplateConfigMutation = (): UseTemplateConfigMutationResult<TemplateConfig> => {
    const [templateConfigMutation, templateConfigMutationStatus] = useFdmMutation<TemplateConfigMutation>(
        'ChecklistTemplateConfig',
        false
    )

    const convertNotificationConfigsToEdges = (templateConfigs: TemplateConfig[]): EdgeInstance[] => {
        return templateConfigs
            .map((templateConfig) => {
                return templateConfig.checklistNotificationConfigs.map((notification) => {
                    const edgeExternalId =
                        'CHECKLISTNOTIFICATIONCONFIGS-' + templateConfig.externalId + '-' + notification.externalId
                    const modelSpace = GetSpace(EntityType.Model)
                    const instanceSpace = templateConfig.space
                        ? templateConfig.space
                        : GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode)

                    const edge: EdgeInstance = {
                        instanceType: 'edge',
                        space: instanceSpace,
                        externalId: edgeExternalId,
                        startNode: {
                            externalId: templateConfig.externalId,
                            space: instanceSpace,
                        },
                        endNode: {
                            externalId: notification.externalId,
                            space: notification.space,
                        },
                        type: {
                            externalId: 'ChecklistTemplateConfig.checklistNotificationConfigs',
                            space: modelSpace,
                        },
                    }

                    return edge
                })
            })
            .flat()
    }

    const convertAssignedRoles = (templateConfigs: TemplateConfig[]): EdgeInstance[] => {
        return templateConfigs
            .map((templateConfig) => {
                return templateConfig.assignedRoles.map((role) => {
                    const edgeExternalId = 'CHECKLISTASSIGNEDROLES-' + templateConfig.externalId + '-' + role.externalId
                    const modelSpace = GetSpace(EntityType.Model)
                    const instanceSpace = templateConfig.space
                        ? templateConfig.space
                        : GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode)

                    const edge: EdgeInstance = {
                        instanceType: 'edge',
                        space: instanceSpace,
                        externalId: edgeExternalId,
                        startNode: {
                            externalId: templateConfig.externalId,
                            space: instanceSpace,
                        },
                        endNode: {
                            externalId: role.externalId,
                            space: role.space,
                        },
                        type: {
                            externalId: 'ChecklistTemplateConfig.assignedRoles',
                            space: modelSpace,
                        },
                    }

                    return edge
                })
            })
            .flat()
    }

    const convertRolesToBeNotified = (templateConfigs: TemplateConfig[]): EdgeInstance[] => {
        return templateConfigs
            .map((templateConfig) => {
                return templateConfig.rolesToBeNotified.map((role) => {
                    const edgeExternalId =
                        'CHECKLISTROLESTOBENOTIFIED-' + templateConfig.externalId + '-' + role.externalId
                    const modelSpace = GetSpace(EntityType.Model)
                    const instanceSpace = templateConfig.space
                        ? templateConfig.space
                        : GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode)

                    const edge: EdgeInstance = {
                        instanceType: 'edge',
                        space: instanceSpace,
                        externalId: edgeExternalId,
                        startNode: {
                            externalId: templateConfig.externalId,
                            space: instanceSpace,
                        },
                        endNode: {
                            externalId: role.externalId,
                            space: role.space,
                        },
                        type: {
                            externalId: 'ChecklistTemplateConfig.rolesToBeNotified',
                            space: modelSpace,
                        },
                    }

                    return edge
                })
            })
            .flat()
    }

    const convertShifts = (templateConfigs: TemplateConfig[]): EdgeInstance[] => {
        return templateConfigs
            .map((templateConfig) => {
                return templateConfig.shiftConfigurations.map((shift) => {
                    const edgeExternalId = 'CHECKLISTSHIFT-' + templateConfig.externalId + '-' + shift.externalId
                    const modelSpace = GetSpace(EntityType.Model)
                    const instanceSpace = templateConfig.space
                        ? templateConfig.space
                        : GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode)

                    const edge: EdgeInstance = {
                        instanceType: 'edge',
                        space: instanceSpace,
                        externalId: edgeExternalId,
                        startNode: {
                            externalId: templateConfig.externalId,
                            space: instanceSpace,
                        },
                        endNode: {
                            externalId: shift.externalId,
                            space: shift.space,
                        },
                        type: {
                            externalId: 'ChecklistTemplateConfig.shiftConfigurations',
                            space: modelSpace,
                        },
                    }

                    return edge
                })
            })
            .flat()
    }

    const newMutationFunction = useCallback(
        async ({ newEntities }: TemplateConfigMutationArgs<TemplateConfig>) => {
            const newEntitiesWithoutEdges = newEntities.map(
                (e) =>
                    ({
                        externalId: e.externalId,
                        space: e.space ? e.space : GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode),
                        checklistNotificationConfigs: undefined,
                        reportingUnit: e.reportingUnit,
                        reportingLocation: e.reportingLocation,
                        userManagementRoles: undefined,
                        userManagementRolesToBeNotified: undefined,
                        template: e.template,
                        disciplines: e.disciplines,
                        daysUntilDue: e.daysUntilDue,
                        shiftConfigurations: undefined,
                    } as TemplateConfigMutation)
            )

            const edgeInstances: EdgeInstance[] = []

            edgeInstances.push(...convertNotificationConfigsToEdges(newEntities))
            edgeInstances.push(...convertAssignedRoles(newEntities))
            edgeInstances.push(...convertRolesToBeNotified(newEntities))
            edgeInstances.push(...convertShifts(newEntities))

            const templateConfigMutationResult = await templateConfigMutation(newEntitiesWithoutEdges, edgeInstances)

            return {
                ok: templateConfigMutationResult.ok,
                data: templateConfigMutationResult.data,
                error: templateConfigMutationResult.error,
            }
        },
        [templateConfigMutation]
    )

    const newResetFunction = useCallback(() => {
        templateConfigMutationStatus.reset()
    }, [templateConfigMutationStatus])

    return [newMutationFunction, { ...templateConfigMutationStatus, reset: newResetFunction }]
}
