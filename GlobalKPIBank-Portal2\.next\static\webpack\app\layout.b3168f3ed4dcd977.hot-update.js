"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/LayoutHeaderNavBar/index.tsx":
/*!*****************************************************!*\
  !*** ./src/components/LayoutHeaderNavBar/index.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderNavBar: function() { return /* binding */ HeaderNavBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _celanese_ui_lib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @celanese/ui-lib */ \"(app-pages-browser)/./node_modules/@celanese/ui-lib/dist/celanese-ui-lib.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_icons_material_Language__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/icons-material/Language */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Language.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @celanese/celanese-sdk */ \"(app-pages-browser)/./node_modules/@celanese/celanese-sdk/build/esm/index.ts\");\n/* harmony import */ var _common_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/common/hooks/useAuthGuard */ \"(app-pages-browser)/./src/common/hooks/useAuthGuard.tsx\");\n/* harmony import */ var _UserPopover__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../UserPopover */ \"(app-pages-browser)/./src/components/UserPopover/index.tsx\");\n/* harmony import */ var _common_hooks_useLocale__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/common/hooks/useLocale */ \"(app-pages-browser)/./src/common/hooks/useLocale.tsx\");\n/* harmony import */ var _common_contexts_AuthGuardContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/common/contexts/AuthGuardContext */ \"(app-pages-browser)/./src/common/contexts/AuthGuardContext.tsx\");\n/* harmony import */ var _common_configurations_enviroment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/common/configurations/enviroment */ \"(app-pages-browser)/./src/common/configurations/enviroment.ts\");\n/* harmony import */ var _barrel_optimize_names_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CircularProgress!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _mui_icons_material_Translate__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/icons-material/Translate */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Translate.js\");\n/* harmony import */ var _BaseLayout_style__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BaseLayout.style */ \"(app-pages-browser)/./src/components/LayoutHeaderNavBar/BaseLayout.style.ts\");\n/* harmony import */ var _common_theme_globalStyles__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/common/theme/globalStyles */ \"(app-pages-browser)/./src/common/theme/globalStyles.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_SITE = {\n    externalId: \"STS-COR\",\n    value: \"All Sites\"\n};\nconst HeaderNavBar = (param)=>{\n    let { children, setLocaleCode, shouldTranslateDynamicState, dynamicTranslationLoadingState } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [authorizedMenuItems, setAuthorizedMenuItems] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { checkPermissionsFromRoutes } = (0,_common_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_5__.useAuthGuard)();\n    const [anchorElUser, setAnchorElUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedReportingSite, setSelectedReportingSite] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(undefined);\n    const { userInfo, updateSiteInfo } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_common_contexts_AuthGuardContext__WEBPACK_IMPORTED_MODULE_8__.AuthGuardContext);\n    const { availableLanguages, locale, updateLocale } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.TranslationContext);\n    const [disableTranslateDynamic, setDisableTranslateDynamic] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const { shouldTranslateDynamic, setShouldTranslateDynamic } = shouldTranslateDynamicState;\n    const { dynamicTranslationLoading, setDynamicTranslationLoading } = dynamicTranslationLoadingState;\n    const [isTranslated, setIsTranslated] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if ((userInfo === null || userInfo === void 0 ? void 0 : userInfo.availableReportingSites) && userInfo.availableReportingSites.length > 0) {\n            setSelectedReportingSite(userInfo.selectedSite);\n        }\n    }, [\n        userInfo\n    ]);\n    const handleOpenUserMenu = (event)=>{\n        setAnchorElUser(event.currentTarget);\n    };\n    const handleCloseUserMenu = ()=>{\n        setAnchorElUser(null);\n    };\n    const selectedLanguage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (availableLanguages === null || availableLanguages === void 0 ? void 0 : availableLanguages.length) {\n            var _availableLanguages_find;\n            return (_availableLanguages_find = availableLanguages.find((l)=>(l === null || l === void 0 ? void 0 : l.code) == locale)) !== null && _availableLanguages_find !== void 0 ? _availableLanguages_find : availableLanguages.find((l)=>(l === null || l === void 0 ? void 0 : l.code) == _common_hooks_useLocale__WEBPACK_IMPORTED_MODULE_7__.DEFAULT_LOCALE);\n        }\n        return {\n            externalId: \"EN\",\n            language: \"English\",\n            code: \"EN\"\n        };\n    }, [\n        locale,\n        availableLanguages\n    ]);\n    const availableMenuItems = [\n        {\n            route: \"/home\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"MENU.HOME\"),\n            icon: \"home\",\n            onClickHandler: ()=>{\n                router.push(\"/home\");\n            }\n        },\n        {\n            route: \"/site-view\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"MENU.SITE_VIEW\"),\n            icon: \"factory\",\n            onClickHandler: ()=>{\n                router.push(\"/site-view\");\n            }\n        },\n        {\n            route: \"/global-view\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"MENU.GLOBAL_VIEW\"),\n            icon: \"language\",\n            onClickHandler: ()=>{\n                router.push(\"/global-view\");\n            }\n        },\n        {\n            route: \"/kpi-target\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"KPI_TARGET.TARGET_SETTINGS\"),\n            icon: \"adjust\",\n            onClickHandler: ()=>{\n                router.push(\"/kpi-target\");\n            }\n        },\n        {\n            route: \"/manual-input\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"MENU.MANUAL_INPUT\"),\n            icon: \"settings\",\n            onClickHandler: ()=>{\n                router.push(\"/manual-input\");\n            }\n        },\n        {\n            route: \"/kpi-input-management\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"MENU.KPI_INPUT_MANAGEMENT\"),\n            icon: \"data_check\",\n            onClickHandler: ()=>{\n                router.push(\"/kpi-input-management\");\n            }\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const isTitleTranslated = availableMenuItems.every((item)=>!item.title.includes(\"MENU.\"));\n        setIsTranslated(isTitleTranslated);\n    }, [\n        availableMenuItems,\n        locale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const items = availableMenuItems.filter((item)=>checkPermissionsFromRoutes(item.route));\n        setAuthorizedMenuItems(items);\n    }, [\n        userInfo,\n        isTranslated,\n        locale\n    ]);\n    const DynamicTranslationIcon = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                dynamicTranslationLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 20,\n                    color: \"inherit\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 47\n                }, undefined),\n                disableTranslateDynamic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Translate__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"translate-ico\",\n                    sx: _BaseLayout_style__WEBPACK_IMPORTED_MODULE_10__.dynamicTranslationDisabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 21\n                }, undefined) : shouldTranslateDynamic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Translate__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"translate-ico\",\n                    sx: _BaseLayout_style__WEBPACK_IMPORTED_MODULE_10__.dynamicTranslationOff\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Translate__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"translate-ico\",\n                    sx: _BaseLayout_style__WEBPACK_IMPORTED_MODULE_10__.dynamicTranslationOn\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    const headerNavbarProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _availableLanguages_map;\n        return {\n            disableDarkMode: true,\n            logo: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Language__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    fontSize: \"26px\",\n                    marginTop: \"5px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                lineNumber: 165,\n                columnNumber: 19\n            }, undefined),\n            alt: userInfo.lastName + \", \" + userInfo.firstName,\n            src: userInfo.avatar,\n            nameBold: \"Digital Global\",\n            nameLight: \"KPI Bank\",\n            language: selectedLanguage,\n            languages: (_availableLanguages_map = availableLanguages === null || availableLanguages === void 0 ? void 0 : availableLanguages.map((language)=>({\n                    ...language,\n                    language: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.NoTranslate, {\n                        children: language.language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 31\n                    }, undefined)\n                }))) !== null && _availableLanguages_map !== void 0 ? _availableLanguages_map : [],\n            selectedPlants: [\n                DEFAULT_SITE\n            ],\n            plants: [\n                DEFAULT_SITE\n            ],\n            multPlants: false,\n            items: authorizedMenuItems,\n            buttonConfigurations: [\n                {\n                    Icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_ui_lib__WEBPACK_IMPORTED_MODULE_1__.MatIcon, {\n                        icon: \"notifications\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 27\n                    }, undefined),\n                    Tooltip: \"Notifications\",\n                    onClick: ()=>{\n                         true && window.open(_common_configurations_enviroment__WEBPACK_IMPORTED_MODULE_9__.enviroment.notificationPortalUrl, \"_blank\");\n                    }\n                },\n                {\n                    Icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicTranslationIcon, {}, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 27\n                    }, undefined),\n                    Tooltip: \"\",\n                    onClick: ()=>{},\n                    disabled: true\n                },\n                {\n                    Icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_ui_lib__WEBPACK_IMPORTED_MODULE_1__.MatIcon, {\n                        icon: \"dark_mode\",\n                        sx: {\n                            color: \"#********\",\n                            fontSize: \"1.5rem\",\n                            mt: 0.5\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 27\n                    }, undefined),\n                    Tooltip: \"\",\n                    onClick: ()=>{},\n                    disabled: true\n                }\n            ],\n            onChangeLang: (li)=>{\n                if (li) {\n                    updateLocale(li.code);\n                    setLocaleCode(li.code);\n                }\n            },\n            onChangePlant: (_)=>{},\n            userMenuOnClick: handleOpenUserMenu,\n            sxProps: {\n                fontFamily: 'Roboto, \"Segoe UI\", Arial, Helvetica, sans-serif'\n            }\n        };\n    }, [\n        authorizedMenuItems,\n        selectedLanguage,\n        selectedReportingSite,\n        userInfo\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_theme_globalStyles__WEBPACK_IMPORTED_MODULE_11__.GlobalStyles, {}, void 0, false, {\n                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                lineNumber: 217,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.NoTranslate, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_ui_lib__WEBPACK_IMPORTED_MODULE_1__.ClnHeaderNavbar, {\n                        ...headerNavbarProps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserPopover__WEBPACK_IMPORTED_MODULE_6__.UserPopover, {\n                        anchorEl: anchorElUser,\n                        onClose: handleCloseUserMenu\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                lineNumber: 218,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(HeaderNavBar, \"+FawANvfe72my/A+QQcQqihy8dQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _common_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_5__.useAuthGuard\n    ];\n});\n_c = HeaderNavBar;\nvar _c;\n$RefreshReg$(_c, \"HeaderNavBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LayoutHeaderNavBar/index.tsx\n"));

/***/ })

});