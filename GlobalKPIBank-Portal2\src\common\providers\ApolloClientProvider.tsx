import { ApolloProvider } from '@apollo/client'
import React, { PropsWithChildren } from 'react'
import { useAuthToken } from '../hooks'
import { createApolloClient } from '../factories/apollo-client-factory'

export const ApolloClientProvider = ({ children }: PropsWithChildren) => {
    const { getAuthToken } = useAuthToken()
    const client = React.useMemo(() => createApolloClient(getAuthToken), [getAuthToken])

    return <ApolloProvider client={client}>{children}</ApolloProvider>
}
