import { translate } from '@celanese/celanese-sdk'
import { Autocomplete, Box, Grid, TextField, styled } from '@mui/material'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { ClnButton, ClnDatePicker } from '@celanese/ui-lib'
import { Site } from '@/common/models/site'
import { KpiGroup } from '@/common/models/kpiGroup'
import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { AuthGuardContext } from '@/common/contexts/AuthGuardContext'
import { useContext } from 'react'

interface KpiTargetFiltersProps {
    sites: Site[]
    groups: KpiGroup[]
    onSubmit: (filters: any) => void
}

const kpiTargetFilterSchema = z.object({
    site: z.object({
        externalId: z.string(),
        description: z.string(),
    }),
    group: z.object({
        externalId: z.string(),
        name: z.string(),
    }),
    year: z.string(),
})

type KpiTargetFilterSchema = z.infer<typeof kpiTargetFilterSchema>

const Form = styled('form')({})

export const KPITargetFilters: React.FC<KpiTargetFiltersProps> = ({ sites, groups, onSubmit }) => {
    dayjs.extend(utc)
    dayjs.extend(timezone)
    const { userInfo } = useContext(AuthGuardContext)
    const { setValue, watch, handleSubmit, control } = useForm<KpiTargetFilterSchema>({
        defaultValues: {
            site: {},
            group: {},
            year: '',
        },
        resolver: zodResolver(kpiTargetFilterSchema),
    })
    const auxSite = watch(`site`)
    const auxGroup = watch(`group`)
    const auxYear = watch(`year`)
    const [isDisabled, setIsDisabled] = useState<boolean>(true)
    const minDateCalendar = dayjs().startOf('year').subtract(2, 'year')
    const maxDateCalendar = dayjs().startOf('month')

    useEffect(() => {
        const isValidSite = auxSite.externalId !== undefined && auxSite.externalId !== ''
        const isValidGroup = auxGroup.externalId !== undefined && auxGroup.externalId !== ''
        if (isValidSite && isValidGroup && auxYear !== '') {
            setIsDisabled(false)
        } else {
            setIsDisabled(true)
        }
    }, [auxSite.externalId, auxGroup.externalId, auxYear])

    const handleFiltersSubmit = (data: any) => {
        onSubmit(data)
    }

    return (
        <Form onSubmit={handleSubmit(handleFiltersSubmit)}>
            <Grid container spacing={2} sx={{ height: '100%', marginTop: '5px' }}>
                <Grid item xs={6} md={4.5}>
                    <Controller
                        control={control}
                        name="site"
                        render={({ field }) => (
                            <Autocomplete
                                {...field}
                                loading
                                limitTags={1}
                                size="small"
                                id="site"
                                options={
                                    userInfo.applications[0]?.roles[0]?.roleName === 'GKPI Site Admin'
                                        ? sites.filter((ele) => {
                                              return userInfo.applications[0]?.userSites?.some(
                                                  (item) => item.siteId == ele.externalId
                                              )
                                          })
                                        : sites
                                }
                                getOptionLabel={(option) => option.description || ''}
                                value={field.value || null}
                                onChange={(event, value) => {
                                    if (!value) {
                                        setValue('site', { externalId: '', description: '' })
                                    } else {
                                        setValue('site', value)
                                    }
                                }}
                                renderInput={(params) => (
                                    <TextField {...params} label={translate('COMMONFILTER.SITE')} size="small" />
                                )}
                                renderOption={(props, option) => (
                                    <li {...props} key={option.externalId} data-value={option}>
                                        <span>{option.description}</span>
                                    </li>
                                )}
                            />
                        )}
                    />
                </Grid>

                <Grid item xs={6} md={4.5}>
                    <Controller
                        control={control}
                        name="group"
                        render={({ field }) => (
                            <Autocomplete
                                {...field}
                                loading
                                limitTags={1}
                                size="small"
                                id="group"
                                options={groups}
                                getOptionLabel={(option) => option.name || ''}
                                value={field.value || null}
                                onChange={(_, value) => {
                                    if (!value) {
                                        setValue('group', { externalId: '', name: '' })
                                    } else {
                                        setValue('group', value)
                                    }
                                }}
                                renderInput={(params) => (
                                    <TextField {...params} label={translate('COMMONFILTER.CATEGORY')} size="small" />
                                )}
                                renderOption={(props, option) => (
                                    <li {...props} key={option.externalId} data-value={option}>
                                        <span>{option.name}</span>
                                    </li>
                                )}
                            />
                        )}
                    />
                </Grid>

                <Grid item xs={6} md={2}>
                    <Controller
                        control={control}
                        name="year"
                        render={({ field }) => (
                            <ClnDatePicker
                                label={translate('COMMONFILTER.YEAR')}
                                size="small"
                                views={['year']}
                                minDate={minDateCalendar}
                                maxDate={maxDateCalendar}
                                onChange={(date) => {
                                    if (date) {
                                        setValue('year', date.format('YYYY'))
                                    } else {
                                        setValue('year', '')
                                    }
                                }}
                                sx={{ width: '100%' }}
                            />
                        )}
                    />
                </Grid>

                <Grid item xs={6} md={1}>
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            justifyContent: 'center',
                            margin: '5px 0 10px 0',
                            div: {
                                width: '100%',
                            },
                            button: {
                                width: '100%',
                            },
                        }}
                    >
                        <ClnButton
                            type="submit"
                            size="small"
                            variant="contained"
                            disabled={isDisabled}
                            label={translate('COMMONBUTTON.APPLY')}
                            fullWidth
                        />
                    </Box>
                </Grid>
            </Grid>
        </Form>
    )
}
