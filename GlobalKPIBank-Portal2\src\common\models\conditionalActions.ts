export interface ConditionalAction {
    instanceType: string
    version: number
    space: string
    externalId: string
    createdTime: number
    lastUpdatedTime: number
    properties: {
        cdf_apm: {
            'ConditionalAction/v1': {
                logic: string
                parentObject: {
                    space: string
                    externalId: string
                }
                createdBy: {
                    space: string
                    externalId: string
                }
            }
        }
    }
}

export interface Condition {
    instanceType: string
    version: number
    space: string
    externalId: string
    createdTime: number
    lastUpdatedTime: number
    properties: {
        cdf_apm: {
            'Condition/v1': {
                field: string
                value: string
                source: {
                    space: string
                    externalId: string
                }
                operator: string
                sourceView: string
                conditionalAction: {
                    space: string
                    externalId: string
                }
                createdBy: {
                    space: string
                    externalId: string
                }
                updatedBy: {
                    space: string
                    externalId: string
                }
            }
        }
    }
}

export interface ParentObjectItem {
    instanceType: string
    version: number
    space: string
    externalId: string
    createdTime: number
    lastUpdatedTime: number
    properties: {
        cdf_apm: {
            'ChecklistItem/v1': {
                asset?: {
                    space: string
                    externalId: string
                }
                order: number
                status: string
                createdBy: {
                    space: string
                    externalId: string
                }
                updatedBy: {
                    space: string
                    externalId: string
                }
                title: string
                description?: string
                endTime: string
                startTime: string
                sourceId: string
            }
        }
    }
}

export interface Action {
    instanceType: string
    version: number
    space: string
    externalId: string
    createdTime: number
    lastUpdatedTime: number
    properties: {
        cdf_apm: {
            'Action/v1': {
                actionType: string
                parameters: {
                    message: string
                }
                conditionalActions: {
                    space: string
                    externalId: string
                }
                createdBy: {
                    space: string
                    externalId: string
                }
                updatedBy: {
                    space: string
                    externalId: string
                }
            }
        }
    }
}

export interface NextCursor {
    conditionalActions: string
    conditions: string
    parentObjectItems: string
    actions: string
}

export interface ConditionalActionsObject {
    conditionalActions: ConditionalAction[]
    conditions: Condition[]
    parentObjectItems: ParentObjectItem[]
    actions: Action[]
}
