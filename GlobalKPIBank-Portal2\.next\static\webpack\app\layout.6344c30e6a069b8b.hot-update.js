"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/components/LayoutHeaderNavBar/index.tsx":
/*!*****************************************************!*\
  !*** ./src/components/LayoutHeaderNavBar/index.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderNavBar: function() { return /* binding */ HeaderNavBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _celanese_ui_lib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @celanese/ui-lib */ \"(app-pages-browser)/./node_modules/@celanese/ui-lib/dist/celanese-ui-lib.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_icons_material_Language__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mui/icons-material/Language */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Language.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @celanese/celanese-sdk */ \"(app-pages-browser)/./node_modules/@celanese/celanese-sdk/build/esm/index.ts\");\n/* harmony import */ var _common_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/common/hooks/useAuthGuard */ \"(app-pages-browser)/./src/common/hooks/useAuthGuard.tsx\");\n/* harmony import */ var _UserPopover__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../UserPopover */ \"(app-pages-browser)/./src/components/UserPopover/index.tsx\");\n/* harmony import */ var _common_hooks_useLocale__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/common/hooks/useLocale */ \"(app-pages-browser)/./src/common/hooks/useLocale.tsx\");\n/* harmony import */ var _common_contexts_AuthGuardContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/common/contexts/AuthGuardContext */ \"(app-pages-browser)/./src/common/contexts/AuthGuardContext.tsx\");\n/* harmony import */ var _common_configurations_enviroment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/common/configurations/enviroment */ \"(app-pages-browser)/./src/common/configurations/enviroment.ts\");\n/* harmony import */ var _barrel_optimize_names_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CircularProgress!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _mui_icons_material_Translate__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/icons-material/Translate */ \"(app-pages-browser)/./node_modules/@mui/icons-material/Translate.js\");\n/* harmony import */ var _BaseLayout_style__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BaseLayout.style */ \"(app-pages-browser)/./src/components/LayoutHeaderNavBar/BaseLayout.style.ts\");\n/* harmony import */ var _common_theme_globalStyles__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/common/theme/globalStyles */ \"(app-pages-browser)/./src/common/theme/globalStyles.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_SITE = {\n    externalId: \"STS-COR\",\n    value: \"All Sites\"\n};\nconst HeaderNavBar = (param)=>{\n    let { children, setLocaleCode, shouldTranslateDynamicState, dynamicTranslationLoadingState } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [authorizedMenuItems, setAuthorizedMenuItems] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { checkPermissionsFromRoutes } = (0,_common_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_5__.useAuthGuard)();\n    const [anchorElUser, setAnchorElUser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedReportingSite, setSelectedReportingSite] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(undefined);\n    const { userInfo, updateSiteInfo } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_common_contexts_AuthGuardContext__WEBPACK_IMPORTED_MODULE_8__.AuthGuardContext);\n    const { availableLanguages, locale, updateLocale } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.TranslationContext);\n    const [disableTranslateDynamic, setDisableTranslateDynamic] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const { shouldTranslateDynamic, setShouldTranslateDynamic } = shouldTranslateDynamicState;\n    const { dynamicTranslationLoading, setDynamicTranslationLoading } = dynamicTranslationLoadingState;\n    const [isTranslated, setIsTranslated] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if ((userInfo === null || userInfo === void 0 ? void 0 : userInfo.availableReportingSites) && userInfo.availableReportingSites.length > 0) {\n            setSelectedReportingSite(userInfo.selectedSite);\n        }\n    }, [\n        userInfo\n    ]);\n    const handleOpenUserMenu = (event)=>{\n        setAnchorElUser(event.currentTarget);\n    };\n    const handleCloseUserMenu = ()=>{\n        setAnchorElUser(null);\n    };\n    const selectedLanguage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (availableLanguages === null || availableLanguages === void 0 ? void 0 : availableLanguages.length) {\n            var _availableLanguages_find;\n            return (_availableLanguages_find = availableLanguages.find((l)=>(l === null || l === void 0 ? void 0 : l.code) == locale)) !== null && _availableLanguages_find !== void 0 ? _availableLanguages_find : availableLanguages.find((l)=>(l === null || l === void 0 ? void 0 : l.code) == _common_hooks_useLocale__WEBPACK_IMPORTED_MODULE_7__.DEFAULT_LOCALE);\n        }\n        return {\n            externalId: \"EN\",\n            language: \"English\",\n            code: \"EN\"\n        };\n    }, [\n        locale,\n        availableLanguages\n    ]);\n    const availableMenuItems = [\n        {\n            route: \"/home\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"MENU.HOME\"),\n            icon: \"home\",\n            onClickHandler: ()=>{\n                router.push(\"/home\");\n            }\n        },\n        {\n            route: \"/global-view\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"MENU.GLOBAL_VIEW\"),\n            icon: \"language\",\n            onClickHandler: ()=>{\n                router.push(\"/global-view\");\n            }\n        },\n        {\n            route: \"/kpi-target\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"KPI_TARGET.TARGET_SETTINGS\"),\n            icon: \"adjust\",\n            onClickHandler: ()=>{\n                router.push(\"/kpi-target\");\n            }\n        },\n        {\n            route: \"/manual-input\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"MENU.MANUAL_INPUT\"),\n            icon: \"settings\",\n            onClickHandler: ()=>{\n                router.push(\"/manual-input\");\n            }\n        },\n        {\n            route: \"/kpi-input-management\",\n            title: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.translate)(\"MENU.KPI_INPUT_MANAGEMENT\"),\n            icon: \"data_check\",\n            onClickHandler: ()=>{\n                router.push(\"/kpi-input-management\");\n            }\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const isTitleTranslated = availableMenuItems.every((item)=>!item.title.includes(\"MENU.\"));\n        setIsTranslated(isTitleTranslated);\n    }, [\n        availableMenuItems,\n        locale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const items = availableMenuItems.filter((item)=>checkPermissionsFromRoutes(item.route));\n        setAuthorizedMenuItems(items);\n    }, [\n        userInfo,\n        isTranslated,\n        locale\n    ]);\n    const DynamicTranslationIcon = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                dynamicTranslationLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 20,\n                    color: \"inherit\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 47\n                }, undefined),\n                disableTranslateDynamic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Translate__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"translate-ico\",\n                    sx: _BaseLayout_style__WEBPACK_IMPORTED_MODULE_10__.dynamicTranslationDisabled\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 21\n                }, undefined) : shouldTranslateDynamic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Translate__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"translate-ico\",\n                    sx: _BaseLayout_style__WEBPACK_IMPORTED_MODULE_10__.dynamicTranslationOff\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Translate__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"translate-ico\",\n                    sx: _BaseLayout_style__WEBPACK_IMPORTED_MODULE_10__.dynamicTranslationOn\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 21\n                }, undefined)\n            ]\n        }, void 0, true);\n    };\n    const headerNavbarProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _availableLanguages_map;\n        return {\n            disableDarkMode: true,\n            logo: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Language__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    fontSize: \"26px\",\n                    marginTop: \"5px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                lineNumber: 157,\n                columnNumber: 19\n            }, undefined),\n            alt: userInfo.lastName + \", \" + userInfo.firstName,\n            src: userInfo.avatar,\n            nameBold: \"Digital Global\",\n            nameLight: \"KPI Bank\",\n            language: selectedLanguage,\n            languages: (_availableLanguages_map = availableLanguages === null || availableLanguages === void 0 ? void 0 : availableLanguages.map((language)=>({\n                    ...language,\n                    language: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.NoTranslate, {\n                        children: language.language\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 31\n                    }, undefined)\n                }))) !== null && _availableLanguages_map !== void 0 ? _availableLanguages_map : [],\n            selectedPlants: [\n                DEFAULT_SITE\n            ],\n            plants: [\n                DEFAULT_SITE\n            ],\n            multPlants: false,\n            items: authorizedMenuItems,\n            buttonConfigurations: [\n                {\n                    Icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_ui_lib__WEBPACK_IMPORTED_MODULE_1__.MatIcon, {\n                        icon: \"notifications\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 27\n                    }, undefined),\n                    Tooltip: \"Notifications\",\n                    onClick: ()=>{\n                         true && window.open(_common_configurations_enviroment__WEBPACK_IMPORTED_MODULE_9__.enviroment.notificationPortalUrl, \"_blank\");\n                    }\n                },\n                {\n                    Icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicTranslationIcon, {}, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 27\n                    }, undefined),\n                    Tooltip: \"\",\n                    onClick: ()=>{},\n                    disabled: true\n                },\n                {\n                    Icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_ui_lib__WEBPACK_IMPORTED_MODULE_1__.MatIcon, {\n                        icon: \"dark_mode\",\n                        sx: {\n                            color: \"#********\",\n                            fontSize: \"1.5rem\",\n                            mt: 0.5\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 27\n                    }, undefined),\n                    Tooltip: \"\",\n                    onClick: ()=>{},\n                    disabled: true\n                }\n            ],\n            onChangeLang: (li)=>{\n                if (li) {\n                    updateLocale(li.code);\n                    setLocaleCode(li.code);\n                }\n            },\n            onChangePlant: (_)=>{},\n            userMenuOnClick: handleOpenUserMenu,\n            sxProps: {\n                fontFamily: 'Roboto, \"Segoe UI\", Arial, Helvetica, sans-serif'\n            }\n        };\n    }, [\n        authorizedMenuItems,\n        selectedLanguage,\n        selectedReportingSite,\n        userInfo\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_theme_globalStyles__WEBPACK_IMPORTED_MODULE_11__.GlobalStyles, {}, void 0, false, {\n                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                lineNumber: 209,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_4__.NoTranslate, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_celanese_ui_lib__WEBPACK_IMPORTED_MODULE_1__.ClnHeaderNavbar, {\n                        ...headerNavbarProps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserPopover__WEBPACK_IMPORTED_MODULE_6__.UserPopover, {\n                        anchorEl: anchorElUser,\n                        onClose: handleCloseUserMenu\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\LayoutHeaderNavBar\\\\index.tsx\",\n                lineNumber: 210,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(HeaderNavBar, \"+FawANvfe72my/A+QQcQqihy8dQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _common_hooks_useAuthGuard__WEBPACK_IMPORTED_MODULE_5__.useAuthGuard\n    ];\n});\n_c = HeaderNavBar;\nvar _c;\n$RefreshReg$(_c, \"HeaderNavBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LayoutHeaderNavBar/index.tsx\n"));

/***/ })

});