"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/components/Home/HomeViewGeneral/WidgetGeneralTable.tsx":
/*!********************************************************************!*\
  !*** ./src/components/Home/HomeViewGeneral/WidgetGeneralTable.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WidgetGeneralTable: function() { return /* binding */ WidgetGeneralTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Table,TableBody,TableCell,TableContainer,TableHead,TableRow!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _common_utils_stripedRowTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/common/utils/stripedRowTable */ \"(app-pages-browser)/./src/common/utils/stripedRowTable.ts\");\n/* harmony import */ var _common_utils_numberFormat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/common/utils/numberFormat */ \"(app-pages-browser)/./src/common/utils/numberFormat.ts\");\n/* harmony import */ var _common_contexts_GlobalKpiContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/common/contexts/GlobalKpiContext */ \"(app-pages-browser)/./src/common/contexts/GlobalKpiContext.tsx\");\n/* harmony import */ var _common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/common/utils/targetCalculator */ \"(app-pages-browser)/./src/common/utils/targetCalculator.ts\");\n/* harmony import */ var _celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @celanese/celanese-sdk */ \"(app-pages-browser)/./node_modules/@celanese/celanese-sdk/build/esm/index.ts\");\n/* harmony import */ var _common_utils_kpiCalculator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/common/utils/kpiCalculator */ \"(app-pages-browser)/./src/common/utils/kpiCalculator.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst WidgetGeneralTable = (param)=>{\n    let { data, target, filters, generalFilter, generalViewMore, setOpenModal, setSelectedKpi } = param;\n    _s();\n    const { setSelectedSiteId, setSelectedMultipleSiteIds } = (0,_common_contexts_GlobalKpiContext__WEBPACK_IMPORTED_MODULE_4__.useGlobalKpiContext)();\n    const [sanitizedData, setSanitizedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getTargetValue = (kpi)=>{\n        const targetObject = getTargetObject(kpi);\n        if (!targetObject || targetObject.length === 0 || !targetObject[0]) {\n            return 0;\n        }\n        if (filters.siteList.length === 1) {\n            return (0,_common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__.calculateSingleTarget)(kpi.kpi.externalId, targetObject[0], generalFilter);\n        } else {\n            return (0,_common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__.calculateMultiTarget)(kpi, targetObject, generalFilter);\n        }\n    };\n    const getTargetObject = (kpi)=>{\n        if (!target || target.length === 0) {\n            return [];\n        }\n        if (filters.siteList.length === 1) {\n            const matchedKpi = target.filter((item)=>item && item.refGlobalVisionKPI && item.refGlobalVisionKPI.externalId === kpi.kpi.externalId);\n            const matchedSite = matchedKpi === null || matchedKpi === void 0 ? void 0 : matchedKpi.filter((item)=>item && item.refReportingSite && kpi.data && kpi.data[0] && kpi.data[0].refReportingSite && item.refReportingSite.externalId === kpi.data[0].refReportingSite.externalId);\n            return matchedSite || [];\n        } else {\n            const matchedKpi = target.filter((item)=>item && item.refGlobalVisionKPI && item.refGlobalVisionKPI.externalId === kpi.kpi.externalId);\n            const matchedSites = matchedKpi.filter((item)=>item && item.refReportingSite && filters.siteList.includes(item.refReportingSite.externalId));\n            return matchedSites || [];\n        }\n    };\n    const isRedKpi = (kpi)=>{\n        var _currentTarget_;\n        const currentTarget = getTargetObject(kpi);\n        let targetValue = 0;\n        let kpiValue = 0;\n        if (!currentTarget || currentTarget.length === 0 || !currentTarget[0]) {\n            return false;\n        }\n        if (filters.siteList.length === 1) {\n            targetValue = (0,_common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__.calculateSingleTarget)(kpi.kpi.externalId, currentTarget[0], generalFilter);\n            kpiValue = (0,_common_utils_kpiCalculator__WEBPACK_IMPORTED_MODULE_7__.getKpiValue)(kpi, filters.period, generalFilter);\n        } else {\n            targetValue = (0,_common_utils_targetCalculator__WEBPACK_IMPORTED_MODULE_5__.calculateMultiTarget)(kpi, currentTarget, generalFilter);\n            kpiValue = (0,_common_utils_kpiCalculator__WEBPACK_IMPORTED_MODULE_7__.getKpiValue)(kpi, filters.period, generalFilter);\n        }\n        return ((_currentTarget_ = currentTarget[0]) === null || _currentTarget_ === void 0 ? void 0 : _currentTarget_.rangeDirection) === \"Above\" ? kpiValue < targetValue : kpiValue > targetValue;\n    };\n    const widgetClick = (kpi)=>{\n        setOpenModal(true);\n        setSelectedKpi(kpi);\n        if (kpi.data.length === 1) {\n            setSelectedSiteId(kpi.data[0].refReportingSite.externalId);\n            setSelectedMultipleSiteIds([]);\n        } else {\n            setSelectedSiteId(\"\");\n            setSelectedMultipleSiteIds(filters.siteList);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"*************** data ***************\", data);\n        if (data && data.length > 0) {\n            try {\n                data.map((kpi)=>{\n                    try {\n                        kpi.redKpi = isRedKpi(kpi);\n                    } catch (error) {\n                        kpi.redKpi = false;\n                    }\n                });\n                const mappedResult = [\n                    ...data\n                ];\n                mappedResult.sort((a, b)=>{\n                    if (a.redKpi === b.redKpi) {\n                        const orderA = a.kpi.order || 0;\n                        const orderB = b.kpi.order || 0;\n                        return orderA - orderB;\n                    }\n                    return a.redKpi ? -1 : 1;\n                });\n                setSanitizedData(mappedResult);\n                console.log(\"**********\", mappedResult);\n            } catch (error) {\n                setSanitizedData([\n                    ...data\n                ]);\n                console.log(\"**********\", [\n                    ...data\n                ]);\n            }\n        } else {\n            setSanitizedData([]);\n        }\n    }, [\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        sx: {\n            overflowX: \"initial\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    sx: {\n                        position: \"sticky\",\n                        top: generalViewMore ? \"45.97px\" : 0,\n                        backgroundColor: \"#ffffff\",\n                        fontWeight: \"bold\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                align: \"left\",\n                                sx: {\n                                    width: \"60%\",\n                                    padding: \"8px 16px\"\n                                },\n                                children: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_6__.translate)(\"TABLE_COLS.KPI\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                align: \"right\",\n                                sx: {\n                                    width: \"20%\",\n                                    padding: \"8px 16px\"\n                                },\n                                children: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_6__.translate)(\"TABLE_COLS.TARGET\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                align: \"right\",\n                                sx: {\n                                    width: \"20%\",\n                                    padding: \"8px 16px\"\n                                },\n                                children: (0,_celanese_celanese_sdk__WEBPACK_IMPORTED_MODULE_6__.translate)(\"TABLE_COLS.ACTUAL\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    children: sanitizedData.map((kpi, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_utils_stripedRowTable__WEBPACK_IMPORTED_MODULE_2__.StripedRow, {\n                            index: index,\n                            sx: {\n                                borderBottom: kpi.redKpi ? \"1px solid #D32F2F\" : \"1px solid rgba(224, 224, 224, 1)\",\n                                backgroundColor: kpi.redKpi ? \"#FCF1EE !important\" : \"inherit\"\n                            },\n                            onClick: ()=>widgetClick(kpi),\n                            style: {\n                                cursor: \"pointer\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    align: \"left\",\n                                    sx: {\n                                        padding: \"8px 16px\",\n                                        borderBottom: \"unset\",\n                                        width: \"60%\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"space-between\",\n                                            alignItems: \"center\",\n                                            fontWeight: \"500\",\n                                            color: kpi.redKpi ? \"#D32F2F\" : \"inherit\"\n                                        },\n                                        children: kpi.kpi.name + (kpi.kpi.symbol != null && kpi.kpi.symbol != \"\" ? \" (\" + kpi.kpi.symbol + \")\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    align: \"right\",\n                                    sx: {\n                                        width: \"20%\",\n                                        color: kpi.redKpi ? \"#D32F2F\" : \"#1F8248\",\n                                        fontWeight: \"600\",\n                                        borderBottom: \"inherit\",\n                                        padding: \"8px 16px\"\n                                    },\n                                    children: (()=>{\n                                        try {\n                                            return (0,_common_utils_numberFormat__WEBPACK_IMPORTED_MODULE_3__.numberFormat)(getTargetValue(kpi));\n                                        } catch (error) {\n                                            return \"-\";\n                                        }\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    align: \"right\",\n                                    sx: {\n                                        width: \"20%\",\n                                        color: kpi.redKpi ? \"#D32F2F\" : \"#1F8248\",\n                                        fontWeight: \"600\",\n                                        borderBottom: \"inherit\",\n                                        padding: \"8px 16px\"\n                                    },\n                                    children: (()=>{\n                                        try {\n                                            return (0,_common_utils_numberFormat__WEBPACK_IMPORTED_MODULE_3__.numberFormat)((0,_common_utils_kpiCalculator__WEBPACK_IMPORTED_MODULE_7__.getKpiValue)(kpi, filters.period, generalFilter));\n                                        } catch (error) {\n                                            return \"-\";\n                                        }\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 25\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n            lineNumber: 143,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Celanese-WorkSpace\\\\GKPI_17_02_2025\\\\Support_GlobalKPIBank\\\\GlobalKPIBank-Portal2\\\\src\\\\components\\\\Home\\\\HomeViewGeneral\\\\WidgetGeneralTable.tsx\",\n        lineNumber: 142,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WidgetGeneralTable, \"osYg/5hMiVxq8aTFhiwtaDWu0BY=\", false, function() {\n    return [\n        _common_contexts_GlobalKpiContext__WEBPACK_IMPORTED_MODULE_4__.useGlobalKpiContext\n    ];\n});\n_c = WidgetGeneralTable;\nvar _c;\n$RefreshReg$(_c, \"WidgetGeneralTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Home/HomeViewGeneral/WidgetGeneralTable.tsx\n"));

/***/ })

});