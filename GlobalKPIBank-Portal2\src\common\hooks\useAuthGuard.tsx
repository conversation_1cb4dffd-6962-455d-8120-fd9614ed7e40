import { UserRolesPermission } from '../clients/user-management-client'
import { AuthGuardPermission, authGuardRules } from 'auth/auth-guard-rules'
import { enviroment } from '../configurations/enviroment'
import { useContext } from 'react'
import { AuthGuardContext } from '../contexts/AuthGuardContext'

export interface AuthGuardResult {
    isAuthorized: boolean
    message: string
}

export const useAuthGuard = () => {
    const { userInfo, loading } = useContext(AuthGuardContext)
    function CheckPermissions(permission: AuthGuardPermission | undefined): AuthGuardResult {
        const userPermission: UserRolesPermission = userInfo

        if (permission) {
            const roles =
                userPermission?.applications?.find((a) => a.applicationCode == enviroment.userManagementAppCode)
                    ?.roles ?? []
            const hasRoles = permission.roleCodes.some((r) => roles.some((x) => x.roleCode === r))
            const hasFeature = permission.features.some((f) =>
                roles.some((x) =>
                    x.features.some(
                        (y) => y.featureCode === f.featureCode && y.featureAccessLevelCode === f.featureAccessLevelCode
                    )
                )
            )

            return {
                isAuthorized: hasRoles || hasFeature,
                message: permission.notAuthorizedMessage,
            }
        } else {
            return {
                isAuthorized: true,
                message: '',
            }
        }
    }

    function checkPermissionsFromRoutes(path: string): boolean {
        const routePermission = authGuardRules.routes.find((c) => c.path === path)
        return CheckPermissions(routePermission).isAuthorized
    }

    function checkPermissionsFromComponents(componentName: string): AuthGuardResult {
        const componentPermission = authGuardRules.components.find((c) => c.name === componentName)
        return CheckPermissions(componentPermission)
    }

    return {
        checkPermissionsFromRoutes,
        checkPermissionsFromComponents,
        loading,
    }
}
