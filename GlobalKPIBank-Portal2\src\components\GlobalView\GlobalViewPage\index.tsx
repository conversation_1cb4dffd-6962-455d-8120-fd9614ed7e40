import { Box, Typography } from '@mui/material'
import GlobalViewTable from '../GlobalViewTable'
import { translate } from '@celanese/celanese-sdk'
import { useMemo, useState } from 'react'
import { ClnTabs } from '@celanese/ui-lib'
import { useGetKpiGroups } from '@/common/hooks/useGetKpiGroups'
import { useAuthGuard } from '@/common/hooks/useAuthGuard'

const styles = {
    tabWrapper: {
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: '8px',
        width: 'auto',
        padding: '30px',
        height: 'calc(100% - 44px)',
    },
}

export const GlobalViewPage = () => {
    const [currentTab, setCurrentTab] = useState(0)
    const { kpiGroupsList, loadingKpiGroups } = useGetKpiGroups()
    const { checkPermissionsFromRoutes } = useAuthGuard()

    const tabGroupsPermission = [
        'globalViewFoundationalKpi',
        'globalViewStewardshipKpi',
        'globalViewQualityKpi',
        'globalViewReliabilityKpi',
        'globalViewEngagementKpi',
    ]
    const tabsContent: React.ReactNode[] = useMemo(() => {
        return kpiGroupsList.map((groupName) => {
            return groupName?.name === 'Engagement' ? (
                <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" sx={{ color: '#083D5B', marginTop: '2rem' }}>
                        {translate('COMING_SOON')}
                    </Typography>
                </Box>
            ) : (
                <GlobalViewTable tabGroup={groupName} />
            )
        })
    }, [kpiGroupsList])

    const tabs = kpiGroupsList.map((groupName, index) => ({
        label: translate(('KPI_TAB_LIST.' + groupName.name.toUpperCase()).toUpperCase()),
        content: tabsContent[index],
        disabled: !checkPermissionsFromRoutes(tabGroupsPermission[index]),
    }))

    return loadingKpiGroups ? (
        <div></div>
    ) : (
        <div style={{ height: '100%', overflow: 'hidden' }}>
            <Typography variant="h3" sx={{ color: '#083D5B', marginBottom: '1rem', fontWeight: 'bold' }}>
                {translate('MENU.GLOBAL_VIEW')}
            </Typography>

            <Box sx={styles.tabWrapper}>
                <ClnTabs value={currentTab} tabs={tabs} onChange={(_e, value) => setCurrentTab(value)} />
            </Box>
        </div>
    )
}
