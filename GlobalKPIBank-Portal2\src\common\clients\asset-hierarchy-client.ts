import { cognite } from '../configurations/cognite'

type Node = {
    externalId: string
    space: string
}

type NodeResponse = {
    items: {
        reportingSites: Node[]
    }
}

const BODY = {
    with: {
        reportingSites: {
            limit: 10000,
            nodes: {
                filter: {
                    and: [
                        {
                            hasData: [
                                {
                                    type: 'container',
                                    space: 'EDG-COR-ALL-DMD',
                                    externalId: 'ReportingSite',
                                },
                            ],
                        },
                    ],
                },
            },
        },
    },
    select: {
        reportingSites: {},
    },
}

export async function getReportingSites(accessToken: string): Promise<Node[]> {
    const url = cognite.baseUrl + '/api/v1/projects/' + cognite.project + '/models/instances/query'
    const request = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
            'X-Cdp-App': cognite.cogniteXCdpApp,
        },
        body: JSON.stringify(BODY),
    })

    if (request.status !== 200) {
        return []
    }

    const result = (await request.json()) as NodeResponse

    return result.items.reportingSites
}
