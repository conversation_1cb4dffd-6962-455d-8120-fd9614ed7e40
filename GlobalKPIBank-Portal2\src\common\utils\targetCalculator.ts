import dayjs from 'dayjs'
import { GlobalVisionKpiTableModel } from '../models/globalVisionKpiTableModel'
import { GlobalVisionKpiTarget } from '../models/globalVisionKpiTarget'
import { getOperatorType } from './kpiCalculationRules'
import { GeneralFilter } from '../models/homeGeneralFilter'

const getTargetYtdFactor = (operationType: string): number => {
    if (operationType === 'Sum') {
        const month = dayjs().subtract(1, 'month').month()
        return month + 1
    }
    if (operationType === 'Average') {
        return 1
    }

    return 1
}

export const calculateSingleTarget = (
    kpiExternalId: string,
    targetValue: GlobalVisionKpiTarget,
    generalFilter: GeneralFilter
): number => {
    const operationType = getOperatorType(kpiExternalId)
    if (generalFilter.externalId !== 'MONTH') {
        const factor = getTargetYtdFactor(operationType)
        return targetValue.value * factor
    }
    return targetValue.value
}

export const calculateMultiTarget = (
    kpi: GlobalVisionKpiTableModel,
    target: GlobalVisionKpiTarget[],
    generalFilter: GeneralFilter
): number => {
    const operationType = getOperatorType(kpi.kpi.externalId)

    if (generalFilter.externalId !== 'MONTH') {
        const targetValue: number = target.reduce((accumulator, currentItem) => {
            return accumulator + currentItem.value
        }, 0)
        const factor = getTargetYtdFactor(operationType)

        if (operationType === 'Sum') {
            return targetValue * factor
        }
        return (targetValue / target.length) * factor
    }

    const targetValue: number = target.reduce((accumulator, currentItem) => {
        return accumulator + currentItem.value
    }, 0)

    if (operationType === 'Sum') {
        return targetValue
    }
    return targetValue / target.length
}
