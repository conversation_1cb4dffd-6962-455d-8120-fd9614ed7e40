<h1 align="center">
   <a href="#"> GlobalKPIBank Portal2 </a>
</h1>

<h3 align="center">
    Set of Next.js frontend tools for integration with GlobalKPIBank API in order to show a friendly user interface of all KPIs.
</h3>

<h4 align="center">
    Status: in development
</h4>

<p align="center">
 <a href="#about">About</a> •
 <a href="#how-to-run">How to Run</a>

</p>

## About

The GlobalKPIBank Portal 2 is a Next.js project that shows the main user interface with all its features and integrates with GlobalKPIBank API to requests data from Cognite.

It uses ng-apexcharts to show barCharts related to KPIs.

## How to run

### Pre-requisites

Before you begin, you will need to have the following tools installed on your machine:
Windows
[Node.js v14.21.3 or newer](https://nodejs.org/en/download/prebuilt-installer/current) and a Package Manager (npm or yarn).

[Visual Studio Code](https://code.visualstudio.com/) (Windows/Linux/MacOs).

### Instalation

1. Clone the repository: `git clone <url>`
2. Navigate to your project's directory: `cd ...\Support_GlobalKPIBank\GlobalKPIBank-Portal2`

## Setting Configurations

### Celanese MaterialsUI

Follow this steps before running the project:

1. On the project's terminal, run: `npx vsts-npm-auth -f -config .npmrc`
2. An popup window will show to log in with your @celanese credentials. If the popup doesn't open, there will be a link on terminal with the code to authenticate.

### Change to Localhost

Change the environments constants to localhost from `GlobalKPIBank-Portal2\src\common\configurations\environment.ts`

### Build project for the first time

On the project's terminal, run this command: `npm run build` this will take a while.

### Apps Translation

1. Clone the Apps Translation repository: `git clone <url>`

<details>
<summary>.env file</summary>

```bash
PYTHON_ENV="development"
ECHO="FALSE"
#change this to AppTranslation
CLIENT_ID=07d2151f-7b19-4e88-976b-c84cbbc24a3b,0ff3eea0-1496-4975-b132-4ed3c12c97b2,a04d3021-5d21-4b23-98eb-d8ab6a10e826,7ed067f5-56f5-4f01-a8e2-79e89294b731,9a42fc66-6786-408f-b1e6-4cbb1070f285,c39d0a92-c8e5-4768-8ba6-b0025907991d,af872243-3c30-4d33-ba85-18cd4c2717b8,********-9012-41a4-b2b8-4de598b997f7,70611b89-2271-438d-bdd6-ad2e8192d9b6,a3b50837-4905-4868-a373-30ce00633854,55d46918-83e6-4704-b0dc-3b55e6dd7484,2d7f85ad-224d-4802-9f7b-27b2634f81d2,f512413e-b55d-4185-9df7-3c9053bd5844,5458b11e-b631-46e4-a52c-e33e203df321,5c7b97ae-9014-4461-af0e-d4884e3b82e9,7681c596-8545-4b94-8c4b-b130f482daf4,01c3a55a-e036-4030-854b-8c256966f6aa,71dd7f5e-19e1-4353-94b2-a9af3d089a2e,be515d0f-0f65-4125-a7a2-69e7b53d5713,16aeb38e-9dae-4d81-ba4e-2461b8148e14,8e4bd660-ea93-463e-920a-8642392b71a0,07d2151f-7b19-4e88-976b-c84cbbc24a3b,2101fcfc-0bb2-480b-b8f0-69f228218fda,68904def-7651-4fb2-ae99-695d2c397fb3,a5cadb9c-6b96-448f-8845-1c27d127d442,a0dad8b1-c271-4761-94e9-65daea5adf1c,c78f4d88-6cc1-40ea-ba5e-105c64e40fc7,
TENANT_ID=7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37
USER_KEY=upn

#MSGRAPH
MS_GRAPH_API_BASE_URI=https://graph.microsoft.com/v1.0

#COGNITE
COGNITE_GRAPHQL_MODEL_SPACE=ATL-COR-ALL-DML
COGNITE_GRAPHQL_INSTANCES_SPACE=ATL-COR-ALL-DAT
COGNITE_GRAPHQL_SHARED_MODEL_SPACE=TEMP-AppsMigration
COGNITE_PROJECT_NAME=celanese-dev
COGNITE_CLIENT_NAME=app-translation
COGNITE_BASE_URI=https://az-eastus-1.cognitedata.com
COGNITE_GRAPHQL_URI=https://az-eastus-1.cognitedata.com/api/v1/projects/celanese-dev/userapis/spaces/TEMP-AppsMigration/datamodels/AppMigrationPOC/versions/1_0_0/graphql
COGNITE_CLUSTER=az-eastus-1.cognitedata.com

#COGNITE - AUTH
AUTH_CLIENT_ID=e717f8cd-78f8-4c9f-bfc1-4b3b28f7e553
#change this
AUTH_TENANT_ID=7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37
AUTH_SECRET=****************************************
AUTH_SCOPES=https://az-eastus-1.cognitedata.com/.default
AUTH_GRAPH_SCOPES=offline_access,openid,profile,email,user.read
AUTH_TOKEN_URI=https://login.microsoftonline.com/7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37/oauth2/v2.0/token
AUTH_TOKEN_OVERRIDE=
AUTHORITY=https://login.microsoftonline.com/7a3c88ff-a5f6-449d-ac6d-e8e3aa508e37

AZURE_SERVICES_KEY=528c988430ff4e0ebb77ca283787544f
COGNITE_SPACE=ATL-COR-ALL-DML
COGNITE_TABLE=LabelTranslation

AZURE_SERVICES_REGION=eastus
AZURE_SERVICES_ENDPOINT=https://api.cognitive.microsofttranslator.com/

UTILITIES_APPS=APP-GKPI
UTILITIES_LANGUAGES_TO_IGNORE=en
```

</details><p>

```bash
# Access the project folder cmd/terminal or vscode
$ Apps%20Translation\

# Create or update the .env file to run locally
$ Use the referenced copy above

# Run the following command to create a virtual environment named `.venv`:
$ python -m venv .venv

# Run the following command to activate the virtual environment:
$ .\.venv\Scripts\activate

# Install dependencies:
$ pip install -r requirements.txt

# Run the User Management API built with FastAPI:
$ uvicorn app.main:app --reload --port 8003

# Access the link by entering the port entered at the time of compilation. Currently the port hosted by GlobalKPIBank-Portal is the main interface
$ http://localhost:[port]/
```

### Run in VsCode or Terminal

```bash
# Access the project folder cmd/terminal or vscode
$ Support_GlobalKPIBank\GlobalKPIBank-Portal2

# Run the command in the terminal
$ npm run dev

# Access the link by entering the port entered at the time of compilation
$ http://localhost:[port]/
```

After the application is running, you might notice that the App Translation project creates a registry on Local Storage. Once created, you don't need to keep the App Translation project running anymore.

## Lint before Pull Request

Before opening a new PR, on the project's terminal, run the command: `npm run lint` so the code can be organized and follow the project's pattern.
